apply from: file("${rootDir}/config/library.gradle")
apply plugin: 'kotlin-android'

android {
    namespace "com.loper7.date_time_picker"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {
    implementation 'androidx.core:core:1.0.0'
    implementation rootProject.ext.dependencies["design"]
    implementation rootProject.ext.dependencies["kotlin-stdlib"]
}

// 创建自定义任务，将生成的 aar 文件复制到 libs 目录
task copyAarToLibs {
    doLast {
        // 复制到 app-wms/lib 目录
        copy {
            from "$buildDir/outputs/aar/"
            into "$rootDir/app-wms/lib/"
            include "*.aar"
        }
        println "AAR files copied to $rootDir/app-wms/lib/"

        // 复制到 cyclecount/lib 目录
        copy {
            from "$buildDir/outputs/aar/"
            into "$rootDir/cyclecount/lib/"
            include "*.aar"
        }
        println "AAR files copied to $rootDir/cyclecount/lib/"
    }
}

// 让 assembleRelease 任务完成后自动执行复制任务
afterEvaluate {
    tasks.named('assembleRelease').configure {
        finalizedBy copyAarToLibs
    }
}
