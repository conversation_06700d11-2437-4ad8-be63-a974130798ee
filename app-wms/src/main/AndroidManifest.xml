<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.unis.wms">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /><!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" /><!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 连接网络权限，用于执行云端语音能力 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 读取网络信息状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 获取当前wifi状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 允许程序改变网络连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 读取手机信息权限 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" /> <!-- 外存储写权限，构建语法需要用到此权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 配置权限，用来记录应用配置信息 -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 手机定位信息，用来为语义等功能提供定位，提供更精准的服务 -->
    <!-- 定位信息是敏感信息，可通过Setting.setLocationEnable(false)关闭定位请求 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 允许解锁屏幕 -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 震动 -->

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- android 9.0上使用前台服务，需要添加权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".WmsApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/appThemeStyle"
        android:usesCleartextTraffic="true">
        <!-- 适配全面屏 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.3" />

        <activity
            android:name=".login.SplashActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".login.LoginActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <activity
            android:name=".home.HomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTask" />

        <activity
            android:name=".fcm.LucidActivity"
            android:theme="@style/activityAlertDialog" />

        <service android:name=".fcm.AppUpdateService" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.file.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/update_apk_paths" />
        </provider>

        <activity
            android:name=".fcm.messagecenter.takeovermessage.MessageGroupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".fcm.messagecenter.takeovermessage.MessageWindowActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".fcm.messagecenter.taskmessage.TaskMessageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.takeovermanage.TakeOverManageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.profile.PasswordManagementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.profile.MyAccountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />

        <activity
            android:name=".home.more.MoreChildActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.tasklist.ReceiveTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.dockcheckin.DockCheckInActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.offload.OffloadActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.lpsetup.LpSetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.stage.ReceiveStageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.snscan.SnScanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.review.LpSetupReviewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.materialmanager.MaterialManagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.materialmanager.itemline.MaterialItemLineActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.materialmanager.receiptlist.ReceiptListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.pick.work.PickStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

        <activity
            android:name=".pick_task.pick.work.progress.PickStepProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.tasklist.PickTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".step_menu.TaskStepMenuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.stage.StageStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

        <activity
            android:name=".movement.method.MovementMethodActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".movement.create.MovementCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".movement.bindequipment.MovementBindEquipmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".movement.work.MovementTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".movement.tasklist.MovementTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".put_away_task.ui.PutAwayTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

        <activity
            android:name=".put_away_task.ui.PutAwayHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".put_away_task.tasklist.PutAwayTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".replenishment.tasklist.ReplenishmentTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".replenishment.create.ReplenishmentCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".replenishment.bindequipment.ReplenishmentBindEquipmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".replenishment.work.ReplenishmentTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.receiving.tasklist.TransLoadReceivingTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.receiving.TransLoadReceivingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.loading.tasklist.TransLoadLoadingTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.loading.TransLoadLoadingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.spotcheck.SpotCheckActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.transferdocklocation.TransferDockLocationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.dockcheck.tasklist.DockCheckTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.dockcheck.DockCheckTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.gps.GpsBoundListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload.gps.GpsBindActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.userinfolist.UserInfoListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".general.tasklist.GeneralTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".general.work.GeneralWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".put_back.tasklist.PutBackTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".put_back.emptytask.EmptyPutBackTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".more.inventorysearch.InventorySearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".more.inventorysearch.view.InventoryViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.lpsncollect.LpSnCollectWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".load.taskList.LoadTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".load.LoadTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.stage_to_wall.StageToWallStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.order_pick.OrderPickStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.sorting_to_wall.SortingToWallActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".clp_bonding.CLPBondingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.consolidatetopallet.ConsolidateToPalletTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pack.createtask.PackCreateTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pack.tasklist.PackTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".put_back.create.CreatePutBackTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".task_search.TaskSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".task_search.advanced.AdvancedSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.customerlist.CustomerInfoListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_task.pick.return_inventory.PickReturnActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.printertest.PrinterTestActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.palletlabelreprint.PalletLabelReprintActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.lptemplate.LpTemplateListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.lptemplate.create.LpTemplateCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".receive.lpsetup.review.edit.LpSetupEditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".common.bluetoothdevice.BluetoothDeviceActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_to_light.setting.location.SetupLightAndLocationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".dockcheckin.DockCheckInActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".load.dockcheckin.checkinverification.CheckInVerificationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>

        <activity
            android:name=".receive.dockcheckin.review.DockCheckInItemReviewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".internaltransfer.out.tasklist.TransferOutTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".internaltransfer.inn.tasklist.TransferInTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".internaltransfer.out.TransferOutTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".internaltransfer.inn.TransferInTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".material.receive.MaterialReceiveActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.labelbatchprint.LabelBatchPrintActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.time_sheet.PunchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.shuttletask.tasklist.ShuttleTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.shuttletask.create.ShuttleTaskCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.shuttletask.selectequipment.SelectEquipmentListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.lpdetail.LpDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.equipmentInquiry.EquipmentInquiryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.equipmentInquiry.detailinfo.EquipmentDetailInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.equipmentInquiry.transfer.InventoryTransferConfirmActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick_task.pick.light_event.PickToLightEventActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".put_away_task.ui.PutAwayByItemHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.lplabelprint.PrintLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.itemlabelprint.ItemLabelPrintActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".movement.batchmovement.BatchMovementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".ot_control.OtControlActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".ot_control.list.OtRequestListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".ot_control.request_detail.OtRequestDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".put_away_task.ui.PutAwayUploadPhotosActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".assemblytask.work.AssemblyTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".assemblytask.task_list.AssemblyTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".outbound_qc_task.OutboundQcTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".outbound_qc_task.task_list.OutboundQcTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".task_action.TaskActionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden"/>

        <activity
            android:name=".task_action.padding_task_action.PendingTaskActionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.approvalcenter.ApprovalCenterSummaryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".more.approvalcenter.locationoverride.LocationOverrideRequestProcessListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.stackheight.CollectStackHeightCardActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".more.locationoccupancy.LocationOccupancyCardActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

    </application>
</manifest>