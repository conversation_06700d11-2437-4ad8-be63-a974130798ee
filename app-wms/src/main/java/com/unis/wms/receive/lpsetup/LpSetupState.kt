package com.unis.wms.receive.lpsetup

import com.linc.platform.print.model.LabelSizeEntry
import com.unis.platform.common.model.configurationmap.ConfigurationMapEntity
import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.lp_v2.model.ReceiveMethodEntity
import com.unis.platform.receive.model.ItemLineEntity
import com.unis.platform.receive.model.ReceiveTaskEntity
import com.unis.reactivemvi.mvi.DeferredUiEvent
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent
import com.unis.wms.receive.lpsetup.collectiteminfo.CollectItemInfoConfigData
import com.unis.wms.receive.lpsetup.itemconfig.SingleItemConfigDataState

data class LpSetupDataState(
    val userId: String,
    val facilityId: String,
    val receiveTaskEntity: ReceiveTaskEntity,
    val customerEntity: CustomerEntity? = null,
    val configurationMapEntity: ConfigurationMapEntity? = null,
    val selectedLocation: LocationEntity? = null,
    val receiptItemLines: List<ItemLineEntity> = listOf(),
    val receiveMethod: ReceiveMethodEntity = ReceiveMethodEntity.RECEIVE_BY_SINGLE_ITEM,
    val labelSize: LabelSizeEntry? = LabelSizeEntry.TWO_ONE,
    val receivedLpTemplateIdsMap: Map<String, Set<String>> = mapOf(), // Map<ItemSpecId,TemplateIds>
) : ReactiveDataState {
}

data class LpSetupUiState(val id: String? = null) : ReactiveUiState {
}

interface LpSetupEvent {
    data class LpSetupProcessChanged(val lpSetupProcess: LpSetupProcess) : UiEvent
    data class LocationItemNotMatchConfirm(val message: String) : DeferredUiEvent<Boolean?>
    object ReceiveDoneAskCloseStep : UiEvent
    data class CloseStepFailure(val errMsg: String) : UiEvent
    data class CloseTaskFailure(val errMsg: String) : UiEvent
    object CollectWorkerTotalHours : UiEvent
    object FinishActivity : UiEvent
    object InvalidateOptionsMenu : UiEvent
}

sealed interface LpSetupProcess {
    object SelectReceiveMethod : LpSetupProcess
    object SingleItemConfig : LpSetupProcess
    object LpSetupReview : LpSetupProcess
    data class SingleItemSummary(val itemLineCompose: SingleItemConfigDataState.ReceiveItemLineCompose) : LpSetupProcess
    object NewItemLine : LpSetupProcess
    data class CollectItemInfo(val configData: CollectItemInfoConfigData) : LpSetupProcess
}