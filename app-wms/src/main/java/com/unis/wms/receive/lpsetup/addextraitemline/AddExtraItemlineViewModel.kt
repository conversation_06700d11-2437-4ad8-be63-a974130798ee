package com.unis.wms.receive.lpsetup.addextraitemline

import com.unis.platform.item.model.ItemEntity
import com.customer.widget.common.VoidCallBack
import com.customer.widget.common.safeCount
import com.unis.linc.common.extensions.toException
import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.uom.model.UomEntity
import com.unis.platform.receipt.model.ReceiptEntity
import com.unis.platform.receipt.model.ReceiptItemlineCreate
import com.unis.platform.receive.model.ItemLineEntity
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.*
import com.unis.reactivemvi.SnackType
import com.unis.wms.R
import com.unis.wms.receive.lpsetup.LpSetupViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * <AUTHOR>
 * @date 2022/09
 */
class AddExtraItemlineViewModel(
        private val taskItemLines: List<ItemLineEntity>,
        receipts: List<ReceiptEntity>,
        val customer: CustomerEntity,
        val activityViewModel : LpSetupViewModel,
        initialDataState: AddExtraItemlineDataState = AddExtraItemlineDataState(receipts = receipts),
        initialUiState: AddExtraItemlineUiState = AddExtraItemlineUiState(),
        val repository: AddExtraItemlineRepository = AddExtraItemlineRepository(),
) : ReactiveViewModel<AddExtraItemlineDataState, AddExtraItemlineUiState>(initialDataState, initialUiState) {
    init {
        if (dataState.receipts.size == 1) {
            selectReceipt(dataState.receipts.first())
        }
    }

    fun selectReceipt(receiptEntry: ReceiptEntity?) {
        receiptEntry ?: return
        setDataState { copy(selectedReceipt = receiptEntry) }
        setUiState { copy(selectedReceipt = receiptEntry, isReceiptPanelExpanded = false, isItemPanelExpanded = true) }
    }

    fun scanItem(
        barcode: String,
        onMultiItem: (List<ItemEntity>) -> Flow<ItemEntity?>,
    ) {
        val customerId = dataState.selectedReceipt?.customerId
        requireNotNull(customerId)

        launch {
            val itemResult = requestAwait(repository.searchItemSpec(barcode, customerId)).onFailure { return@launch }
            val items = itemResult.getOrNull()?.data ?: listOf()
            val item = when (items.safeCount()) {
                0 -> {
                    showToast(R.string.msg_item_not_found)
                    return@launch
                }
                1 -> items.first()
                else -> onMultiItem(items).firstOrNull() ?: return@launch
            }
            val uomsResult = requestAwait(repository.searchSortedUom(item.id)).onFailure { return@launch }
            val uoms = uomsResult.getOrNull() ?: listOf()
            val selectedUom = if (uoms.size == 1) uoms.first() else null
            setDataState { copy(selectedItem = item, uoms = uoms, selectedUom = selectedUom) }
            setUiState { copy(selectedItem = item, selectedUom = selectedUom, isItemPanelExpanded = false, isInfoPanelExpanded = true) }
        }
    }

    fun selectUom(uom: UomEntity?) {
        uom ?: return
        setDataState { copy(selectedUom = uom) }
        setUiState { copy(selectedUom = uom) }
    }

    fun scanLotNumber(barcode: String) {
        setDataState { copy(lotNumber = barcode) }
        setUiState { copy(lotNumber = barcode) }
    }

    fun setQty(qty: String) {
        setDataState { copy(qty =  qty.toDoubleOrNull()) }
    }

    fun submit(onSuccess: VoidCallBack) {
        runCatching { validateSubmit() }.onFailure {
            showToast(it.message!!)
            return
        }
        val itemLine = ReceiptItemlineCreate(
                itemId = dataState.selectedItem!!.id,
                uomId = dataState.selectedUom!!.uomId,
                lotNo = dataState.lotNumber,
                receiptId = dataState.selectedReceipt!!.id,
                qty = dataState.qty?: 1.0
        )
        request(repository.addItemLine(itemLine)) {
            showSnack(SnackType.SuccessV1(), R.string.success)
            activityViewModel.refreshItemLines()
            onSuccess.call()
        }
    }

    @Throws
    private fun validateSubmit() {
        dataState.selectedReceipt ?: throw getString(R.string.error_msg_select_receipt).toException
        dataState.selectedItem ?: throw getString(R.string.msg_please_select_one_item).toException
        dataState.selectedUom ?: throw getString(R.string.msg_please_select_one_unit).toException
        dataState.qty ?: throw getString(R.string.msg_please_input_qty).toException
        if ((dataState.qty ?: 0.0) < 1.0) throw getString(R.string.msg_please_input_qty).toException
        validateLotNumber()
    }

    @Throws
    private fun validateLotNumber() {
        if (!dataState.selectedItem!!.requireCollectLotNoOnReceive) return
        val lotNumber = dataState.lotNumber
        if (lotNumber.isNullOrEmpty()) return
        val hasSameLotNumber = taskItemLines.any { it.itemId == dataState.selectedItem!!.id && it.lotNo == lotNumber }
        if (hasSameLotNumber) throw getString(R.string.toast_add_item_same_lot).toException
    }

}