package com.unis.wms.receive.lpsetup.collectiteminfo

import android.text.TextUtils
import com.customer.widget.GridChipItem
import com.customer.widget.common.addToNewList
import com.linc.platform.baseapp.model.BaseItemDimension
import com.linc.platform.foundation.model.UpdateFromEntry
import com.linc.platform.utils.StringUtil
import com.linc.platform.utils.UnitConvetUtil
import com.unis.linc.common.extensions.toException
import com.unis.platform.common.model.template.SingleItemLpConfigurationEntity
import com.unis.platform.common.model.template.SingleItemLpConfigurationQueryEntity
import com.unis.platform.common.model.template.SingleItemLpConfigurationUpdateEntity
import com.unis.platform.common.model.template.TemplateStatusEntity
import com.unis.platform.common.model.uom.VolumeUomEntity
import com.unis.platform.common.model.uom.WeightUomEntity
import com.unis.platform.item.model.ItemEntity
import com.unis.platform.item.model.ItemStatusEntity
import com.unis.platform.item.model.ItemUpdateEntity
import com.unis.platform.itemgroup.model.ItemGroupEntity
import com.unis.platform.location_v2.model.LinearUnit
import com.unis.platform.uom.model.DualUomTypeEntity
import com.unis.platform.uom.model.UomEntity
import com.unis.platform.uom.model.UomStatusEntity
import com.unis.platform.util.NumberUtil
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvi.subscribe
import com.unis.reactivemvi.mvi.subscribeNotNull
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.requestAllAwait
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.receive.lpsetup.LpSetupViewModel
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @date 2022/09
 */

class CollectItemInfoViewModel(
    val activityViewModel : LpSetupViewModel,
    val itemSpec: ItemEntity,
    private val recurringStorageRateByPallet: Boolean,
    private val isCollectItemGroupAtReceiving: Boolean,
    initialDataState: CollectItemInfoDataState = CollectItemInfoDataState(photoIds = itemSpec.imageFileIds),
    initialUiState: CollectItemInfoUiState = CollectItemInfoUiState(),
    private val repository: CollectItemInfoRepository = CollectItemInfoRepository(),
) : ReactiveViewModel<CollectItemInfoDataState, CollectItemInfoUiState>(initialDataState, initialUiState) {

    private val itemSpecId get() = itemSpec.id
    private var cacheDimension: BaseItemDimension? = null

    init {
        setInitialState()
        initData()
        autoUpdateShowInsideQty()
        autoUpdateUnitInfo()
        mapDataToUi()
    }

    private fun setInitialState() {
        setDataState {
            copy(
                description = itemSpec.description,
                shortDescription = itemSpec.shortDescription,
                upcCode = itemSpec.upcCode,
                upcCodeCase = itemSpec.upcCodeCase,
            )
        }
    }

    private fun initData() = launch {
        setDataState { copy(newUomDefinitionName = null, isNewUom = false) }
        if (isCollectItemGroupAtReceiving) {
            requestAwait(repository.getItemGroups(itemSpec.customerId)).onSuccess {
                setDataState { copy(selectItemGroup = it?.findLast { itemGroup-> itemSpec.groupIds?.contains(itemGroup.id)?: false}, itemGroups = it?: listOf()) }
            }
        }
        if (TextUtils.isEmpty(itemSpecId)) {
            val msg = getString(R.string.msg_item_id_empty_please_contact_administrator)
            fireEvent { CollectItemInfoUiEvent.ShowDataExceptionDialog(msg) }
            return@launch
        }
        val unitResult = requestAwait(repository.getItemUnit(itemSpecId))
        var uomList = unitResult.getOrNull()
        if (uomList.isNullOrEmpty()) return@launch
        val baseUnit = uomList.firstOrNull { it.isBaseUom } ?: uomList.first()
        setDataState {
            copy(unitList = uomList, selectedUomDefinition = baseUnit)
        }
        if (recurringStorageRateByPallet) {
            awaitDataState()
            updateUoms(uomList[0])
            val itemUoms = uomList.map { unit ->
                GridChipItem(
                    id = unit.id,
                    item = unit,
                    title = unit.name,
                    selected = TextUtils.equals(uomList[0].id, unit.id))
            }
            setUiStateAwait { copy(itemUoms = itemUoms) }
            loadItemLpTemplate()
        }
    }

    private fun autoUpdateShowInsideQty() = subscribe(CollectItemInfoDataState::unitOfSelectedUom) {
        setDataState {
            val needCollectInsideQty = selectedUomDefinition != null && it == null
            copy(insideQtyCompose = insideQtyCompose.copy(needCollect = needCollectInsideQty))
        }
    }

    private fun autoUpdateUnitInfo() = subscribeNotNull(CollectItemInfoDataState::unitOfSelectedUom) {
        val length = UnitConvetUtil.convertToIN(it.length?:0.0, if (it.linearUom != null) it.linearUom else LinearUnit.INCH)
        val width = UnitConvetUtil.convertToIN(it.width?:0.0, if (it.linearUom != null) it.linearUom else LinearUnit.INCH)
        val height = UnitConvetUtil.convertToIN(it.height?:0.0, if (it.linearUom != null) it.linearUom else LinearUnit.INCH)
        val weight = UnitConvetUtil.convertToPound(it.weight?:0.0, if (it.weightUom != null) it.weightUom else WeightUomEntity.POUND)
        setDataState { copy(length = length, width = width, height = height, weight = weight) }
    }

    private fun mapDataToUi() {
        mapDataToUi(CollectItemInfoDataState::description, CollectItemInfoUiState::description) { it }
        mapDataToUi(CollectItemInfoDataState::shortDescription, CollectItemInfoUiState::shortDescription) { it }
        mapDataToUi(CollectItemInfoDataState::upcCode, CollectItemInfoUiState::upcCode) { it }
        mapDataToUi(CollectItemInfoDataState::upcCodeCase, CollectItemInfoUiState::upcCodeCase) { it }
        mapDataToUi(CollectItemInfoDataState::selectedUomDefinition, CollectItemInfoUiState::selectedUomDefinition) { it }
        mapDataToUi(CollectItemInfoDataState::length, CollectItemInfoUiState::length) { it }
        mapDataToUi(CollectItemInfoDataState::width, CollectItemInfoUiState::width) { it }
        mapDataToUi(CollectItemInfoDataState::height, CollectItemInfoUiState::height) { it }
        mapDataToUi(CollectItemInfoDataState::weight, CollectItemInfoUiState::weight) { it }
        mapDataToUi(CollectItemInfoDataState::insideQtyCompose, CollectItemInfoUiState::insideQtyCompose) { it }
        mapDataToUi(CollectItemInfoDataState::isNewUom, CollectItemInfoUiState::isNewUom) { it }
        mapDataToUi(CollectItemInfoDataState::selectItemGroup, CollectItemInfoUiState::selectItemGroup) { it }
        mapDataToUi(CollectItemInfoDataState::layer, CollectItemInfoUiState::layer) { it }
    }

    fun addItemPhoto(photoId: String) {
        val newPhotoIds = dataState.photoIds.addToNewList(photoId)
        request(repository.updateItemSpec(itemSpecId, ItemUpdateEntity().apply {
            this.imageFileIds = newPhotoIds
            this.tags = itemSpec.tags
            this.name = itemSpec.name
            this.code = itemSpec.code
            this.customerId = itemSpec.customerId
            this.shippingRule = itemSpec.shippingRule
        })) {
            setDataState { copy(photoIds = newPhotoIds, itemInfoUpdated = true) }
        }
    }

    fun deleteItemPhotos(photoIds: List<String>) {
        val toMutableList = dataState.photoIds?.toMutableList()
        toMutableList?.removeAll(photoIds)
        toMutableList?.let {
            request(repository.updateItemSpec(itemSpecId, ItemUpdateEntity().apply {
                this.imageFileIds = toMutableList
                this.tags = itemSpec.tags
                this.name = itemSpec.name
                this.code = itemSpec.code
                this.customerId = itemSpec.customerId
                this.shippingRule = itemSpec.shippingRule
            })) {
                setDataState { copy(photoIds = toMutableList, itemInfoUpdated = true) }
            }
        }
    }

    fun addNewUomPhotos(photoIds: List<String>) {
        val newPhotoIds = dataState.newUomPhotoIds.addToNewList(photoIds)
        setDataState { copy(newUomPhotoIds = newPhotoIds) }
    }

    fun deleteNewUomPhotos(photoIds: List<String>) {
        val toMutableList = dataState.newUomPhotoIds?.toMutableList()
        toMutableList?.removeAll(photoIds)
        toMutableList?.let {
            setDataState { copy(newUomPhotoIds = toMutableList) }
        }
    }

    fun setNewUomName(uomName: String) {
        setDataState { copy(newUomDefinitionName = uomName) }
    }

    fun setIsNewUom(isNewUom: Boolean) {
        setDataState { copy(isNewUom = isNewUom) }
        if (isNewUom) {
            clearUomDimension()
        }
    }

    fun setLayerValue(layer: String) {
        setDataState { copy(layer = layer) }
        updateDimensionForLayer()
    }

    fun setDescription(desc: String) {
        setDataState { copy(description = desc) }
    }

    fun setShortDescription(shortDesc: String) {
        setDataState { copy(shortDescription = shortDesc) }
    }

    fun setUpc(upc: String) {
        setDataState { copy(upcCode = upc) }
    }

    fun setUpcCase(upcCase: String) {
        setDataState { copy(upcCodeCase = upcCase) }
    }

    private fun updateDimension() {
        withDataState {
            cacheDimension?.apply {
                val length = convertToIN(getBaseLength())
                val width = convertToIN(getBaseWidth())
                val layer = (dataState.layer ?: "1").toInt()
                val resultH = getBaseHeight() / layer
                val resultW = getBaseWeight() / layer
                val height = convertToIN(resultH)
                val weight = convertToLB(resultW)
                setDataState {
                    copy(length = length, width = width, height = height, weight = weight)
                }
            }
        }
    }

    private fun updateDimensionForLayer() {
        withDataState {
            cacheDimension?.apply {
                val length = dataState.length
                val width = dataState.width
                val layer = (dataState.layer ?: "1").toInt()
                val resultH = getBaseHeight() / layer
                val resultW = getBaseWeight() / layer
                val height = convertToIN(resultH)
                val weight = convertToLB(resultW)
                setDataState {
                    copy(length = length, width = width, height = height, weight = weight)
                }
            }
        }
    }

    fun setUom(uom: UomEntity) {
        setDataState { copy(selectedUomDefinition = uom) }
    }

    fun setItemGroup(group: ItemGroupEntity) {
        setDataState { copy(selectItemGroup = group) }
    }

    fun setLength(length: String) {
        setDataState { copy(length = length) }
    }

    fun setWidth(width: String) {
        setDataState { copy(width = width) }
    }

    fun setHeight(height: String) {
        setDataState { copy(height = height) }
    }

    fun setWeight(weight: String) {
        setDataState { copy(weight = weight) }
    }

    fun setInsideQty(qty: String) {
        setDataState { copy(insideQtyCompose = insideQtyCompose.copy(qty = qty)) }
        
        val insideUnit = dataState.insideQtyCompose.selectedUnit
        val baseUnit = dataState.unitList.firstOrNull { it.isBaseUom }
        if (insideUnit != null) {
            val unitBaseQty = if (baseUnit != null) (baseUnit.baseQty * (qty.toDoubleOrNull() ?: 0.0)) else (qty.toDoubleOrNull() ?: 0.0)
            val insideBaseQty = insideUnit.baseQty
            if (insideBaseQty != 0.0 && insideBaseQty == unitBaseQty) {
                setDataState { 
                    copy(
                        length = insideUnit.length.toString(),
                        width = insideUnit.width.toString(),
                        height = insideUnit.height.toString(),
                        weight = insideUnit.weight?.toString()
                    )
                }
            } else {
                clearUomDimension()
            }
        }
    }

    fun setInsideUom(unit: UomEntity) {
        setDataState { copy(insideQtyCompose = insideQtyCompose.copy(selectedUnit = unit)) }
        
        val unitQty = dataState.insideQtyCompose.qty?.toDoubleOrNull() ?: 0.0
        val baseUnit = dataState.unitList.firstOrNull { it.isBaseUom }
        val unitBaseQty = if (baseUnit != null) (baseUnit.baseQty * unitQty) else (unitQty)
        val insideBaseQty = unit.baseQty
        if (insideBaseQty != 0.0 && insideBaseQty == unitBaseQty) {
            setDataState { 
                copy(
                    length = unit.length.toString(),
                    width = unit.width.toString(),
                    height = unit.height.toString(),
                    weight = unit.weight?.toString()
                )
            }
        } else {
            clearUomDimension()
        }
    }

    private fun clearUomDimension() {
        setDataState {
            copy(
                length = "",
                width = "",
                height = "",
                weight = ""
            )
        }
    }

    fun updateValidate() {
        runCatching { validateUpdate() }.onFailure {
            if (it.message == getString(R.string.hint_collect_item_info_scan_or_enter_upc)) {
                fireEvent { CollectItemInfoUiEvent.ShowCollectUpcEmptyDialog(dataState.upcCode.isNullOrEmpty()) }
            } else {
                showToast(it.message!!)
            }
            return
        }
        update()
    }

    fun update() {
        launch {
            val (itemSpecResult, updateResult) = requestAllAwait(getUpdateItemSpecRequest(), getCreateOrUpdateUnitRequest())
            val uomResult = requestAwait(repository.getItemUnit(itemSpecId))
            if (itemSpecResult.isFailure || updateResult.isFailure || uomResult.isFailure) return@launch
            val uomList = uomResult.getOrNull()
            setDataState {
                copy(
                    unitList = uomList ?: listOf(),
                    itemInfoUpdated = true,
                    selectedUomDefinition = unitList.firstOrNull(),
                    layer = "1",
                    length = null,
                    width = null,
                    height = null,
                    weight = null,
                    insideQtyCompose = CollectItemInfoDataState.InsideQtyCompose(),
                    newUomDefinitionName = null,
                    isNewUom = false,
                    selectItemGroup = null
                )
            }
            activityViewModel.refreshItemLines()
            fireEvent { CollectItemInfoUiEvent.RefreshCubitScan }
            fireEvent { CollectItemInfoUiEvent.ItemUpdateSuccess }
        }
    }

    private fun getUpdateItemSpecRequest(): Flow<Void?> {
        val updateEntry = ItemUpdateEntity().apply {
            this.name = itemSpec.name
            this.tags = itemSpec.tags
            this.hasSerialNumber = itemSpec.hasSerialNumber
            this.customerId = itemSpec.customerId
            this.channel = UpdateFromEntry.ANDROID.name
            this.description = dataState.description
            this.shortDescription = dataState.shortDescription
            this.upcCode = dataState.upcCode
            this.upcCodeCase = dataState.upcCodeCase
            if (isCollectItemGroupAtReceiving && dataState.selectItemGroup?.id != null) {
                this.groupIds = listOf(dataState.selectItemGroup?.id!!)
            }
            this.brandId = itemSpec.brandId
            this.status = ItemStatusEntity.ACTIVE
            this.code = itemSpec.code
            this.shippingRule = itemSpec.shippingRule
        }
        return repository.updateItemSpec(itemSpecId, updateEntry)
    }

    private fun getCreateOrUpdateUnitRequest(): Flow<Any?> {
        val length = dataState.length!!.toDouble()
        val width = dataState.width!!.toDouble()
        val height = dataState.height!!.toDouble()
        val volume = StringUtil.twoDecimalPointKeep(length * width * height * 0.0005787).toDoubleOrNull()
        return if (dataState.insideQtyCompose.needCollect) {
            val name = if (dataState.isNewUom) {
                dataState.newUomDefinitionName?:""
            } else {
                dataState.selectedUomDefinition!!.name
            }
            val unitEntry = UomEntity(id= "", uomId = "", name = name).apply {
                this.itemId = <EMAIL>
                this.length = length
                this.width = width
                this.height = height
                this.weight = dataState.weight!!.toDouble()
                this.volume = volume
                this.linearUom = LinearUnit.INCH
                this.weightUom = WeightUomEntity.POUND
                this.volumeUom = VolumeUomEntity.CU_FT
                this.dualUomType = DualUomTypeEntity.PRIMARY

                this.qty = dataState.insideQtyCompose.qty!!.toDouble()
                this.insideUomId = dataState.insideQtyCompose.selectedUnit!!.id.toIntOrNull()
            }
            repository.createUnit(unitEntry)
        } else {
            val unitEntry = dataState.unitOfSelectedUom!!.apply {
                this.length = length
                this.width = width
                this.height = height
                this.weight = dataState.weight!!.toDouble()
                this.volume = volume
                this.status = UomStatusEntity.ENABLE
                this.linearUom = LinearUnit.INCH
                this.weightUom = WeightUomEntity.POUND
                this.volumeUom = VolumeUomEntity.CU_FT
                this.dualUomType = if (this.dualUomType == null) DualUomTypeEntity.PRIMARY else this.dualUomType
            }
            repository.updateUnit(unitEntry)
        }
    }

    @Throws
    private fun validateUpdate() {
        if (dataState.description.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_desc).toException
//        if (dataState.shortDescription.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_short_desc).toException
        if (dataState.isNewUom) {
            if (dataState.newUomDefinitionName.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
            if (dataState.newUomPhotoIds.isNullOrEmpty()) throw getString(R.string.text_take_photo_for_case).toException
        } else {
            if (dataState.selectedUomDefinition?.name.isNullOrEmpty()) throw getString(R.string.msg_please_select_oum).toException
        }

        val lengthString = dataState.length
        if (lengthString.isNullOrEmpty()) throw getString(R.string.toast_input_item_length).toException
        val length = lengthString.toDoubleOrNull()
        if (length == null || length <= 0) throw getString(R.string.toast_input_item_length_greater_zero).toException

        val widthString = dataState.width
        if (widthString.isNullOrEmpty()) throw getString(R.string.toast_input_item_width).toException
        val width = widthString.toDoubleOrNull()
        if (width == null || width <= 0) throw getString(R.string.toast_input_item_width_greater_zero).toException

        val heightString = dataState.height
        if (heightString.isNullOrEmpty()) throw getString(R.string.toast_input_item_height).toException
        val height = heightString.toDoubleOrNull()
        if (height == null || height <= 0) throw getString(R.string.toast_input_item_height_greater_zero).toException

        val weightString = dataState.weight
        if (weightString.isNullOrEmpty()) throw getString(R.string.toast_input_item_weight).toException
        val weight = weightString.toDoubleOrNull()
        if (weight == null || weight <= 0) throw getString(R.string.toast_input_item_weight_greater_zero).toException
        if (dataState.insideQtyCompose.needCollect) {
            val insideQtyString = dataState.insideQtyCompose.qty
            if (insideQtyString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_qty).toException
            val insideQty = insideQtyString.toDoubleOrNull()
            if (insideQty == null || insideQty <= 0) throw getString(R.string.toast_input_item_qty_greater_zero).toException
            if (dataState.insideQtyCompose.selectedUnit == null) {
                throw getString(R.string.please_select_inside_uom).toException
            }
        }
        val defaultTemplates = dataState.itemLpConfigs.filter { template -> template.isDefault?:false }
        // uat 先改成让user 自己决定是否采集 lp config
//        if (recurringStorageRateByPallet && defaultTemplates.size < dataState.unitList.size) {
//            dataState.itemLpConfigs.find { lpConfig -> lpConfig.isDefault?:false }
//                ?: throw getString(R.string.msg_collect_item_info_new_lp_config_tip).toException
//        }

        val uom = dataState.unitList.filter { unitEntry -> unitEntry.name == dataState.newUomDefinitionName}
        if (uom.isNotEmpty() && dataState.isNewUom) throw getString(R.string.toast_input_uom_already_exists).toException

        if (isCollectItemGroupAtReceiving && dataState.selectItemGroup == null)
            throw getString(R.string.please_select_item_group).toException
        if (dataState.upcCode.isNullOrEmpty() && dataState.upcCodeCase.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_upc).toException
    }


    fun loadItemLpTemplate() {
        launch {
            val singleItemLpConfigurationQueryEntity = SingleItemLpConfigurationQueryEntity()
            singleItemLpConfigurationQueryEntity.itemId = itemSpecId
            requestAwait(repository.loadSingleItemLpConfiguration(singleItemLpConfigurationQueryEntity)).onSuccess {
                setDataStateAwait { copy(itemLpConfigs = it ?: listOf()) }
                updateLpTemplatesForUom(dataState.currentUnitForConfig)
            }
        }
    }


    fun updateItemLpTemplate(select: Boolean?, itemLpTemplateEntry: SingleItemLpConfigurationEntity?) {
        if (null == itemLpTemplateEntry || itemLpTemplateEntry.isDefault == true) {
            return
        }
        launch {
            val updateEntry = SingleItemLpConfigurationUpdateEntity()
            updateEntry.isDefault = select
            updateEntry.name = itemLpTemplateEntry.name
            updateEntry.uomId = itemLpTemplateEntry.uomId
            updateEntry.lpConfigurationTemplateId = itemLpTemplateEntry.lpConfigurationTemplateId
            updateEntry.itemId = itemLpTemplateEntry.itemId
            updateEntry.status = TemplateStatusEntity.ENABLE
            requestAwait(repository.updateSingleItemLpConfiguration(itemLpTemplateEntry.id, updateEntry)).onSuccess {
                loadItemLpTemplate()
            }.onFailure {
                loadItemLpTemplate()
            }
        }
    }

    fun createUom() {
        runCatching { validateCreateUom() }.onFailure {
            showToast(it.message!!)
            return
        }
        val length = dataState.length!!.toDouble()
        val width = dataState.width!!.toDouble()
        val height = dataState.height!!.toDouble()
        val volume = StringUtil.twoDecimalPointKeep(length * width * height * 0.0005787).toDoubleOrNull()
        val name = if (dataState.isNewUom) {
            dataState.newUomDefinitionName?:""
        } else {
            dataState.selectedUomDefinition!!.name
        }
        val baseUnit = dataState.unitList.firstOrNull { it.isBaseUom }
        val inputQty = dataState.insideQtyCompose.qty?.toDoubleOrNull() ?: 0.0
        val unitEntry = UomEntity(id= "", uomId = "", name = name).apply {
            this.baseQty = if (baseUnit != null) baseUnit.baseQty * inputQty else 1.0
            this.itemId = <EMAIL>
            this.length = length
            this.width = width
            this.height = height
            this.weight = dataState.weight!!.toDouble()
            this.volume = volume
            this.linearUom = LinearUnit.INCH
            this.weightUom = WeightUomEntity.POUND
            this.volumeUom = VolumeUomEntity.CU_FT
            this.qty = inputQty
            this.insideUomId = dataState.insideQtyCompose.selectedUnit!!.id.toIntOrNull()
        }

        val newPhotoIds = dataState.photoIds.addToNewList(dataState.newUomPhotoIds)
        val itemUpdateEntity = ItemUpdateEntity().apply {
            this.imageFileIds = newPhotoIds
            this.tags = itemSpec.tags
            this.name = itemSpec.name
            this.code = itemSpec.code
            this.customerId = itemSpec.customerId
            this.shippingRule = itemSpec.shippingRule
        }
        launch {
            val (unitResult, itemResult) = requestAllAwait(repository.createUnit(unitEntry), repository.updateItemSpec(itemSpecId, itemUpdateEntity))
            if (unitResult.isFailure || itemResult.isFailure) return@launch
            setDataState { copy(insideQtyCompose = CollectItemInfoDataState.InsideQtyCompose()) }
            initData()
        }
    }

    @Throws
    private fun validateCreateUom() {
        if (dataState.isNewUom) {
            if (dataState.newUomDefinitionName.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
            if (dataState.newUomPhotoIds.isNullOrEmpty()) throw getString(R.string.text_take_photo_for_case).toException
        } else {
            val uomString = dataState.selectedUomDefinition?.name
            if (uomString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
        }
        val insideQtyString = dataState.insideQtyCompose.qty
        if (insideQtyString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_qty).toException
        val insideQty = insideQtyString.toDoubleOrNull()
        if (insideQty == null || insideQty <= 0) throw getString(R.string.toast_input_item_qty_greater_zero).toException
        if (dataState.insideQtyCompose.selectedUnit == null) {
            throw getString(R.string.please_select_inside_uom).toException
        }
        val uom = dataState.unitList.filter { unitEntry -> unitEntry.name == dataState.newUomDefinitionName}
        if (uom.isNotEmpty() && dataState.isNewUom) throw getString(R.string.toast_input_uom_already_exists).toException
    }

    fun updateLpTemplatesForUom(unit: UomEntity?) {
        unit?.let {
            updateUoms(unit)
            val templatesForUom = dataState.itemLpConfigs.filter { template -> TextUtils.equals(template.uomId, it.uomId) }
            val itemLpTemplates = templatesForUom.map { itemLpTemplate ->
                val tempName = itemLpTemplate.lpConfigurationTemplate?.let {
                    "${NumberUtil.doubleToStr(it.stack)}x${NumberUtil.doubleToStr(it.layer)} (${itemLpTemplate.name})"
                } ?: "${itemLpTemplate.name}"
                GridChipItem(id = itemLpTemplate.lpConfigurationTemplateId?:"", item = itemLpTemplate, title = tempName, selected = itemLpTemplate.isDefault?:false)
            }
            setUiState { copy(itemLpTemplates = itemLpTemplates) }
        }
    }

    private fun updateUoms(unitEntry: UomEntity) {
        dataState.unitList.let {
            val itemUoms = it.map { unit ->
                GridChipItem<UomEntity>(id = unit.id, item = unit, title = unit.name, selected = TextUtils.equals(unitEntry.id, unit.id))
            }
            setUiState { copy(itemUoms = itemUoms) }
            setDataState { copy(currentUnitForConfig = unitEntry) }
        }
    }

}