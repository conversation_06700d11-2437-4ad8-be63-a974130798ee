package com.unis.wms.receive

import android.content.DialogInterface
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import com.customer.widget.common.VoidCallBack
import com.customer.widget.extensions.isVisible
import com.customer.widget.extensions.setVisible
import com.linc.platform.utils.ToastUtil
import com.unis.platform.general.api.JobCodeApi
import com.unis.platform.general.model.JobCodeEntity
import com.unis.platform.general.model.JobCodeQueryEntity
import com.unis.platform.receive.model.HoursInfoEntity
import com.unis.platform.receive.model.ReceiveTaskUpdateEntity
import com.unis.platform.receive.model.WorkerTypeEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.BaseVM
import com.unis.reactivemvi.mvvm.kotlin.BaseVMBindingDialog
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.wms.R
import com.unis.wms.databinding.DialogWorkerTotalHoursBinding
import java.io.Serializable
import kotlin.properties.Delegates

class WorkerTotalHoursDialog : BaseVMBindingDialog<WorkerTotalHoursViewModel, DialogWorkerTotalHoursBinding>() {

    data class WorkerTotalHoursParam(
        val taskId: String,
        val customerId: String?,
        val jobCode: JobCodeEntity? = null,
        val forkliftWorkCount: Int = 0,
        val forkliftTotalHours: Double = 0.0,
        val lumperWorkCount: Int = 0,
        val lumperTotalHours: Double = 0.0
    ) : Serializable

    companion object {
        private const val PARAM = "param"

        fun newInstance(param: WorkerTotalHoursParam) = newFragmentInstance<WorkerTotalHoursDialog>(PARAM to param)
    }

    private val param: WorkerTotalHoursParam by lazy { getBundleObject(PARAM) }

    private var forkliftWorkCount by Delegates.notNull<Int>()
    private var forkliftTotalHours by Delegates.notNull<Double>()
    private var lumperWorkCount by Delegates.notNull<Int>()
    private var lumperTotalHours by Delegates.notNull<Double>()
    private var jobCodes by Delegates.notNull<List<JobCodeEntity>>()

    private var confirmListener: WorkerHoursConfirmListener? = null
    private var dismissListener: VoidCallBack? = null

    override fun createViewModel(): WorkerTotalHoursViewModel {
        return WorkerTotalHoursViewModel()
    }

    override fun initView(savedInstanceState: Bundle?) {
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        
        // Intercept physical back button
        dialog.setOnKeyListener { _, keyCode, event -> 
            keyCode == android.view.KeyEvent.KEYCODE_BACK && event.action == android.view.KeyEvent.ACTION_UP
        }
        
        forkliftWorkCount = param.forkliftWorkCount
        forkliftTotalHours = param.forkliftTotalHours
        lumperWorkCount = param.lumperWorkCount
        lumperTotalHours = param.lumperTotalHours

        binding?.apply {
            forkliftWorkCountLev.setText(if (forkliftWorkCount > 0) forkliftWorkCount.toString() else "")
            forkliftTotalHoursLev.setText(if (forkliftTotalHours > 0) forkliftTotalHours.toString() else "")
            lumperWorkCountLev.setText(if (lumperWorkCount > 0) lumperWorkCount.toString() else "")
            lumperTotalHoursLev.setText(if (lumperTotalHours > 0) lumperTotalHours.toString() else "")
            
            forkliftWorkCountLev.setInputType(InputType.TYPE_CLASS_NUMBER)
            forkliftTotalHoursLev.setInputType(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL)
            lumperWorkCountLev.setInputType(InputType.TYPE_CLASS_NUMBER)
            lumperTotalHoursLev.setInputType(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL)
            
            updateTotalDuration()
            
            forkliftWorkCountLev.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    forkliftWorkCount = s?.toString()?.toIntOrNull() ?: 0
                    updateTotalDuration()
                }
            })
            
            forkliftTotalHoursLev.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    forkliftTotalHours = s?.toString()?.toDoubleOrNull() ?: 0.0
                    updateTotalDuration()
                }
            })
            
            lumperWorkCountLev.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    lumperWorkCount = s?.toString()?.toIntOrNull() ?: 0
                    updateTotalDuration()
                }
            })
            
            lumperTotalHoursLev.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    lumperTotalHours = s?.toString()?.toDoubleOrNull() ?: 0.0
                    updateTotalDuration()
                }
            })

            viewModel.loadJobCodes(param.customerId) {
                jobCodes = it
                jobCodeSpinner.setVisible()
                val jobCodeList = it.map { jobCodeEntity -> "${jobCodeEntity.accountItem}(${jobCodeEntity.description})" }
                jobCodeSpinner.setDataOptions(jobCodeList)
            }
            
            confirmBtn.setOnClickListener { confirmClick() }
        }
    }

    private fun updateTotalDuration() {
        val totalHours = forkliftTotalHours + lumperTotalHours
        binding?.totalDurationTv?.text = String.format("%.2f hrs", totalHours)
    }

    private fun confirmClick() {
        val forkliftWorkCount = binding?.forkliftWorkCountLev?.getText()?.toIntOrNull() ?: 0
        val forkliftTotalHours = binding?.forkliftTotalHoursLev?.getText()?.toDoubleOrNull() ?: 0.0
        val lumperWorkCount = binding?.lumperWorkCountLev?.getText()?.toIntOrNull() ?: 0
        val lumperTotalHours = binding?.lumperTotalHoursLev?.getText()?.toDoubleOrNull() ?: 0.0
        val jobCode = if (binding?.jobCodeSpinner?.isVisible == true ) {
            binding?.jobCodeSpinner?.getSelectedPosition()?.let { jobCodes[it] }
        } else {
            null
        }

        if (forkliftWorkCount < 1) {
            ToastUtil.showToast(getString(R.string.msg_please_input_valid_forklift_work_count))
            return
        }
        
        if (forkliftTotalHours <= 0) {
            ToastUtil.showToast(getString(R.string.msg_please_input_valid_forklift_total_hours))
            return
        }
        
        if (lumperWorkCount < 1) {
            ToastUtil.showToast(getString(R.string.msg_please_input_valid_lumper_work_count))
            return
        }
        
        if (lumperTotalHours <= 0) {
            ToastUtil.showToast(getString(R.string.msg_please_input_valid_lumper_total_hours))
            return
        }

        if (binding?.jobCodeSpinner?.isVisible == true && jobCode == null) {
            ToastUtil.showToast(getString(R.string.msg_please_select_job_code))
            return
        }

        val updatedParam = WorkerTotalHoursParam(
            taskId = param.taskId,
            customerId = param.customerId,
            jobCode = jobCode,
            forkliftWorkCount = forkliftWorkCount,
            forkliftTotalHours = forkliftTotalHours,
            lumperWorkCount = lumperWorkCount,
            lumperTotalHours = lumperTotalHours
        )
        
        viewModel.updateWorkerTotalHours(param.taskId, updatedParam) {
            confirmListener?.confirm(updatedParam)
            dismiss()
        }
    }

    fun setConfirmListener(listener: WorkerHoursConfirmListener) {
        this.confirmListener = listener
    }

    fun interface WorkerHoursConfirmListener {
        fun confirm(param: WorkerTotalHoursParam)
    }

    fun setDismissListener(listener: VoidCallBack) {
        this.dismissListener = listener
    }

    override fun onDismiss(dialog: DialogInterface?) {
        super.onDismiss(dialog)
        dismissListener?.call()
    }
} 

class WorkerTotalHoursViewModel : BaseVM() {

    private val repository by lazy { WorkerTotalHoursRepository() }
    private val receiveTaskRepository by lazy { ReceiveTaskRepository() }

    fun updateWorkerTotalHours(taskId: String, param: WorkerTotalHoursDialog.WorkerTotalHoursParam, action: () -> Unit) {
        val receiveTaskUpdateEntity = ReceiveTaskUpdateEntity(
            id = taskId,
            collectHoursInfos = listOf(
                HoursInfoEntity(
                    workerType = WorkerTypeEntity.FORKLIFT_DRIVER,
                    qty = param.forkliftWorkCount,
                    totalHours = param.forkliftTotalHours
                ),
                HoursInfoEntity(
                    workerType = WorkerTypeEntity.LUMPER,
                    qty = param.lumperWorkCount,
                    totalHours = param.lumperTotalHours
                )
            )
        ).apply {
            param.jobCode?.let { jobCode ->
                this.jobCode = jobCode.accountItem
                this.jobCodeDescription = jobCode.description
            }
        }

        launch {
            requestAwait(receiveTaskRepository.updateTask(receiveTaskUpdateEntity)).onSuccess {
                action.invoke()
            }
        }
    }
    
    fun loadJobCodes(customerId: String?, action: (List<JobCodeEntity>) -> Unit) {
        customerId?: return
        val jobCodeQueryEntity = JobCodeQueryEntity(customerId, "JobCode")
        launch {
            requestAwait(repository.loadJobCodes(jobCodeQueryEntity)).onSuccess {
                if (!it.isNullOrEmpty()) {
                    action.invoke(it)
                }
            }
        }
    }
}

class WorkerTotalHoursRepository : BaseRepository() {
    private val jobCodeApi by apiServiceLazy<JobCodeApi>()

    fun loadJobCodes(query: JobCodeQueryEntity) = requestV2({jobCodeApi.getAccountItems(query)})
}