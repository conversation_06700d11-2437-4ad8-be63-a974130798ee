package com.unis.wms.receive.lpsetup

import com.customer.widget.common.addToNewList
import com.linc.platform.baseapp.api.LocationApi
import com.linc.platform.baseapp.model.LocationItemCheckEntry
import com.linc.platform.http.ErrorCode
import com.linc.platform.inventory.model.LpJobEntry
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.receive.api.ReceiveLpSetupApi
import com.linc.platform.receive.model.*
import com.linc.platform.utils.Constant
import com.linc.platform.utils.Lists
import com.unis.linc.common.extensions.toException
import com.unis.platform.common.api.ConfigurationMapApi
import com.unis.platform.common.model.configurationmap.ConfigurationMapQueryEntity
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.common.model.template.SingleItemLpConfigurationEntity
import com.unis.platform.customer_v2.CustomerApiService
import com.unis.platform.db.WMSDBManager
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.lp_v2.model.ReceiveMethodEntity
import com.unis.platform.receive.api.LpSetupApi
import com.unis.platform.receive.api.ReceiveTaskApi
import com.unis.platform.receive.model.ForceCloseRequestEntity
import com.unis.platform.receive.model.ItemLineEntity
import com.unis.platform.receive.model.LpSetupForSingleItemEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.reactivemvi.mvvm.kotlin.extensions.toErrorResponse
import com.unis.wms.R
import com.unis.wms.receive.lpsetup.itemconfig.SingleItemConfigDataState
import kotlinx.coroutines.flow.Flow

class LpSetupViewModel(
    initialDataState: LpSetupDataState,
    initialUiState: LpSetupUiState = LpSetupUiState()
) : ReactiveViewModel<LpSetupDataState, LpSetupUiState>(initialDataState, initialUiState) {

    private val repository = Repository()


    init {
        if (getStep()?.isDone() == false) {
            loadCustomer()
            loadConfigurationMap()
            refreshItemLines()
        }
    }

    fun onLpSetupProcessChanged(processChange: LpSetupProcess) {
        fireEvent { LpSetupEvent.LpSetupProcessChanged(processChange) }
    }

    fun onSingleItemSummaryChanged(singleItemSummary: SingleItemConfigDataState.ReceiveItemLineCompose) {
        val processChange = LpSetupProcess.SingleItemSummary(singleItemSummary)
        fireEvent { LpSetupEvent.LpSetupProcessChanged(processChange) }
    }

    fun getTask() = dataState.receiveTaskEntity

    fun getTaskId() = dataState.receiveTaskEntity.id

    fun getStepId() = dataState.receiveTaskEntity.getTaskStep(TaskStepType.LP_SETUP)?.id.toString()

    fun getStep() = dataState.receiveTaskEntity.getTaskStep(TaskStepType.LP_SETUP)

    fun getCustomerId() = getStep()?.customerId

    fun getCustomer() = dataState.customerEntity

    fun getConfigurationMap() = dataState.configurationMapEntity

    fun getReceiveMethod() = dataState.receiveMethod

    fun getSelectedLocation() = dataState.selectedLocation

    fun getLabelSize() = dataState.labelSize

    fun hasContainStageStep() = dataState.receiveTaskEntity.getTaskStep(TaskStepType.STAGE) != null

    private fun getItemLines() = dataState.receiptItemLines

    fun saveReceiveMethod(receiveMethod: ReceiveMethodEntity) {
        setDataState { copy(receiveMethod = receiveMethod) }
    }

    fun saveLocation(locationEntity: LocationEntity) {
        setDataState { copy(selectedLocation = locationEntity) }
    }

    fun saveLabelSize(labelSize: LabelSizeEntry) {
        setDataState { copy(labelSize = labelSize) }
    }

    fun updateLpTemplateToItemLine(itemLineId: String, templateConfig: SingleItemLpConfigurationEntity) {
        val itemLine = getItemLines().find { v -> v.id == itemLineId }
        itemLine?.let {
            itemLine.itemLpConfigurations = itemLine.itemLpConfigurations.addToNewList(templateConfig)
        }
    }

    fun updateTaskStatusToInProgress() {
        val receiveTaskEntity = getTask()
        getStep()?.status = TaskStepStatus.IN_PROGRESS
        receiveTaskEntity.status = TaskStatus.IN_PROGRESS
        setDataState { copy(receiveTaskEntity = receiveTaskEntity) }
    }

    fun refreshData() {
        loadCustomer()
        refreshItemLines()
    }

    fun removeLpTemplateFromItemLine(itemLineId: String, configId: Long) {
        val itemLine = getItemLines().find { v -> v.id == itemLineId }
        itemLine?.let {
            val index = itemLine.itemLpConfigurations?.indexOfFirst { v -> v.id == configId }
            if (index != null && index >= 0) {
                val toMutableList = itemLine.itemLpConfigurations?.toMutableList()
                toMutableList?.removeAt(index)
                itemLine.itemLpConfigurations = toMutableList
            }
        }
    }

    private fun loadCustomer() {
        getCustomerId()?.let {
            launch {
                val result = requestAwait(repository.customerApi(it)).getOrNull()?: return@launch
                setDataStateAwait { copy(customerEntity = result) }
                awaitDataState()
                initLabelSize()
                fireEvent { LpSetupEvent.InvalidateOptionsMenu }
            }
        }
    }

    private fun loadConfigurationMap() {
        getCustomerId()?.let {
            launch {
                val result = requestAwait(repository.searchConfigurationMap(ConfigurationMapQueryEntity().apply {
                    this.tableName = "CollectItemGroupAtReceiving"
                    this.customerId = it
                })).getOrNull()?: return@launch
                if (result.isNotEmpty()) {
                    setDataStateAwait { copy(configurationMapEntity = result[0]) }
                }
            }
        }
    }

    private fun initLabelSize() {
        val customerLabelSizes = dataState.customerEntity?.masterDataSetting?.facilityLPLabelSizeSettings
        if (!customerLabelSizes.isNullOrEmpty()) {
            setDataState { copy(labelSize = customerLabelSizes.find { it.facilityId == dataState.facilityId }?.lpLabelSize) }
            return
        }

        val facility = repository.getFacility(dataState.userId)
        if (facility != null && facility.facilitySettingEntity?.lpLabelSize != null) {
            setDataState { copy(labelSize = facility.facilitySettingEntity?.lpLabelSize) }
            return
        }

        setDataState { copy(labelSize = LabelSizeEntry.TWO_ONE) }
    }

    fun isNotAllowOverReceive(itemLine: ItemLineEntity): Boolean{
        val receive = getTask().receipts?.find { it.id == itemLine.receiptId }
        val receiveQtyCheck = getCustomer()?.inboundSetting?.receiveQtyChecks?.find { it.receiptType == receive?.receiptType }
        return receiveQtyCheck?.notAllowOverReceive ?: false
    }

    fun refreshItemLines() = request(repository.getItemLines(getStepId())) {
        setDataState { copy(receiptItemLines = it ?: listOf()) }
        if (it.isReceiveDone()) fireEvent { LpSetupEvent.ReceiveDoneAskCloseStep }
    }

    fun refreshTaskSpecialItemLines(itemLineId: String) = request(repository.getItemLineById(getStepId(), itemLineId)) {
        it?.let { itemLine ->
            val receiptItemLines = dataState.receiptItemLines.toMutableList()
            val indexOfSpecialItemLine = receiptItemLines.indexOfFirst { v -> v.id == itemLineId }
            receiptItemLines[indexOfSpecialItemLine] = itemLine
            setDataState { copy(receiptItemLines = receiptItemLines) }
            // Ask close step when all received
            if (receiptItemLines.isReceiveDone()) fireEvent { LpSetupEvent.ReceiveDoneAskCloseStep }
        }
    }


    private fun List<ItemLineEntity>?.isReceiveDone(): Boolean {
        this ?: return false
        return this.all {
            it.receivedBaseQty>= it.expectBaseQty
        }
    }

    /**
     * Check if [selectedLocation] and Item are matched.
     * @return
     *  - Result.success(Unit) if matched or user confirmed.
     *  - Result.failure with empty message Exception if user canceled.
     *  - Result.failure with not empty message Exception if other errors.
     */
    suspend fun checkLocationItem(itemLineCompose: SingleItemConfigDataState.ReceiveItemLineCompose): Result<Unit> {
        requireNotNull(this.getSelectedLocation())

        val checkEntry = LocationItemCheckEntry().apply {
            this.itemSpecId = itemLineCompose.itemSpecId
            this.expirationDate = itemLineCompose.expirationDate
            this.lotNo = itemLineCompose.lotNumber
            this.shelfLifeDays = itemLineCompose.shelfLifeDays?.toInt()
            this.mfgDate = itemLineCompose.manufactureDate
        }
        val result = requestAwait(repository.locationItemCheck(getTaskId()!!, getSelectedLocation()!!.id, checkEntry), error = null)
        if (result.isSuccess) return Result.success(Unit)

        val error = result.exceptionOrNull()!!.toErrorResponse()
        if (error.code == ErrorCode.LOCATION_ITEM_CHECK_NOT_MATCH) {
            val confirm = awaitEvent { LpSetupEvent.LocationItemNotMatchConfirm(error.errorMessage) } ?: false
            return if (confirm) {
                Result.success(Unit)
            } else {
                Result.failure(Exception())
            }
        }
        return Result.failure(error.errorMessage.toException)
    }

    suspend fun createLpAndLpSetup(
        lpSetupForSingleItemEntity: LpSetupForSingleItemEntity
    ): Result<List<String>?> {
        return requestAwait(repository.lpSetupForSingItem(getStepId(), lpSetupForSingleItemEntity))
    }

    fun batchAddLpsRequestForSku(
        lpJobEntryList: List<LpJobEntry>,
        receiptItemLineMap: Map<String, SingleItemConfigDataState.ReceiveItemLineCompose>,
    ): Flow<List<LPSetupResultEntry>?> {
        requireNotNull(this.getSelectedLocation())

        val list = mutableListOf<LPBatchSetupRequestEntry>()
        var index = 0
        receiptItemLineMap.values.forEach {
            val itemLineCompose = it
            val lpItem = LpItemEntry().apply {
                this.itemLineId = itemLineCompose.itemLineId
                this.lotNo = itemLineCompose.itemLine.lotNo
//                this.expirationDate = itemLineCompose.itemLine.expirationDate
//                this.mfgDate = itemLineCompose.itemLine.manufactureDate
//                this.qty = itemLineCompose.itemLine.qty
//                this.unitId = itemLineCompose.itemLine.unitId
            }
            val lpDetail = LpDetailEntry().apply {
                this.itemEntries = listOf(lpItem)
//                this.selectedLocation = <EMAIL>()!!
                this.type = itemLineCompose.selectedGoodsType.toString()
                this.itemSpecId = itemLineCompose.itemSpecId
                this.receiptId = itemLineCompose.itemLine.receiptId
            }
            val lpSetupEntry = LPBatchSetupRequestEntry().apply {
                this.lpDetailEntry = lpDetail
                this.lpIds = lpJobEntryList[index].lpIds
                this.locationId = <EMAIL>()!!.id
                this.receiptId = itemLineCompose.itemLine.receiptId
                this.lpItemSpecId = itemLineCompose.itemSpecId
                this.itemLineId = itemLineCompose.itemLineId
                this.lotNo = itemLineCompose.itemLine.lotNo
            }
            list.add(lpSetupEntry)
            index++
        }
        return repository.batchAddLps(getTaskId()!!, getStepId(), list)
    }

    fun batchAddLpsRequestForReceipt(
        lpJobEntryList: List<LpJobEntry>,
        receiptItemLineMap: Map<String, SingleItemConfigDataState.ReceiveItemLineCompose>,
    ): Flow<List<LPSetupResultEntry>?> {
        requireNotNull(this.getSelectedLocation())

        val list = mutableListOf<LPBatchSetupRequestEntry>()
        var index = 0
        val receiptList = Lists.uniq(receiptItemLineMap.values.map { it.itemLine.receiptId }.toList())
        receiptList.forEach { receiptId ->
            val itemLineComposeList = receiptItemLineMap.values.filter { it.itemLine.receiptId == receiptId }.toList()
            val lpItemList = mutableListOf<LpItemEntry>()
            itemLineComposeList.forEach { itemLineCompose ->
                val lpItem = LpItemEntry().apply {
                    this.itemLineId = itemLineCompose.itemLineId
                    this.lotNo = itemLineCompose.itemLine.lotNo
//                    this.expirationDate = itemLineCompose.itemLine.expirationDate
//                    this.mfgDate = itemLineCompose.itemLine.manufactureDate
//                    this.qty = itemLineCompose.itemLine.qty
//                    this.unitId = itemLineCompose.itemLine.unitId
//                    this.itemSpecId = itemLineCompose.itemSpecId
//                    this.goodsType = itemLineCompose.itemLine.goodsType
                    this.receiptId = itemLineCompose.itemLine.receiptId
                }
                lpItemList.add(lpItem)
            }
            val lpDetail = LpDetailEntry().apply {
                this.itemEntries = lpItemList
//                this.selectedLocation = <EMAIL>()!!
                this.type = Constant.GOODS_TYPE_GOOD
            }
            val lpSetupEntry = LPBatchSetupRequestEntry().apply {
                this.lpDetailEntry = lpDetail
                this.lpIds = lpJobEntryList[index].lpIds
                this.locationId = <EMAIL>()!!.id
            }
            list.add(lpSetupEntry)
            index++
        }
        return repository.batchAddLps(getTaskId()!!, getStepId(), list)
    }

    fun closeStep() {
        launch {
            requestAwait(repository.closeStep(getStepId()), error = {
                if (it.code == ErrorCode.NOT_MATCH_EXPECTED_QTY_AND_ALLOW_FORCE_CLOSE
                    || it.code == ErrorCode.ALLOW_PARTIAL_RECEIVE_CLOSE
                    || it.code == ErrorCode.ALLOW_SHORT_RECEIVE_CLOSE_ERROR
                    || it.code == ErrorCode.ALLOW_PARTIAL_AND_SHORT_RECEIVED_CLOSE_ERROR
                    || it.code == ErrorCode.ALLOW_OVER_RECEIVE_CLOSE_ERROR
                ) {
                    fireEvent { LpSetupEvent.CloseStepFailure(it.error) }
                } else {
                    showSnack(SnackType.ErrorV1(), it.error)
                }
            }).onSuccess {
                if (enableCloseTask()) {
                    if (needCollectWorkerTotalHours()) {
                        fireEvent { LpSetupEvent.CollectWorkerTotalHours }
                    } else {
                        closeTask()
                    }
                } else {
                    fireEvent { LpSetupEvent.FinishActivity }
                }
            }
        }
    }

    private fun enableCloseTask(): Boolean {
        val isAllDoneForOtherStep = getTask().taskSteps?.filter { it.stepType != TaskStepType.LP_SETUP }?.all { it.isDone() }?: false
        val forbidCloseReceiveTaskBeforePutAway = getCustomer()?.inboundSetting?.forbidCloseReceiveTaskBeforePutAway?: false
        return !hasContainStageStep() && !hasSnScanStep() && isAllDoneForOtherStep && !forbidCloseReceiveTaskBeforePutAway
    }

    fun forceCloseStep(reason: String, isMatch: Boolean) {
        launch {
            requestAwait(repository.forceCloseStep(getStepId(), reason, isMatch)).onSuccess {
                if (enableCloseTask()) {
                    if (needCollectWorkerTotalHours()) {
                        fireEvent { LpSetupEvent.CollectWorkerTotalHours }
                    } else {
                        closeTask()
                    }
                } else {
                    fireEvent { LpSetupEvent.FinishActivity }
                }
            }
        }
    }

    fun clearReceivedLpTemplate() {
        setDataState { copy(receivedLpTemplateIdsMap = mapOf()) }
    }

    fun closeTask() {
        launch {
            requestAwait(repository.closeTask(getTaskId()!!)).onSuccess {
                fireEvent { LpSetupEvent.FinishActivity }
            }.onFailure {
                val errMsg = getString(R.string.msg_force_close)
                fireEvent { LpSetupEvent.CloseTaskFailure(errMsg) }
            }
        }
    }

    fun forceCloseTask() {
        launch {
            requestAwait(repository.forceCloseTask(getTaskId()!!)).onSuccess {
                fireEvent { LpSetupEvent.FinishActivity }
            }
        }
    }

    private fun hasSnScanStep(): Boolean {
        return getTask().getTaskStep(TaskStepType.SN_SCAN) != null
    }

    private fun needCollectWorkerTotalHours(): Boolean {
        return getCustomer()?.inboundSetting?.collectTotalHours?: false
    }

    suspend fun submitCarton(
        lpSetupForSingleItemEntity: LpSetupForSingleItemEntity
    ): Result<List<String>?> {
        return requestAwait(repository.submitCarton(getStepId(), lpSetupForSingleItemEntity))
    }
}

private class Repository : BaseRepository() {

    private val customerApi by apiServiceLazy<CustomerApiService>()
    private val configurationMapApi by apiServiceLazy<ConfigurationMapApi>()
    private val lpSetupApi2 by apiServiceLazy<LpSetupApi>()
    private val receiveTaskApi by apiServiceLazy<ReceiveTaskApi>()
    private val lpSetupApi by apiServiceLazy<ReceiveLpSetupApi>()
    private val locationApi by apiServiceLazy<LocationApi>()

    fun customerApi(id: String) = rxRequest2(customerApi.getCustomer(id))

    fun searchConfigurationMap(query: ConfigurationMapQueryEntity) = requestV2({configurationMapApi.search(query)})

    fun getFacility(userId: String) = WMSDBManager.facilityManager.getOneByUserId(userId)

    fun getItemLines(stepId: String) = rxRequest2(lpSetupApi2.getItemLine(stepId))

    fun getItemLineById(stepId: String, itemLineId: String) = rxRequest2(lpSetupApi2.getItemViewById(stepId,itemLineId))

    fun closeStep(stepId: String) = rxRequest2(lpSetupApi2.close(stepId))

    fun forceCloseStep(stepId: String, reason: String, isMatch: Boolean) = rxRequest2(lpSetupApi2.forceClose(stepId, ForceCloseRequestEntity().apply {
        this.reason = reason
        this.isMatch = isMatch
    }))

    fun closeTask(taskId: String) = requestV2({receiveTaskApi.close(taskId)})

    fun forceCloseTask(taskId: String) = requestV2({receiveTaskApi.forceClose(taskId)})

    fun lpSetupForSingItem(stepId: String, lpSetupForSingleItemEntity: LpSetupForSingleItemEntity) =
        rxRequest2(lpSetupApi2.lpSetupForSingItem(stepId, lpSetupForSingleItemEntity))

    fun batchAddLps(taskId: String, stepId: String, request: LPBatchSetupRequestEntry) =
        rxRequest(lpSetupApi.occupyEquipmentAndBatchAddLPs(taskId, stepId, request))

    fun batchAddLps(taskId: String, stepId: String, request: List<LPBatchSetupRequestEntry>) =
        rxRequest(lpSetupApi.occupyEquipmentAndBatchAddLPs(taskId, stepId, request))

    fun locationItemCheck(taskId: String, locationId: String, checkEntry: LocationItemCheckEntry) =
        rxRequest(locationApi.checkLocationItem(taskId, locationId, checkEntry))

    fun submitCarton(stepId: String, lpSetupForSingleItemEntity: LpSetupForSingleItemEntity) =
        rxRequest2(lpSetupApi2.lpSetupForCarton(stepId, lpSetupForSingleItemEntity))
}