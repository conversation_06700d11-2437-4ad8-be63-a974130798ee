package com.unis.wms.receive.lpsetup.itemconfig

import android.text.TextUtils
import com.annimon.stream.Collectors
import com.annimon.stream.Stream
import com.customer.widget.common.mapToLinkedMap
import com.linc.platform.common.lp.LpTypeEntry
import com.linc.platform.common.lp.ScenarioEntry
import com.linc.platform.inventory.model.LpBatchCreateEntry
import com.linc.platform.inventory.model.LpCreateResponseEntry
import com.linc.platform.inventory.model.LpJobEntry
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintJobCreate
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.commonprintlp.PrintResult
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.receive.dal.ItemMFGDateDal
import com.linc.platform.receive.model.LPSetupResultEntry
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.Lists
import com.linc.platform.utils.PrintUtil
import com.linc.platform.utils.TimeUtil
import com.unis.linc.common.extensions.toException
import com.unis.platform.common.model.template.SceneEntity
import com.unis.platform.common.model.template.SingleItemLpConfigurationEntity
import com.unis.platform.common.model.template.SingleItemLpConfigurationQueryEntity
import com.unis.platform.common.model.template.SingleItemLpTemplateCreateEntity
import com.unis.platform.common.model.template.SingleItemLpTemplateEntity
import com.unis.platform.item.model.ItemEntity
import com.unis.platform.item.model.ItemStatusEntity
import com.unis.platform.location_v2.model.LocationInventoryStatus
import com.unis.platform.lp_v2.model.LpBatchCreateEntity
import com.unis.platform.lp_v2.model.LpPlateLabelRequest
import com.unis.platform.lp_v2.model.LpSubType
import com.unis.platform.lp_v2.model.LpType
import com.unis.platform.lp_v2.model.ReceiveMethodEntity
import com.unis.platform.receive.model.ItemLineEntity
import com.unis.platform.receive.model.LpItemEntity
import com.unis.platform.receive.model.LpSetupForSingleItemEntity
import com.unis.platform.uom.model.UomEntity
import com.unis.platform.uom.model.splitUomNameAndNumber
import com.unis.platform.util.UomUtils
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.Tuple
import com.unis.reactivemvi.Tuple3
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.interceptDataState
import com.unis.reactivemvi.mvi.interceptUiState
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvi.subscribeInner
import com.unis.reactivemvi.mvi.subscribeTo
import com.unis.reactivemvi.mvvm.kotlin.extensions.facilityEntity
import com.unis.reactivemvi.mvvm.kotlin.extensions.getFormattedString
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showLoading
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.reactivemvi.print.PrinterManager
import com.unis.wms.R
import com.unis.wms.common.GoodsTypeHelper
import com.unis.wms.receive.lpsetup.LpSetupDataState
import com.unis.wms.receive.lpsetup.LpSetupProcess
import com.unis.wms.receive.lpsetup.LpSetupViewModel
import com.unis.wms.receive.lpsetup.collectiteminfo.CollectItemInfoConfigData
import com.unis.wms.receive.lpsetup.itemconfig.SingleItemConfigDataState.ReceiveItemLineCompose
import com.unis.wms.receive.lpsetup.itemconfig.components.QtyConfirmDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.text.SimpleDateFormat
import java.time.ZoneId
import java.util.Calendar
import java.util.Date
import kotlin.math.ceil

/** * <AUTHOR>
 * @date 2022/07
 */
class SingleItemConfigViewModel(
    private val activityViewModel: LpSetupViewModel,
    initialDataState: SingleItemConfigDataState = SingleItemConfigDataState(),
    initialUiState: SingleItemConfigUiState = SingleItemConfigUiState(),
) : ReactiveViewModel<SingleItemConfigDataState, SingleItemConfigUiState>(initialDataState, initialUiState) {

    private val selectedLocation = activityViewModel.getSelectedLocation()!!
    private val lpLabelSize get() = activityViewModel.getLabelSize()
    private val receiveMethod get() = activityViewModel.getReceiveMethod()
    private val customerEntity get() = activityViewModel.getCustomer()
    private val configurationMap get() = activityViewModel.getConfigurationMap()
    private val repository = SingleItemConfigRepository()

    private val printUtil = PrintUtil.newInstance()

    init {
        interceptDataState {
            interceptOnce(SingleItemConfigDataState::configCompose) {
                it.copy(
                    needCollectGoodsType = true,
                    allAllowedGoodsTypes = GoodsTypeHelper(customerEntity).buildGoodsTypes(),
                    needCollectPhotoGoodsTypes = GoodsTypeHelper(customerEntity).getNeedCollectPhotoGoodsTypes(),
                    forceScanLotNumber = false,
                    lotNumberRegex = "",
                    needCollectCustomerPallet = customerEntity?.inboundSetting?.collectPalletNoOnReceiving == true,
                    forceDefaultUOM = customerEntity?.inboundSetting?.forceDefaultUOMReceiving == true,
                    forbidLpSetupPalletization = customerEntity?.inboundSetting?.forbidLpSetupPalletization == true,
                    needAlertHighValueItem = false,
                    needCollectItemInfo = customerEntity?.inboundSetting?.forceCollectItemInfoAtReceiving == true,
                    needCollectItemSmallParcelPackaging = false,
                    forbidCreateLpConfigAtReceiveTask = customerEntity?.inboundSetting?.forbidCreateLpConfigAtReceiveTask == true,
                    forbidMultipleLpConfigAtReceiveTask = customerEntity?.inboundSetting?.forbidMultipleLpConfigAtReceiveTask == true,
                )
            }
        }
        interceptUiState {
            interceptDiffer(SingleItemConfigUiState::receiptItemLines, ::interceptUiReceiptItemLines)
        }
        setUiState { copy(showScanCarton = activityViewModel.dataState.receiveMethod == ReceiveMethodEntity.RECEIVE_BY_CARTON) }
    }

    /*** Intercept and update [ReceiveItemLineCompose.canExpand] */
    @Suppress("UNUSED_PARAMETER")
    private fun interceptUiReceiptItemLines(_state: SingleItemConfigUiState, it: List<ReceiveItemLineCompose>) = it.map { compose ->
        compose.copy(canExpand = true)
    }

    init {
        launch {
            if (dataState.configCompose.autoConvertMfgDateByLotNumber) updateMfgDateByLotNumber()
        }
        launch {
            delay(350) // For smoothing the transition animation of Fragment, delay waiting for it to complete.
            subscribeTo(activityViewModel, LpSetupDataState::receiptItemLines, ::autoUpdateReceiptItemLines)
        }
        autoUpdateUiItemLines()
        setUiState { copy(showAddNewItem = false) }
    }

    /**
     * When enable automatically convert mfgDate by lot#, should convert mfgDate after lot# changed.
     * @see ItemMFGDateDal
     */
    private fun updateMfgDateByLotNumber() =
        subscribeInner(SingleItemConfigDataState::receiptItemLineMap, ReceiveItemLineCompose::lotNumber) {
            val mfgDates = it.mapNotNull { (itemLineId, lotNumber) ->
                val shouldCollectMfgDate = dataState.receiptItemLineMap[itemLineId]!!.requireCollectMfgDate
                if (lotNumber.isNullOrEmpty() || !shouldCollectMfgDate) {
                    return@mapNotNull null
                }
                val mfgStr = ItemMFGDateDal.convertMFGDateByLotNo(lotNumber, true)
                val mfgDate = runCatching { SimpleDateFormat("MM/dd/yy").parse(mfgStr) }.getOrNull() ?: return@mapNotNull null
                itemLineId to mfgDate
            }.toMap()
            setDataState {
                withItemLines(mfgDates.keys) { copy(manufactureDate = mfgDates[itemLineId]) }
            }
        }

    private suspend fun autoUpdateReceiptItemLines(list: List<ItemLineEntity>) {
        var filterList = list
        val config = dataState.configCompose
        val map = filterList.mapToLinkedMap {
            val oldItem = dataState.receiptItemLineMap[it.id]
            val item = if (oldItem != null) {
                val allUnits = if (config.forceDefaultUOM) {
                    it.uoms?.filter { v -> v.isDefaultUom }
                } else {
                    it.uoms
                }
                val allTemplatesForSelectedUnit = it.filterItemLpConfigurationsByUom(oldItem.selectedUnit) ?: listOf()
                oldItem.copy(itemLine = it, allTemplates = allTemplatesForSelectedUnit, allUnits = allUnits?: oldItem.allUnits, selectedUnit = oldItem.selectedUnit)
            } else {
                val (allUnits, selectedUnit) = if (config.forceDefaultUOM) {
                    val allUnits = it.uoms?.filter { v -> v.isDefaultUom }
                    if (allUnits.isNullOrEmpty()) showToast(R.string.please_setup_default_uom, it.itemName ?: "")
                    allUnits to allUnits?.firstOrNull()
                } else {
                    val allUnits = it.uoms
                    if (allUnits.isNullOrEmpty()) showToast(R.string.no_uom_found_for_item, it.itemName ?: "")
                    allUnits to allUnits?.find { v -> v.isDefaultUom }
                }
                val allTemplatesForSelectedUnit = it.filterItemLpConfigurationsByUom(selectedUnit) ?: listOf()
                val checkedTemplate = allTemplatesForSelectedUnit.find { v -> v.isDefault?: false } ?: allTemplatesForSelectedUnit.firstOrNull()
                val enableSelectLPTemplateByTiHiOnReceive = it.itemLpConfigurations?.isNotEmpty() ?: false
                val configurationMode = if (enableSelectLPTemplateByTiHiOnReceive && !config.forbidLpSetupPalletization) SingleItemConfigDataState.ConfigurationMode.TixHi else SingleItemConfigDataState.ConfigurationMode.QtyPerPallet
                ReceiveItemLineCompose(
                    itemLine = it,
                    receiveMethod = receiveMethod,
                    lotNumber = when {
                        config.forceScanLotNumber -> null
                        it.lotNo.isNullOrEmpty() -> null
                        else -> it.lotNo
                    },
                    needCollectCustomerPallet = config.needCollectCustomerPallet,
                    needCollectGoodsType = config.needCollectGoodsType,
                    allAllowedGoodsTypes = config.allAllowedGoodsTypes,
                    needCollectPhotoGoodsTypes = config.needCollectPhotoGoodsTypes,
                    selectedGoodsType = config.allAllowedGoodsTypes.first(),
                    allUnits = allUnits ?: listOf(),
                    selectedUnit = selectedUnit ?: allUnits?.firstOrNull(),
                    qtyPerPallet = calcQtyPerPallet(
                        selectedUnit ?: allUnits?.firstOrNull(),
                        checkedTemplate,
                        it,
                        configurationMode
                    ).toString(),
                    allTemplates = allTemplatesForSelectedUnit,
                    usedTemplates = it.usedTemplates ?: listOf(),
                    checkedTemplate = checkedTemplate,
                    needAlertHighValue = config.needAlertHighValueItem && (it.item?.grade?.isNotEmpty()
                        ?: false), //                    enableSelectLPTemplateByTiHiOnReceive = config.enableLpCountSuggestion,
                    enableSelectLPTemplateByTiHiOnReceive = enableSelectLPTemplateByTiHiOnReceive,
                    forbidMultipleLpConfigAtReceiveTask = config.forbidMultipleLpConfigAtReceiveTask,
                    forbidCreateLpConfigAtReceiveTask = config.forbidCreateLpConfigAtReceiveTask,
                    forbidLpSetupPalletization = config.forbidLpSetupPalletization,
                    selectedConfigurationMode = configurationMode)
            }
            it.id to item
        }
        setDataStateAwait { copy(receiptItemLineMap = map) }
    }

    private fun autoUpdateUiItemLines() = mapDataToUi(
        SingleItemConfigDataState::receiptItemLineMap,
        SingleItemConfigDataState::itemLineFilter,
        SingleItemConfigUiState::receiptItemLines) { list, filter ->
        if (filter.isNullOrEmpty()) list.values.toList() else list.values.filter { it.isFiltered }
    }

    fun filterItemLine(keyword: String) {
        setDataState {
            copy(itemLineFilter = keyword, receiptItemLineMap = receiptItemLineMap.asIterable().associate { (key, value) ->
                val isFiltered = if (keyword.isNullOrEmpty()) false else {
                    value.itemSpec?.id.contentEquals(keyword, true) || value.itemSpec?.name.contentEquals(
                        keyword, true) || value.itemSpec?.upcCode.contentEquals(keyword, true) || value.itemSpec?.upcCodeCase.contentEquals(
                        keyword, true) || value.itemSpec?.eanCode.contentEquals(
                        keyword, true) || value.itemSpec?.abbreviation.contentEquals(
                        keyword, true)
                }
                val isExpanded = if (isFiltered) true else value.isExpanded
                key to value.copy(isFiltered = isFiltered, isExpanded = isExpanded)
            })
        }
    }

    fun interceptExpand(itemLineId: String): Boolean {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        val itemSpec = itemLineCompose.itemSpec ?: return false
        val needCollectItemInfo = dataState.configCompose.needCollectItemInfo
        val needCollectItemSmallParcelPackaging = dataState.configCompose.needCollectItemSmallParcelPackaging
        if (!needCollectItemInfo) {
            if (checkMissingItemInfo(itemSpec, itemLineCompose.allUnits)) {
                fireEvent {
                    SingleItemConfigUiEvent.ShowAlert(
                        message = getFormattedString(R.string.msg_item_info_missing_or_unit_disabled_contact_supervisor, itemSpec.name)
                    )
                }
                return true
            }
            if (itemSpec.status == ItemStatusEntity.INACTIVE) {
                fireEvent {
                    SingleItemConfigUiEvent.ShowAlert(
                        message = getFormattedString(R.string.error_item_status_is_inactive, itemSpec.name)
                    )
                }
                return true
            }
        }
        if (itemLineCompose.forbidCreateLpConfigAtReceiveTask && itemLineCompose.itemLine.itemLpConfigurations.isNullOrEmpty()) {
            fireEvent {
                SingleItemConfigUiEvent.ShowAlert(
                    message = getFormattedString(R.string.msg_item_template_not_set_up, itemSpec.name)
                )
            }
            return true
        }
        if (checkMissingItemInfo(itemSpec, itemLineCompose.allUnits)) {
            fireEvent {
                SingleItemConfigUiEvent.AskCollectItemInfo(
                    itemLineCompose,
                    needCollectItemSmallParcelPackaging,
                    getFormattedString(R.string.msg_item_info_missing_or_unit_disabled, itemSpec.name)
                )
            }
            return true
        }
        if (itemSpec.status == ItemStatusEntity.INACTIVE) {
            fireEvent {
                SingleItemConfigUiEvent.AskCollectItemInfo(
                    itemLineCompose,
                    needCollectItemSmallParcelPackaging,
                    getFormattedString(R.string.msg_item_inactive_check_info, itemSpec.name)
                )
            }
            return true
        }
        return false
    }

    private fun checkMissingItemInfo(item: ItemEntity, uomList: List<UomEntity>): Boolean {
        return item.description.isNullOrEmpty()
                || uomList.all { !it.isValidUom() }
    }

    fun goCollectItemInfo(itemLineCompose: ReceiveItemLineCompose, needCollectItemSmallParcelPackaging: Boolean) {
        itemLineCompose.itemSpec?.let {
            val configData = CollectItemInfoConfigData(it).apply {
                this.item = it
                this.photos = it.imageFileIds
                this.isCollectItemGroupAtReceiving = configurationMap?.valueMapping?.get("collectItemGroupAtReceiving") == "true"
                this.canNewUom = customerEntity?.inboundSetting?.allowNewUomInReceiving?: false
                this.recurringStorageRateByPallet = true
                this.customerName = customerEntity?.name
            }
            activityViewModel.onLpSetupProcessChanged(LpSetupProcess.CollectItemInfo(configData))
        }
    }

    fun changeExpandStatus(itemLineId: String, isExpand: Boolean) {
        if (isExpand) {
            val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
            itemLineCompose.considerShowAlertAtFirstExpand()
        }
        setDataState {
            withItemLine(itemLineId) { copy(isFirstExpand = false, isExpanded = isExpand) }
        }
    }

    private fun ReceiveItemLineCompose.considerShowAlertAtFirstExpand() {
        if (!isFirstExpand) return // Alert Seasonal Pack
        if (false) fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.text_remind_seasonal_pack)) } // Alert High Value Item
        if (needAlertHighValue) fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.msg_alert_high_value_item)) }
    }

    fun selectGoodsType(itemLineId: String, goodsType: String) {
        setDataState {
            withItemLine(itemLineId) { copy(selectedGoodsType = goodsType) }
        }
    }

    fun updateGoodsTypePhotos(itemLineId: String, photos: List<String>) {
        setDataState {
            withItemLine(itemLineId) { copy(damagedPhotos = photos) }
        }
    }

    fun selectConfigurationMode(itemLineId: String, mode: SingleItemConfigDataState.ConfigurationMode) {
        setDataState {
            withItemLine(itemLineId) {
                if (mode == SingleItemConfigDataState.ConfigurationMode.TixHi) {
                    copy(selectedConfigurationMode = mode).updateSelectedUnitByTemplate()
                } else {
                    copy(selectedConfigurationMode = mode, qtyPerPallet = calcQtyPerPalletForPerPallet(itemLineId).toString())
                }
            }
        }
    }

    private fun calcQtyPerPalletForPerPallet(itemLineId: String): Int {
        val receiptItemLine = dataState.receiptItemLineMap[itemLineId]
        return receiptItemLine?.let {
            val uomBaseQty = it.selectedUnit?.baseQty?:1.0
            val remainingReceiveBaseQty = it.itemLine.remainingReceiveBaseQty()
            return if (remainingReceiveBaseQty < 1) {
                0
            } else if (remainingReceiveBaseQty < uomBaseQty) {
                1
            } else {
                (remainingReceiveBaseQty.toInt()/uomBaseQty).toInt()
            }
        }?: 0
    }

    private fun calcQtyPerPalletForPerPallet(selectedUnit: UomEntity?, itemLine: ItemLineEntity?): Int {
        val uomBaseQty = selectedUnit?.baseQty?:1.0
        val remainingReceiveBaseQty = itemLine?.remainingReceiveBaseQty()?:0
        return  if (remainingReceiveBaseQty < 1) {
            0
        } else if (remainingReceiveBaseQty < uomBaseQty) {
            1
        } else {
            (remainingReceiveBaseQty.toInt()/uomBaseQty).toInt()
        }
    }

    private fun calcQtyPerPalletByTemplate(checkedTemplate: SingleItemLpConfigurationEntity?, itemLine: ItemLineEntity?): Int {
        val remainingReceiveBaseQty = itemLine?.remainingReceiveBaseQty()?:0
        val templateTotalBaseQty = checkedTemplate?.lpConfigurationTemplate?.totalQty?:1.0
        return if (remainingReceiveBaseQty > 1 && remainingReceiveBaseQty < templateTotalBaseQty) {
            remainingReceiveBaseQty.toInt()
        } else {
            (checkedTemplate?.lpConfigurationTemplate?.totalQty?:1.0).toInt()
        }
    }

    private fun calcQtyPerPallet(selectedUnit: UomEntity?,
                                 checkedTemplate: SingleItemLpConfigurationEntity?,
                                 itemLine: ItemLineEntity?,
                                 configurationMode: SingleItemConfigDataState.ConfigurationMode
    ): Int {
       return if (configurationMode == SingleItemConfigDataState.ConfigurationMode.TixHi) {
           calcQtyPerPalletByTemplate(checkedTemplate, itemLine)
        } else {
           calcQtyPerPalletForPerPallet(selectedUnit, itemLine)
        }
    }

    private fun updateQtyPerPalletByTemplate(itemLineId: String, checkedTemplate: SingleItemLpConfigurationEntity?) {
        checkedTemplate?.let {
            val receiptItemLine = dataState.receiptItemLineMap[itemLineId]
            receiptItemLine?.let {
                val uomBaseQty = it.selectedUnit?.baseQty?:1.0
                val remainingReceiveBaseQty = it.itemLine.remainingReceiveBaseQty()
                val templateTotalBaseQty = checkedTemplate.lpConfigurationTemplate?.totalQty?:1.0
                val qtyPerPallet = if (remainingReceiveBaseQty > 1 && remainingReceiveBaseQty < templateTotalBaseQty) {
                    remainingReceiveBaseQty.toInt()
                } else {
                    (checkedTemplate.lpConfigurationTemplate?.totalQty?:1.0).toInt()
                }
                setDataState {
                    withItemLine(itemLineId) {
                        copy(qtyPerPallet = qtyPerPallet.toString())
                    }
                }
            }
        }
    }

    private fun ReceiveItemLineCompose.updateSelectedUnitByTemplate(isSuggestQty: Boolean = true): ReceiveItemLineCompose {
        val checkedTemplate = this.checkedTemplate ?: return this
        val uomBriefEntity = splitUomNameAndNumber(checkedTemplate.uomId)
        val uomEntity = this.allUnits.find { it.uomId == uomBriefEntity?.uomId } ?: this.selectedUnit
        return if (dataState.configCompose.forceDefaultUOM && uomEntity != null && !uomEntity.isDefaultUom) {
            this
        } else {
            if (isSuggestQty) updateQtyPerPalletByTemplate(this.itemLineId, checkedTemplate)
            this.copy(selectedUnit = uomEntity)
        }
    }

    fun selectTiHi(itemLineId: String, template: SingleItemLpConfigurationEntity?) {
        setDataState {
            withItemLine(itemLineId) { copy(checkedTemplate = template).updateSelectedUnitByTemplate() }
        }
    }

    fun addTiHi(itemLineId: String, ti: String?, hi: String?) {
        ti ?: return
        hi ?: return
        val tiQty = ti.toIntOrNull()
        val hiQty = hi.toIntOrNull()
        if (tiQty == null || hiQty == null) {
            showToast(R.string.msg_invalid_qty_format)
            return
        }
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        launch {
            val itemTemplateName = "${tiQty}x$hiQty" // First search lpTemplate by ti & hi
            val singleItemLpConfigurationQueryEntity = SingleItemLpConfigurationQueryEntity().apply {
                this.itemId = itemLineCompose.itemSpecId
            }
            val lpTemplateResult = requestAwait(
                repository.searchItemLpTemplate(singleItemLpConfigurationQueryEntity)).onFailure {
                return@launch
            }
            val lpTemplates = lpTemplateResult.getOrNull()
            val find = lpTemplates?.find {
                isMatchLpTemplate(
                    hiQty.toDouble(),
                    tiQty.toDouble(),
                    activityViewModel.getCustomerId()!!,
                    it.lpConfigurationTemplate
                )
            }
            if (find == null) {
                val createEntity = SingleItemLpTemplateCreateEntity().apply {
                    this.scene = SceneEntity.INBOUND
                    this.itemId = itemLineCompose.itemSpecId
                    this.uomId = itemLineCompose.selectedUnit?.uomId
                    this.name = itemTemplateName
                    this.description = itemTemplateName
                    this.layer = hiQty.toDouble()
                    this.stack = tiQty.toDouble()
                    this.totalQty = (tiQty * hiQty).toDouble()
                    this.customerId = activityViewModel.getCustomerId()
                }
                requestAwait(repository.createSingleItemLpTemplate(createEntity)).onSuccess {
                    it?.let { lpTemplate ->
                        setDataState {
                            withItemLine(itemLineId) {
                                copy(allTemplates = allTemplates + lpTemplate).updateSelectedUnitByTemplate()
                            }
                        }
                        activityViewModel.updateLpTemplateToItemLine(itemLineId, lpTemplate)
                    }
                }
            } else {
                showToast(String.format(getString(R.string.msg_template_is_exist), "${tiQty}x$hiQty"))
            }
        }
    }

    private fun isMatchLpTemplate(hiQty: Double, tiQty: Double, customerId: String, singleItemLpTemplateEntity: SingleItemLpTemplateEntity?): Boolean {
        return singleItemLpTemplateEntity?.let {
            it.layer == hiQty && it.stack == tiQty && it.customerId == customerId
        }?: false
    }

    fun removeTiHi(itemLineId: String, template: SingleItemLpConfigurationEntity) {
        request(repository.removeSingleItemLpTemplate(template.id)) {
            setDataState {
                withItemLine(itemLineId) {
                    copy(
                        allTemplates = allTemplates - template,
                        checkedTemplate = if (checkedTemplate == template) null else checkedTemplate
                    ).updateSelectedUnitByTemplate()
                }
            }
            activityViewModel.removeLpTemplateFromItemLine(itemLineId, template.id)
        }
    }

    fun updateQtyPerPallet(itemLineId: String, qtyStr: String?, inputDone: Boolean) {
        if (inputDone) {
            val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
            runCatching { validateQtyPerPallet(itemLineCompose, qtyStr) }.onFailure {
                showToast(it.message!!)
                return
            }
        }
        setDataState {
            withItemLine(itemLineId) { copy(qtyPerPallet = qtyStr) }
        }
    }

    @Throws
    private fun validateQtyPerPallet(itemLineCompose: ReceiveItemLineCompose, qtyStr: String?): Double {
        qtyStr ?: throw getString(R.string.msg_input_total_qty_per_pallet).toException
        val qty = qtyStr.toDoubleOrNull() ?: throw getString(R.string.msg_invalid_qty_format).toException
        if (qty <= 0) {
            throw getString(R.string.msg_ti_hi_cannot_be_zero).toException
        }
        if (itemLineCompose.receiveMethod != ReceiveMethodEntity.RECEIVE_BY_CARTON && itemLineCompose.selectedConfigurationMode == SingleItemConfigDataState.ConfigurationMode.TixHi && qty > (itemLineCompose.selectedTemplate?.lpConfigurationTemplate?.totalQty
                ?: 0.0)
        ) {
            throw getFormattedString(R.string.error_qty_not_match, qtyStr).toException
        }
        return qty
    }

    fun selectUom(itemLineId: String, unit: UomEntity) {
        val receiveItemLineCompose = dataState.receiptItemLineMap[itemLineId]?:return
        val isTiHiModel = receiveItemLineCompose.selectedConfigurationMode == SingleItemConfigDataState.ConfigurationMode.TixHi
        setDataState {
            withItemLine(itemLineId) {
                if (isTiHiModel) {
                    copy(selectedUnit = unit)
                } else {
                    copy(selectedUnit = unit, qtyPerPallet = calcQtyPerPalletForPerPallet(unit, receiveItemLineCompose.itemLine).toString())
                }
            }
        }
        if (isTiHiModel) {
            setDataState {
                copy(receiptItemLineMap = this.receiptItemLineMap.mapValues { (_, value) ->
                    val templates = receiveItemLineCompose.itemLine.filterItemLpConfigurationsByUom(unit)?: listOf()
                    val checkedTemplate = when {
                        templates.any {
                            it.isDefault == true && it.uomId == unit.uomId
                        } -> templates.first { it.isDefault == true && it.uomId == unit.uomId }
                        else -> templates.firstOrNull()
                    }
                    value.copy(allTemplates = templates, checkedTemplate = checkedTemplate)
                })
            }
            withDataState { updateQtyPerPalletByTemplate(itemLineId, this.receiptItemLineMap[itemLineId]?.checkedTemplate) }
        }
    }

    fun updateLotNumber(itemLineId: String, lotNumber: String) {
        runCatching { validateLotNumber(itemLineId, lotNumber) }.onFailure {
            showToast(it.message!!)
            return
        }
        setDataState {
            withItemLine(itemLineId) { copy(lotNumber = lotNumber) }
        }
    }

    fun updateCustomerPalletNumber(itemLineId: String, palletNumber: String) {
        setDataState {
            withItemLine(itemLineId) { copy(customerPalletNumber = palletNumber) }
        }
    }

    @Throws
    private fun validateLotNumber(itemLineId: String, lotNumber: String?) {
        lotNumber ?: throw getString(R.string.msg_please_input_lot_no).toException

        val regex = dataState.configCompose.lotNumberRegex
        if (!regex.isNullOrEmpty() && !lotNumber.matches(Regex(regex))) {
            throw getFormattedString(R.string.toast_receiving_lot_violation, regex).toException
        }
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId] ?: return
        if (!TextUtils.isEmpty(itemLineCompose.itemLine.lotNo) && lotNumber != itemLineCompose.itemLine.lotNo) {
            throw getString(R.string.msg_lot_no_not_match_and_wait).toException
        }
    }

    fun updateShelfLifeDays(itemLineId: String, days: String?, inputDone: Boolean) {
        if (inputDone) {
            runCatching { validateShelfLifeDays(days) }.onFailure {
                showToast(it.message!!)
                return
            }
        }
        setDataState {
            withItemLine(itemLineId) { copy(shelfLifeDays = days) }
        }
    }

    private fun validateShelfLifeDays(days: String?) {
        if (days.isNullOrEmpty()) throw getString(R.string.msg_self_life_day_require).toException
        days.toIntOrNull() ?: throw getString(R.string.msg_invalid_qty_format).toException
    }

    fun updateMfgDate(itemLineId: String, mfgDate: Date) {
        setDataState {
            withItemLine(itemLineId) { copy(manufactureDate = mfgDate) }
        }
    }

    fun updateExpirationDate(itemLineId: String, expDate: Date) {
        setDataState {
            withItemLine(itemLineId) { copy(expirationDate = expDate) }
        }
    }

    fun printLpAndSubmit(
        itemLineId: String,
        onShowExpirationDateConfirm: (String?) -> Flow<Boolean?>,
        onShowQtyConfirm: (param: QtyConfirmDialog.Param) -> Flow<QtyConfirmDialog.Param?>,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId] ?: return
        runCatching { validateConfiguration(itemLineCompose) }.onFailure {
            showToast(it.message!!)
            return
        }
        launch { // check printer
            val printData = repository.getPrintData(lpLabelSize)
            if (!PrintUtil.hasPrinter(printData)) {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                return@launch
            }

            // confirm lp count
            val lpParam = showLpCountConfirmDialog(itemLineCompose, onShowQtyConfirm) ?: return@launch

            val checkExpirationDateResult = expirationDateConfirm(itemLineCompose, onShowExpirationDateConfirm)
            if (checkExpirationDateResult == false) return@launch

            if (!validateOverReceive(itemLineCompose, lpParam)) return@launch

            // print
            val result = lpSetupAndPrint(itemLineCompose, lpParam, printData, onReprintConfirm)
            if (!result) {
                showToast(R.string.text_print_fail)
                return@launch
            } // all done, refresh data
            changeExpandStatus(itemLineId, false)
            showSnack(SnackType.SuccessV1(), R.string.toast_update_success)
        }
    }

    private suspend fun showLpCountConfirmDialog(
        itemLineCompose: ReceiveItemLineCompose,
        onShowQtyConfirm: (param: QtyConfirmDialog.Param) -> Flow<QtyConfirmDialog.Param?>,
    ): QtyConfirmDialog.Param? {
        val (suggestLpCount, hasNotFullSubmit, notFullSubmitQty) = getSuggestLpCount(itemLineCompose)
        val remainingReceiveBaseQty = itemLineCompose.itemLine.remainingReceiveBaseQty()
        val toReceiveUnitName = itemLineCompose.itemLine.expectUomName
        val qtyPerPallet = itemLineCompose.qtyPerPallet?.toInt() ?: 0
        val lpUnitName = itemLineCompose.selectedUnit!!.name
        val lpParam = onShowQtyConfirm(
            QtyConfirmDialog.Param(
                lpCount = suggestLpCount,
                qtyPerPallet = qtyPerPallet,
                initialReceivingText = if (hasNotFullSubmit) "${(suggestLpCount - 1) * qtyPerPallet}+${notFullSubmitQty.toInt()}" else "${suggestLpCount * qtyPerPallet}",
                lpUnitName = lpUnitName,
                toReceiveText = "$remainingReceiveBaseQty $toReceiveUnitName",
                receivingQty = if (hasNotFullSubmit) ((suggestLpCount - 1) * qtyPerPallet + notFullSubmitQty.toInt()) else suggestLpCount * qtyPerPallet,
                forbidLpSetupPalletization = dataState.configCompose.forbidLpSetupPalletization,
                selectedLpSubType = if (itemLineCompose.selectedConfigurationMode == SingleItemConfigDataState.ConfigurationMode.TixHi) LpSubType.PALLET else LpSubType.CARTON
            )).firstOrNull()
            ?: return null

        if (lpParam.lpCount <= 0) {
            showToast(R.string.lp_count_must_greater_than_zero)
            return null
        }
        return lpParam
    }

    private suspend fun lpSetupAndPrint(
        itemLineCompose: ReceiveItemLineCompose,
        lpParam: QtyConfirmDialog.Param,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>
    ): Boolean {
        val qtyPerPallet = itemLineCompose.qtyPerPallet!!.toDouble()
        val lpCount = ceil(lpParam.receivingQty / qtyPerPallet).toInt()
        val lpBatchCreateEntity = LpBatchCreateEntity().apply {
            this.lpCount = lpCount
            this.type = LpType.ILP
            this.confId = itemLineCompose.selectedTemplate?.lpConfigurationTemplateId
            this.locationId = selectedLocation.id
            this.lpSubType = lpParam.selectedLpSubType
        }
        val lpItemEntity = LpItemEntity().apply {
            this.itemId = itemLineCompose.itemSpecId
            this.itemLineId = itemLineCompose.itemLineId
            this.lotNo = itemLineCompose.lotNumber
            this.uomId = itemLineCompose.selectedUnit?.uomId
            this.mfgDate = itemLineCompose.manufactureDate
            this.expirationDate = itemLineCompose.expirationDate
            this.shelfLifeDays = itemLineCompose.shelfLifeDays?.toIntOrNull()
        }
        val lpSetupForSingleItemEntity = LpSetupForSingleItemEntity().apply {
            this.batchCreateLpCmd = lpBatchCreateEntity
            this.items = listOf(lpItemEntity)
            this.fullPalletQty = qtyPerPallet
            this.receivedQty = lpParam.receivingQty.toDouble()
            this.stepId = activityViewModel.getStepId()?.toLong()
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.palletNo = itemLineCompose.customerPalletNumber
            this.lpTemplateId = itemLineCompose.selectedTemplate?.lpConfigurationTemplateId
            this.lpGoodsType = itemLineCompose.selectedGoodsType
            this.damagePhotoIds = if (itemLineCompose.needCollectPhoto) itemLineCompose.damagedPhotos else null
        }

        val createLpAndLpSetupResult = activityViewModel.createLpAndLpSetup(lpSetupForSingleItemEntity).onFailure { return false }
        val lpIds = createLpAndLpSetupResult.getOrNull()
        if (lpIds.isNullOrEmpty()) {
            fireEvent {
                SingleItemConfigUiEvent.ShowAlert(
                    title = getString(R.string.title_alert), message = getString(R.string.msg_create_lp_or_lp_set_up_failure))
            }
            return false
        }

        activityViewModel.refreshItemLines()

        val lpJobEntry = LpJobEntry().apply {
            this.lpIds = lpIds
            this.copyNO = "1"
        }

        if (printData.paperSize == LabelSizeEntry.FOUR_SIX) {
            val lpPlateLabel = LpPlateLabelRequest().apply {
                this.receiptId = itemLineCompose.itemLine.receiptId
                this.lpIds = lpJobEntry.lpIds
                this.itemLineId = itemLineCompose.itemLine.id
                this.lpItemQty = qtyPerPallet.toInt()
                this.itemId = itemLineCompose.itemLine.itemId
                this.lotNo = if (itemLineCompose.lotNumber.isNullOrEmpty()) itemLineCompose.itemLine.lotNo else itemLineCompose.lotNumber
                this.expirationDate = itemLineCompose.expirationDate?: itemLineCompose.itemLine.expirationDate
            }
            val printJobs = buildPrintJob4X6(lpPlateLabel)
            if (printJobs.isEmpty()) return false
            lpJobEntry.zplCode = printJobs.joinToString("")
        }

        printLps(lpJobEntry, printData, onReprintConfirm)
        return true
    }

    private suspend fun expirationDateConfirm(
        itemLineCompose: ReceiveItemLineCompose,
        onShowExpirationDateConfirm: (String?) -> Flow<Boolean?>
    ): Boolean? {
        val shipAllowDays = itemLineCompose.shipAllowDays
        val storageDays = itemLineCompose.storageDays?: 0
        val itemName = itemLineCompose.itemSpec?.name?: ""
        val zoneId = repository.facilityEntity?.timeZone
        val expirationDate = itemLineCompose.expirationDate ?: if (itemLineCompose.manufactureDate != null){
            itemLineCompose.manufactureDate.plusDays(storageDays)
        } else {
            null
        }

        if(expirationDate == null || shipAllowDays == null) return true

        val daysDiff = getDateDiffDays(TimeUtil.getCurrentTimeByZone(zoneId), expirationDate)

        if (daysDiff <= shipAllowDays) {
            return onShowExpirationDateConfirm.invoke(itemName).firstOrNull()
        }

        return true
    }

    private suspend fun validateOverReceive(
        itemLineCompose: ReceiveItemLineCompose,
        lpParam: QtyConfirmDialog.Param,
    ): Boolean{
        val isNotAllowOverReceive = activityViewModel.isNotAllowOverReceive(itemLineCompose.itemLine)
        val receivingBaseQty = UomUtils.calculateBaseQty(lpParam.receivingQty.toDouble(), itemLineCompose.selectedUnit!!.uomId)
        if (!isNotAllowOverReceive && receivingBaseQty > itemLineCompose.itemLine.remainingReceiveBaseQty()) {
            return awaitEvent {
                SingleItemConfigUiEvent.AskConfirmOverReceive(
                    message = getFormattedString(R.string.msg_item_exceeds_expected_quantity_confirm,
                        itemLineCompose.itemLine.itemName ?: "",
                        "${itemLineCompose.itemLine.expectQty}${itemLineCompose.itemLine.expectUomName}"
                    )
                )
            } ?: false
        }
        return true
    }

    private fun getDateDiffDays(date1: Date, date2: Date): Long {
        // 使用 Calendar 去除时分秒
        val cal1 = Calendar.getInstance().apply {
            time = date1
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val cal2 = Calendar.getInstance().apply {
            time = date2
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        // 计算天数差
        return (cal2.timeInMillis - cal1.timeInMillis) / (1000L * 60 * 60 * 24)
    }

    private suspend fun buildPrintJob4X6(lpPlateLabel: LpPlateLabelRequest): List<String> {
        return requestAwait(repository.buildPlateLabelJob(lpPlateLabel)).getOrNull() ?: return emptyList()
    }

    private fun joinZplCode(fullPalletLpCode: LpJobEntry, particalPalletLpCode: LpJobEntry?): String {
        var zplCode = ""
        if (!TextUtils.isEmpty(fullPalletLpCode.zplCode)) {
            zplCode = fullPalletLpCode.zplCode
        }
        if (!TextUtils.isEmpty(particalPalletLpCode?.zplCode)) {
            zplCode += particalPalletLpCode?.zplCode
        }
        return zplCode
    }

    fun printILPForReceiveAll(printType: ReceiveAllPrintType, onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        if (CollectionUtil.isNullOrEmpty(dataState.receiptItemLineMap.values)) {
            return
        }

        launch {
            val printData = repository.getPrintData(lpLabelSize)
            if (!PrintUtil.hasPrinter(printData)) {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                return@launch
            }
            createAndAddLpsForReceiveAll(printData, onReprintConfirm, printType).onSuccess {
                completeStep()
            }
        }
    }

    private suspend fun createAndAddLpsForReceiveAll(
        printData: PrintData, onReprintConfirm: (message: String) -> Flow<Boolean?>, printType: ReceiveAllPrintType
    ): Result<Boolean> {
        val failsResult = Result.failure<Boolean>("".toException)
        val createLpsResult =
            (if (printType == ReceiveAllPrintType.PrintBySku) createLpsBySku(printData.paperSize) else createLpsByReceipt(printData.paperSize)).onFailure { return failsResult }
        val printJobs =
            if (CollectionUtil.isNotNullOrEmpty(createLpsResult.getOrNull())) createLpsResult.getOrNull() else return failsResult
        val addLpResult = batchAddLpsForReceiveAll(printJobs!!, printType).onFailure { return failsResult }
        val failedLps = addLpResult.getOrNull()!!.filter { !it.success }
        if (failedLps.isNotEmpty()) {
            val error = failedLps.joinToString {
                getFormattedString(R.string.label_lp_setup_submit_respond, it.lpId, it.errorMessage)
            }
            fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.title_alert), message = error) }
            return failsResult
        }
        val printJob = LpJobEntry()
        printJob.jobIds = mutableListOf()
        if (printType == ReceiveAllPrintType.PrintBySku || printData.paperSize == LabelSizeEntry.TWO_ONE) { // PrintBySku or 2X1 -> print by jobId
            printJobs.forEach {
                printJob.jobIds.addAll(it.jobIds)
            }
        } else { // PrintByReceipt and 4X6 -> print by commands
            printJob.zplCode =
                Stream.ofNullable(printJobs.map { lpJobEntry -> lpJobEntry.zplCode }.toList()).collect(Collectors.joining("\r\n"))
        }
        return printLps(printJob, printData, onReprintConfirm)
    }

    private suspend fun batchAddLpsForReceiveAll(
        printJobs: List<LpJobEntry>, printType: ReceiveAllPrintType
    ): Result<List<LPSetupResultEntry>?> {
        return when (printType) {
            ReceiveAllPrintType.PrintBySku -> requestAwait(
                activityViewModel.batchAddLpsRequestForSku(
                    printJobs, dataState.receiptItemLineMap))

            ReceiveAllPrintType.PrintByReceipt -> requestAwait(
                activityViewModel.batchAddLpsRequestForReceipt(
                    printJobs, dataState.receiptItemLineMap))
        }
    }

    private suspend fun batchCreateLps(
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose,
    ): Result<LpCreateResponseEntry?> {
        val lpBatchCreateEntry = LpBatchCreateEntry().apply {
            this.count = lpCount
            this.type = LpTypeEntry.ILP
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.lpItemSpecId = itemLineCompose.itemSpecId
            this.confId = if (isNotFullSubmit) null else itemLineCompose.selectedTemplate?.id.toString() // 4*6
            this.locationId = selectedLocation.id // 4*6
            this.itemLineId = itemLineCompose.itemLineId // 4*6
            this.lpItemQty =
                if (isNotFullSubmit) notFullSubmitQty.toInt().toString() else itemLineCompose.qtyPerPallet!!.toInt().toString() // 4*6
            this.lotNo = itemLineCompose.lotNumber ?: "" // 4*6
            this.mfgDate = itemLineCompose.manufactureDate // 2*1
            this.expirationDate = itemLineCompose.expirationDate // 2*1
            this.printScenario = ScenarioEntry.RECEIVE_WITH_SINGLE_ITEM // 2*1
        }
        val request = repository.batchCreateLp(lpBatchCreateEntry)
        return requestAwait(request)
    }

    private suspend fun batchCreatePrintJobs(
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose,
        labelSize: LabelSizeEntry,
        lpIds: List<String>
    ): Result<LpJobEntry?> {
        val lpBatchCreateEntry = LpBatchCreateEntry().apply {
            this.count = lpCount
            this.type = LpTypeEntry.ILP
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.lpItemSpecId = itemLineCompose.itemSpecId
            this.confId = if (isNotFullSubmit) null else itemLineCompose.selectedTemplate?.id.toString() // 4*6
            this.locationId = selectedLocation.id // 4*6
            this.itemLineId = itemLineCompose.itemLineId // 4*6
            this.lpItemQty =
                if (isNotFullSubmit) notFullSubmitQty.toInt().toString() else itemLineCompose.qtyPerPallet!!.toInt().toString() // 4*6
            this.lotNo = itemLineCompose.lotNumber ?: "" // 4*6
            this.mfgDate = itemLineCompose.manufactureDate // 2*1
            this.expirationDate = itemLineCompose.expirationDate // 2*1
            this.printScenario = ScenarioEntry.RECEIVE_WITH_SINGLE_ITEM // 2*1
            this.lpIds = lpIds
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.createPrintJobs4X6(lpBatchCreateEntry)
        } else {
            repository.createPrintJobs2X1(lpBatchCreateEntry)
        }
        return requestAwait(request)
    }

    private suspend fun batchCreatePrintJobs(
        lpCount: Int, itemLineCompose: ReceiveItemLineCompose, labelSize: LabelSizeEntry, lpIds: List<String>
    ): Result<LpJobEntry?> {
        val lpBatchCreateEntry = LpBatchCreateEntry().apply {
            this.count = lpCount
            this.type = LpTypeEntry.ILP
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.lpItemSpecId = itemLineCompose.itemSpecId
            this.confId = itemLineCompose.selectedTemplate?.id.toString() // 4*6
            this.locationId = selectedLocation.id // 4*6
            this.itemLineId = itemLineCompose.itemLineId // 4*6
            this.lpItemQty = itemLineCompose.qtyPerPallet!!.toInt().toString() // 4*6
            this.lotNo = itemLineCompose.lotNumber ?: "" // 4*6
            this.mfgDate = itemLineCompose.manufactureDate // 2*1
            this.expirationDate = itemLineCompose.expirationDate // 2*1
            this.printScenario = ScenarioEntry.RECEIVE_WITH_SINGLE_ITEM // 2*1
            this.lpIds = lpIds
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.createPrintJobs4X6(lpBatchCreateEntry)
        } else {
            repository.createPrintJobs2X1(lpBatchCreateEntry)
        }
        return requestAwait(request)
    }

    private suspend fun createLpsBySku(labelSize: LabelSizeEntry?): Result<List<LpJobEntry>?> {
        val list = mutableListOf<LpBatchCreateEntry>()
        dataState.receiptItemLineMap.values.forEach {
            val createEntry = LpBatchCreateEntry().apply {
                this.count = 1
                this.type = LpTypeEntry.ILP
                this.receiptId = it.itemLine.receiptId
                this.lpItemSpecId = it.itemSpecId
                this.locationId = selectedLocation.id
                this.itemLineId = it.itemLineId
                this.lotNo = it.itemLine.lotNo //                this.lpItemQty = (it.itemLine.qty / 1).toInt().toString()
            }
            list.add(createEntry)
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.create4X6Lp(list)
        } else {
            repository.create2X1Lp(list)
        }
        return requestAwait(request)
    }

    private suspend fun createLpsByReceipt(labelSize: LabelSizeEntry?): Result<List<LpJobEntry>?> {
        val list = mutableListOf<LpBatchCreateEntry>()
        val receiptList = Lists.uniq(dataState.receiptItemLineMap.values.map { it.itemLine.receiptId }.toList())
        receiptList.forEach { receiptId ->
            val receiveItemLineComposes = dataState.receiptItemLineMap.values.filter { it.itemLine.receiptId == receiptId }
            val createEntry = LpBatchCreateEntry().apply {
                this.count = 1
                this.type = LpTypeEntry.ILP
                this.receiptId = receiptId
                this.lpItemSpecId = receiveItemLineComposes[0].itemSpecId
                this.locationId = selectedLocation.id
                val lpItemQty = countLpItemQty(receiveItemLineComposes)
                if (isSameUnitForItemLine(receiveItemLineComposes) && lpItemQty > 0) {
                    this.lpItemQty = lpItemQty.toString()
                }
            }
            list.add(createEntry)
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.printILPByReceipt(list)
        } else {
            repository.create2X1Lp(list)
        }
        return requestAwait(request)
    }

    private fun countLpItemQty(receiveItemLineComposes: List<ReceiveItemLineCompose>): Int {
        var qtyCount = 0
        for (receiveItemLineCompose in receiveItemLineComposes) { //            qtyCount += (receiveItemLineCompose.itemLine.qty / 1).toInt()
        }
        return qtyCount
    }

    private suspend fun printLps(
        printJob: LpJobEntry,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        val laPrintJobCreate = PrintJobCreate.LpLabel().apply {
            this.lpIds = printJob.lpIds
            this.lpItemQty = printJob.copyNO.toIntOrNull() ?: 1
        }
        if (printJob.zplCode.isNullOrEmpty()) {
            printData.jobData = PrintData.JobData.ZPL(jobCreate = laPrintJobCreate)
        } else {
            printData.jobData = PrintData.JobData.ZPL(printCommands = printJob.zplCode)
        }
        val lpLabelSize = activityViewModel.dataState.labelSize ?: LabelSizeEntry.TWO_ONE
        val printer =
            PrinterManager.getPrinter(printData.idmUserId ?: "", printData.facilityName ?: "", lpLabelSize) ?: return Result.success(false)
        val result = printUtil.printWithFlow(printData, onShowProgress = { showLoading(it) }, printer).firstOrNull()

        return when (result) {
            null -> Result.success(false)
            PrintResult.NoPrinter -> {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                Result.success(false)
            }

            is PrintResult.Success -> Result.success(true)
            is PrintResult.Failed -> {
                val reprint = onReprintConfirm(PrintMsg.formatError(result.printerEntry, result.response.errorMessage)).firstOrNull()
                if (reprint == true) {
                    printLps(printJob, printData, onReprintConfirm)
                } else {
                    Result.success(false)
                }
            }
        }
    }

    @Throws
    private fun validateConfiguration(selectedItemLine: ReceiveItemLineCompose) {
        if (selectedItemLine.receiveMethod == ReceiveMethodEntity.RECEIVE_BY_CARTON && TextUtils.isEmpty(dataState.cartonNo)) {
            throw getString(R.string.hint_please_scan_carton).toException
        }
        val itemLineId = selectedItemLine.itemLineId
        if (selectedItemLine.requireCollectLotNumber) {
            validateLotNumber(itemLineId, selectedItemLine.lotNumber)
        }
        if (selectedItemLine.needCollectCustomerPallet && selectedItemLine.customerPalletNumber.isNullOrEmpty()) {
            throw getString(R.string.msg_remind_input_customer_pallet_no).toException
        }
        if (selectedItemLine.requireCollectShelfLife) {
            validateShelfLifeDays(selectedItemLine.shelfLifeDays)
        }
        if (selectedItemLine.requireCollectMfgDate && selectedItemLine.manufactureDate == null) {
            throw getString(R.string.msg_mfg_date_require).toException
        }
        if (selectedItemLine.requireCollectExpDate && selectedItemLine.expirationDate == null) {
            throw getString(R.string.msg_expiration_date_require).toException
        }
        validateGoodsType(selectedItemLine)
        if (selectedItemLine.selectedUnit == null) {
            throw getString(R.string.msg_please_select_oum).toException
        }
        validateQtyPerPallet(selectedItemLine, selectedItemLine.qtyPerPallet)
        if (selectedItemLine.forbidMultipleLpConfigAtReceiveTask
            && selectedItemLine.selectedConfigurationMode == SingleItemConfigDataState.ConfigurationMode.TixHi
            && isUsedDifferentTemplate(selectedItemLine)) {
            val templateName = selectedItemLine.usedTemplates.find { it.lpConfigurationTemplateId != selectedItemLine.selectedTemplate?.lpConfigurationTemplateId }?.name
            throw String.format(getString(R.string.msg_item_received_using_template_cannot_use_different_template), templateName).toException
        }
    }

    private fun isUsedDifferentTemplate(selectedItemLine: ReceiveItemLineCompose): Boolean {
       return selectedItemLine.usedTemplates.any { it.lpConfigurationTemplateId != selectedItemLine.selectedTemplate?.lpConfigurationTemplateId }
    }

    @Throws
    private fun validateGoodsType(itemLineCompose: ReceiveItemLineCompose) {
        val goodsType = itemLineCompose.selectedGoodsType ?: throw getString(R.string.msg_select_item_condition).toException
        val location = activityViewModel.getSelectedLocation()!!
        if (location.inventoryStatus == LocationInventoryStatus.REWORK && !GoodsTypeHelper().isReworkNeededGoodsType(goodsType)) {
            throw getFormattedString(R.string.msg_rework_location_xx_support_item_condition_rework_needed, location.name).toException
        }
        if (location.inventoryStatus != LocationInventoryStatus.REWORK && GoodsTypeHelper().isReworkNeededGoodsType(goodsType)) {
            throw getString(R.string.msg_rework_needed_condition_only_allow_select_rework_location).toException
        }
        if (itemLineCompose.needCollectPhoto && itemLineCompose.damagedPhotos.isEmpty()) {
            throw getString(R.string.msg_please_capture_photo).toException
        }
    }

    private fun isSameUnitForItemLine(receiptItemLines: List<ReceiveItemLineCompose>?): Boolean {
        receiptItemLines?.let {
            val unitName: String? = it[0].itemLine.expectUomName
            return Stream.ofNullable(it).allMatch { itemLineCompose -> itemLineCompose.itemLine.expectUomName == unitName }
        }
        return false
    }

    /**
     * Get Lp Count Suggestion.
     * Return <lpCount:Int, hasNotFullSubmit:Boolean, notFullSubmitQty:Double>
     */
    private fun getSuggestLpCount(itemLineCompose: ReceiveItemLineCompose): Tuple3<Int, Boolean, Double> {
        var suggestLpCount = 1
        var hasNotFullSubmit = false
        var notFullSubmitQty = -1.0

        if (dataState.configCompose.forbidLpSetupPalletization) return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)

        requireNotNull(itemLineCompose.qtyPerPallet)
        val qtyPerPallet = itemLineCompose.qtyPerPallet.toDoubleOrNull()
        requireNotNull(qtyPerPallet)
        requireNotNull(itemLineCompose.selectedUnit)
        val selectedBaseQty = itemLineCompose.selectedUnit.baseQty
        val needReceiveBaseQty = itemLineCompose.itemLine.remainingReceiveBaseQty()
        val lpCount = needReceiveBaseQty / (qtyPerPallet * selectedBaseQty)

        if (lpCount <= 1) {
            suggestLpCount = 1
            return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)
        }

        hasNotFullSubmit = lpCount != ceil(lpCount)
        if (hasNotFullSubmit) {
            val batchSubmitBaseQty = needReceiveBaseQty % (qtyPerPallet * selectedBaseQty)
            notFullSubmitQty = ceil(batchSubmitBaseQty / selectedBaseQty)
        }
        suggestLpCount = ceil(lpCount).toInt()

        return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)
    }

    fun goToSummary(itemLineId: String) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        runCatching { validateConfiguration(itemLineCompose) }.onFailure {
            showToast(it.message!!)
            return
        }
        activityViewModel.onSingleItemSummaryChanged(itemLineCompose)
    }

    fun completeStep() {
        activityViewModel.closeStep()
    }

    fun collapseAll() {
        setDataState {
            copy(receiptItemLineMap = receiptItemLineMap.mapValues { it.value.copy(isExpanded = false) })
        }
    }

    fun updateItemInfo(itemLineId: String) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        val needCollectItemSmallParcelPackaging = dataState.configCompose.needCollectItemSmallParcelPackaging
        fireEvent { SingleItemConfigUiEvent.UpdateItemInfo(itemLineCompose, needCollectItemSmallParcelPackaging) }
    }

    fun isShowReceiveAll(): Boolean {
        return false //        return activityViewModel.getTask().receiveType == ReceiveTypeEntry.SMALL_PARCEL_RECEIVING
    }

    fun isEnableReceiveAll(): Boolean {
        return Stream.ofNullable(dataState.receiptItemLineMap.values)
            .allMatch { receiptItemLine -> !receiptItemLine.requireCollectLotNumber && !receiptItemLine.requireCollectExpDate && !receiptItemLine.requireCollectMfgDate }
    }

    fun isAllGoodTypes(): Boolean {
        return true //        return Stream.ofNullable(dataState.receiptItemLineMap.values)
        //            .allMatch { receiptItemLine -> Constant.GOODS_TYPE_GOOD.equals(receiptItemLine.itemLine.goodsType, true) }
    }

    fun scanCarton(carton: String) {
        if (TextUtils.isEmpty(carton)) return
        launch {
            val result = requestAwait(repository.validateCartonByWcs(carton))
            if (result.isSuccess) {
                setDataState { copy(cartonNo = carton) }
            } else {
                setDataState { copy(cartonNo = null) }
            }
        }
    }

    fun submitCarton(itemLineId: String) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        runCatching { validateConfiguration(itemLineCompose) }.onFailure {
            showToast(it.message!!)
            return
        }
        val qtyPerPallet = itemLineCompose.qtyPerPallet!!.toDouble()
        val lpBatchCreateEntity = LpBatchCreateEntity().apply {
            this.locationId = selectedLocation.id
            this.lpCount = 1
            this.type = LpType.ILP
            this.cartonNo = dataState.cartonNo
        }
        val lpItemEntity = LpItemEntity().apply {
            this.itemLineId = itemLineCompose.itemLineId
            this.lotNo = itemLineCompose.lotNumber
            this.uomId = itemLineCompose.selectedUnit?.uomId
            this.mfgDate = itemLineCompose.manufactureDate
            this.expirationDate = itemLineCompose.expirationDate
            this.shelfLifeDays = (itemLineCompose.shelfLifeDays ?: "0").toInt()
        }
        val lpSetupForSingleItemEntity = LpSetupForSingleItemEntity().apply {
            this.batchCreateLpCmd = lpBatchCreateEntity
            this.items = listOf(lpItemEntity)
            this.fullPalletQty = qtyPerPallet
            this.receivedQty = qtyPerPallet
            this.stepId = activityViewModel.getStepId()?.toLong()
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.cartonNo = dataState.cartonNo
            this.lpGoodsType = itemLineCompose.selectedGoodsType
            this.receiveMethod = itemLineCompose.receiveMethod
            this.damagePhotoIds = if (itemLineCompose.needCollectPhoto) itemLineCompose.damagedPhotos else null
        }
        launch {
            val result = activityViewModel.submitCarton(lpSetupForSingleItemEntity)
            if (result.isFailure) {
                return@launch
            } // all done, refresh data
            changeExpandStatus(itemLineId, false)
            showSnack(SnackType.SuccessV1(), R.string.submit_success)
            delay(300)
            activityViewModel.refreshItemLines()
        }
    }
}

fun Date?.plusDays(days: Int): Date? {
    val localDateTime = this?.toInstant()
        ?.atZone(ZoneId.systemDefault())
        ?.toLocalDateTime()
        ?.plusDays(days.toLong())
    return Date.from(localDateTime?.atZone(ZoneId.systemDefault())?.toInstant())
}

