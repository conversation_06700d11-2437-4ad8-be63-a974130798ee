package com.unis.wms.receive

import com.unis.platform.receive.api.ReceiveTaskApi
import com.unis.platform.receive.model.ReceiveTaskUpdateEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy

class ReceiveTaskRepository : BaseRepository() {

    private val receiveTaskApi by apiServiceLazy<ReceiveTaskApi>()

    fun closeTask(taskId: String) = requestV2({receiveTaskApi.close(taskId)})

    fun forceCloseTask(taskId: String) = requestV2({receiveTaskApi.forceClose(taskId)})

    fun getTask(taskId: String) = requestV2({receiveTaskApi.getReceiveTask(taskId)})

    fun updateTask(receiveTaskUpdateEntity: ReceiveTaskUpdateEntity) = requestV2({receiveTaskApi.updateReceiveTask(receiveTaskUpdateEntity)})

    fun batchUpdateTask(receiveTaskUpdateEntities: List<ReceiveTaskUpdateEntity>) = requestV2({receiveTaskApi.batchUpdateReceiveTask(receiveTaskUpdateEntities)})
}