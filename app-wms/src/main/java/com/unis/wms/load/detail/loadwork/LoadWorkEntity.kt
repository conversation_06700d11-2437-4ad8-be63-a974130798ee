package com.unis.wms.load.detail.loadwork

import com.unis.platform.lp_v2.model.LpEntity
import com.unis.platform.lp_v2.model.LpStatus
import com.unis.platform.order.model.OrderStatusEntity
import com.unis.wms.photo.UploadPhotoBean

data class LoadOrderView(
    val orderId: String? = null,
    val orderState: OrderStatusEntity? = null,
    val orderSequence: String? = null,
    val lpIds: List<String>? = null,
    val proNo: String? = null,
    val lps: List<LpEntity>? = null,
    val loadId: String? = null,
    val customerId: String? = null,
    val retailerId: String? = null,
    val retailerName: String? = null,
    val orderPhotos: List<UploadPhotoBean>? = null
)

data class LoadPalletView(
    val loadId: String? = null,
    val lpId: String? = null,
    val lpState: LpStatus? = null,
    val orderId: String? = null,
    val orderState: OrderStatusEntity? = null,
    val isLoaded: Boolean? = false,
    val isUnLoadLP: Boolean = false,
    val lpPhotos: List<UploadPhotoBean>? = null,
    val length: Double? = null,
    val width: Double? = null,
    val height: Double? = null,
    val linearUnit: String? = null,
    val weight: Double? = null,
    val weightUOM: String? = null,
)