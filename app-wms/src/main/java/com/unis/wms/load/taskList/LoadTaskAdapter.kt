package com.unis.wms.load.taskList

import androidx.core.content.ContextCompat
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.unis.platform.common.model.task.TaskPriority
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.load.model.task.LoadTaskEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemLoadTaskListBinding

class LoadTaskAdapter : BaseBindingDifferQuickAdapter<LoadTaskEntity, ItemLoadTaskListBinding>() {
    override fun convert(helper: BaseBindingViewHolder<ItemLoadTaskListBinding>?, item: LoadTaskEntity?) {
        helper?.binding?.apply {
            // Handle asset display
            if (item?.status == TaskStatus.CLOSED || item?.status == TaskStatus.FORCE_CLOSED) {
                forkliftLl.setGone()
            } else {
                forkliftLl.setVisible()
                if (!item?.assetId.isNullOrEmpty()) {
                    assetIdTv.text = item?.assetId
                    assetIdTv.setTextColor(ContextCompat.getColor(mContext, R.color.white))
                    assetTypeTv.setVisible()
                    removeAssetIv.setVisible()
                } else {
                    assetIdTv.text = helper.getString(R.string.claim_forklift)
                    assetIdTv.setTextColor(ContextCompat.getColor(mContext, R.color.colorAccent))
                    assetTypeTv.setGone()
                    removeAssetIv.setGone()
                }
            }
            helper.addOnClickListener(R.id.asset_id_tv)
            helper.addOnClickListener(R.id.remove_asset_iv)

            tvTaskId.text = item?.id
            noteTv.text = item?.note?: ""
            if (item?.status == TaskStatus.CLOSED || item?.status == TaskStatus.CANCELLED) {
                stateCompleteTv.setVisible()
                tvDock.setGone()
                llTaskDetail.setGone()
                taskTitleLayoutLl.setBackgroundResource(R.drawable.rect_color_bg_medium_r4)
            } else {
                tvLoadNo.text = item?.loads?.joinToString(",", postfix = "") { it.loadNo }
                stateCompleteTv.setGone()
                tvDock.setVisible()
                llTaskDetail.setVisible()
                tvCreateBy.text = item?.createdBy
                tvDock.text = item?.dockName
                tvPriority.apply {
                    text = when (item?.priority) {
                        TaskPriority.HIGH -> helper.getString(R.string.label_priority_high)
                        TaskPriority.LOW -> helper.getString(R.string.label_priority_low)
                        TaskPriority.TOP -> helper.getString(R.string.label_priority_top)
                        else -> helper.getString(R.string.label_priority_middle)
                    }.uppercase()

                    when (item?.priority) {
                        TaskPriority.HIGH, TaskPriority.TOP -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.color_ef4134))
                            setBackgroundResource(R.drawable.rect_521b17_r2)
                        }
                        TaskPriority.LOW -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.color_f0cc4d))
                            setBackgroundResource(R.drawable.rect_897915_r2)
                        }
                        else -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.accent_green_v1))
                            setBackgroundResource(R.drawable.rect_214d36_r2)
                        }
                    }
                }

                taskTitleLayoutLl.setBackgroundResource(R.drawable.rect_color_bg_light_top_4)
            }
        }
    }

    override fun areItemsTheSame(oldItem: LoadTaskEntity, newItem: LoadTaskEntity): Boolean =
        oldItem.id == newItem.id && oldItem.status == newItem.status

    override fun areContentsTheSame(oldItem: LoadTaskEntity, newItem: LoadTaskEntity) =
        oldItem.id == newItem.id && oldItem.status == newItem.status
}