package com.unis.wms.load.detail.loadwork.orderwork

import com.customer.widget.common.addToNewList
import com.customer.widget.common.safeCount
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.StringUtil
import com.linc.platform.utils.ToastUtil.showToast
import com.unis.platform.common.model.dimension.DimensionEntity
import com.unis.platform.common.model.dimension.DimensionExtraEntity
import com.unis.platform.common.model.material.MaterialLineQueryEntity
import com.unis.platform.common.model.task.CheckType
import com.unis.platform.common.model.task.TaskChecklistEntity
import com.unis.platform.common.model.task.TaskChecklistQuery
import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.load.model.task.LoadingRequest
import com.unis.platform.load.model.task.UnloadAllRequest
import com.unis.platform.lp_v2.model.LpDimensionUpdateEntity
import com.unis.platform.lp_v2.model.LpStatus
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.requestAllAwait
import com.unis.reactivemvi.mvvm.kotlin.extensions.showLoading
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.wms.R
import com.unis.wms.load.LoadTaskViewModel
import com.unis.wms.load.detail.loadwork.LoadOrderView
import com.unis.wms.load.detail.loadwork.LoadPalletView
import com.unis.wms.photo.FileRepository
import com.unis.wms.photo.UploadPhotoBean
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.io.File

class LoadOrderWorkViewModel(
    val activityViewModel: LoadTaskViewModel, initialDataState: LoadOrderWorkState
) : SimpleReactiveViewModel<LoadOrderWorkState>(initialDataState) {

    private val repository = activityViewModel.repository
    private val stepId = activityViewModel.dataState.loadStep?.id!!
    private val taskId = activityViewModel.dataState.taskId!!
    private val fileRepository = FileRepository()
    private val stepStatus = activityViewModel.dataState.loadStep?.status

    init {
        getLoadOrderViewDetail()
        getPalletTypeIds()
        loadMaterial()
    }

    private fun getLoadOrderViewDetail() {
        val lpIds = dataState.loadOrderView.lpIds
        if (lpIds.isNullOrEmpty()) return
        setDataState { copy(orderId = dataState.loadOrderView.orderId) }
        launch {
            val (lpListResult, lpPhotosResult) = requestAllAwait(request1 = repository.lpSearch(lpIds),
                request2 = repository.searchCheckList(TaskChecklistQuery().apply {
                    checkType = CheckType.LOAD_PALLET_PHOTO
                    resourceIds = lpIds
                }))
            if (lpListResult.isFailure || lpPhotosResult.isFailure) return@launch
            val truckPhotos = requestAwait(repository.searchCheckList(TaskChecklistQuery().apply {
                resourceId = <EMAIL>
                checkType = CheckType.TRUCK_PHOTO
            }), showLoading = false).getOrNull()
            val truckPhotoIds = if (truckPhotos.isNullOrEmpty()) {
                emptyList()
            } else {
                truckPhotos[0].photoIds
            }
            val lps = lpListResult.getOrNull() ?: listOf()
            val lpPhotos = lpPhotosResult.getOrNull()
            val newLoadOrderView = LoadOrderView(
                orderId = dataState.loadOrderView.orderId,
                loadId = dataState.loadOrderView.loadId,
                lpIds = dataState.loadOrderView.lpIds,
                proNo = dataState.loadOrderView.proNo,
                customerId = dataState.loadOrderView.customerId,
                orderState = dataState.loadOrderView.orderState,
                lps = lps,
                retailerId = dataState.loadOrderView.retailerId,
                retailerName = dataState.loadOrderView.retailerName)
            val loadedPalletViews = lps.map { lp ->
                LoadPalletView(orderId = dataState.loadOrderView.orderId,
                    loadId = dataState.loadOrderView.loadId,
                    lpId = lp.id,
                    lpState = lp.status,
                    orderState = dataState.loadOrderView.orderState,
                    isLoaded = lp.status == LpStatus.LOADED || lp.status == LpStatus.SHIPPED,
                    isUnLoadLP = dataState.isUnLoadMode,
                    lpPhotos = lpPhotos?.find { lpPhoto -> lpPhoto.resourceId == lp.id }?.photoIds?.map {
                        UploadPhotoBean(
                            photoServerId = it,
                        )
                    },
                    weightUOM = lp.weightUnit,
                    weight = lp.weight,
                    length = lp.length,
                    width = lp.width,
                    height = lp.height,
                    linearUnit = lp.linearUnit)
            }.filter { lp -> lp.lpState == LpStatus.LOADED || lp.lpState == LpStatus.SHIPPED }
            val unloadedPalletIds = lps.filter { it.status != LpStatus.LOADED && it.status != LpStatus.SHIPPED }.map { it.id }
            setDataState {
                copy(loadOrderView = newLoadOrderView, palletLoadedViewList = loadedPalletViews.sortedByDescending {
                    it.isUnLoadLP
                }, truckPhotoIds = truckPhotoIds, unLoadedPalletIds = unloadedPalletIds)
            }
            val isLoadAll = lps.all { lp -> lp.status == LpStatus.LOADED || lp.status == LpStatus.SHIPPED }
            if (isLoadAll) {
                fireEvent { LoadOrderWorkEvent.ShowLoadOverDialog }
            }
        }
    }

    fun loadPallet(keyword: String) {
        launch {
            val lpId = getLPByCode(keyword) ?: return@launch

            if (!dataState.palletTypeIds.isNullOrEmpty()) {
                fireEvent {
                    LoadOrderWorkEvent.ShowAddPalletTypeDialog(
                        dataState.palletTypeIds ?: emptyList(), lpId, dataState.orderId ?: "", dataState.loadOrderView.customerId ?: "")
                }
                return@launch
            }
            requestAwait(repository.loadSlp(stepId, LoadingRequest().apply {
                this.loadId = dataState.loadOrderView.loadId
                this.orderId = dataState.loadOrderView.orderId
                this.lpId = lpId
            })).onSuccess {
                getLoadOrderViewDetail()
                showToast(R.string.text_load_success)
            }
        }
    }

    fun updateLpPalletAndLoadLp(slp: String, palletId: String) {
        launch {
            requestAwait(repository.loadSlp(stepId, LoadingRequest().apply {
                this.loadId = dataState.loadOrderView.loadId
                this.orderId = dataState.loadOrderView.orderId
                this.lpId = slp
            })).onSuccess {
                requestAwait(repository.updateLpPalletType(slp, palletId))
                getLoadOrderViewDetail()
                showToast(R.string.text_load_success)
            }
        }
    }

    fun unLoadPallet(keyword: String, palletView: LoadPalletView? = null) {
        launch {
            val lpId = getLPByCode(keyword) ?: return@launch
            val lpPalletView = palletView ?: (dataState.palletLoadedViewList?.find { it.lpId == lpId } ?: return@launch)
            
            request(repository.unLoadSlp(stepId, LoadingRequest().apply {
                this.loadId = lpPalletView.loadId
                this.orderId = lpPalletView.orderId
                this.lpId = lpId
            })) {
                getLoadOrderViewDetail()
                showToast(R.string.text_unload_success)
            }
        }
    }

    fun switchUnloadMode(isCheck: Boolean) {
        val loadModePalletViews = mutableListOf<LoadPalletView>()
        dataState.palletLoadedViewList?.forEach {
            loadModePalletViews.add(
                LoadPalletView(
                    orderId = it.orderId,
                    loadId = it.loadId,
                    lpId = it.lpId,
                    lpState = it.lpState,
                    orderState = it.orderState,
                    isLoaded = it.lpState == LpStatus.LOADED || it.lpState == LpStatus.SHIPPED,
                    isUnLoadLP = isCheck,
                    lpPhotos = it.lpPhotos,
                    weightUOM = it.weightUOM,
                    weight = it.weight,
                    width = it.width,
                    height = it.height,
                    length = it.length,
                    linearUnit = it.linearUnit
                ))
        }
        setDataState { copy(isUnLoadMode = isCheck, palletLoadedViewList = loadModePalletViews.sortedByDescending { it.isLoaded }) }
    }

    private suspend fun getLPByCode(code: String): String? {
        return if (StringUtil.isNumeric(code)) {
            val result = requestAwait(repository.searchLpsByCode(code))
            if (result.isFailure) return null
            val lpEntities = result.getOrNull()
            val lp = when (lpEntities.safeCount()) {
                0 -> {
                    showSnack(SnackType.ErrorV1(), R.string.msg_lp_not_found)
                    null
                }

                1 -> lpEntities!!.first()
                else -> {
                    val lpIds = lpEntities!!.joinToString(separator = ",") { it.id }
                    showSnack(SnackType.ErrorV1(), String.format(ResUtil.getString(R.string.msg_multiple_lp_found), lpIds))
                    null
                }
            }
            lp?.id
        } else {
            code
        }
    }

    fun uploadTruckPhoto(path: String) {
        launch {
            showLoading(true)
            awaitDataState()
            fileRepository.uploadFile(File(path), { fileIds ->
                val truckPhotoIds = dataState.truckPhotoIds.addToNewList(fileIds)
                request(repository.checkListUpdate(taskId, TaskChecklistEntity().apply {
                    checkType = CheckType.TRUCK_PHOTO
                    resourceId = taskId
                    photoIds = truckPhotoIds
                }), showLoading = false) {
                    setDataState { copy(truckPhotoIds = truckPhotoIds) }
                }
                showLoading(false)
            }, { error ->
                showToast(error?.error ?: "")
                showLoading(false)
            })

        }
    }

    private suspend fun uploadPalletPhotos(lpId: String?, photoIds: List<String>?) =
        requestAwait(repository.checkListUpdate(taskId, TaskChecklistEntity().apply {
            checkType = CheckType.LOAD_PALLET_PHOTO
            resourceId = lpId
            this.photoIds = photoIds ?: emptyList()
        })).isSuccess


    private fun getPalletTypeIds() {
        launch {
            val customerId = dataState.loadOrderView.customerId ?: return@launch
            val customer = requestAwait(repository.getCustomer(customerId)).getOrNull() ?: return@launch
            val palletTypeIds = getPalletTypeIdsByCustomer(customer, dataState.loadOrderView.retailerId)
            setDataState { copy(palletTypeIds = palletTypeIds) }
        }
    }

    private fun getPalletTypeIdsByCustomer(customer: CustomerEntity?, retailerId: String?): List<String>? {
        retailerId ?: return null
        val retailerPalletTypeSetting = customer?.outboundSetting?.retailerPalletTypeSetting
        if (retailerPalletTypeSetting?.suggestPalletTypeOnLoad != true) return null
        val retailerPalletType = retailerPalletTypeSetting.retailerPalletTypes?.find { it.retailerIds?.contains(retailerId) == true }
        return retailerPalletType?.itemIds
    }

    fun loadMaterial() {
        launch {
            requestAwait(repository.searchMaterialLine(MaterialLineQueryEntity().apply {
                this.taskId = <EMAIL>
                this.orderId = dataState.loadOrderView.orderId
            })).getOrNull().let {
                setDataState { copy(materialList = it) }
            }
        }
    }

    fun deleteMaterial(materialId: Long) {
        request(repository.deleteMaterialLine(materialId)) {
            setDataState {
                copy(materialList = dataState.materialList?.toMutableList()?.apply { removeIf { material -> material.id == materialId } })
            }
        }
    }

    fun collectPalletInfo(palletView: LoadPalletView) {
        activityViewModel.navigationLoadCollectPalletInfoEvent(palletView,LoadOrderWorkFragment.TAG)
    }

    fun updatePalletInfo(dimensionEntity: DimensionEntity, dimensionExtraEntity: DimensionExtraEntity?) {
        launch {
            val requestEntity = LpDimensionUpdateEntity().apply {
                this.lpId = dimensionExtraEntity?.titleValue
                this.length = dimensionEntity.length
                this.width = dimensionEntity.width
                this.height = dimensionEntity.height
                this.linearUnit = dimensionEntity.linearUnit
                this.weight = dimensionEntity.weight
                this.weightUnit = dimensionEntity.weightUOM
                this.applyToRestPalletsForSameItem = dimensionExtraEntity?.needApplyToRestPallets?:false
            }
            var uploadPhotoResult: Boolean? = null
            val result = requestAwait(repository.updateLpDimensions(requestEntity))
            if (dimensionExtraEntity?.needPalletPhotos == true && null != dimensionExtraEntity.photoIds) {
                uploadPhotoResult = uploadPalletPhotos(dimensionExtraEntity.titleValue, dimensionExtraEntity.photoIds)
            }
            var newPalletViewList: List<LoadPalletView>? = null
            dataState.palletLoadedViewList?.forEach {
                if (result.getOrNull()?.updatedLpIds?.contains(it.lpId) == true) {
                    if (newPalletViewList == null) {
                        newPalletViewList = dataState.palletLoadedViewList?.toMutableList()
                    }
                    var newPalletView: LoadPalletView? = null
                    if (result.isSuccess) {
                        newPalletView = it.copy(
                            length = dimensionEntity.length,
                            width = dimensionEntity.width,
                            height = dimensionEntity.height,
                            linearUnit = dimensionEntity.linearUnit.name,
                            weight = dimensionEntity.weight,
                            weightUOM = dimensionEntity.weightUOM?.name)
                    }
                    if (uploadPhotoResult == true && it.lpId == dimensionExtraEntity?.titleValue) {
                        val lpPhotos = dimensionExtraEntity?.photoIds?.map { photoId ->
                            UploadPhotoBean(
                                photoServerId = photoId,
                            )
                        }
                        newPalletView = newPalletView?.copy(lpPhotos = lpPhotos) ?: it.copy(lpPhotos = lpPhotos)
                    }
                    if (null == newPalletView) {
                        return@launch
                    }
                    newPalletViewList = newPalletViewList?.toMutableList()?.apply {
                        val index = indexOfFirst { lp -> lp.lpId == it.lpId }
                        if (index < 0) return@apply
                        set(index, newPalletView)
                    }
                }
            }
            if (newPalletViewList != null) {
                setDataState { copy(palletLoadedViewList = newPalletViewList) }
            }
        }
    }

    fun unloadAllPallets(confirmAction: () -> Flow<Boolean?>) {
        launch {
            val loadedPallets = dataState.palletLoadedViewList ?: return@launch
            if (loadedPallets.isEmpty()) {
                showToast(R.string.msg_no_unloadable_pallets)
                return@launch
            }
            
            val isConfirmed = confirmAction().firstOrNull() ?: false
            if (!isConfirmed) return@launch
            
            val unloadLpIds = loadedPallets.map { it.lpId?:"" }
            
            requestAwait(repository.unloadAllLps(stepId, UnloadAllRequest().apply {
                loadId = dataState.loadOrderView.loadId
                orderId = dataState.loadOrderView.orderId
                this.unloadLpIds = unloadLpIds
            })).onSuccess {
                getLoadOrderViewDetail()
                showToast(R.string.text_unload_success)
            }
        }
    }
}