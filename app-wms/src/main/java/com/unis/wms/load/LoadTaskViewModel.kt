package com.unis.wms.load

import com.unis.platform.common.model.dimension.DimensionEntity
import com.unis.platform.common.model.dimension.DimensionExtraEntity
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.common.model.uom.WeightUomEntity
import com.unis.platform.load.model.dockcheck.DockReleaseEntity
import com.unis.platform.load.model.task.LoadWorkFlowProcess
import com.unis.platform.load.model.task.LoadWorkFlowProcess.ALLOW_AUTO_CLOSE_TASK
import com.unis.platform.load.model.task.LoadWorkFlowProcess.LOAD_ITEMS
import com.unis.platform.location_v2.model.LinearUnit
import com.unis.platform.order.model.OrderStatusEntity
import com.unis.platform.outbound_qc_task.model.OutboundQCTaskQueryEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.common.taskworktime.TaskWorkTimeUiEvent
import com.unis.wms.load.detail.loadwork.LoadOrderView
import com.unis.wms.load.detail.loadwork.LoadPalletView
import com.unis.wms.outbound_qc_task.OutboundQcTaskRepository

class LoadTaskViewModel(
    val initialDataState: LoadTaskDataState = LoadTaskDataState()
) : SimpleReactiveViewModel<LoadTaskDataState>(initialDataState) {

    val repository by lazy { LoadTaskRepository() }
    val qcTaskRepository by lazy { OutboundQcTaskRepository() }

    init {
        loadTask(initialDataState.taskId!!)
    }

    fun loadTask(taskId: String) {
        launch {
            val loadTask = requestAwait(repository.getLoadTask(taskId)).getOrNull() ?: return@launch
            val loadStep = loadTask.taskSteps?.find { step -> step.stepType == TaskStepType.LOADING } ?: return@launch
            setDataStateAwait { copy(loadTask = loadTask, loadStep = loadStep, sealPhotos = loadTask.sealPhotos, sealNo = loadTask.sealNo) }
            if (loadStep.status == TaskStepStatus.IN_PROGRESS) {
                getCurrentProcess(taskId)
            } else {
                fireEvent { LoadTaskUiEvent.StepStatusEvent(loadStep) }
            }
            getCustomer(loadTask.customerId)
        }
    }

    fun updateSealPhotos(sealPhotos: List<String>?) {
        setDataState { copy(sealPhotos = sealPhotos) }
    }

    fun updateSealNo(sealNo: String?) {
        setDataState { copy(sealNo = sealNo) }
    }

    private fun getCurrentProcess(taskId: String) {
        launch {
            val it = requestAwait(repository.getProcessNode(taskId)).getOrNull()
            val node = it?.currentNodeResults?.firstOrNull()?.nodeName
            if (node.isNullOrEmpty()) {
                navigationProcessEvent(LOAD_ITEMS)
                return@launch
            } else {
                val allowAutoCloseTask = it.currentNodeResults?.firstOrNull()?.variables?.get(ALLOW_AUTO_CLOSE_TASK) as? Boolean ?: false
                setDataStateAwait { copy(isAutoCompleteTask = allowAutoCloseTask) }
            }
            navigationProcessEvent(node)
        }
    }

    fun closeStepAndTask() {
        request(repository.closeStepWithTask(dataState.loadStep?.id ?: "")) {
            showReleaseDickDialog()
        }
    }

    private fun checkUccLabel() {
        request(repository.checkUccPrintedLabel(taskId = dataState.taskId!!), error = {
            handelErrorNode(errorNode = it.error)
        }) {
            val node = it?.currentNodeResults?.firstOrNull()?.nodeName ?: ""
            navigationProcessEvent(node)
        }
    }

    private fun checkPalletConfig() {
        request(repository.checkPalletConfig(taskId = dataState.taskId!!), error = {
            handelErrorNode(errorNode = it.error)
        }) {
            val node = it?.currentNodeResults?.firstOrNull()?.nodeName ?: ""
            navigationProcessEvent(node)
        }
    }

    fun navigationLoadByOrderEvent(loadOrderView: LoadOrderView) {
        launch {
            if (loadOrderView.orderState !in
                listOf(
                    OrderStatusEntity.READY_TO_SHIP, OrderStatusEntity.SHIPPED,
                    OrderStatusEntity.PARTIAL_SHIPPED, OrderStatusEntity.LOADED, OrderStatusEntity.SHORT_SHIPPED
                )
                && dataState.customer?.outboundSetting?.requireQCTask == true
                && dataState.customer?.outboundSetting?.notAllowLoadWithoutQC == true
            ) {
                val qcTasks = requestAwait(qcTaskRepository.searchByPaging(OutboundQCTaskQueryEntity().apply {
                    orderId = loadOrderView.orderId
                })).getOrNull()
                if (!qcTasks?.list.isNullOrEmpty() && qcTasks?.list?.firstOrNull()?.isDone() != true) {
                    showSnack(SnackType.ErrorV1(), R.string.msg_order_qc_task_not_complete)
                } else {
                    fireEvent { LoadTaskUiEvent.LoadWorkByOrderEvent(loadOrderView) }
                }
            } else {
                fireEvent { LoadTaskUiEvent.LoadWorkByOrderEvent(loadOrderView) }
            }
        }
    }

    fun navigationLoadDetailEvent(loadId: String, proNo: String) {
        fireEvent { LoadTaskUiEvent.LoadDetailEvent(loadId, proNo) }
    }

    fun navigationLoadSignatureEvent(loadId: String, isMaster: Boolean) {
        fireEvent { LoadTaskUiEvent.LoadSignatureEvent(loadId, isMaster) }
    }

    fun navigationBolPrintEvent(contentId: String, isLoadBol: Boolean = false) {
        fireEvent { LoadTaskUiEvent.BolPrintEvent(contentId, isLoadBol) }
    }

    fun finishFragment() {
        fireEvent { LoadTaskUiEvent.PopStepFragmentEvent }
    }

    fun navigationProcessEvent(process: LoadStepEvent?) {
        if (process == null) return
        when (process) {
            LoadStepEvent.UccCheckEvent -> {
                checkUccLabel()
            }

            LoadStepEvent.CheckPalletConfig -> {
                checkPalletConfig()
            }

            LoadStepEvent.TaskCompleteEvent -> {
                closeTask()
            }

            else -> {
                fireEvent { LoadTaskUiEvent.PopAllStepFragmentEvent }
                fireEvent { LoadTaskUiEvent.ProcessEvent(process) }
                fireTaskWorkTimeEvent(process)
            }
        }
    }

    fun navigationProcessEvent(process: String?) {
        if (process.isNullOrEmpty()) {
            return
        }
        val loadStep = getProcessEvent(process) ?: return
        navigationProcessEvent(loadStep)
    }

    fun getProcessEvent(process: String): LoadStepEvent? {
        return when (process) {
            LoadWorkFlowProcess.DOCK_CHECK_IN -> {
                LoadStepEvent.DockCheckInEvent
            }

            LoadWorkFlowProcess.EQUIPMENT_INSPECTION -> {
                LoadStepEvent.EquipmentInspectEvent
            }

            LoadWorkFlowProcess.CHECK_UCC_PRINT -> {
                LoadStepEvent.UccCheckEvent
            }

            LoadWorkFlowProcess.LOAD_ITEMS -> {
                LoadStepEvent.LoadListEvent
            }

            LoadWorkFlowProcess.SIGN_BOL -> {
                LoadStepEvent.SignBolEvent
            }

            LoadWorkFlowProcess.COLLECT_TASK_INFO -> {
                LoadStepEvent.CollectInfoEvent
            }

            LoadWorkFlowProcess.TASK_COMPLETE -> {
                LoadStepEvent.TaskCompleteEvent
            }

            LoadWorkFlowProcess.TAKE_OVER -> {
                LoadStepEvent.TakeOverEvent
            }

            LoadWorkFlowProcess.CHECK_PALLET_CONFIG -> {
                LoadStepEvent.CheckPalletConfig
            }

            else -> {
                handelErrorNode(process)
                null
            }
        }
    }

    private fun fireTaskWorkTimeEvent(process: LoadStepEvent) {
        when (process) {
            LoadStepEvent.DockCheckInEvent, LoadStepEvent.EquipmentInspectEvent,
            LoadStepEvent.LoadListEvent, LoadStepEvent.CollectInfoEvent,
            LoadStepEvent.SignBolEvent -> {
                val taskId = dataState.taskId
                val stepId = dataState.loadStep?.id
                taskId ?: return
                stepId ?: return
                fireEvent { TaskWorkTimeUiEvent.StartWorkTime(taskId, stepId) }
            }

            else -> {}
        }
    }

    private fun handelErrorNode(errorNode: String) {
        fireEvent { LoadTaskUiEvent.PopAllStepFragmentEvent }
        fireEvent { LoadTaskUiEvent.ProcessEvent(LoadStepEvent.LoadError(errorNode)) }
    }

    fun closeTask() {
        fireEvent { LoadTaskUiEvent.CloseTaskDialog }
    }

    fun forceCloseTask(reason: String) {
        launch {
            val result = requestAwait(repository.forceCloseTask(dataState.taskId!!))
            if (result.isSuccess) {
                showReleaseDickDialog()
            }
        }
    }

    fun navigationLoadCollectPalletInfoEvent(palletView: LoadPalletView, fragmentTag: String) {
        val dimensionEntity = DimensionEntity(
            length = palletView.length ?: 40.0,
            width = palletView.width ?: 48.0,
            height = palletView.height,
            linearUnit = if (null == palletView.linearUnit) LinearUnit.INCH else LinearUnit.toLinearUnit(palletView.linearUnit)
                ?: LinearUnit.INCH,
            weight = palletView.weight,
            weightUOM = if (null == palletView.weightUOM) WeightUomEntity.POUND else WeightUomEntity.toUom(palletView.weightUOM)
        )
        val dimensionExtraEntity = DimensionExtraEntity(
            titleLabel = getString(R.string.title_pallet_no),
            titleValue = palletView.lpId,
            photoIds = palletView.lpPhotos?.map { it.photoServerId!! }?.toList(),
            needPalletPhotos = true,
            taskFrom = TaskType.LOAD,
            deviationWarningWeight = dataState.customer?.outboundSetting?.deviationWarningWeight,
            needShowApplyToRestPalletsSwitch = true
        )
        fireEvent { LoadTaskUiEvent.CollectPalletInfo(dimensionEntity, dimensionExtraEntity, fragmentTag) }
    }


    private fun getCustomer(customerId: String?) {
        customerId?.let {
            request(repository.getCustomer(customerId), success = {
                setDataState { copy(customer = it) }
            })
        }
    }

    fun showReleaseDickDialog(isReleaseByMenu: Boolean = false) {
        fireEvent { LoadTaskUiEvent.ShowReleaseDockCheckDialog(isReleaseByMenu) }
    }

    fun releaseDock(isReleaseByMenu: Boolean) {
        launch {
            if (isReleaseByMenu) {
                val loadStep = requestAwait(repository.getLoadTask(dataState.taskId!!)).getOrNull()?.getTaskStep(TaskStepType.LOADING)
                if (loadStep?.isDone() != true) {
                    showToast(R.string.load_task_not_succest_unable_release_dock)
                    return@launch
                }
            }
            val dockId = dataState.loadTask?.dockId
            val entryId = dataState.loadTask?.entryId
            val result = requestAwait(repository.dockRelease(DockReleaseEntity().apply {
                this.dockId = dockId
                this.entryId = entryId
            }))
            if (result.isSuccess) {
                fireEvent { LoadTaskUiEvent.FinishActivity }
            }
        }
    }
}