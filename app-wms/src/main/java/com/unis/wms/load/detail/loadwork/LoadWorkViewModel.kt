package com.unis.wms.load.detail.loadwork

import com.customer.widget.common.addToNewList
import com.customer.widget.common.safeCount
import com.linc.platform.utils.ConfigurationMapUtil
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.StringUtil
import com.linc.platform.utils.ToastUtil.showToast
import com.unis.platform.common.model.dimension.DimensionEntity
import com.unis.platform.common.model.dimension.DimensionExtraEntity
import com.unis.platform.common.model.task.CheckType
import com.unis.platform.common.model.task.TaskChecklistEntity
import com.unis.platform.common.model.task.TaskChecklistQuery
import com.unis.platform.configuration_map.model.ConfigurationMapSearch
import com.unis.platform.load.model.load.LoadOrderEntity
import com.unis.platform.load.model.load.ProNoAddEntity
import com.unis.platform.load.model.task.LoadingRequest
import com.unis.platform.lp_v2.model.LpDimensionUpdateEntity
import com.unis.platform.lp_v2.model.LpStatus
import com.unis.platform.order.model.OrderUpdateEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.requestAllAwait
import com.unis.reactivemvi.mvvm.kotlin.extensions.showLoading
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.speakAndToast
import com.unis.wms.R
import com.unis.wms.load.LoadTaskViewModel
import com.unis.wms.photo.FileRepository
import com.unis.wms.photo.UploadPhotoBean
import com.unis.wms.photo.UploadStatus
import com.unis.wms.photo.filteredDeleteItems
import com.unis.wms.photo.getAvailablePhotoIds
import com.unis.wms.photo.mergeToNewList
import java.io.File

class LoadWorkViewModel(
    val activityViewModel: LoadTaskViewModel, val initialDataState: LoadWorkDataState
) : SimpleReactiveViewModel<LoadWorkDataState>(initialDataState) {

    private val repository get() = activityViewModel.repository
    private val taskId get() = activityViewModel.dataState.taskId ?: ""
    private val stepId get() = activityViewModel.dataState.loadStep?.id ?: ""
    private val fileRepository = FileRepository()

    fun getLoadOrders() {
        request(repository.getLoadOrderView(dataState.loadId)) {
            if (it == null) return@request
            getOrderListView(it)
        }
    }

    private fun getOrderListView(loadOrders: LoadOrderEntity) {
        launch {
            val orders = loadOrders.orders
            val orderPhotos = requestAwait(repository.searchCheckList(TaskChecklistQuery().apply {
                this.taskId = <EMAIL>
                this.checkType = CheckType.ORDER_PHOTO
                resourceIds = orders?.map { it.id }
            })).getOrNull()
            val locationAndLpMap = loadOrders.orderLocationLPs?.groupBy { it.orderId }
            val orderViewList = mutableListOf<LoadOrderView>()
            orders?.forEach { order ->
                val orderPhotoIds = orderPhotos?.find { orderPhoto -> orderPhoto.resourceId == order.id }?.photoIds
                val orderView = LoadOrderView(
                    orderId = order.id,
                    orderState = order.status,
                    lpIds = locationAndLpMap?.get(order.id)?.get(0)?.lpIds,
                    proNo = order.proNo,
                    loadId = dataState.loadId,
                    customerId = order.customerId,
                    orderPhotos = orderPhotoIds?.map {
                        UploadPhotoBean().apply {
                            this.photoServerId = it
                        }
                    },
                    retailerId = order.retailerId,
                    retailerName = order.retailerName)
                orderViewList.add(orderView)
            }

            val truckPhotos = requestAwait(repository.searchCheckList(TaskChecklistQuery().apply {
                resourceId = taskId
                checkType = CheckType.TRUCK_PHOTO
            }), showLoading = false).getOrNull()
            val truckPhotoIds = if (truckPhotos.isNullOrEmpty()) {
                emptyList()
            } else {
                truckPhotos[0].photoIds
            }
            setDataState { copy(loadOrder = loadOrders, orderViewList = orderViewList, truckPhotoIds = truckPhotoIds) }
            getLoadPalletListView(loadOrders)
        }
    }

    private fun getLoadPalletListView(loadOrder: LoadOrderEntity) {
        val lpIds = loadOrder.orderLocationLPs?.mapNotNull { it.lpIds.takeIf { likes -> likes?.isNotEmpty() == true } }?.flatten()
        if (lpIds.isNullOrEmpty()) return

        launch {
            val (lpListResult, lpPhotosResult) = requestAllAwait(request1 = repository.lpSearch(lpIds),
                request2 = repository.searchCheckList(TaskChecklistQuery().apply {
                    checkType = CheckType.LOAD_PALLET_PHOTO
                    resourceIds = lpIds
                }))
            if (lpListResult.isFailure || lpPhotosResult.isFailure) return@launch
            val lps = lpListResult.getOrNull()
            val lpPhotos = lpPhotosResult.getOrNull()
            val loadPalletViews = lps?.map { lp ->
                val lpPhotoIds = lpPhotos?.find { lpPhoto -> lpPhoto.resourceId == lp.id }?.photoIds
                LoadPalletView(
                    orderId = loadOrder.orderLocationLPs?.find { v -> v.lpIds?.any { lpId -> lpId == lp.id } ?: false }?.orderId,
                    loadId = dataState.loadId,
                    lpId = lp.id,
                    lpState = lp.status,
                    orderState = loadOrder.orders?.find { order -> order.id == loadOrder.orderLocationLPs?.find { v -> v.lpIds?.any { lpId -> lpId == lp.id } ?: false }?.orderId }?.status,
                    isLoaded = lp.status == LpStatus.LOADED || lp.status == LpStatus.SHIPPED,
                    isUnLoadLP = dataState.isUnLoadMode,
                    lpPhotos = lpPhotoIds?.map { UploadPhotoBean().apply { this.photoServerId = it } },
                    weightUOM = lp.weightUnit,
                    weight = lp.weight,
                    width = lp.width,
                    height = lp.height,
                    length = lp.length,
                    linearUnit = lp.linearUnit)
            }
            setDataState { copy(palletViewList = loadPalletViews?.sortedByDescending { item -> item.isLoaded }) }
        }
    }

    fun updateOrderProNo(orderView: LoadOrderView, proNo: String?) {
        launch {
            var reqProNo = if (proNo.isNullOrEmpty()) {
                if (dataState.loadProNo.isNullOrEmpty()) {
                    dataState.loadId.filter { it.isDigit() }
                } else {
                    dataState.loadProNo
                }
            } else {
                proNo
            }
            val isNeedGenerateUniqueProNOToOrder = requestAwait(repository.searchConfigurationMap(ConfigurationMapSearch().apply {
                this.customerId = orderView.customerId
                this.tableName = ConfigurationMapUtil.TABLE_GENERATE_UNIQUE_PRO_NO_TO_ORDER
            })).getOrNull()?.let {
                it.any {configMap->
                    configMap.valueMapping?.get(ConfigurationMapUtil.KEY_GENERATE_UNIQUE_PRO_NO_RETAILER) == orderView.retailerName
                } && proNo.isNullOrEmpty()
            }?: false

            if (isNeedGenerateUniqueProNOToOrder) {
                val index = dataState.orderViewList?.sortedBy { orderView->
                    orderView.orderId?.filter { it.isDigit() }
                }?.indexOf(orderView)?:0
                reqProNo = if (index > 0) "$reqProNo-$index" else reqProNo
            }
            request(repository.orderUpdate(orderView.orderId!!, OrderUpdateEntity().apply {
                this.proNo = reqProNo
                this.customerId = orderView.customerId
            })) {
                val orderViewList = dataState.orderViewList?.toMutableList()?.map { order ->
                    if (order.orderId == orderView.orderId) {
                        order.copy(proNo = reqProNo)
                    } else {
                        order
                    }
                }
                if (proNo.isNullOrEmpty()) {
                    setDataState { copy(orderViewList = orderViewList) }
                }
                showToast(R.string.text_pro_no_submit_success)
            }
        }

    }

    fun loadPallet(keyword: String) {
        launch {
            val lpId = getLPByCode(keyword) ?: return@launch
            if (!invalidateLPId(lpId)) {
                speakAndToast(ResUtil.format(R.string.msg_order_not_found, lpId))
                return@launch
            }
            val palletView = dataState.palletViewList?.find { it.lpId == lpId }
            request(repository.loadSlp(stepId, LoadingRequest().apply {
                this.loadId = palletView?.loadId
                this.orderId = palletView?.orderId
                this.lpId = palletView?.lpId
            })) {
                dataState.loadOrder?.let { order -> getLoadPalletListView(order) }
                showToast(R.string.text_load_success)
            }
        }
    }

    fun unLoadPallet(keyword: String, palletView: LoadPalletView? = null) {
        launch {
            // 检查step是否已关闭
            val lpId = getLPByCode(keyword) ?: return@launch
            if (!invalidateLPId(lpId)) {
                speakAndToast(ResUtil.format(R.string.msg_order_not_found, lpId))
                return@launch
            }
            val lpPalletView = palletView?: dataState.palletViewList?.find { it.lpId == lpId } ?: return@launch
            request(repository.unLoadSlp(stepId, LoadingRequest().apply {
                this.loadId = lpPalletView.loadId
                this.orderId = lpPalletView.orderId
                this.lpId = lpPalletView.lpId
            })) {
                dataState.loadOrder?.let { order -> getLoadPalletListView(order) }
                showToast(R.string.text_unload_success)
            }
        }
    }

    fun loadAllPallets(palletViews: List<LoadPalletView>) {
        val loadingRequests = palletViews.map {
            LoadingRequest().apply {
                this.loadId = dataState.loadId
                this.orderId = it.orderId
                this.lpId = it.lpId
            }
        }
        request(repository.batchLoadSlp(stepId, loadingRequests)) {
            dataState.loadOrder?.let { order -> getLoadPalletListView(order) }
            showToast(R.string.text_load_success)
        }
    }

    private fun invalidateLPId(lpId: String): Boolean {
        val loadLpIds = dataState.palletViewList?.map { it.lpId }
        return loadLpIds?.contains(lpId) == true || LPUtil.isSLP(lpId)
    }

    fun switchUnloadMode(isCheck: Boolean) {
        val loadModePalletViews = mutableListOf<LoadPalletView>()
        dataState.palletViewList?.forEach {
            loadModePalletViews.add(
                LoadPalletView(
                    orderId = it.orderId,
                    loadId = it.loadId,
                    lpId = it.lpId,
                    lpState = it.lpState,
                    orderState = it.orderState,
                    isLoaded = it.lpState == LpStatus.LOADED || it.lpState == LpStatus.SHIPPED,
                    isUnLoadLP = isCheck,
                    lpPhotos = it.lpPhotos,
                    weightUOM = it.weightUOM,
                    weight = it.weight,
                    width = it.width,
                    height = it.height,
                    length = it.length,
                    linearUnit = it.linearUnit))
        }
        setDataState { copy(isUnLoadMode = isCheck, palletViewList = loadModePalletViews.sortedByDescending { it.isLoaded }) }
    }

    fun addOrderPhotos(loadOrderView: LoadOrderView, uploadPhotos: List<UploadPhotoBean>) {
        launch {
            awaitDataState()
            dataState.orderViewList?.find { it.orderId == loadOrderView.orderId }?.let { loadOrderView ->
                uploadPhotos.filter { uploadPhotoBean ->
                    val photoBeanExit = loadOrderView.orderPhotos?.any { uploadPhotoBean.filePath == it.filePath } ?: false
                    uploadPhotoBean.uploadStatus != UploadStatus.UPLOADED || (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED && photoBeanExit)
                }.apply {
                    val newUploadPhotoIds = loadOrderView.orderPhotos.getAvailablePhotoIds()
                        .addToNewList(filter { it.uploadStatus == UploadStatus.UPLOADED }.mapNotNull { it.photoServerId })?.distinct() //do upload count sheet
                    if (!newUploadPhotoIds.isNullOrEmpty() && !uploadOrderPhotos(loadOrderView.orderId, newUploadPhotoIds)) {
                        forEach { it.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL }
                    }
                }
            }?.let { newUploadPhotoBeanList ->
                val newOrderViewList = dataState.orderViewList?.toMutableList()?.apply {
                    indexOfFirst { it.orderId == loadOrderView.orderId }.let {
                        if (it < 0) return@apply
                        val oldOrderViewWrapper = this[it]
                        val newUploadPhotoBean = oldOrderViewWrapper.orderPhotos.mergeToNewList(newUploadPhotoBeanList)
                        set(it, oldOrderViewWrapper.copy(orderPhotos = newUploadPhotoBean))
                    }
                }
                setDataState { copy(orderViewList = newOrderViewList) }
            }
        }
    }

    fun deleteOrderPhoto(loadOrderView: LoadOrderView, delPhotoList: List<UploadPhotoBean>, isRemoveAll: Boolean) {
        launch {
            awaitDataState()
            dataState.orderViewList?.find { it.orderId == loadOrderView.orderId }?.let {
                val lastOrderViewPhotos = it.orderPhotos?.filteredDeleteItems(delPhotoList, isRemoveAll)
                if (uploadOrderPhotos(loadOrderView.orderId, lastOrderViewPhotos.getAvailablePhotoIds())) {
                    return@let it.copy(orderPhotos = lastOrderViewPhotos)
                }
                it
            }?.let {
                val newOrderViewList = dataState.orderViewList?.toMutableList()?.apply {
                    val index = indexOfFirst { it.orderId == loadOrderView.orderId }
                    if (index < 0) return@apply
                    set(index, it)
                }
                setDataState { copy(orderViewList = newOrderViewList) }
            }
        }
    }

    private suspend fun uploadOrderPhotos(orderId: String?, photoIds: List<String>?): Boolean =
        requestAwait(repository.checkListUpdate(taskId, TaskChecklistEntity().apply {
            checkType = CheckType.ORDER_PHOTO
            resourceId = orderId
            this.photoIds = photoIds ?: emptyList()
        })).isSuccess

    fun uploadTruckPhoto(path: String) {
        launch {
            showLoading(true)
            awaitDataState()
            fileRepository.uploadFile(File(path), { fileIds ->
                val truckPhotoIds = dataState.truckPhotoIds.addToNewList(fileIds)
                request(repository.checkListUpdate(taskId, TaskChecklistEntity().apply {
                    checkType = CheckType.TRUCK_PHOTO
                    resourceId = taskId
                    photoIds = truckPhotoIds
                }), showLoading = false) {
                    showLoading(false)
                    setDataState { copy(truckPhotoIds = truckPhotoIds) }
                }
            }, { error ->
                showLoading(false)
                showToast(error?.error ?: "")
            })

        }
    }

    fun completeLoad() {
        request(repository.completeLoad(taskId, dataState.loadId, ProNoAddEntity().apply {
            this.proNo = dataState.loadProNo
        })) {
            val node = it?.currentNodeResults?.firstOrNull()?.nodeName ?: ""
            activityViewModel.navigationProcessEvent(node)
        }
    }

    private suspend fun getLPByCode(code: String): String? {
        return if (StringUtil.isNumeric(code)) {
            val result = requestAwait(repository.searchLpsByCode(code))
            if (result.isFailure) return null
            val lpEntities = result.getOrNull()
            val lp = when (lpEntities.safeCount()) {
                0 -> {
                    showSnack(SnackType.ErrorV1(), R.string.msg_lp_not_found)
                    null
                }

                1 -> lpEntities!!.first()
                else -> {
                    val lpIds = lpEntities!!.joinToString(separator = ",") { it.id }
                    showSnack(SnackType.ErrorV1(), String.format(ResUtil.getString(R.string.msg_multiple_lp_found), lpIds))
                    null
                }
            }
            lp?.id
        } else {
            code
        }
    }


    private suspend fun uploadPalletPhotos(lpId: String?, photoIds: List<String>?) =
        requestAwait(repository.checkListUpdate(taskId, TaskChecklistEntity().apply {
            checkType = CheckType.LOAD_PALLET_PHOTO
            resourceId = lpId
            this.photoIds = photoIds ?: emptyList()
        })).isSuccess

    fun collectPalletInfo(palletView: LoadPalletView) {
        activityViewModel.navigationLoadCollectPalletInfoEvent(palletView, LoadWorkFragment.TAG)
    }

    fun updatePalletInfo(dimensionEntity: DimensionEntity, dimensionExtraEntity: DimensionExtraEntity?) {
        launch {
            val requestEntity = LpDimensionUpdateEntity().apply {
                this.lpId = dimensionExtraEntity?.titleValue
                this.length = dimensionEntity.length
                this.width = dimensionEntity.width
                this.height = dimensionEntity.height
                this.linearUnit = dimensionEntity.linearUnit
                this.weight = dimensionEntity.weight
                this.weightUnit = dimensionEntity.weightUOM
                this.applyToRestPalletsForSameItem = dimensionExtraEntity?.needApplyToRestPallets ?: false
            }
            var uploadPhotoResult: Boolean? = null
            val result = requestAwait(repository.updateLpDimensions(requestEntity))
            if (dimensionExtraEntity?.needPalletPhotos == true && null != dimensionExtraEntity.photoIds) {
                uploadPhotoResult = uploadPalletPhotos(dimensionExtraEntity.titleValue, dimensionExtraEntity.photoIds)
            }
            var newPalletViewList: List<LoadPalletView>? = null
            dataState.palletViewList?.forEach {
                if (result.getOrNull()?.updatedLpIds?.contains(it.lpId) == true) {
                    if (newPalletViewList == null) {
                        newPalletViewList = dataState.palletViewList?.toMutableList()
                    }
                    var newPalletView: LoadPalletView? = null
                    if (result.isSuccess) {
                        newPalletView = it.copy(
                            length = dimensionEntity.length,
                            width = dimensionEntity.width,
                            height = dimensionEntity.height,
                            linearUnit = dimensionEntity.linearUnit.name,
                            weight = dimensionEntity.weight,
                            weightUOM = dimensionEntity.weightUOM?.name)
                    }
                    if (uploadPhotoResult == true && it.lpId == dimensionExtraEntity?.titleValue) {
                        val lpPhotos = dimensionExtraEntity?.photoIds?.map { photoId ->
                            UploadPhotoBean(
                                photoServerId = photoId,
                            )
                        }
                        newPalletView = newPalletView?.copy(lpPhotos = lpPhotos) ?: it.copy(lpPhotos = lpPhotos)
                    }
                    if (null == newPalletView) {
                        return@launch
                    }
                    newPalletViewList = newPalletViewList?.toMutableList()?.apply {
                        val index = indexOfFirst { lp -> lp.lpId == it.lpId }
                        if (index < 0) return@apply
                        set(index, newPalletView)
                    }
                }
            }
            if (newPalletViewList != null) {
                setDataState { copy(palletViewList = newPalletViewList) }
            }
        }
    }

}