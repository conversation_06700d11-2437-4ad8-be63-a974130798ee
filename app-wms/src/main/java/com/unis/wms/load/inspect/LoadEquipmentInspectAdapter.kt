package com.unis.wms.load.inspect

import android.text.Editable
import android.text.TextWatcher
import androidx.core.content.ContextCompat
import com.customer.widget.extensions.setVisibleOrGone
import com.unis.platform.load.model.inspect.InspectionCondition
import com.unis.platform.load.model.inspect.InspectionDirection
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemLoadInspectionBinding
import com.unis.wms.photo.UploadPhotoBean
import com.unis.wms.photo.UploadPhotoCallBack
import com.unis.wms.photo.initLoadParamInfo

class LoadEquipmentInspectAdapter(
    private val addOrUpdatePhotos: (Int, List<UploadPhotoBean>, Boolean) -> Unit, private val updateComment: (Int, String) -> Unit
) : BaseBindingDifferQuickAdapter<LoadInspectionEntity, ItemLoadInspectionBinding>() {
    override fun convert(helper: BaseBindingViewHolder<ItemLoadInspectionBinding>?, item: LoadInspectionEntity) {
        helper?.binding?.apply {
            val textColor = ContextCompat.getColor(mContext, R.color.white)
            val goodSelectBg = R.drawable.rect_214d36_border_color_bg_light_left_8
            val badNormalBg = R.drawable.rect_bg_color_light_border_bg_color_dark_right_8
            val goodSelectTextColor = ContextCompat.getColor(mContext, com.loper7.date_time_picker.R.color.dt_color_56c288)
            val goodNormalBg = R.drawable.rect_bg_color_dark_border_bg_color_light_left_8
            val badSelectBg = R.drawable.rect_4f0000_border_5e5f5f_right_8
            val badSelectTextColor = ContextCompat.getColor(mContext, com.loper7.date_time_picker.R.color.dt_color_ec4a44)
            tvTitle.text = item.direction?.getDirectionName()
            etComment.setText(item.comment)
            etComment.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                }

                override fun afterTextChanged(s: Editable) {
                    updateComment.invoke(helper.layoutPosition, s.toString())
                }
            })
            when (item.condition) {
                null -> {
                    tvGood.setBackgroundResource(goodNormalBg)
                    tvGood.setTextColor(textColor)
                    tvBad.setBackgroundResource(badNormalBg)
                    tvBad.setTextColor(textColor)
                    clComment.setVisibleOrGone(false)
                }

                InspectionCondition.GOOD -> {
                    tvGood.setBackgroundResource(goodSelectBg)
                    tvGood.setTextColor(goodSelectTextColor)
                    tvBad.setBackgroundResource(badNormalBg)
                    tvBad.setTextColor(textColor)
                    clComment.setVisibleOrGone(false)
                }

                else -> {
                    tvGood.setBackgroundResource(goodNormalBg)
                    tvGood.setTextColor(textColor)
                    tvBad.setBackgroundResource(badSelectBg)
                    tvBad.setTextColor(badSelectTextColor)
                    clComment.setVisibleOrGone(true)
                }
            }
            uploadPhoto.apply {
                initLoadParamInfo()
                setVideoParam("1", null, false)
                initPhotoListByAdapter(item.photos)
                setUploadPhotoCallBack(object : UploadPhotoCallBack {

                    override fun addPhotos(photoId: List<String>) {
                        if (photoId.isEmpty()) return
                        val photoBeans = photoId.map { id -> UploadPhotoBean(photoServerId = id) }.toList()
                        addOrUpdatePhotos.invoke(helper.layoutPosition, photoBeans, true)
                    }

                    override fun deletePhotos(photos: List<String>?, isRemoveAll: Boolean) {
                        photos ?: return
                        val photoBeans = photos.map { id -> UploadPhotoBean(photoServerId = id) }.toList()
                        addOrUpdatePhotos.invoke(helper.layoutPosition, photoBeans, false)
                    }
                })
            }
            helper.addOnClickListener(R.id.tv_good).addOnClickListener(R.id.tv_bad)
        }

    }

    override fun areItemsTheSame(oldItem: LoadInspectionEntity, newItem: LoadInspectionEntity): Boolean {
        return oldItem.condition == newItem.condition && oldItem.direction == newItem.direction
    }

    override fun areContentsTheSame(oldItem: LoadInspectionEntity, newItem: LoadInspectionEntity): Boolean {
        return oldItem.condition == newItem.condition && oldItem.direction == newItem.direction
    }
}

data class LoadInspectionEntity(
    var condition: InspectionCondition? = null,
    var photos: List<UploadPhotoBean> = listOf(),
    var comment: String? = null,
    var direction: InspectionDirection? = null,
    val photoTime: String? = null
)