package com.unis.wms.load

import com.unis.platform.common.api.MaterialApi
import com.unis.platform.common.api.TaskApi
import com.unis.platform.common.model.material.MaterialLineQueryEntity
import com.unis.platform.common.model.task.TaskChecklistEntity
import com.unis.platform.common.model.task.TaskChecklistQuery
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.configuration_map.ConfigurationMapApi
import com.unis.platform.configuration_map.model.ConfigurationMapSearch
import com.unis.platform.customer_v2.CustomerApiService
import com.unis.platform.entryticket.EntryTicketApiService
import com.unis.platform.load.LoadApiService
import com.unis.platform.load.model.CollectSealEntity
import com.unis.platform.load.model.dockcheck.DockCheckEntity
import com.unis.platform.load.model.dockcheck.DockReleaseEntity
import com.unis.platform.load.model.inspect.EquipmentInspectEntity
import com.unis.platform.load.model.load.BolTypeEntity
import com.unis.platform.load.model.load.LoadQueryEntity
import com.unis.platform.load.model.load.LoadUpdateEntity
import com.unis.platform.load.model.load.ProNoAddEntity
import com.unis.platform.load.model.load.UploadSignatureEntity
import com.unis.platform.load.model.task.LoadingRequest
import com.unis.platform.load.model.task.UnloadAllRequest
import com.unis.platform.lp_v2.LpApiService
import com.unis.platform.lp_v2.model.LpDimensionUpdateEntity
import com.unis.platform.lp_v2.model.LpRequestEntity
import com.unis.platform.order.OrderApiService
import com.unis.platform.order.model.OrderSearchEntity
import com.unis.platform.order.model.OrderUpdateEntity
import com.unis.platform.ucc.UCCApiService
import com.unis.platform.ucc.UCCSearchEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.platform.load.model.LoadTaskUpdateEntity

class LoadTaskRepository : BaseRepository() {

    private val loadTaskApiService by apiServiceLazy<LoadApiService>()
    private val uccApiService by apiServiceLazy<UCCApiService>()
    private val lpApiService by apiServiceLazy<LpApiService>()
    private val orderApiService by apiServiceLazy<OrderApiService>()
    private val taskApi by apiServiceLazy<TaskApi>()
    private val customerApi by apiServiceLazy<CustomerApiService>()
    private val materialApi by apiServiceLazy<MaterialApi>()
    private val entryTicketApi by apiServiceLazy<EntryTicketApiService>()
    private val configurationMapApi by apiServiceLazy<ConfigurationMapApi>()

    fun searchByPaging(request: TaskQueryEntity) = requestV2({ loadTaskApiService.searchByPaging(request) })

    fun getLoadTask(id: String) = requestV2({ loadTaskApiService.getLoadTask(id) })

    fun dockCheckIn(taskId: String, request: DockCheckEntity) = requestV2({ loadTaskApiService.dockCheckIn(taskId, request) })

    fun equipmentInspect(taskId: String, request: List<EquipmentInspectEntity>) =
        requestV2({ loadTaskApiService.equipmentInspect(taskId, request) })

    fun checkUccPrintedLabel(taskId: String) = requestV2({ loadTaskApiService.checkUccPrintedLabel(taskId) })

    fun checkPalletConfig(taskId: String) = requestV2({ loadTaskApiService.checkPalletConfig(taskId) })

    fun getProcessNode(taskId: String) = requestV2({ loadTaskApiService.getProcessNode(taskId) })

    ////////step////////
    fun loadSlp(stepId: String, request: LoadingRequest) = requestV2({ loadTaskApiService.loadSlp(stepId, request) })

    fun batchLoadSlp(stepId: String, request: List<LoadingRequest>) = requestV2({ loadTaskApiService.batchLoadSlp(stepId, request) })

    fun unLoadSlp(stepId: String, request: LoadingRequest) = requestV2({ loadTaskApiService.unLoadSlp(stepId, request) })
    
    fun unloadAllLps(stepId: String, request: UnloadAllRequest) = requestV2({ loadTaskApiService.unloadAllLps(stepId, request) })

    fun searchLoadByPaging(query: LoadQueryEntity) = requestV2({ loadTaskApiService.searchLoadByPaging(query) })

    fun searchLoad(loadIds: List<String>) = requestV2({
        loadTaskApiService.searchLoad(LoadQueryEntity().apply {
            ids = loadIds
        })
    })

    fun getLoad(loadId: String) = requestV2({ loadTaskApiService.getLoad(loadId) })

    fun updateLoad(loadId: String, request: LoadUpdateEntity) = requestV2({ loadTaskApiService.updateLoad(loadId, request) })

    fun uploadLoadSignature(taskId: String, loadId: String, cmd: UploadSignatureEntity)
    = requestV2({ loadTaskApiService.uploadLoadSignature(taskId,loadId, cmd) })

    fun uploadOrderSignature(taskId: String, orderId: String,  cmd: UploadSignatureEntity)
    = requestV2({ loadTaskApiService.uploadOrderSignature(taskId,orderId, cmd) })

    fun completeLoad(taskId: String, loadId: String, cmd: ProNoAddEntity) =
        requestV2({ loadTaskApiService.completeLoad(taskId, loadId, cmd) })

    fun buildMasterBol(loadId: String, request: BolTypeEntity) = requestV2({
        loadTaskApiService.buildMasterBol(loadId,request)
    })

    fun buildBolByOrder(orderId: String, request: BolTypeEntity) = requestV2({
        loadTaskApiService.buildBolByOrder(orderId, request)
    })

    fun closeStep(stepId: String) = requestV2({ loadTaskApiService.closeStep(stepId) })

    fun closeStepWithTask(stepId: String) = requestV2({ loadTaskApiService.closeWithTask(stepId) })

    fun searchUcc(query: UCCSearchEntity) = requestV2({ uccApiService.searchUCC(query) })

    fun collectSeal(taskId: String, sealCmd: CollectSealEntity) = requestV2({ loadTaskApiService.collectSeal(taskId, sealCmd) })

    fun getLoadOrderView(loadId: String) = requestV2({ loadTaskApiService.getLoadOrderView(loadId) })

    fun lpSearch(lpIs: List<String>) = requestV2({ lpApiService.searchLps(LpRequestEntity().apply { ids = lpIs }) })

    fun lpSearch(query: LpRequestEntity) = requestV2({ lpApiService.searchLps(query) })

    fun orderUpdate(orderId: String, update: OrderUpdateEntity) = requestV2({ orderApiService.updateOrder(orderId, update) })

    fun completeSignature(taskId: String) = requestV2({ loadTaskApiService.completeSignature(taskId) })

    fun searchLpsByCode(code: String) = requestV2({lpApiService.searchLps(LpRequestEntity().apply { this.code = code }) })

    fun checkListUpdate(taskId: String, cmd: TaskChecklistEntity) = requestV2({ taskApi.check(taskId, cmd) })

    fun searchCheckList(query: TaskChecklistQuery) = requestV2({ taskApi.searchChecklist(query) })

    fun getEntryTicketCheckInfoByYms(entryId: String) = requestV2({ 
        entryTicketApi.getEntryTicketCheckInfoByYms(entryId)
    })

    fun completeCollectTaskInfo(taskId: String) = requestV2({ loadTaskApiService.completeCollectTaskInfo(taskId) })

    fun getCustomer(customerId: String) = rxRequest2(customerApi.getCustomer(customerId))

    fun updateLpPalletType(lpId: String, palletId: String) = requestV2({ lpApiService.updateLp(LpRequestEntity().apply {
        id = lpId; packagingTypeSpecId = palletId }   ) })

    fun closeTask(taskId: String) = requestV2({
        loadTaskApiService.closeTask(taskId)
    })

    //force close task
    fun forceCloseTask(taskId: String) = requestV2({
        loadTaskApiService.forceCloseTask(taskId)
    })

    fun searchMaterialLine(query: MaterialLineQueryEntity) = rxRequest2(
        materialApi.searchMaterialLine(query)
    )

    fun deleteMaterialLine(id: Long) = rxRequest2(
        materialApi.deleteMaterialLine(id)
    )

    fun updateLpDimensions(request: LpDimensionUpdateEntity) = requestV2({ lpApiService.updateLpDimensions(request) })

    fun orderSearch(query: OrderSearchEntity) = requestV2({ orderApiService.searchOrder(query) })

    fun dockRelease(request: DockReleaseEntity) = requestV2({ loadTaskApiService.dockRelease(request) })

    fun searchConfigurationMap(query: ConfigurationMapSearch) = requestV2({ configurationMapApi.search(query) })

    fun batchUpdateTask(tasks: List<LoadTaskUpdateEntity>) = requestV2({ loadTaskApiService.batchUpdateTask(tasks) })

    fun getLoadTaskDescribe(taskId: String) = requestV2({ loadTaskApiService.getLoadTaskDescribe(taskId) })
}