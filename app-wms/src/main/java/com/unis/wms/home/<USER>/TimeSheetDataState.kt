package com.unis.wms.home.time_sheet

import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry
import com.linc.platform.infoclock.model.InfoClockFacilityEntry
import com.linc.platform.infoclock.model.PunchInDetailEntry
import com.linc.platform.infoclock.model.PunchInHistoryEntry
import com.linc.platform.infoclock.model.PunchTypeEntry
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.UiEvent

data class TimeSheetDataState(
    val punchHistories: List<PunchInHistoryEntry> = listOf(),
    val curPunchWeekCycle: List<Long>,
    var infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry? = null,
    val punchHistory: List<PunchInDetailEntry>? = null,
    val curPunchType: PunchTypeEntry = PunchTypeEntry.IN_DAY,
    val infoClockFacility: InfoClockFacilityEntry? = null,
    val isIpCheckSuccess: Boolean = true,
    val ipCheckError: String = "",
    val todayPunchHistory: List<PunchInDetailEntry>? = null
) : ReactiveDataState

interface TimeSheetEvent {
    data class StartTakePhotoEvent(val type: PunchTypeEntry) : UiEvent
    data class ClockErrEvent(val error: String) : UiEvent
    data class PunchSuccessEvent(val tips: String, val type: PunchTypeEntry) : UiEvent
    data class PunchTodayHistoryEvent(val list: List<PunchInDetailEntry>?) : UiEvent
    data class PunchHistoryEvent(val list: List<PunchInHistoryEntry>,val isInitAllExpand:Boolean) : UiEvent
    data class PunchWeekCycleEvent(val string: String) : UiEvent
}
