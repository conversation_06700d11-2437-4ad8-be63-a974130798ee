package com.unis.wms.home.profile

import android.content.DialogInterface
import android.content.Intent
import android.location.Location
import android.os.Bundle
import com.customer.widget.GeneralAlertDialog
import com.customer.widget.ScanBarcodeDialog
import com.customer.widget.ScanBarcodeDialog.OnBarcodeResult
import com.customer.widget.common.CenterDialog
import com.customer.widget.scanner.decoder.DriverLicense
import com.linc.platform.common.AppLocationDataManager
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.MainEnv
import com.linc.platform.home.WorkHourManager
import com.linc.platform.http.HttpService
import com.linc.platform.utils.LocationHelper
import com.unis.platform.db.WMSDBManager
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.util.SwitchLangUtils
import com.unis.platform.util.SwitchLangUtils.setLanguage
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.wms.BuildConfig
import com.unis.wms.R
import com.unis.wms.databinding.FragmentHomeMyProfileBinding
import com.unis.wms.home.ToHomeHandler
import com.unis.wms.home.profile.dialog.MyProfileSelectDialog
import com.unis.wms.ot_control.OtCountDownTimerHandler
import com.unis.wms.ot_control.worker_assignment.WorkerAssignmentDialog
import java.util.Locale

class HomeMyProfileFragment : ReactiveFragment<MyProfileViewModel, MyProfileUiState, FragmentHomeMyProfileBinding>() {


    private val logoutScanDialog by lazy { initLogoutScanDialog() }

    companion object {
        @JvmStatic
        fun newInstance() = HomeMyProfileFragment()
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            hwFacility.setOnClickListener { viewModel.changeFacilityItems() }
//            hwCompany.setOnClickListener { viewModel.loadCompany() }
            hwAccount.setOnClickListener {
                val intent = Intent()
                intent.setClass(context!!, MyAccountActivity::class.java)
                startActivity(intent)
            }
            hwPsw.setOnClickListener {
                val intent = Intent()
                intent.setClass(context!!, PasswordManagementActivity::class.java)
                startActivity(intent)
            }
            hwLanguage.setTitleText(getSelectedLanguage())
            hwLanguage.setOnClickListener {
                showLanguageDialog()
            }
            btnLogout.setOnClickListener {
                logout()
            }
//            if (isUserFromInfoClock() && isRoleOnlyPunch()) {
//                llTimeSheet.visibility = View.VISIBLE
//            } else {
//                llTimeSheet.visibility = View.GONE
//            }
        }

        onEvent()
    }


    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(MyProfileUiState::account) {
            it?.let {
                binding?.hwAccount?.setSubTitleText(it + " | " + getString(R.string.label_version, BuildConfig.BUILD_TYPE+" . "+BuildConfig.VERSION_NAME))
            }
        }
        subscribe(MyProfileUiState::facility) {
            it?.let {
                binding?.hwFacility?.setSubTitleText(it)
            }
            if (viewModel.dataState.userInfo?.enableSkipWorkerAssignment() == false
                && viewModel.dataState.userInfo?.isNeedOtControl() == true) {// User of pay type hourly
                context?: return@subscribe
                val allowOTControl = viewModel.dataState.facility?.facilitySettingEntity?.allowOTControl?: false
                OtCountDownTimerHandler.getInstance().createCountDownTimerOrRemove(context!!, allowOTControl, idmUserId)
            }
        }
        subscribe(MyProfileUiState::company) {
            it?.let {
                binding?.hwCompany?.setSubTitleText(it)
            }
        }
        subscribe(MyProfileUiState::role) {
            it?.let {
                binding?.hwRole?.setSubTitleText(it)
            }
        }
    }

    override fun createViewModel() = MyProfileViewModel(
        initialState = MyProfileState(
            idmUserId = idmUserId,
            userInfo = LocalPersistence.getIamEntity(context!!),
            facility = WMSDBManager.facilityManager.getOneByUserId(idmUserId),
            userCompany = WMSDBManager.companyManager.getOneByUserId(idmUserId),
            tokenInfo = LocalPersistence.getTokenInfoEntity(context!!),
            registrationId = LocalPersistence.getRegistrationId(context!!, idmUserId))
    )


    private fun onEvent() {
        onEvent<MyProfileUiEvent.Choose> {
            MyProfileSelectDialog.newInstance(selectType = selectType, index, dates = list, callback = ::selectResult)
                .show(childFragmentManager, MyProfileSelectDialog::javaClass.name)
        }
        onEvent<MyProfileUiEvent.ShowScanLocation> {
            GeneralAlertDialog.createOkAlertDialog(
                context, "", getString(R.string.please_scan_location_to_log_out)) { dialog: DialogInterface, which: Int ->
                logoutScanDialog.show(childFragmentManager, "log out barcode")
                dialog.dismiss()
            }.show()
        }
        onEvent<MyProfileUiEvent.SelectWorkerAssignment> {
            selectWorkerAssignment(facility, lastFacility)
        }
        onEvent<MyProfileUiEvent.Logout> {
            logout()
        }
        onEvent<MyProfileUiEvent.NotifyBuriedPointEvent> {
            notifyBuriedPointEvent(evenType)
        }
    }

    private fun selectWorkerAssignment(
        facility: FacilityEntity,
        lastFacility: FacilityEntity?,
    ) {
        val workerAssignmentDialog = WorkerAssignmentDialog.newInstance(facility, lastFacility)
        workerAssignmentDialog.setOnWorkerAssignmentListener(object : WorkerAssignmentDialog.OnWorkerAssignmentListener {
            override fun onSelectedWorkerAssignment(
                facility: FacilityEntity,
                lastFacility: FacilityEntity?,
                teamId: Long,
                laborTypeId: Long
            ) {
                viewModel.afterExchangeFacility(facility, lastFacility)
            }

            override fun onCancel(facility: FacilityEntity, lastFacility: FacilityEntity?) {
                //When cancel, reset facility
                HttpService.updateFacilityId(lastFacility?.id)
                HttpService.updateFacility(lastFacility)
            }

        })
        workerAssignmentDialog.show(childFragmentManager, "")
    }

    private fun selectResult(selectType: Int, index: Int) {
        when (selectType) {
            1 -> {
                viewModel.facilityChange(index) //change facility restart userGatherService because facility setting is Change
                if (activity != null) {
//                    stopService(activity!!)
//                    startService(activity!!)
                }
            }
            2 -> {
                viewModel.companyChange(viewModel.dataState.assignedCompanies[index])
            }
            else -> {

            }
        }
    }

    private fun initLogoutScanDialog(): ScanBarcodeDialog {
        val logoutScanDialog = ScanBarcodeDialog()
        logoutScanDialog.setOnBarcodeResult(object : OnBarcodeResult {
            override fun onBarcode(locationBarcode: String) {
                viewModel.searchLocation(locationBarcode)
            }

            override fun onDriverLicense(driverLicense: DriverLicense) {}
            override fun onDismiss() {}
        })
        return logoutScanDialog
    }


    private fun logout() {
        ToHomeHandler.getHandler().clearLoginData(activity!!, idmUserId)
        MainEnv.startLoginActivity()
        activity!!.finish()
    }

    private fun notifyBuriedPointEvent(eventType: String) {
        if (WorkHourManager.TYPE_END_WORK_HOUR == eventType) {
            val userInfo = LocalPersistence.getIamEntity(activity!!)
            val userName = userInfo?.userName ?: ""
            val userFullName = if (userInfo != null) userInfo.firstName + " " + userInfo.lastName else ""
            WorkHourManager.newInstance().endWorkHourEvent(activity, idmUserId, facilityId, userName, userFullName, facilityName)
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val userInfo = LocalPersistence.getIamEntity(it)
            LocalPersistence.setIamEntity(it, userInfo)
            viewModel.setCurrentFacility(WMSDBManager.facilityManager.getOneByUserId(idmUserId))
            viewModel.companyChange(WMSDBManager.companyManager.getOneByUserId(idmUserId))
            LocationHelper.getInstance().startLocation(it) { location: Location? ->
                AppLocationDataManager.getInstance().putAppLocation(location)
            }
        }

    }

    private fun showLanguageDialog() {
        context?: return
        CenterDialog.singleChoiceList(
            context = context!!,
            title = getString(R.string.label_speak_language),
            items = resources.getStringArray(R.array.language).toList(),
            itemTitleMapper = {
                it
            },
            defaultChose = getSelectedLanguage(),
            positiveClick = { language ->
                language?.let {
                    val locale = when(it) {
                        getString(R.string.language_english) -> Locale.ENGLISH
                        getString(R.string.language_chinese) -> Locale.CHINESE
                        getString(R.string.language_spanish) -> Locale("es")
                        getString(R.string.language_japanese) -> Locale("ja")
                        else -> Locale.ENGLISH
                    }
                    setLanguage(locale)
                    viewModel.updateLanguage(locale)
                }
            }
        ).show()
    }

    private fun getSelectedLanguage(): String {
        val language = SwitchLangUtils.getLanguage()
        return getDisplayLanguage(language)
    }

    private fun getDisplayLanguage(locale: Locale): String {
        return when(locale) {
            Locale.ENGLISH -> getString(R.string.language_english)
            Locale.CHINESE -> getString(R.string.language_chinese)
            Locale("es") -> getString(R.string.language_spanish)
            Locale("ja") -> getString(R.string.language_japanese)
            else -> getString(R.string.language_english)
        }
    }
}