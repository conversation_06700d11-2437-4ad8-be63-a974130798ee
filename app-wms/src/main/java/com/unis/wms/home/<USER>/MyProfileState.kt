package com.unis.wms.home.profile

import com.linc.platform.http.TokenInfoEntity
import com.unis.platform.common.model.company.CompanyEntity
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.iam.model.UserInfoEntity
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

data class MyProfileState(
    val idmUserId: String,
    val userInfo: UserInfoEntity?,
    val facility: FacilityEntity?,
    val userCompany: CompanyEntity?,
    val assignedFacilities: List<FacilityEntity> = listOf(),// 目前登录中返回的facility只有Id,name， 预留后期如果需要加载facility使用
    val assignedCompanies:List<CompanyEntity> = listOf(),
    val tokenInfo:TokenInfoEntity,
    val registrationId:String
) : ReactiveDataState {

}


data class MyProfileUiState(
    val account: String? = null, val company: String? = null, val facility: String? = null, val role: String? = null
) : ReactiveUiState

interface MyProfileUiEvent {
    data class Choose(val selectType:Int,val index:Int=0,val list:List<String> = listOf()) : UiEvent
    data class SelectWorkerAssignment(val facility: FacilityEntity, val lastFacility: FacilityEntity?): UiEvent
    class ShowScanLocation: UiEvent
    class Logout: UiEvent
    data class NotifyBuriedPointEvent(val evenType:String): UiEvent
}