package com.unis.wms.home.task

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.RxBus
import com.linc.platform.home.HttpServiceUpdateEvent
import com.linc.platform.home.MainFragmentRefreshEvent
import com.linc.platform.utils.Logger
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.taskcenter.model.HomeTaskBean
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.wms.R
import com.unis.wms.assemblytask.task_list.AssemblyTaskListActivity
import com.unis.wms.databinding.FragmentHomeTaskBinding
import com.unis.wms.general.tasklist.GeneralTaskListActivity
import com.unis.wms.internaltransfer.inn.tasklist.TransferInTaskListActivity
import com.unis.wms.internaltransfer.out.tasklist.TransferOutTaskListActivity
import com.unis.wms.load.taskList.LoadTaskListActivity
import com.unis.wms.movement.tasklist.MovementTaskListActivity
import com.unis.wms.ot_control.list.OtRequestListActivity
import com.unis.wms.outbound_qc_task.task_list.OutboundQcTaskListActivity
import com.unis.wms.pack.tasklist.PackTaskListActivity
import com.unis.wms.pick_task.tasklist.PickTaskListActivity
import com.unis.wms.put_away_task.tasklist.PutAwayTaskListActivity
import com.unis.wms.put_back.tasklist.PutBackTaskListActivity
import com.unis.wms.receive.tasklist.ReceiveTaskListActivity
import com.unis.wms.replenishment.tasklist.ReplenishmentTaskListActivity
import com.unis.wms.task_action.TaskActionActivity
import com.unis.wms.task_action.dialog.UserCapabilityLocationDialog
import com.unis.wms.transload.dockcheck.tasklist.DockCheckTaskListActivity
import com.unis.wms.transload.loading.tasklist.TransLoadLoadingTaskListActivity
import com.unis.wms.transload.receiving.tasklist.TransLoadReceivingTaskListActivity
import com.unis.wms.transload.spotcheck.SpotCheckActivity
import com.unis.wms.transload.transferdocklocation.TransferDockLocationActivity
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers


/**
 * A simple [Fragment] subclass.
 * Use the [HomeTaskFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class HomeTaskFragment : ReactiveFragment<HomeTaskViewModel, HomeTaskUiState, FragmentHomeTaskBinding>() {


    private lateinit var mContext: Context
    private lateinit var mAdapter: HomeTaskAdapter
    private lateinit var disposable: Disposable
    private lateinit var manualResumeDisposable: Disposable
    private var userCapabilityLocationDialog: UserCapabilityLocationDialog? = null
    private var claimForkliftDialog: ClaimForkliftDialog? = null
    override fun initView(savedInstanceState: Bundle?) {
        onEvents()
        mContext = activity!!
        binding?.apply {
            slTask.setColorSchemeResources(
                android.R.color.holo_purple, android.R.color.holo_orange_light, android.R.color.holo_red_light)
            slTask.setOnRefreshListener {
                viewModel.getTaskAmount(false)
                refreshOtRequest()
            }
            rvTask.layoutManager = LinearLayoutManager(mContext)
            mAdapter = HomeTaskAdapter()
            rvTask.adapter = mAdapter
            mAdapter.setOnItemClickListener { _, _, position -> onItemClick(position) }
            mAdapter.bindToRecyclerView(rvTask)
            claimForkliftLl.setOnClickListener {
                showClaimForkliftDialog()
            }
            if (viewModel.dataState.userInfo?.isOtSupervisorOrApprover() == true && viewModel.getFacility()?.facilitySettingEntity?.allowOTControl == true) {
                otRequestCl.setVisible()
            }
            otRequestCl.setOnClickListener {
                startActivity(Intent(context, OtRequestListActivity::class.java))
            }
            taskActionLl.setOnClickListener {
                val userCapabilities = LocalPersistence.getUserCapabilities(context!!, idmUserId)?: emptyList()
                activity?.let {
                    userCapabilityLocationDialog = UserCapabilityLocationDialog.show(it, userCapabilities) { capability, location->
                        viewModel.checkLocationAndSaveUserCapability(location, capability)
                    }
                }
            }
        }
        disposable = RxBus.getDefault(HttpServiceUpdateEvent::class.java).toFlowable().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe {
                viewModel.updateApiService()
                viewModel.getFacilityName()
            }

        manualResumeDisposable = RxBus.getDefault(MainFragmentRefreshEvent::class.java).toFlowable().subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread()).subscribe { message: MainFragmentRefreshEvent ->
                if (message.forceRefresh) {
                   forceRefresh()
                }
            }
    }

    companion object {
        @JvmStatic
        fun newInstance() = HomeTaskFragment()
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(HomeTaskUiState::currentDate) {
            it?.let {
                binding?.tvData?.text = it
            }
        }
        subscribe(HomeTaskUiState::facilityName) {
            it?.let {
                binding?.tvFacility?.text = it
            }
        }
        subscribe(HomeTaskUiState::todayTask) {
            it?.let {
                binding?.tvTaskSubtitle?.text = it
            }
        }
        subscribe(HomeTaskUiState::taskResult) {
            it?.let {
                mAdapter.setNewData(it)
                mAdapter.setEmptyView(R.layout.new_home_empty)
            }
        }
        subscribe(HomeTaskUiState::title) {
            it?.let {
                binding?.tvTaskTitle?.text = it
            }
        }
        subscribe(HomeTaskUiState::collectLocationWifiInfo) {
            binding?.locatingRight?.visibility=if(it) View.VISIBLE else View.GONE
        }
        subscribe(HomeTaskUiState::otRequestsCount) {
            binding?.otBadgeTv?.visibility = if (it > 0) View.VISIBLE else View.GONE
            binding?.otBadgeTv?.text = if (it > 99) "99+" else it.toString()
        }
    }

    override fun createViewModel() = HomeTaskViewModel(initialState = HomeTaskState(idmUserId,userInfo = LocalPersistence.getIamEntity(activity!!)))

    private fun onEvents() {
        onEvent<HomeTaskUiEvent.FinishRefresh> {
            binding?.slTask?.isRefreshing = false
        }
        onEvent<HomeTaskUiEvent.StartTaskActionActivity> {
            userCapabilityLocationDialog?.dismiss()
            TaskActionActivity.startActivity(context!!, location?.name)
        }
    }

    private fun onItemClick(position: Int) {
        val intent = Intent()
        val entry: HomeTaskBean = mAdapter.getItem(position)!!
        if (entry == null) {
            Logger.e("Task type not find")
            return
        }
        when (entry.taskTypeEntity) {
            TaskType.RECEIVE -> {
                intent.setClass(context!!, ReceiveTaskListActivity::class.java)
                context!!.startActivity(intent)
            }

            TaskType.REPLENISH -> ReplenishmentTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)

            TaskType.GENERAL -> {
                GeneralTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.PUT_BACK ->  PutBackTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)

            TaskType.MOVEMENT -> {
                MovementTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.TRANSLOAD_RECEIVE -> {
                TransLoadReceivingTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.TRANSLOAD_LOAD -> {
                TransLoadLoadingTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.PICK -> {
                PickTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.PUT_AWAY -> {
                PutAwayTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.LOAD -> {
                LoadTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.PACK -> {
                PackTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.INTERNAL_TRANSFER_OUT -> {
                TransferOutTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.INTERNAL_TRANSFER_IN -> {
                TransferInTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.DOCK_CHECK -> {
                DockCheckTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.SPOT_CHECK -> {
                SpotCheckActivity.startActivity(context!!)
            }

            TaskType.TRANSFER_DOCK_LOCATION -> {
                TransferDockLocationActivity.startActivity(context!!)
            }

            TaskType.ASSEMBLY -> {
                AssemblyTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            TaskType.QC -> {
                OutboundQcTaskListActivity.startActivity(context!!, entry.newCount, entry.progressCount, 0)
            }

            else -> {
                Logger.e("Task type not find")
            }
        }
    }

    private fun showClaimForkliftDialog() {
        activity?.let {
            claimForkliftDialog =
                ClaimForkliftDialog.newInstance(idmUserId, LocalPersistence.getIamEntity(activity!!).userName)
            claimForkliftDialog?.setForkliftClaimListener(object : ClaimForkliftDialog.ForkliftClaimListener {
                override fun onClaimSuccess(forkliftCode: String) {
                }

            })
            claimForkliftDialog?.show(it.supportFragmentManager, "claim_forklift_dialog")
        }
    }

    private fun refreshOtRequest() {
        if (viewModel.dataState.userInfo?.isOtSupervisorOrApprover() == true && viewModel.getFacility()?.facilitySettingEntity?.allowOTControl == true) {
            viewModel.queryOtRequests()
            binding?.apply {
                otRequestCl.setVisible()
            }
        } else {
            binding?.apply {
                otRequestCl.setGone()
            }
        }
    }

    override fun forceRefresh() {
        super.forceRefresh()
        binding?.slTask?.isRefreshing = true
        if (isViewModelInitialized) {
            viewModel.getTaskAmount(false)
        }
        refreshOtRequest()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!disposable.isDisposed) {
            disposable.dispose()
        }
        if (!manualResumeDisposable.isDisposed) {
            manualResumeDisposable.dispose()
        }
        claimForkliftDialog = null
    }
}