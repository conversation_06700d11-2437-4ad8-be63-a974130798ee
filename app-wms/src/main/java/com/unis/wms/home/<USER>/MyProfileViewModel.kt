package com.unis.wms.home.profile

import android.text.TextUtils
import com.linc.platform.baseapp.api.LocationApi
import com.linc.platform.baseapp.model.LocationSearchEntry
import com.linc.platform.bluetooth.presenter.BluetoothLocationPresenter
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.RxBus
import com.linc.platform.home.HttpServiceUpdateEvent
import com.linc.platform.home.MainFragmentRefreshEvent
import com.linc.platform.home.WorkHourManager
import com.linc.platform.http.AppLocationInterceptor
import com.linc.platform.http.HttpService
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.LogoutRequestEntry
import com.linc.platform.localconfig.CompanyConfigPresenterImpl
import com.linc.platform.localconfig.FacilityConfigPresenterImpl
import com.linc.platform.push.PushMessageApi
import com.linc.platform.utils.ResUtil
import com.unis.platform.common.model.company.CompanyEntity
import com.unis.platform.db.WMSDBManager
import com.unis.platform.facility_v2.FacilityApiService
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.facility_v2.mode.FacilitySearchEntity
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.TeamSettingQueryEntity
import com.unis.platform.util.SwitchLangUtils
import com.unis.platform.warehouse.model.WarehouseServerQueryEntity
import com.unis.platform.warehouse.model.WarehouseServerStatus
import com.unis.platform.warehouse.model.WarehouseServerType
import com.unis.platform.wcs.WcsLocalManager
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.common.server.WarehouseServerRepository
import com.unis.wms.pick_to_light.base.LightManager
import kotlinx.coroutines.delay
import java.util.Locale

class MyProfileViewModel(
    private val repository: WorkStageRepo = WorkStageRepo(),
    initialState: MyProfileState,
    initialUiState: MyProfileUiState = MyProfileUiState(),
) : ReactiveViewModel<MyProfileState, MyProfileUiState>(initialState, initialUiState) {

    private val warehouseRepo = WarehouseServerRepository()

    init {
        if (null == dataState.userInfo) {
            showToast(String.format(getString(R.string.user_info_no_found_tip), getString(R.string.label_user)))
            fireEvent { MyProfileUiEvent.Logout() }
        } else {
//            val role = dataState.userInfo!!.roleIds.joinToString(",") { roleEntry: RoleEntry -> roleEntry.name }
            val role = ""
            setUiState {
                copy(
                    account = "id：" + dataState.userInfo!!.userId,
                    company = dataState.userCompany?.name,
                    role = role)
            }
        }
        mapDataToUi(MyProfileState::facility, MyProfileUiState::facility) { it?.name }
        loadFacilityDetail()
    }

    private fun loadFacilityDetail() {
        launch {
            val facilityId = dataState.facility?.id?: return@launch
            val facility = requestAwait(repository.getFacility(facilityId)).getOrNull()?: return@launch
            if (dataState.facility == null) {
                WMSDBManager.facilityManager.insertByUserId(dataState.idmUserId, facility)
            } else {
                WMSDBManager.facilityManager.updateByUserId(dataState.idmUserId, facility)
            }
            setDataState { copy(facility = facility) }
        }
    }

    fun loadCompany() {
        if (null == dataState.userInfo) {
            showToast(R.string.hint_user_load_fail_please_re_login)
            return
        }
//        val companyEntries =
//            dataState.userInfo!!.assignedCompanyFacilities.filter { companyFacility -> companyFacility?.userCompanyEntry != null }
//                .map { companyFacility -> companyFacility.userCompanyEntry }.distinctBy { userCompanyEntry -> userCompanyEntry.id }
//        setDataState { copy(assignedCompanies = companyEntries) }
//        val companyList: List<String> = companyEntries.map { entry -> entry.name }.toList()
//        val companyItemIndex = companyList.indexOf(dataState.userCompany?.name)
//        fireEvent { MyProfileUiEvent.Choose(selectType = 2, companyItemIndex, companyList) }
    }

    fun changeFacilityItems() {
        launch {
            val facilityIds = dataState.userInfo?.getUserFacilities()?.map { it.id }
            val facilities = requestAwait(repository.searchFacility(FacilitySearchEntity().apply {
                ids = facilityIds
            })).getOrNull()?: return@launch
            setDataStateAwait { copy(assignedFacilities = facilities) }
            val facilityNames = facilities.map { it.name }
            val index = facilityNames.indexOf(dataState.facility?.name)
            fireEvent { MyProfileUiEvent.Choose(1, index, facilityNames) }
        }
    }

    private fun getLocalServer(facilityId: String) {
        launch {
            val serverResults = requestAwait(warehouseRepo.search(WarehouseServerQueryEntity().apply {
                types = listOf(WarehouseServerType.PICK_TO_LIGHT, WarehouseServerType.WCS)
                status = WarehouseServerStatus.ACTIVE
                this.facilityId = facilityId
            })).getOrNull()

            if (!serverResults.isNullOrEmpty()) {
                val pickToLightServer = serverResults.find { it.type == WarehouseServerType.PICK_TO_LIGHT }
                LightManager.savePickToLightServer(pickToLightServer)
                val wcsServer = serverResults.find { it.type == WarehouseServerType.WCS }
                WcsLocalManager.saveWcsServer(wcsServer)
            } else {
                LightManager.savePickToLightServer(null)
                WcsLocalManager.saveWcsServer(null)
            }
        }
    }

    fun facilityChange(index: Int) {
        val facilityEntity: FacilityEntity = dataState.assignedFacilities[index]
        val lastFacility: FacilityEntity? = dataState.facility
        HttpService.updateFacilityId(facilityEntity.id)
        HttpService.updateFacility(facilityEntity)
        if (dataState.userInfo?.isItRoleOrTag() == true) {
            afterExchangeFacility(facilityEntity, lastFacility)
        } else {
            //When exchange facility, ot control api need new facility in request header
            //If cancel select worker assignment remember to reset facility, only select worker assignment after to exchange facility
            getUserOtControlRole({
                val iamEntity = LocalPersistence.getIamEntity(ResUtil.getContext())
                iamEntity.isSupervisor = it.first
                iamEntity.isOTApprover = it.second
                iamEntity.isUpperApprover = it.third
                LocalPersistence.setIamEntity(ResUtil.getContext(), iamEntity)
                setDataState { copy(userInfo = iamEntity) }
                if (it.first || it.second || it.third) {
                    afterExchangeFacility(facilityEntity, lastFacility)
                } else {
                    fireEvent { MyProfileUiEvent.SelectWorkerAssignment(facilityEntity, lastFacility) }
                }
            }, {//When failure, un-exchange, facility need reset
                HttpService.updateFacilityId(lastFacility?.id)
                HttpService.updateFacility(lastFacility)
            })
        }
    }

    fun afterExchangeFacility(facilityEntity: FacilityEntity, lastFacility: FacilityEntity?) {
        WMSDBManager.facilityManager.updateByUserId(dataState.idmUserId, facilityEntity)
        setCurrentFacility(facilityEntity)
        val userName = if (dataState.userInfo != null) dataState.userInfo!!.userName else ""
        val userFullName = if (dataState.userInfo != null) dataState.userInfo!!.firstName + " " + dataState.userInfo!!.lastName else ""
        if (lastFacility != null) {
            WorkHourManager.newInstance().endWorkHourEvent(
                ResUtil.getContext(), dataState.idmUserId, lastFacility.id, userName, userFullName, lastFacility.name)
        }
        WorkHourManager.newInstance().startWorkHourEvent(
            ResUtil.getContext(), dataState.idmUserId, facilityEntity.id, userName, userFullName, facilityEntity.name)
        updateBluetoothLocation()
        getLocalServer(facilityEntity.id)
    }

    fun companyChange(userCompany: CompanyEntity?) {
        if (userCompany == null) {
            showToast(String.format(getString(R.string.user_info_no_found_tip), getString(R.string.label_company)))
            fireEvent { MyProfileUiEvent.Logout() }
            return
        }
        HttpService.updateCompanyIds(userCompany.id)
        WMSDBManager.companyManager.updateByUserId(dataState.idmUserId, userCompany)
        setUiState { copy(company = userCompany.name) }
        setDataState { copy(userCompany = userCompany) }
    }

    private fun getSelectedCompanyId(): String? {
        return if (dataState.userCompany == null) CompanyConfigPresenterImpl.getInstance()
            .getCompany(dataState.idmUserId).id else dataState.userCompany!!.id
    }

    private fun updateBluetoothLocation() {
        val bluetoothLocationPresenter = BluetoothLocationPresenter()
        bluetoothLocationPresenter.searchBluetoothLocation()
    }

    private fun logout(locationBarcode: String? = null) {
        if (TextUtils.isEmpty(dataState.idmUserId) || TextUtils.isEmpty(dataState.registrationId)) {
            doLogout(locationBarcode = locationBarcode)
        } else {
            doSendUnRegistration(locationBarcode)
        }
    }

    fun searchLocation(locationBarcode: String) {
        launch {
            val searchEntry = LocationSearchEntry()
            searchEntry.name = locationBarcode
            requestAwait(repository.search(searchEntry)).onSuccess {
                it?.let { locationEntries ->
                    if (locationEntries.size == 1) {
                        logout(locationBarcode)
                    } else {
                        showToast(R.string.message_invalid_location)
                    }
                }
            }
        }
    }

    private fun doLogout(locationBarcode: String? = null) {
        launch {
            val entry = LogoutRequestEntry()
            entry.oauthToken = dataState.tokenInfo.accessToken
            if (!TextUtils.isEmpty(locationBarcode)) {
                entry.logoutLocation =
                    FacilityConfigPresenterImpl.getInstance().getFacility(dataState.idmUserId).name + "." + locationBarcode
            }
            fireEvent { MyProfileUiEvent.NotifyBuriedPointEvent(WorkHourManager.TYPE_END_WORK_HOUR) }
            requestAwait(repository.logout(entry)).onSuccess {
                fireEvent { MyProfileUiEvent.Logout() }
            }.onFailure {
                fireEvent { MyProfileUiEvent.Logout() }
            }
        }
    }

    private fun doSendUnRegistration(locationBarcode: String? = null) {
        launch {
            requestAwait(repository.unregister(dataState.idmUserId, dataState.registrationId)).onSuccess {
                doLogout(locationBarcode)
            }.onFailure {
                doLogout(locationBarcode)
            }
        }
    }

    private fun newCurrentPresenterAndPushEvent() {
        RxBus.getDefault(HttpServiceUpdateEvent::class.java).send(HttpServiceUpdateEvent())
        RxBus.getDefault(MainFragmentRefreshEvent::class.java).send(MainFragmentRefreshEvent(true))
    }

    fun setCurrentFacility(facilityEntity: FacilityEntity?) {
        if (null == facilityEntity) {
            showToast(String.format(getString(R.string.user_info_no_found_tip), getString(R.string.label_facility)))
            fireEvent { MyProfileUiEvent.Logout() }
            return
        }
        setDataState { copy(facility = facilityEntity) }
        setUiState { copy(facility = facilityEntity.name) }
        newCurrentPresenterAndPushEvent()
    }

    fun updateLanguage(locale: Locale){
        launch {
            delay(500)
            SwitchLangUtils.setLanguage(locale)
        }
    }

    private fun getUserOtControlRole(onSuccess: (Triple<Boolean, Boolean, Boolean>) -> Unit, onFailure: () -> Unit) {
        launch {
            requestAwait(repository.getTeams()).onSuccess { teamSettingsResult ->
                val userId = dataState.idmUserId
                val teamSettings = teamSettingsResult?.list?: listOf()
                val isSupervisor = teamSettings.any { it.supervisorIds?.contains(userId) ?: false }
                val isOTApprover = teamSettings.any { it.otApproverIds?.contains(userId) ?: false }
                val isUpperApprover = teamSettings.any { it.upperApproverIds?.contains(userId) ?: false }
                onSuccess.invoke(Triple(isSupervisor, isOTApprover, isUpperApprover))
            }.onFailure {
                onFailure.invoke()
            }
        }
    }

    class WorkStageRepo : BaseRepository() {
        private val facilityApi: FacilityApiService = HttpService.createService(FacilityApiService::class.java)
        private val idmApi = HttpService.createService(IdmApi::class.java, AppLocationInterceptor.getInstance())
        private val location = HttpService.createService(LocationApi::class.java)
        private val pushMessageApi = HttpService.createService(PushMessageApi::class.java)
        private val otControlApi = HttpService.createService(OtControlApi::class.java)
        fun logout(request: LogoutRequestEntry) = rxRequest(idmApi.logout(request))
        fun searchFacility(search: FacilitySearchEntity) = rxRequest2(facilityApi.searchFacility(search))
        fun getFacility(facilityId: String) = rxRequest2(facilityApi.getFacility(facilityId))
        fun search(search: LocationSearchEntry) = rxRequest(location.search(search))
        fun unregister(idmUserId: String, registrationId: String) = rxRequest(pushMessageApi.unregister(idmUserId, registrationId))
        fun getTeams() = requestV2({otControlApi.searchTeamsByPaging(TeamSettingQueryEntity().apply { this.pageSize = 1000 })})
    }
}

