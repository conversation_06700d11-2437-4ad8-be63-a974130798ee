package com.unis.wms.home.task

import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.RxBus
import com.linc.platform.home.MAIN_NAVIGATION_BAR_TASKS
import com.linc.platform.home.MainNotificationRefreshEvent
import com.linc.platform.http.HttpService
import com.linc.platform.utils.DateUtils
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.TimeUtil
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.iam.api.IamApi
import com.unis.platform.iam.model.UpdateUserProfileEntity
import com.unis.platform.iam.model.UserCapability
import com.unis.platform.location_v2.LocationApiService
import com.unis.platform.location_v2.model.LocationRequestEntity
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OtRequestQueryEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.platform.taskcenter.api.TaskCenterApi
import com.unis.platform.taskcenter.model.HomeTaskEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.facilityEntity
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R

class HomeTaskViewModel(
    private val repository: WorkStageRepo = WorkStageRepo(),
    initialState: HomeTaskState,
    initialUiState: HomeTaskUiState = HomeTaskUiState(),
) : ReactiveViewModel<HomeTaskState, HomeTaskUiState>(initialState, initialUiState) {


    init {
        setUiState { copy(currentDate = DateUtils.getCurrentMonthToEN() + " ${DateUtils.getToday()}, ${DateUtils.getCurrentYear()}") }
        setUiState { copy(title = getString(R.string.home_hello) + " " + (dataState.userInfo?.userName)) }
        getFacilityName()
        if (dataState.userInfo?.isOtSupervisorOrApprover() == true && getFacility()?.facilitySettingEntity?.allowOTControl == true) {
            queryOtRequests()
        }
    }

    fun getFacility() = repository.facilityEntity

    fun getFacilityName() {
        launch {
            val facility = repository.facilityEntity
            awaitUiState()
            setUiState { copy(facilityName = facility?.name, collectLocationWifiInfo = false) }
        }
    }

    fun getTaskAmount(isShow: Boolean = true) {
        val taskQuery = TaskQueryEntity().apply {
                this.assigneeUserId = dataState.idmUserId
                this.statuses = listOf(TaskStatus.IN_PROGRESS, TaskStatus.NEW)
            }
        launch {
            awaitUiState()
            requestAwait(repository.getTask(taskQuery), showLoading = isShow).onSuccess {
                fireEvent { HomeTaskUiEvent.FinishRefresh }
                val homeTaskEntity = it ?: HomeTaskEntity()
                loadData(homeTaskEntity)
            }.onFailure {
                fireEvent { HomeTaskUiEvent.FinishRefresh }
               it.message?.let { errorMsg->
                  showToast(errorMsg)
               }
            }
        }
    }

    fun updateApiService() {
        repository.updateApiService()
    }

    fun queryOtRequests() {
        launch {
            requestAwait(repository.queryOtRequests(), showLoading = false).onSuccess {
                setUiState { copy(otRequestsCount = it?.size ?: 0) }
            }
        }
    }

    private fun loadData(homeTaskEntity: HomeTaskEntity) {
        val companyCode = dataState.userInfo?.companyCode
        val task = homeTaskEntity.getTask(companyCode)
        setUiState { copy(taskResult = task, todayTask = String.format(getString(R.string.home_today_task), homeTaskEntity.sum)) }
        RxBus.getDefault(MainNotificationRefreshEvent::class.java).sendByQueue(
            MainNotificationRefreshEvent(String.format("%s", homeTaskEntity.sum), MAIN_NAVIGATION_BAR_TASKS))
    }

    fun checkLocationAndSaveUserCapability(locationName: String, selectedUserCapability: List<UserCapability>) {
        launch {
            if (selectedUserCapability.isEmpty()) {
                showSnack(SnackType.ErrorV1(), R.string.error_please_select_user_capability_first)
                return@launch
            }
            val userCapabilities = LocalPersistence.getUserCapabilities(ResUtil.getContext(), repository.idmUserId)?: emptyList()
            if (userCapabilities.size != selectedUserCapability.size ||  !userCapabilities.containsAll(selectedUserCapability)) {
                val result = requestAwait(repository.updateUserProfile(UpdateUserProfileEntity().apply {
                    userId = repository.idmUserId
                    capabilities = selectedUserCapability
                }))
                if (result.isFailure) return@launch
            }
            LocalPersistence.saveUserCapabilities(ResUtil.getContext(), repository.idmUserId, selectedUserCapability)
            val locations = requestAwait(repository.searchLocationByName(locationName)).getOrNull()
            if (locations.isNullOrEmpty()) {
                showToast(getString(R.string.location_not_found))
                return@launch
            }
            fireEvent { HomeTaskUiEvent.StartTaskActionActivity(locations.first()) }
        }
    }


    class WorkStageRepo : BaseRepository() {
        private var taskCenterApi = HttpService.createService(TaskCenterApi::class.java)
        private val otControlApi by apiServiceLazy<OtControlApi>()
        private val locationApiService by apiServiceLazy<LocationApiService>()
        private var iamApi = HttpService.createService(IamApi::class.java)

        fun getTask(searchEntity: TaskQueryEntity) = rxRequest2(taskCenterApi.getTask(searchEntity))
        fun updateApiService() {
            taskCenterApi = HttpService.createService(TaskCenterApi::class.java)
        }
        fun queryOtRequests() = requestV2({otControlApi.queryOtRequests(OtRequestQueryEntity().apply {
            this.status = OtRequestStatus.PENDING
            this.submitTimeFrom = TimeUtil.toDate(TimeUtil.getDateToString(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L, TimeUtil.FORMAT_FULL_T_FORMAT))//from before 7 day
            this.submitTimeTo = TimeUtil.toDate(TimeUtil.getDateToString(System.currentTimeMillis(), TimeUtil.FORMAT_FULL_T_FORMAT))
        })})

        fun searchLocationByName(name: String)= rxRequest2(locationApiService.searchLocations(LocationRequestEntity().apply {
            this.name = name
        }))

        fun updateUserProfile(request: UpdateUserProfileEntity) = rxRequest2(iamApi.updateUserProfile(request))
    }
}

