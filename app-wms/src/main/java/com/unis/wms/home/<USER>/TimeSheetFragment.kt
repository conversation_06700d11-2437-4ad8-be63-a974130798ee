package com.unis.wms.home.time_sheet

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.customer.widget.GeneralPromptDialog
import com.customer.widget.ListPopupWindow
import com.customer.widget.common.safeCount
import com.customer.widget.takecamerafile.TakePhotoDialog
import com.customer.widget.takecamerafile.TakePhotoLayout
import com.linc.platform.core.LocalPersistence
import com.linc.platform.http.HttpService
import com.linc.platform.infoclock.model.PunchTypeEntry
import com.linc.platform.utils.NetWorkUtil
import com.linc.platform.utils.TimeUtil
import com.linc.platform.utils.doubleClick
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.SimpleReactiveFragment
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.launchScope
import com.unis.wms.R
import com.unis.wms.databinding.FragmentTimeSheetBinding
import com.unis.wms.ot_control.OtCountDownTimerHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR> on 2024/12/3
 */
class TimeSheetFragment : SimpleReactiveFragment<TimeSheetViewModel, TimeSheetDataState, FragmentTimeSheetBinding>(), SwipeRefreshLayout.OnRefreshListener,
                          View.OnClickListener {


    companion object {
        fun newInstance() = TimeSheetFragment()
    }

    private val punchHistoryAdapter by lazy { TimesheetPunchHistoryAdapter { viewModel.updateExpanded(it) } }
    private val todayPunchDataAdapter by lazy { TimesheetPunchDetailAdapter() }
    private var promptDialog: GeneralPromptDialog? = null
    private val coroutineScope by lazy { CoroutineScope(Dispatchers.Main) }

    override fun createViewModel() = TimeSheetViewModel(
        TimeSheetDataState(
            curPunchWeekCycle = TimeUtil.getWeekDateList(System.currentTimeMillis()),
            infoClockEmployeeViewEntry = LocalPersistence.getEmployeeInfoEntry(context!!)))

    @SuppressLint("SetTextI18n")
    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            swipeRefreshLayout.isEnabled = false
            swipeRefreshLayout.setOnRefreshListener(this@TimeSheetFragment)
            btnMore.setOnClickListener(this@TimeSheetFragment)
            btnInDay.setOnClickListener(this@TimeSheetFragment)
            btnInLunch.setOnClickListener(this@TimeSheetFragment)
            btnOutLunch.setOnClickListener(this@TimeSheetFragment)
            btnOutDay.setOnClickListener(this@TimeSheetFragment)
            btnHistoryLastWeek.setOnClickListener(this@TimeSheetFragment)
            btnHistoryNextWeek.setOnClickListener(this@TimeSheetFragment)
            cbExpend.setOnCheckedChangeListener { _, isChecked ->
                viewModel.updateAllExpanded(isChecked)
                cbExpend.text = if (isChecked) getString(R.string.timesheet_collapse_all) else getString(R.string.timesheet_expand_all)
            }
            tvTodayDate.text = TimeUtil.getDateToWeekString(System.currentTimeMillis())
            tvTodayWorkHours.text = getString(R.string.timesheet_time_count, "0:00")
            rcvTodayPunch.layoutManager = LinearLayoutManager(activity)
            rcvTodayPunch.adapter = todayPunchDataAdapter
            rcvHistory.layoutManager = LinearLayoutManager(activity)
            rcvHistory.adapter = punchHistoryAdapter
            viewModel.dataState.infoClockEmployeeViewEntry?.run {
                tvUserName.text = "$firstName $lastName"
                tvEmployeeId.text = getString(R.string.timesheet_employee_id, employeeId)
            }

        }
        initEvent()
        onRefresh()
    }

    override fun ReactiveViewScope.subscribeToUiState() {

    }

    override fun onRefresh() {
        viewModel.refreshData(true)
    }

    override fun onClick(v: View?) {
        if (!doubleClick()) return
        when (v?.id) {
            R.id.btn_more -> showMorePunchBtnPopWindow(v, viewModel.dataState.curPunchType)
            R.id.btn_in_day -> checkFacilityIPToPunch(PunchTypeEntry.IN_DAY, false)
            R.id.btn_in_lunch -> checkFacilityIPToPunch(PunchTypeEntry.IN_LUNCH, false)
            R.id.btn_out_lunch -> checkFacilityIPToPunch(PunchTypeEntry.OUT_LUNCH, false)
            R.id.btn_out_day -> checkFacilityIPToPunch(PunchTypeEntry.OUT_DAY, false)
            R.id.btn_history_last_week -> viewModel.loadLastWeekHistory()
            R.id.btn_history_next_week -> viewModel.loadNextWeekHistory()
        }
    }

    private fun initEvent() {
        onEvent<TimeSheetEvent.StartTakePhotoEvent> {
            startTakePhotoDialog(type)
        }
        onEvent<TimeSheetEvent.PunchTodayHistoryEvent> {
            setPunchBtnStatusOfType()
            binding?.apply {
                tvTodayWorkHours.text = getString(R.string.timesheet_time_count, TimeUtil.formatTimeScale(viewModel.getTodayWorkHours()))
                todayPunchDataAdapter.setDiffList(list)
                if (list.safeCount() <= 0) {
                    vLineBtnBottom.visibility = View.INVISIBLE
                } else {
                    vLineBtnBottom.visibility = View.VISIBLE
                    launchScope {
                        delay(500)
                        rcvTodayPunch.smoothScrollToPosition(list!!.lastIndex)
                    }
                }
            }
        }
        onEvent<TimeSheetEvent.PunchWeekCycleEvent> {
            binding?.apply {
                tvHistoryCycle.text = string
                btnHistoryNextWeek.isEnabled = !viewModel.isTodayInCurHistoryWeek()
            }

        }
        onEvent<TimeSheetEvent.PunchHistoryEvent> {
            punchHistoryAdapter.setDiffList(list)
            if (!isInitAllExpand) return@onEvent
            binding?.apply {
                cbExpend.isChecked = false
            }
        }

        onEvent<TimeSheetEvent.PunchSuccessEvent> {
            binding?.apply {
                ivClockStatus.setImageResource(R.drawable.ic_suc_green_26)
                tvClockStatus.text = tips
                llClockStatus.setBackgroundColor(ContextCompat.getColor(context!!, R.color.accent_purple))
                activity?.run {
                    tvClockStatus.setTextColor(ContextCompat.getColor(this, R.color.white))
                }
                punchResultStatusCountdown()
            }
            context?: return@onEvent
            val userInfoEntity = LocalPersistence.getIamEntity(context!!)
            if (type == PunchTypeEntry.IN_DAY || type == PunchTypeEntry.IN_LUNCH
                && userInfoEntity.enableSkipWorkerAssignment()
                && userInfoEntity.isNeedOtControl()) {// User of pay type hourly
                val allowOTControl = HttpService.getFacility()?.facilitySettingEntity?.allowOTControl?: false
                OtCountDownTimerHandler.getInstance().createCountDownTimerOrRemove(context!!, allowOTControl, idmUserId)
            }
        }

        onEvent<TimeSheetEvent.ClockErrEvent> {
            binding?.apply {
                ivClockStatus.setImageResource(R.drawable.ic_error_red_26)
                tvClockStatus.text = error ?: getString(R.string.timesheet_clock_fail)
                activity?.run {
                    tvClockStatus.setTextColor(ContextCompat.getColor(this, R.color.take_photo_btn_red))
                }
                llClockStatus.setBackgroundColor(ContextCompat.getColor(context!!, R.color.extended_red_100))
                punchResultStatusCountdown()
            }
        }
    }

    private fun checkFacilityIPToPunch(type: PunchTypeEntry, isAutoClockIn: Boolean) {
        viewModel.checkFacilityIPToPunch(type, NetWorkUtil.getIpAddress(activity), isAutoClockIn)
    }


    private fun setPunchBtnStatusOfType() {
        binding?.apply {
            when (viewModel.dataState.curPunchType) {
                PunchTypeEntry.IN_DAY -> {
                    btnInDay.visibility = View.VISIBLE
                    btnOutLunch.visibility = View.GONE
                    btnInLunch.visibility = View.GONE
                    btnOutDay.visibility = View.GONE
                }

                PunchTypeEntry.OUT_LUNCH -> {
                    btnInDay.visibility = View.GONE
                    btnOutLunch.visibility = View.VISIBLE
                    btnInLunch.visibility = View.GONE
                    btnOutDay.visibility = View.VISIBLE
                }

                PunchTypeEntry.IN_LUNCH -> {
                    btnInDay.visibility = View.GONE
                    btnOutLunch.visibility = View.GONE
                    btnInLunch.visibility = View.VISIBLE
                    btnOutDay.visibility = View.GONE
                }

                PunchTypeEntry.OUT_DAY -> {
                    btnInDay.visibility = View.GONE
                    btnOutLunch.visibility = View.GONE
                    btnInLunch.visibility = View.GONE
                    btnOutDay.visibility = View.VISIBLE
                }

                else -> {
                    btnInDay.visibility = View.GONE
                    btnOutLunch.visibility = View.GONE
                    btnInLunch.visibility = View.GONE
                    btnOutDay.visibility = View.GONE
                }
            }
        }
    }

    private fun showMorePunchBtnPopWindow(v: View, type: PunchTypeEntry) {
        val list = when (type) {
            PunchTypeEntry.IN_DAY -> {
                listOf(
                    getString(R.string.timesheet_out_lunch), getString(R.string.timesheet_in_lunch), getString(R.string.timesheet_out_day))
            }

            PunchTypeEntry.OUT_LUNCH -> {
                listOf(getString(R.string.timesheet_in_day), getString(R.string.timesheet_in_lunch))
            }

            PunchTypeEntry.IN_LUNCH -> {
                listOf(
                    getString(R.string.timesheet_in_day), getString(R.string.timesheet_out_lunch), getString(R.string.timesheet_out_day))
            }

            PunchTypeEntry.OUT_DAY -> {
                listOf(
                    getString(R.string.timesheet_in_day), getString(R.string.timesheet_out_lunch), getString(R.string.timesheet_in_lunch))
            }
        }
        val listPop = ListPopupWindow(activity, list)
        listPop.setOnItemListener { adapter, _, position ->
            listPop.dismiss()
            showPromptDialog(
                type, when (adapter.getItem(position)) {
                    getString(R.string.timesheet_in_day) -> PunchTypeEntry.IN_DAY
                    getString(R.string.timesheet_out_lunch) -> PunchTypeEntry.OUT_LUNCH
                    getString(R.string.timesheet_in_lunch) -> PunchTypeEntry.IN_LUNCH
                    getString(R.string.timesheet_out_day) -> PunchTypeEntry.OUT_DAY
                    else -> null
                })
        }
        listPop.showAsDropDown(v)
    }

    private fun showPromptDialog(curType: PunchTypeEntry, toType: PunchTypeEntry?) {
        toType?.let {
            activity?.let {
                if (promptDialog == null) {
                    promptDialog = GeneralPromptDialog(it)
                }
                promptDialog?.run {
                    setTitleStatus(false)
                    setTitleText(
                        getString(
                            R.string.timesheet_miss_punch_type_title, when (curType) {
                                PunchTypeEntry.IN_DAY -> getString(R.string.timesheet_in_day)
                                PunchTypeEntry.OUT_LUNCH -> getString(R.string.timesheet_out_lunch) + "," + getString(
                                    R.string.timesheet_out_day)

                                PunchTypeEntry.IN_LUNCH -> getString(R.string.timesheet_in_lunch)
                                PunchTypeEntry.OUT_DAY -> getString(R.string.timesheet_out_day)
                                else -> ""
                            }))
                    setContentText(
                        getString(
                            R.string.timesheet_miss_punch_type_content, when (toType) {
                                PunchTypeEntry.IN_DAY -> getString(R.string.timesheet_in_day)
                                PunchTypeEntry.OUT_LUNCH -> getString(R.string.timesheet_out_lunch)
                                PunchTypeEntry.IN_LUNCH -> getString(R.string.timesheet_in_lunch)
                                PunchTypeEntry.OUT_DAY -> getString(R.string.timesheet_out_day)
                                else -> ""
                            }))
                    setClickCancelListener {
                        dismiss()
                    }
                    setClickConfirmListener {
                        checkFacilityIPToPunch(toType, false)
                        dismiss()
                    }
                    show()
                }
            }
        }
    }

    private fun startTakePhotoDialog(type: PunchTypeEntry) {
        val takePhotoDialog = TakePhotoDialog()
        takePhotoDialog.setOnTakePhotoResultCallBack(object : TakePhotoLayout.OnTakePhotoResultCallBack {
            override fun onTakePhotoResult(data: ByteArray?, photoRect: Rect?, parentWidth: Int, parentHeight: Int) {
                takePhotoDialog.dismiss()
                viewModel.startPunch(type, NetWorkUtil.getIpAddress(activity), data, photoRect, parentWidth, parentHeight)
            }
        })
        takePhotoDialog.show(childFragmentManager, takePhotoDialog::class.java.simpleName)
    }

    private fun punchResultStatusCountdown() {
        coroutineScope.launch {
            binding?.apply {
                llClockStatus.visibility = View.VISIBLE
                delay(3000)
                llClockStatus.visibility = View.GONE
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        coroutineScope.cancel()
    }
}