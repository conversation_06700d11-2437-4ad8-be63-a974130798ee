package com.unis.wms.home

import android.content.Context
import android.text.TextUtils
import com.customer.widget.common.safeCount
import com.customer.widget.util.GlobalConfig
import com.linc.platform.core.LocalPersistence
import com.linc.platform.home.UpdateApi
import com.linc.platform.home.VersionEntry
import com.linc.platform.http.AppLocationInterceptor
import com.linc.platform.http.DownloadInterceptor
import com.linc.platform.http.HttpService
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.NotificationTypeEntry
import com.linc.platform.idm.model.SystemNotificationSearchEntry
import com.linc.platform.infoclock.InfoClockApi
import com.linc.platform.infoclock.model.PunchInHistoryReqEntry
import com.linc.platform.infoclock.model.base.BaseInfoClockRequest
import com.linc.platform.utils.Logger
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.TimeUtil
import com.linc.platform.utils.UpdateManager
import com.unis.platform.warehouse.model.WarehouseServerQueryEntity
import com.unis.platform.warehouse.model.WarehouseServerStatus
import com.unis.platform.warehouse.model.WarehouseServerType
import com.unis.platform.wcs.WcsLocalManager
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.facilityId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.wms.common.server.WarehouseServerRepository
import com.unis.wms.pick_to_light.base.LightManager
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.observers.DisposableObserver
import io.reactivex.rxjava3.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import okhttp3.ResponseBody
import retrofit2.Response
import java.math.RoundingMode
import java.util.Timer
import java.util.TimerTask

private const val FILE_NAME = "wms-debug.apk"

class HomeViewModel(
    private var repository: WorkStageRepo = WorkStageRepo(""),
    initialState: HomeState,
    initialUiState: HomeStateUiState = HomeStateUiState(),
) : ReactiveViewModel<HomeState, HomeStateUiState>(initialState, initialUiState) {
    private var timer: Timer? = null
    private val updateDownloadApi = if (TextUtils.isEmpty(dataState.defaultMainServerUrl)) HttpService.createService(UpdateApi::class.java,
        DownloadInterceptor { current: Long, length: Long, isDone: Boolean ->
            updateProgressStep(current, length, isDone)
        }) else HttpService.createService(dataState.defaultMainServerUrl,
        UpdateApi::class.java,
        DownloadInterceptor { current: Long, length: Long, isDone: Boolean ->
            updateProgressStep(current, length, isDone)
        })

    private val warehouseRepo = WarehouseServerRepository()

    init {
        repository = WorkStageRepo(dataState.defaultMainServerUrl)
        getPickToLightLocalConnectServer()
//        initTimer()
    }

    fun updateNavigationBarMap(map: Map<String, Int>) {
        setDataState { copy(navigationBarMap = map) }
    }

    fun getValueInNavigationBarMap(key: String) = dataState.navigationBarMap[key]

    fun checkUpdate(supportUpdate: Boolean, versionCode: Int) {
        if (supportUpdate) {
            fireEvent { HomeEvent.ShowUnSupportUpdateDialog() }
            return
        }
        launch {
            setUiState { copy(enableCheckUpdate = false) }
            requestAwait(repository.checkUpdate()).onSuccess { versionEntry ->
                when {
                    versionEntry != null && versionCode < versionEntry.versionCode -> {
                        val confirm = awaitEvent { HomeEvent.ShowUpdateDialog() } ?: false
                        if (confirm) {
                            downLoadNewVersion(versionEntry)
                        } else {
                            setUiState { copy(enableCheckUpdate = true) }
                        }
                    }
                    else -> setUiState { copy(enableCheckUpdate = true) }
                }
            }
        }
    }


    private fun downLoadNewVersion(versionEntry: VersionEntry) {
        var name = FILE_NAME
        if (!TextUtils.isEmpty(versionEntry.name)) {
            name = versionEntry.name
        }
        launch {
            updateDownloadApi.downloadFile(name).map { responseBody: Response<ResponseBody?> ->
                UpdateManager.writeFileToSD(GlobalConfig.getUpdateFilePath(ResUtil.getContext()), responseBody.body())
            }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(object : DisposableObserver<Boolean?>() {
                override fun onComplete() {
                    setUiState { copy(enableCheckUpdate = true) }
                }

                override fun onError(e: Throwable) {
                    Logger.handleException(e)
                }

                override fun onNext(aBoolean: Boolean) {
                    if (aBoolean) {
                        fireEvent { HomeEvent.LaunchInstall(fileName = GlobalConfig.getUpdateFilePath(ResUtil.getContext())) }
                    }
                }
            })
        }
    }

    private fun updateProgressStep(current: Long, length: Long, isDone: Boolean) {
        launch(context = Dispatchers.Main) {
            fireEvent { HomeEvent.ShowDownloadProgressDialog(current, length, isDone) }
        }
    }

    fun getSystemNotifications() {
        launch {
            val search = SystemNotificationSearchEntry()
            search.notificationType = NotificationTypeEntry.RELEASE
            requestAwait(repository.getSystemNotifications(search)).onSuccess {
                if (it.isNullOrEmpty()) {
                    return@launch
                }
                val last = it.last()
                val releaseNoteVersion = LocalPersistence.getReleaseNoteVersion(ResUtil.getContext())
                if (!TextUtils.equals(releaseNoteVersion, last.version)) {
                    fireEvent { HomeEvent.ShowReleaseNoteDialog(last) }
                }
            }
        }
    }

    private fun getPickToLightLocalConnectServer() {
        launch {
            val serverResults = requestAwait(warehouseRepo.search(WarehouseServerQueryEntity().apply {
                types = listOf(WarehouseServerType.PICK_TO_LIGHT, WarehouseServerType.WCS)
                status = WarehouseServerStatus.ACTIVE
                facilityId = warehouseRepo.facilityId
            })).getOrNull()

            if (!serverResults.isNullOrEmpty()) {
                val pickToLightServer = serverResults.find { it.type == WarehouseServerType.PICK_TO_LIGHT }
                LightManager.savePickToLightServer(pickToLightServer)
                val wcsServer = serverResults.find { it.type == WarehouseServerType.WCS }
                WcsLocalManager.saveWcsServer(wcsServer)
            } else {
                LightManager.savePickToLightServer(null)
                WcsLocalManager.saveWcsServer(null)
            }
        }
    }

    fun isUserFromInfoClock(context: Context): Boolean {
        return LocalPersistence.getEmployeeInfoEntry(context) != null
    }

    fun isRoleOnlyPunch(): Boolean {
        val userRoles = dataState.userInfo?.userRoles
        return userRoles.safeCount() == 1 && null != userRoles?.find { TextUtils.equals(it.name, "Punch") }
    }

    fun changeCurrentFragment(position: Int) {
        fireEvent { HomeEvent.ChangeCurrentFragment(position) }
    }

    private fun initTimer() {
        LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()) ?: return
        timer = Timer()
        timer?.schedule(object : TimerTask() {
            override fun run() {
                getPunchHistory()
            }
        }, 0, 5 * 1000)
    }

    override fun onCleared() {
        super.onCleared()
        timer?.cancel()
    }

    private fun getPunchHistory() {
        val employeeInfoEntry = LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()) ?: return
        val weekDateList = TimeUtil.getWeekDateList(System.currentTimeMillis())
        val reqEntry = BaseInfoClockRequest(
            PunchInHistoryReqEntry(
                employeeInfoEntry.employeeId,
                TimeUtil.getDateToString(weekDateList.first(), TimeUtil.FORMAT_YYYY_MM_DD),
                TimeUtil.getDateToString(weekDateList.last(), TimeUtil.FORMAT_YYYY_MM_DD),
                TimeUtil.getTimeZoneOffsetHrs()))
        request(repository.getEmployeePunchHistory(reqEntry), showLoading = false, success = {
            val list = it?.filter { entry ->
                TimeUtil.getDateToString(
                    System.currentTimeMillis(),
                    TimeUtil.FORMAT_MM_DD_YYYY) == entry.punchDay && entry.firstName == employeeInfoEntry.firstName && entry.lastName == employeeInfoEntry.lastName
            }?.toList()
            val todayWorkHours = list?.sumOf { history -> history.getWorkHour() }?.toBigDecimal()?.setScale(2, RoundingMode.DOWN)?.toDouble() ?: 0.00
        })
    }
}


class WorkStageRepo(defaultMainServerUrl: String) : BaseRepository() {
    private val updateApi = if (TextUtils.isEmpty(defaultMainServerUrl)) HttpService.createService(UpdateApi::class.java)
    else HttpService.createService(defaultMainServerUrl, UpdateApi::class.java, listOf())

    private val idmApi = HttpService.createService(IdmApi::class.java, AppLocationInterceptor.getInstance())

    private val infoClockApi = HttpService.createInfoClockService(InfoClockApi::class.java, -1)

    fun checkUpdate() = rxRequest(updateApi.loadVersion())

    fun getSystemNotifications(search: SystemNotificationSearchEntry) = rxRequest(idmApi.getSystemNotifications(search))

    fun getEmployeePunchHistory(request: BaseInfoClockRequest<PunchInHistoryReqEntry>) = rxRequestInfoClock(infoClockApi.getEmployeePunchHistory(request))
}


