package com.unis.wms.home

import android.content.Context
import android.content.Intent
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.MainEnv
import com.linc.platform.core.PermissionManager
import com.linc.platform.http.HttpService
import com.linc.platform.http.TokenInfoEntity
import com.linc.platform.idm.model.PermissionEntry
import com.unis.platform.common.model.company.CompanyEntity
import com.unis.platform.db.WMSDBManager
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.task_action.utils.TaskActionUtil
import com.unis.wms.AppContext.startService
import com.unis.wms.BuildConfig
import com.unis.wms.common.UserPermissionHelper
import com.unis.wms.fcm.RegistrationService
import com.unis.wms.login.LoginActivity
import com.unis.wms.ot_control.OtCountDownTimerHandler

class ToHomeHandler private constructor(){

    companion object {
        fun getHandler() = ToHomeHandler()
    }

    fun toHome(context: Context, loginResultEntity: LoginResultEntity, toHomeCallback: () -> Unit) {
        val userId = loginResultEntity.userInfo.userId
        LocalPersistence.setIamEntity(context, loginResultEntity.userInfo)
        updateUserCompany(loginResultEntity.userInfo)
        updateUserFacility(loginResultEntity.userInfo)
        resetChromeHttpTrackByPermission()
        val tokenInfoEntity = TokenInfoEntity(
            accessToken = loginResultEntity.accessToken, refreshToken = loginResultEntity.refreshToken, expiresAt = loginResultEntity.expiresAt)
        updateToken(context, tokenInfoEntity)
        updateUserId(context, userId)
        toHome(context, toHomeCallback)
    }

    private fun toHome(context: Context, toHomeCallback: () -> Unit) {
        MainEnv.updateLoginIntent(getLoginIntent(context))
        val intent = Intent()
        intent.setClass(context, HomeActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        toHomeCallback.invoke()
    }

    private fun getLoginIntent(context: Context): Intent {
        return Intent(context, LoginActivity::class.java)
    }

    private fun updateUserCompany(userInfoEntity: UserInfoEntity) {
        val companyEntity = getLoginCompanyAndUpdate(userInfoEntity)
        HttpService.updateCompanyIds(companyEntity.id)
    }

    private fun getLoginCompanyAndUpdate(userInfoEntity: UserInfoEntity): CompanyEntity {
        val userId = userInfoEntity.userId
        val localCompany = WMSDBManager.companyManager.getOneByUserId(userId)
        return if (localCompany == null) {
            val companyEntity = CompanyEntity(userId = userId, id = userInfoEntity.companyCode, name = "")
            WMSDBManager.companyManager.insertByUserId(userId, companyEntity)
            companyEntity
        } else {
            localCompany
        }
    }

    private fun updateUserFacility(userInfoEntity: UserInfoEntity) {
        val facilityEntity = getLoginFacilityAndUpdate(userInfoEntity)?: return
        updateHttpFacilitySet(facilityEntity)
    }

    private fun getLoginFacilityAndUpdate(userInfoEntity: UserInfoEntity): FacilityEntity? {
        val userId = userInfoEntity.userId
        val localFacility = WMSDBManager.facilityManager.getOneByUserId(userId)
        return if (localFacility == null) {
            getMatchFacilityWithInsetDB(userInfoEntity)
        } else {
            val matchFacilityId = userInfoEntity.profile?.facilityIds?.find { it == localFacility.id }
            if (matchFacilityId == null) {
                getMatchFacilityWithUpdateDB(userInfoEntity)
            } else {
                return localFacility
            }
        }
    }

    private fun updateHttpFacilitySet(facilityEntity: FacilityEntity) {
        HttpService.updateFacilityId(facilityEntity.id)
        HttpService.updateFacility(facilityEntity)
    }

    private fun getMatchFacilityWithInsetDB(userInfoEntity: UserInfoEntity): FacilityEntity? {
        val defaultFacility = getDefaultFacility(userInfoEntity)
        defaultFacility?.userId = userInfoEntity.userId
        defaultFacility?.let {
            WMSDBManager.facilityManager.insertByUserId(userInfoEntity.userId, it)
        }
        return defaultFacility
    }

    private fun getMatchFacilityWithUpdateDB(userInfoEntity: UserInfoEntity): FacilityEntity? {
        val defaultFacility = getDefaultFacility(userInfoEntity)
        defaultFacility?.userId = userInfoEntity.userId
        defaultFacility?.let {
            WMSDBManager.facilityManager.updateByUserId(userInfoEntity.userId, it)
        }
        return defaultFacility
    }

    private fun getDefaultFacility(userInfoEntity: UserInfoEntity): FacilityEntity? {
        val defaultFacility =  userInfoEntity.profile?.defaultFacility
        val userFacilityIds = userInfoEntity.profile?.facilityIds
        val defaultFacilityAvailability = defaultFacility != null && userFacilityIds?.contains(defaultFacility.id) == true
        return if (defaultFacilityAvailability) {
            defaultFacility
        } else {
            userInfoEntity.profile?.facilities?.get(0)
        }
    }

    private fun resetChromeHttpTrackByPermission() {
        if (!BuildConfig.DEBUG && PermissionManager.getInstance().hasPermission(PermissionEntry.User_CaptureHttpRequests)) {
            HttpService.addChromeHttpTracker()
        }
    }

    private fun updateToken(context: Context, token: TokenInfoEntity) {
        LocalPersistence.setTokenInfoEntity(context, token)
        HttpService.updateTokenInfo(token)
    }

    private fun updateUserId(context: Context, userId: String?) {
        userId?.let {
            LocalPersistence.saveUserId(context, it)
            registrationId(context,true)
        }
    }

    private fun registrationId(context: Context, isRegister: Boolean) {
        val intent = Intent(context, RegistrationService::class.java)
        intent.putExtra(RegistrationService.ACTION_IS_REGISTER, isRegister)
        startService(intent)
    }

    fun clearLoginData(context: Context, userId: String) {
        LocalPersistence.removeRegistrationId(context, userId)
        LocalPersistence.clearTokenInfoEntity(context)
        LocalPersistence.clearUserName(context)
        OtCountDownTimerHandler.getInstance().removeAllCountDownTimer()
        LocalPersistence.clearUserCapabilities(context, userId)
        TaskActionUtil.clearUserCapability()
        LocalPersistence.clearUserId(context)
        HttpService.updateCompanyIds("")
        UserPermissionHelper.getInstance().clearPermissions()
        LocalPersistence.setBackEndTrackLog(context, false)
        LocalPersistence.setEmployeeInfoEntry(context, null)
        LocalPersistence.setLastPickedLocationId(context, null)
    }
}