package com.unis.wms.home

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.ContextCompat
import androidx.viewpager.widget.ViewPager
import com.aurelhubert.ahbottomnavigation.AHBottomNavigation
import com.aurelhubert.ahbottomnavigation.AHBottomNavigationItem
import com.customer.widget.GeneralAlertDialog
import com.customer.widget.VersionUpdateProgressDialog
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.core.LincBaseFragment
import com.customer.widget.core.adapter.FragmentViewPagerAdapter
import com.customer.widget.util.GlobalConfig
import com.customer.widget.util.UiUtil
import com.data_collection.DataCollectionManager
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.RxBus
import com.linc.platform.home.MAIN_NAVIGATION_BAR_ME
import com.linc.platform.home.MAIN_NAVIGATION_BAR_MESSAGES
import com.linc.platform.home.MAIN_NAVIGATION_BAR_MORE
import com.linc.platform.home.MAIN_NAVIGATION_BAR_TASKS
import com.linc.platform.home.MAIN_NAVIGATION_BAR_TIMESHEET
import com.linc.platform.home.MainNavigationBarItemEvent
import com.linc.platform.home.MainNotificationRefreshEvent
import com.linc.platform.utils.PermissionUtil
import com.linc.platform.utils.UpdateManager
import com.unis.reactivemvi.common.collectDispatchToDeferred
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onDeferredEvent
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.lifecycleScope
import com.unis.wms.BuildConfig
import com.unis.wms.R
import com.unis.wms.databinding.ActivityHomeBinding
import com.unis.wms.home.message.HomeMessageFragment
import com.unis.wms.home.more.HomeMoreFragment
import com.unis.wms.home.profile.HomeMyProfileFragment
import com.unis.wms.home.task.HomeTaskFragment
import com.unis.wms.home.time_sheet.TimeSheetFragment
import com.unis.wms.login.LoginActivity
import com.unis.wms.uitl.EnvUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers

class HomeActivity : ReactiveActivity<HomeViewModel, HomeStateUiState, ActivityHomeBinding>() {

    companion object {
        const val HOME_PAGE_POSITION = "home_page_position"
        const val TIMESHEET_HOME_POSITION = 2
    }

    private lateinit var pagerAdapter: FragmentViewPagerAdapter
    private val versionUpdateProgressDialog by lazy { VersionUpdateProgressDialog(this) }
    private var disposable: Disposable? = null
    private var currentItemDisposable: Disposable? = null
    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(HomeStateUiState::company) {

        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        if (LocalPersistence.getTokenInfoEntity(applicationContext) == null) {
            val intent = Intent(applicationContext, LoginActivity::class.java)
            startActivity(intent)
            finish()
            return
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        }
        PermissionUtil.requestAllPermission(this)
        initUi()
        currentItemDisposable =
            RxBus.getDefault(MainNavigationBarItemEvent::class.java).toFlowable().subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe { event: MainNavigationBarItemEvent -> this.setCurrentFragment(event) }

        disposable =
            RxBus.getDefault(MainNotificationRefreshEvent::class.java).toFlowable().subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe { event: MainNotificationRefreshEvent ->
                    this.refreshNotification(event)
                } //        startPeriodicWorker(120L)
        //        startService(this)
        onShowUpdateEvent()
        onEvent()
        DataCollectionManager.instance().updateDeviceInfo()
        //        viewModel.getSystemNotifications()
    }

    override fun createViewModel() = HomeViewModel(
        initialState = HomeState(
            defaultMainServerUrl = BuildConfig.DEFAULT_MAIN_SERVER_URL, userInfo = LocalPersistence.getIamEntity(this)))

    private fun refreshNotification(event: MainNotificationRefreshEvent) {
        val pos: Int? = viewModel.getValueInNavigationBarMap(event.notificationItemKey)
        if (pos != null) {
            val data = event.notificationData
            val value = if (data == null || "0" == data) "" else data
            binding.bottomNavigation.setNotification(value, pos)
        }
    }

    private fun setCurrentFragment(event: MainNavigationBarItemEvent) {
        val pos: Int? = viewModel.getValueInNavigationBarMap(event.curItemKey)
        if (pos != null) {
            binding.bottomNavigation.currentItem = pos
        }
    }

    private fun onShowUpdateEvent() = onDeferredEvent<HomeEvent.ShowUpdateDialog, Boolean?> {
        CallbackDialogV1.showConfirm(
            this@HomeActivity,
            title = getString(R.string.title_alarm),
            message = getString(R.string.text_update),
            positiveText = getString(R.string.button_ok),
            negativeText = getString(R.string.btn_cancel)).collectDispatchToDeferred(lifecycleScope, it)
    }

    private fun onEvent() {
        onEvent<HomeEvent.ShowDownloadProgressDialog> {
            if (!versionUpdateProgressDialog.isShowing()) {
                versionUpdateProgressDialog.show(current, length)
            } else {
                versionUpdateProgressDialog.updateProgress(current, length)
            }

            if (isDone && versionUpdateProgressDialog.isShowing()) {
                versionUpdateProgressDialog.dismiss()
            }
        }

        onEvent<HomeEvent.LaunchInstall> {
            UpdateManager.installApkForResult(this@HomeActivity, fileName)
        }

        onEvent<HomeEvent.ShowUnSupportUpdateDialog> {
            GeneralAlertDialog.createAlertDialog(this@HomeActivity, getString(R.string.text_update_not_supported_api_under_21)).show()
        }
        onEvent<HomeEvent.ChangeCurrentFragment> {
            binding.bottomNavigation.currentItem = index
            binding.viewpager.currentItem = index
        }
        onEvent<HomeEvent.ShowReleaseNoteDialog> {
            ReleaseNoteDialog.newInstance(systemNotificationResultEntry).show(supportFragmentManager, ReleaseNoteDialog::class.java.name)
        }
    }


    private fun initUi() {
        pagerAdapter = FragmentViewPagerAdapter(supportFragmentManager, this)
        val isUserFromInfoClock = viewModel.isUserFromInfoClock(this)
        val roleOnlyPunch = viewModel.isRoleOnlyPunch()
        val tabMap = mutableMapOf<String, Int>()
        if (isUserFromInfoClock && roleOnlyPunch) {
            pagerAdapter.addItem(TimeSheetFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_TIMESHEET] = pagerAdapter.count - 1
            pagerAdapter.addItem(HomeMyProfileFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_ME] = pagerAdapter.count - 1
        } else {
            pagerAdapter.addItem(HomeTaskFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_TASKS] = pagerAdapter.count - 1
            pagerAdapter.addItem(HomeMyProfileFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_ME] = pagerAdapter.count - 1
            if (isUserFromInfoClock) {
                pagerAdapter.addItem(TimeSheetFragment.newInstance())
                tabMap[MAIN_NAVIGATION_BAR_TIMESHEET] = pagerAdapter.count - 1
            }
            pagerAdapter.addItem(HomeMessageFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_MESSAGES] = pagerAdapter.count - 1
            pagerAdapter.addItem(HomeMoreFragment.newInstance())
            tabMap[MAIN_NAVIGATION_BAR_MORE] = pagerAdapter.count - 1
        }
        viewModel.updateNavigationBarMap(tabMap)
        val bottomNavigationItems = mutableListOf<AHBottomNavigationItem>()

        if (isUserFromInfoClock && roleOnlyPunch) {
            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.nav_title_timesheet, R.drawable.ic_nav_timesheet_blue_24dp, R.color.color_373737))

            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.home_my_profile, R.drawable.ic_baseline_person_outline_24, R.color.color_373737))
        } else {
            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.nav_title_tasks, R.drawable.ic_baseline_list_alt_24, R.color.color_373737))

            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.home_my_profile, R.drawable.ic_baseline_person_outline_24, R.color.color_373737))

            if (isUserFromInfoClock) {
                bottomNavigationItems.add(
                    AHBottomNavigationItem(
                        R.string.nav_title_timesheet, R.drawable.ic_nav_timesheet_blue_24dp, R.color.color_373737))
            }
            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.message_group_topic, R.drawable.ic_baseline_chat_24, R.color.color_373737))
            bottomNavigationItems.add(
                AHBottomNavigationItem(
                    R.string.nav_title_more, R.drawable.ic_baseline_more_horiz_24, R.color.color_373737))
        }
        binding.apply {
            viewpager.offscreenPageLimit = 5
            viewpager.currentItem = 0
            viewpager.adapter = pagerAdapter
            bottomNavigation.addItems(bottomNavigationItems)
            bottomNavigation.setColoredModeColors(
                ContextCompat.getColor(this@HomeActivity, R.color.colorAccent), ContextCompat.getColor(this@HomeActivity, R.color.colorTextSecondary))
            bottomNavigation.isColored = true
            bottomNavigation.titleState = AHBottomNavigation.TitleState.ALWAYS_SHOW
            bottomNavigation.setOnTabSelectedListener { position: Int, _: Boolean ->
                viewpager.setCurrentItem(position, false)
                true
            }
            viewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {

                }

                override fun onPageSelected(position: Int) {
                    bottomNavigation.currentItem = position
                    val item = pagerAdapter.getItem(position) as LincBaseFragment
                    item.forceRefresh()
                }

                override fun onPageScrollStateChanged(p0: Int) {
                }

            })
            val homePageInitPosition = intent.getIntExtra(HOME_PAGE_POSITION, -1)
            if (homePageInitPosition != -1 && homePageInitPosition < pagerAdapter.count) {
                bottomNavigation.currentItem = homePageInitPosition
            } else {
                bottomNavigation.currentItem = 0
            }
        }
    }

    override fun onResume() {
        super.onResume()
        UiUtil.showOverlayPermissionDialog(this)
        if (!BuildConfig.DEBUG && EnvUtil.isSameEnvAndService() && viewModel.uiState.enableCheckUpdate) {
            viewModel.checkUpdate(Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP, BuildConfig.VERSION_CODE)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UpdateManager.REQUEST_CODE_PACKAGE_INSTALL) {
            UpdateManager.installApk(this@HomeActivity, GlobalConfig.getUpdateFilePath(this@HomeActivity))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        disposable?.apply {
            if (!isDisposed) {
                dispose()
            }
        }
        currentItemDisposable?.apply {
            if (!isDisposed) {
                dispose()
            }
        } //        val intent = Intent(this, VoiceService::class.java)
        stopService(intent)
    }
}