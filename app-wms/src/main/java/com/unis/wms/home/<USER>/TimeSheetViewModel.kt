package com.unis.wms.home.time_sheet

import android.graphics.Rect
import android.text.TextUtils
import com.customer.widget.util.ImageUtils
import com.linc.platform.core.LocalPersistence
import com.linc.platform.http.HttpService
import com.linc.platform.infoclock.InfoClockApi
import com.linc.platform.infoclock.model.PunchInDetailEntry
import com.linc.platform.infoclock.model.PunchInHistoryEntry
import com.linc.platform.infoclock.model.PunchInHistoryReqEntry
import com.linc.platform.infoclock.model.PunchInReqEntry
import com.linc.platform.infoclock.model.PunchInResultEntry
import com.linc.platform.infoclock.model.PunchTypeEntry
import com.linc.platform.infoclock.model.base.BaseInfoClockRequest
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.TimeUtil
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.wms.R
import java.math.BigDecimal
import java.util.Calendar

/**
 * <AUTHOR> on 2024/12/3
 */
class TimeSheetViewModel(initialDataState: TimeSheetDataState) : SimpleReactiveViewModel<TimeSheetDataState>(initialDataState) {
    private val repository by lazy { TimeSheetRepository() }

    init {
        fireEvent { TimeSheetEvent.PunchWeekCycleEvent(getWeekCycleToString()) }
    }


    fun updateExpanded(punchInHistory: PunchInHistoryEntry?) {
        punchInHistory ?: return
        val copy = punchInHistory.copy(isExpend = !punchInHistory.isExpend)
        val mutableList = dataState.punchHistories.toMutableList()
        val indexOf = mutableList.indexOf(punchInHistory)
        if (indexOf < 0) return
        mutableList[indexOf] = copy
        launch {
            setDataStateAwait { copy(punchHistories = mutableList) }
            fireEvent { TimeSheetEvent.PunchHistoryEvent(mutableList, false) }
        }
    }

    fun updateAllExpanded(isExpend: Boolean) {
        val list = dataState.punchHistories.map {
            val copy = it.copy(isExpend = isExpend)
            copy
        }.toList()
        launch {
            setDataStateAwait { copy(punchHistories = list) }
            fireEvent { TimeSheetEvent.PunchHistoryEvent(list, false) }
        }
    }

    fun loadLastWeekHistory() {
        val lastDayTime = TimeUtil.getLastDayTime(dataState.curPunchWeekCycle.first())
        requestPunchHistory(TimeUtil.getWeekDateList(lastDayTime), true)
    }

    fun loadNextWeekHistory() {
        val nextDayTime = TimeUtil.getNextDayTime(dataState.curPunchWeekCycle.last())
        requestPunchHistory(TimeUtil.getWeekDateList(nextDayTime), true)
    }

    private fun requestPunchHistory(weekCycle: List<Long>, showLoading: Boolean) {
        val reqEntry = BaseInfoClockRequest(
            PunchInHistoryReqEntry(
                dataState.infoClockEmployeeViewEntry?.employeeId,
                TimeUtil.getDateToString(weekCycle.first(), TimeUtil.FORMAT_YYYY_MM_DD),
                TimeUtil.getDateToString(weekCycle.last(), TimeUtil.FORMAT_YYYY_MM_DD),
                TimeUtil.getTimeZoneOffsetHrs()))

        launch {
            val result = requestAwait(repository.getEmployeePunchHistory(reqEntry), showLoading, error = null)
            val punchHistory = result.getOrNull()
            setDataStateAwait { copy(punchHistory = punchHistory) }
            parsePunchHistory(weekCycle)
            parseTodayPunchDataAndCheckAutoPunch()
        }
    }

    private suspend fun parsePunchHistory(weekCycle: List<Long>) {
        setDataStateAwait { copy(curPunchWeekCycle = weekCycle) }
        fireEvent { TimeSheetEvent.PunchWeekCycleEvent(getWeekCycleToString()) }
        val inHistoryList: ArrayList<PunchInHistoryEntry> = ArrayList()
        for (date in weekCycle) {
            val history = PunchInHistoryEntry(date)
            dataState.punchHistory?.run {
                history.punchInInList = filter { entry ->
                    TimeUtil.getDateToString(
                        date,
                        TimeUtil.FORMAT_MM_DD_YYYY) == entry.punchDay && entry.firstName == dataState.infoClockEmployeeViewEntry?.firstName && entry.lastName == dataState.infoClockEmployeeViewEntry?.lastName
                }
            }
            inHistoryList.add(history)
        }
        launch {
            setDataStateAwait { copy(punchHistories = inHistoryList) }
            fireEvent { TimeSheetEvent.PunchHistoryEvent(inHistoryList, true) }
        }

    }

    private fun parseTodayPunchDataAndCheckAutoPunch() {
        if (isTodayInCurHistoryWeek()) {
            val list = dataState.punchHistory?.filter { entry ->
                TimeUtil.getDateToString(
                    System.currentTimeMillis(),
                    TimeUtil.FORMAT_MM_DD_YYYY) == entry.punchDay && entry.firstName == dataState.infoClockEmployeeViewEntry?.firstName && entry.lastName == dataState.infoClockEmployeeViewEntry?.lastName
            }?.toList()
            list?.lastOrNull()?.punchOutType?.let {
                setDataState { copy(curPunchType = if (it == PunchTypeEntry.OUT_DAY) PunchTypeEntry.IN_DAY else PunchTypeEntry.IN_LUNCH) }
            } ?: list?.lastOrNull()?.punchInType?.let {
                setDataState { copy(curPunchType = if (it == PunchTypeEntry.IN_DAY) PunchTypeEntry.OUT_LUNCH else PunchTypeEntry.OUT_DAY) }
            }
            launch {
                setDataStateAwait { copy(todayPunchHistory = list) }
                fireEvent { TimeSheetEvent.PunchTodayHistoryEvent(list) }
            }

        }
    }


    fun checkFacilityIPToPunch(type: PunchTypeEntry, ipAddress: String?, isAutoClockIn: Boolean) {
        setDataState { copy(infoClockFacility = null) }
        launch {
            val result = requestAwait(repository.getFacilityByIP(ipAddress), !isAutoClockIn, error = {
                setDataState {
                    copy(
                        isIpCheckSuccess = false, ipCheckError = if (!TextUtils.isEmpty(it.error) && it.error.contains("Ip")) it.error.replace(
                            "Not found facility ", "") else "")
                }
                checkFacilityCodeToPunch(type, isAutoClockIn)
            })
            if (result.isSuccess) {
                result.getOrNull()?.let {
                    setDataStateAwait { copy(infoClockFacility = it, isIpCheckSuccess = true) }
                    if (isNeedTakePhoto(type)) {
                        fireEvent { TimeSheetEvent.StartTakePhotoEvent(type) }
                    } else {
                        startPunch(type, ipAddress)
                    }
                }
            }
        }
    }

    private fun checkFacilityCodeToPunch(type: PunchTypeEntry, isAutoClockIn: Boolean) {
        setDataState { copy(infoClockFacility = null) }
        launch {
            val facilityCode = LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()).facilityCode
            val result = requestAwait(repository.checkCanPunchByFacilityCode(facilityCode), !isAutoClockIn, error = {
                fireEvent { TimeSheetEvent.ClockErrEvent(getFacilityCheckByCodeError(it.error)) }
            })
            if (result.isSuccess) {
                result.getOrNull()?.let {
                    setDataStateAwait { copy(infoClockFacility = it) }
                    if (isNeedTakePhoto(type)) {
                        fireEvent { TimeSheetEvent.StartTakePhotoEvent(type) }
                    } else {
                        startPunch(type, null)
                    }
                }
            }
        }
    }


    private fun startPunch(type: PunchTypeEntry, ipAddress: String?) {
        startPunch(type, ipAddress, null, null, 0, 0)
    }

    fun startPunch(type: PunchTypeEntry, ipAddress: String?, data: ByteArray?, photoRect: Rect?, parentWidth: Int, parentHeight: Int) {
        launch {
            val photoData = data?.let {
                ImageUtils.getPictureToBase64String(data, photoRect, parentWidth, parentHeight)
            }?.let {
                "data:image/jpeg;base64,$it"
            }
            val currentTimeMillis = System.currentTimeMillis()
            val infoClockEmployeeViewEntry = dataState.infoClockEmployeeViewEntry
            val punchInReqEntry = BaseInfoClockRequest(
                PunchInReqEntry(
                    infoClockEmployeeViewEntry?.employeeId,
                    infoClockEmployeeViewEntry?.firstName,
                    infoClockEmployeeViewEntry?.lastName,
                    photoData,
                    TimeUtil.getTimeZoneOffsetHrs(),
                    if (dataState.isIpCheckSuccess) ipAddress else null,
                    if (dataState.isIpCheckSuccess) null else LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()).facilityCode))
            val request = when (type) {
                PunchTypeEntry.IN_DAY -> repository.punchIn(punchInReqEntry)
                PunchTypeEntry.OUT_LUNCH -> repository.punchOutLunch(punchInReqEntry)
                PunchTypeEntry.IN_LUNCH -> repository.punchInLunch(punchInReqEntry)
                PunchTypeEntry.OUT_DAY -> repository.punchOut(punchInReqEntry)
            }
            val result = requestAwait(request, error = {
                fireEvent { TimeSheetEvent.ClockErrEvent(getFacilityCheckByCodeError(it.error)) }
            })
            if (result.isSuccess) {
                val punchResult = result.getOrNull()?.firstOrNull() ?: PunchInResultEntry(
                    TimeUtil.getTimeZoneOffsetHrs(), TimeUtil.getDateToString(currentTimeMillis, TimeUtil.FORMAT_FULL_T_FORMAT))
                val punchSucTip = ResUtil.format(
                    R.string.timesheet_punch_suc, when (type) {
                        PunchTypeEntry.IN_DAY -> ResUtil.getString(R.string.timesheet_in_day)
                        PunchTypeEntry.OUT_LUNCH -> ResUtil.getString(R.string.timesheet_out_lunch)
                        PunchTypeEntry.IN_LUNCH -> ResUtil.getString(R.string.timesheet_in_lunch)
                        PunchTypeEntry.OUT_DAY -> ResUtil.getString(R.string.timesheet_out_day)
                        else -> ""
                    }, TimeUtil.formatDataString(
                        punchResult.punchTime, TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_H_MM_A))
                fireEvent { TimeSheetEvent.PunchSuccessEvent(punchSucTip, type) }
                val punchDetail = PunchInDetailEntry(
                    infoClockEmployeeViewEntry?.employeeId,
                    if (type == PunchTypeEntry.IN_DAY || type == PunchTypeEntry.IN_LUNCH) punchResult.punchTime else null,
                    if (type == PunchTypeEntry.OUT_LUNCH || type == PunchTypeEntry.OUT_DAY) punchResult.punchTime else null,
                    TimeUtil.formatDataString(
                        punchResult.punchTime, TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_MM_DD_YYYY),
                    punchResult.timeZone.toString(),
                    infoClockEmployeeViewEntry?.firstName,
                    infoClockEmployeeViewEntry?.lastName,
                    if (type == PunchTypeEntry.IN_DAY || type == PunchTypeEntry.IN_LUNCH) type else null,
                    if (type == PunchTypeEntry.OUT_LUNCH || type == PunchTypeEntry.OUT_DAY) type else null)
                var todayPunchHistory = dataState.todayPunchHistory?.toMutableList()
                if (type == PunchTypeEntry.IN_DAY || type == PunchTypeEntry.IN_LUNCH) {
                    todayPunchHistory?.run {
                        add(punchDetail)
                    } ?: let {
                        todayPunchHistory = arrayListOf(punchDetail)
                    }
                    val curPunchType = if (type == PunchTypeEntry.IN_DAY) PunchTypeEntry.OUT_LUNCH else PunchTypeEntry.OUT_DAY
                    setDataState { copy(curPunchType = curPunchType) }
                } else {
                    todayPunchHistory?.run {
                        val lastDate = lastOrNull()
                        if (lastDate == null || !lastDate.punchOutTime.isNullOrEmpty() || (PunchTypeEntry.IN_LUNCH == lastDate.punchInType && PunchTypeEntry.OUT_LUNCH == type)) {
                            add(punchDetail)
                        } else {
                            val punch = lastDate.copy(punchOutTime = punchResult.punchTime, punchOutType = type)
                            remove(lastDate)
                            add(punch)
                        }
                    } ?: let {
                        todayPunchHistory = arrayListOf(punchDetail)
                    }
                    val curPunchType = if (type == PunchTypeEntry.OUT_LUNCH) PunchTypeEntry.IN_LUNCH else PunchTypeEntry.IN_DAY
                    setDataState { copy(curPunchType = curPunchType) }
                }
                setDataState { copy(todayPunchHistory = todayPunchHistory?.toList()) }
                awaitDataState()
                fireEvent { TimeSheetEvent.PunchTodayHistoryEvent(todayPunchHistory?.toList()) }
            }
        }
    }


    private fun getWeekCycleToString(): String {
        val c = Calendar.getInstance()
        val str = StringBuilder()
        c.timeInMillis = dataState.curPunchWeekCycle.first()
        str.append(TimeUtil.getMonthStr(c.get(Calendar.MONTH))).append(" ").append(c.get(Calendar.DAY_OF_MONTH)).append(", ").append(c.get(Calendar.YEAR))
            .append(" - ")
        c.timeInMillis = dataState.curPunchWeekCycle.last()
        str.append(TimeUtil.getMonthStr(c.get(Calendar.MONTH))).append(" ").append(c.get(Calendar.DAY_OF_MONTH)).append(", ").append(c.get(Calendar.YEAR))
        return str.toString()
    }

    fun isTodayInCurHistoryWeek(): Boolean = TimeUtil.getStringToDate(
        TimeUtil.getDayEndTime(dataState.curPunchWeekCycle.last()), TimeUtil.FORMAT_FULL_FORMAT) >= System.currentTimeMillis() && TimeUtil.getStringToDate(
        TimeUtil.getDayStartTime(dataState.curPunchWeekCycle.first()), TimeUtil.FORMAT_FULL_FORMAT) <= System.currentTimeMillis()

    private fun isNeedTakePhoto(type: PunchTypeEntry): Boolean = dataState.infoClockFacility?.let {
        if (type == PunchTypeEntry.IN_DAY || type == PunchTypeEntry.IN_LUNCH) it.requireInPhoto else it.requireOutPhoto
    } ?: true

    private fun getFacilityCheckByCodeError(errorMsg: String): String {
        var msg = errorMsg
        if (!TextUtils.isEmpty(dataState.ipCheckError)) {
            msg = """
            $errorMsg
            $dataState.ipCheckError
            """.trimIndent()
        }
        return msg.replace("InfoClockApi:", "")
    }

    fun refreshData(showRefresh: Boolean) {
        requestPunchHistory(dataState.curPunchWeekCycle, showRefresh)
    }

    fun getTodayWorkHours(): Double =
        dataState.todayPunchHistory?.sumOf { it.getWorkHour() }?.toBigDecimal()?.setScale(2, BigDecimal.ROUND_DOWN)?.toDouble() ?: 0.00
}

private class TimeSheetRepository : BaseRepository() {
    private val infoClockApi = HttpService.createInfoClockService(InfoClockApi::class.java, -1)


    fun getEmployeePunchHistory(request: BaseInfoClockRequest<PunchInHistoryReqEntry>) = rxRequestInfoClock(infoClockApi.getEmployeePunchHistory(request))

    fun getFacilityByIP(ip: String?) = rxRequestInfoClock(infoClockApi.getFacilityByIP(ip))

    fun checkCanPunchByFacilityCode(code: String?) = rxRequestInfoClock(infoClockApi.checkCanPunchByFacilityCode(code))

    fun punchIn(reqEntry: BaseInfoClockRequest<PunchInReqEntry>) = rxRequestInfoClock(infoClockApi.punchIn(reqEntry))

    fun punchOut(reqEntry: BaseInfoClockRequest<PunchInReqEntry>) = rxRequestInfoClock(infoClockApi.punchOut(reqEntry))

    fun punchInLunch(reqEntry: BaseInfoClockRequest<PunchInReqEntry>) = rxRequestInfoClock(infoClockApi.punchInLunch(reqEntry))

    fun punchOutLunch(reqEntry: BaseInfoClockRequest<PunchInReqEntry>) = rxRequestInfoClock(infoClockApi.punchOutLunch(reqEntry))
}

