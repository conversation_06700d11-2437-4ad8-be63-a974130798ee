package com.unis.wms.home.task

import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.taskcenter.model.HomeTaskBean
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

data class HomeTaskState(val idmUserId: String,val userInfo: UserInfoEntity?=null) :
    ReactiveDataState {

}


data class HomeTaskUiState(
    val currentDate: String? = null,
    val facilityName: String? = null,
    val taskResult: List<HomeTaskBean>? = null,
    val todayTask: String? = null,
    val title: String? = null,
    val collectLocationWifiInfo:Boolean=false,
    val otRequestsCount: Int = 0
) : ReactiveUiState

interface HomeTaskUiEvent {
    object FinishRefresh : UiEvent
    data class StartTaskActionActivity(val location: LocationEntity?) : UiEvent
}