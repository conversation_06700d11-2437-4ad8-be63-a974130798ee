package com.unis.wms.ot_control.request_status

import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setVisibleOrGone
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.linc.platform.utils.TimeUtil
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.wms.R
import com.unis.wms.databinding.FragmentExtendRequestStatusBinding
import com.unis.wms.home.HomeActivity
import com.unis.wms.ot_control.OtControlHelper
import com.unis.wms.ot_control.OtControlViewModel
import com.unis.wms.uitl.WebSocketClient
import com.unis.wms.uitl.WebSocketConfig
import com.unis.wms.uitl.WebSocketResponseEntity

/**
 * <AUTHOR> on 2024/12/11
 */
class RequestStatusFragment : ReactiveFragment<RequestStatusViewModel, RequestStatusUiState, FragmentExtendRequestStatusBinding>() {

    private val socketClient by lazy { WebSocketClient.newInstance(WebSocketClient.TYPE_OT_CONTROL) }

    companion object {
        fun newInstance(configEntity: OtRequestStatusConfigEntity) = newFragmentInstance<RequestStatusFragment>(OtRequestStatusConfigEntity.TAG to configEntity)
    }

    private val activityViewModel by lazy { getActivityViewModel<OtControlViewModel>()!! }
    override fun createViewModel(): RequestStatusViewModel {
        val configEntity = arguments?.getSerializable(OtRequestStatusConfigEntity.TAG) as OtRequestStatusConfigEntity
        return RequestStatusViewModel(RequestStatusDataState(configEntity = configEntity), activityViewModel = activityViewModel)
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            val drawable = progressBar.indeterminateDrawable.mutate()
            drawable.setTint(ContextCompat.getColor(context!!,R.color.colorAccent))
            progressBar.indeterminateDrawable = drawable
            requestedStartExtendedShiftLl.setOnClickListener {
                viewModel.startExtendedShift()
            }
            startExtendedShiftLl.setOnClickListener {
                viewModel.startExtendedShift()
            }
            completedRequestOtherBtn.setOnClickListener {
                viewModel.extendShiftRequest()
            }
            completedEndWorkBtn.setOnClickListener {
                showConfirmEndWorkDialog()
            }
            requestedEndWorkBtn.setOnClickListener {
                showConfirmEndWorkDialog(isLaborReject = true)
            }
            rejectedEndWorkLl.isClickable = true
            rejectedEndWorkLl.setOnClickListener {
                showConfirmEndWorkDialog()
            }
            if (socketClient.isConnected()) {
                socketClient.disconnect()
            }
            socketClient.connect(WebSocketConfig(idmUserId), object : WebSocketClient.WebSocketMessageListener {
                override fun onResult(result: String) {
                    val gson = GsonBuilder()
                        .setDateFormat(TimeUtil.FORMAT_FULL_T_FORMAT)
                        .serializeNulls()
                        .create()
                    try {
                        val type = object : TypeToken<WebSocketResponseEntity<OtRequestDetailEntity>>() {}.type
                        val resultEntity = gson.fromJson<WebSocketResponseEntity<OtRequestDetailEntity>>(result, type)
                        activity?.runOnUiThread {
                            if (viewModel.getOtRequestDetail()?.laborUserId == resultEntity.data.laborUserId && viewModel.getOtRequestDetail()?.id == resultEntity.data.id) {
                                viewModel.setOtRequestDetail(
                                    viewModel.getOtRequestDetail()?.copy(
                                        status = resultEntity.data.status,
                                        processedTime = resultEntity.data.processedTime,
                                        processedBy = resultEntity.data.processedBy,
                                        duration = resultEntity.data.duration,
                                    )
                                )
                            }
                        }
                    } catch (e: JsonSyntaxException) {
                        e.printStackTrace()
                    }
                }

                override fun onConnectSuccess() {
                }

                override fun onFail(message: String?) {
                }

                override fun onDisConnected() {
                }

            })
        }
        onStartedExtendShiftEvent()
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showOtRequestDetail()
    }

    private fun ReactiveViewScope.showOtRequestDetail() =
        subscribe(RequestStatusUiState::otRequestDetail) {
            updateRequestStatusUi(it?.status, it?.dailyWorkHours ?: 0.0)
            when(it?.status) {
                OtRequestStatus.PENDING -> {
                    showPendingView(it)
                }
                OtRequestStatus.REQUESTED -> {
                    showRequestedView(it)
                }
                OtRequestStatus.APPROVED -> {
                    showApprovedView(it)
                }
                OtRequestStatus.APPROVER_REJECTED -> {
                    showRejectedView(it)
                }
                OtRequestStatus.OT_REACH -> {
                    viewModel.endExtendedShift()
                    showCompletedView(it)
                }
                else -> {}
            }
        }

    private fun showPendingView(otRequestDetail: OtRequestDetailEntity) {
        binding?.apply {
            pendingRequestTimeTv.text = TimeUtil.formatDataString(TimeUtil.local2Default(otRequestDetail.createdTime), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_FULL_FORMAT)
            pendingExtensionTimeTv.text = OtControlHelper.formatDuration(context, otRequestDetail.duration)
        }
    }

    private fun showRequestedView(otRequestDetail: OtRequestDetailEntity) {
        binding?.apply {
            requestedTimeTv.text = TimeUtil.formatDataString(TimeUtil.local2Default(otRequestDetail.processedTime), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_FULL_FORMAT)
            requestedExtensionTimeTv.text = OtControlHelper.formatDuration(context, otRequestDetail.duration)
            requestedByTv.text = otRequestDetail.processedBy
        }
    }   
    
    private fun showApprovedView(otRequestDetail: OtRequestDetailEntity) {
        binding?.apply {
            approvedTimeTv.text = TimeUtil.formatDataString(TimeUtil.local2Default(otRequestDetail.processedTime), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_FULL_FORMAT)
            approvedExtensionTimeTv.text = OtControlHelper.formatDuration(context, otRequestDetail.duration)
            approvedByTv.text = otRequestDetail.processedBy
        }
    }
    
    private fun showRejectedView(otRequestDetail: OtRequestDetailEntity) {  
        binding?.apply {
            rejectedTimeTv.text = TimeUtil.formatDataString(TimeUtil.local2Default(otRequestDetail.processedTime), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_FULL_FORMAT)
            rejectedByTv.text = otRequestDetail.processedBy
        }
    }
    
    private fun showCompletedView(otRequestDetail: OtRequestDetailEntity) {
        binding?.apply {
            completedExtensionDurationTv.text = OtControlHelper.formatDuration(context, otRequestDetail.duration)
        }
    }

    private fun updateRequestStatusUi(status: OtRequestStatus?, dailyWorkHours: Double) {
        binding?.apply {
            pendingLl.setVisibleOrGone(status == OtRequestStatus.PENDING)
            requestedLl.setVisibleOrGone(status == OtRequestStatus.REQUESTED)
            approvedLl.setVisibleOrGone(status == OtRequestStatus.APPROVED)
            rejectedLl.setVisibleOrGone(status == OtRequestStatus.APPROVER_REJECTED)
            completedLl.setVisibleOrGone(status == OtRequestStatus.OT_REACH)
            when(status) {
                OtRequestStatus.PENDING -> {
                    statusIv.setImageDrawable(null)
                    statusTv.setText(R.string.pending_tips)
                    statusTv.setTextColor(ContextCompat.getColor(context!!,R.color.extended_yellow_700))
                    statusLl.setBackgroundResource(R.drawable.rect_fff6e6_r4)
                }
                OtRequestStatus.REQUESTED -> {
                    statusIv.setImageDrawable(null)
                    statusTv.text = getString(R.string.request_extend_shift_tips, dailyWorkHours.toString())
                    statusTv.setTextColor(ContextCompat.getColor(context!!,R.color.color_2FC2E6))
                    statusLl.setBackgroundResource(R.drawable.rect_b382fc2e6_4)
                }
                OtRequestStatus.APPROVED -> {
                    statusIv.setImageResource(R.drawable.ic_baseline_check_circle_20)
                    statusTv.setText(R.string.approved_tips_1)
                    statusTv.setTextColor(ContextCompat.getColor(context!!,R.color.accent_green_v1))
                    statusLl.setBackgroundResource(R.drawable.rect_b6d7a8_4)
                }
                OtRequestStatus.APPROVER_REJECTED -> {
                    statusIv.setImageResource(R.drawable.ic_close)
                    statusTv.setText(R.string.rejected_tips)
                    statusTv.setTextColor(ContextCompat.getColor(context!!,R.color.replenish_red))
                    statusLl.setBackgroundResource(R.drawable.rect_ad4d4a_4)
                }
                OtRequestStatus.OT_REACH -> {
                    statusIv.setImageResource(R.drawable.icon_complete)
                    statusTv.setText(R.string.completed_tips)
                    statusTv.setTextColor(ContextCompat.getColor(context!!,R.color.extended_yellow_700))
                    statusLl.setBackgroundResource(R.drawable.rect_fff6e6_r4)

                }
                else -> {}
            }
        }
    }

    private fun showConfirmEndWorkDialog(isLaborReject: Boolean = false) {
        context ?: return
        CenterDialog.confirm(
            context = context!!,
            message = getString(R.string.are_you_sure_you_want_to_end_work),
            positiveClick = {
                viewModel.endWork(isLaborReject)
            },
        ).show()
    }

    private fun onStartedExtendShiftEvent() = onEvent<RequestStatusEvent.StartedExtendShift> {
        context?: return@onEvent
        val intent = Intent()
        intent.setClass(context!!, HomeActivity::class.java)
        intent.putExtra(HomeActivity.HOME_PAGE_POSITION, HomeActivity.TIMESHEET_HOME_POSITION)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        activity?.finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        socketClient.disconnect()
    }
}