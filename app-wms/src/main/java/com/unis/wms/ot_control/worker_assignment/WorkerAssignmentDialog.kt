package com.unis.wms.ot_control.worker_assignment

import android.os.Bundle
import android.util.DisplayMetrics
import androidx.fragment.app.FragmentTransaction
import com.linc.platform.core.LocalPersistence
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDialog
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.wms.databinding.DialogWorkerAssignmentBinding

/**
 * <AUTHOR> based on WorkerAssignmentFragment
 */
class WorkerAssignmentDialog : BaseBindingDialog<DialogWorkerAssignmentBinding>() {

    companion object {
        private const val FACILITY = "facility"
        private const val LAST_FACILITY = "lastFacility"
        fun newInstance(
            facility: FacilityEntity,
            lastFacility: FacilityEntity?
        ) = newFragmentInstance<WorkerAssignmentDialog>(
            FACILITY to facility,
            LAST_FACILITY to lastFacility
        )
    }

    private var listener: OnWorkerAssignmentListener? = null
    private val facility: FacilityEntity by lazy { getBundleObject(FACILITY) }
    private val lastFacility: FacilityEntity by lazy { getBundleObject(LAST_FACILITY) }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        dialog?.setCanceledOnTouchOutside(false)
        dialog.setOnCancelListener {
            listener?.onCancel(facility, lastFacility)
        }
        setDialogHalfHeight()
    }

    override fun initView(savedInstanceState: Bundle?) {
        loadWorkerAssignmentFragment()
    }
    
    private fun setDialogHalfHeight() {
        dialog?.window?.let { window ->
            val displayMetrics = DisplayMetrics()
            window.windowManager.defaultDisplay.getMetrics(displayMetrics)
            val screenHeight = displayMetrics.heightPixels
            
            val params = window.attributes
            params.height = screenHeight * 1 / 2
            window.attributes = params
        }
    }

    private fun loadWorkerAssignmentFragment() {
        context?: return
        val iamEntity = LocalPersistence.getIamEntity(context!!)
        val workerAssignmentFragment = WorkerAssignmentFragment.newInstance(iamEntity.userId)
        workerAssignmentFragment.setOnConfirmWorkerAssignmentListener(object : WorkerAssignmentFragment.OnConfirmWorkerAssignmentListener {
            override fun onConfirmWorkerAssignment(teamId: Long, laborTypeId: Long) {
                listener?.onSelectedWorkerAssignment(facility, lastFacility, teamId, laborTypeId)
                dismiss()
            }
        })
        childFragmentManager.beginTransaction().apply {
            replace(android.R.id.content, workerAssignmentFragment)
            setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
            commit()
        }
    }

    fun setOnWorkerAssignmentListener(listener: OnWorkerAssignmentListener) {
        this.listener = listener
    }

    interface OnWorkerAssignmentListener {
        fun onSelectedWorkerAssignment(
            facility: FacilityEntity,
            lastFacility: FacilityEntity?,
            teamId: Long,
            laborTypeId: Long
        )

        fun onCancel(facility: FacilityEntity, lastFacility: FacilityEntity?)
    }
} 