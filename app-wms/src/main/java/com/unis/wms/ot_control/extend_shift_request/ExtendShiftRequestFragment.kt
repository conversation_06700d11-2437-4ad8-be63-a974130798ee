package com.unis.wms.ot_control.extend_shift_request

import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import com.customer.widget.LabelSpinner
import com.linc.platform.core.LocalPersistence
import com.unis.platform.ot_control.models.OtCountType
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.wms.R
import com.unis.wms.databinding.FragmentExtendShiftRequestBinding
import com.unis.wms.ot_control.OtControlActivity
import com.unis.wms.ot_control.OtControlHelper
import com.unis.wms.ot_control.OtControlHelper.minutesToHours
import com.unis.wms.ot_control.OtControlViewModel

/**
 * <AUTHOR> on 2024/12/11
 */
class ExtendShiftRequestFragment : ReactiveFragment<ExtendShiftRequestViewModel, ExtendShiftRequestUiState, FragmentExtendShiftRequestBinding>(){
    companion object {
        fun newInstance() = ExtendShiftRequestFragment()
    }

    private val activityViewModel by lazy { getActivityViewModel<OtControlViewModel>()!! }
    override fun createViewModel() = ExtendShiftRequestViewModel(activityViewModel = activityViewModel)

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply{
            val hoursList = OtControlHelper.getHoursList()
            val minutesList = OtControlHelper.getMinutesList()
            extendHoursSpinner.setDataOptions(hoursList.map { "${it}h" })
            extendMinutesSpinner.setDataOptions(minutesList.map { "${it}m" }, "${minutesList[1]}m")
            extendHoursSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val hours = hoursList[position].toInt()
                    viewModel.setExtensionHours(hours)
                    val requestType = if (viewModel.getWeeklyWorkHours() > OtControlHelper.THRESHOLD_WEEKLY_OT) {
                       val workHoursWithExtensionDuration = viewModel.getWeeklyWorkHours() + hours + minutesToHours(viewModel.getExtensionMinutes())
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    } else {
                        val workHoursWithExtensionDuration = viewModel.getTodayWorkHours() + hours + minutesToHours(viewModel.getExtensionMinutes())
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    }
                    viewModel.setRequestType(requestType)
                }

            })
            extendMinutesSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val minutes = minutesList[position].toInt()
                    viewModel.setExtensionMinutes(minutes)
                    val requestType = if (viewModel.getWeeklyWorkHours() > OtControlHelper.THRESHOLD_WEEKLY_OT) {
                        val workHoursWithExtensionDuration = viewModel.getWeeklyWorkHours() + viewModel.getExtensionHours() + minutesToHours(minutes)
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    } else {
                        val workHoursWithExtensionDuration = viewModel.getTodayWorkHours() + viewModel.getExtensionHours() + minutesToHours(minutes)
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    }
                    viewModel.setRequestType(requestType)
                }
            })
            submitShiftBtn.setOnClickListener {
                // @see WorkerAssignmentFragment save (teamId laborTypeId)
                context?: return@setOnClickListener
                val iamEntity = LocalPersistence.getIamEntity(context!!)
                viewModel.submitShift(extendsReasonEdt.text.toString().trim(), iamEntity.teamId, iamEntity.laborTypeId)
            }
            extendShiftCancelBtn.setOnClickListener {
                (activity as OtControlActivity).manageBackPress()
            }
            viewModel.setState(facility2.address?.state)
        }
    }

    private fun setRequestTypeChecked(requestType: OtRequestType) {
        binding?.apply {
            extendedHoursTv.isSelected = requestType == OtRequestType.EXTENDED_HOURS
            dailyOtTv.isSelected = requestType == OtRequestType.DAILY_OT
            dailyDtTv.isSelected = requestType == OtRequestType.DAILY_DT
            weeklyOtTv.isSelected = requestType == OtRequestType.WEEKLY_OT
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showState()
        showTodayWorkHours()
        showWeeklyHours()
        showRequestType()
    }

    private fun ReactiveViewScope.showState() =
        subscribe(ExtendShiftRequestUiState::state) {
            binding?.apply {
                stateTv.text = it
            }
        }

    private fun ReactiveViewScope.showTodayWorkHours() =
        subscribe(ExtendShiftRequestUiState::todayWorkHours) {
            binding?.apply {
                todayWorkHoursTv.text = String.format(getString(R.string.text_xx_hours), it.toInt())
            }
        }

    private fun ReactiveViewScope.showWeeklyHours() =
        subscribe(ExtendShiftRequestUiState::weeklyWorkHours) {
            binding?.apply {
                weeklyHoursTv.text = String.format(getString(R.string.text_xx_hours), it.toInt())
            }
        }

    private fun ReactiveViewScope.showRequestType() =
        subscribe(ExtendShiftRequestUiState::requestType) {
            setRequestTypeChecked(it)
        }

    private fun determineRequestType(extensionDuration: Double, otCountType: OtCountType): OtRequestType {
        return when (otCountType) {
            OtCountType.DAILY -> getDailyRequestType(extensionDuration)
            OtCountType.WEEKLY -> getWeeklyRequestType(extensionDuration)
        }
    }
    
    private fun getDailyRequestType(extensionDuration: Double): OtRequestType {
        val dailyHours = viewModel.getTodayWorkHours() + extensionDuration
        return if (dailyHours > 12) OtRequestType.DAILY_DT
        else if (dailyHours > 8) OtRequestType.DAILY_OT
        else OtRequestType.EXTENDED_HOURS
    }

    private fun getWeeklyRequestType(extensionDuration: Double): OtRequestType {
        val weeklyHours = viewModel.getWeeklyWorkHours() + extensionDuration
        return if (weeklyHours > 40) OtRequestType.WEEKLY_OT
        else OtRequestType.EXTENDED_HOURS
    }

}