package com.unis.wms.ot_control.list

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.format.DateFormat
import android.view.View
import android.widget.AdapterView
import com.customer.widget.LabelSpinner
import com.customer.widget.RecyclerViewItemSpace
import com.customer.widget.common.LoadMoreLayout
import com.customer.widget.common.safeCount
import com.customer.widget.extensions.setVisibleOrGone
import com.unis.linc.common.extensions.replaceAll
import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.wms.R
import com.unis.wms.common.userinfolist.UserInfoListActivity
import com.unis.wms.databinding.ActivityOtRequestListBinding
import com.unis.wms.ot_control.request_detail.OtRequestDetailActivity
import java.util.Date

/**
 * <AUTHOR> on 2024/12/11
 */
class OtRequestListActivity : ReactiveActivity<OtRequestListViewModel, OtRequestListUiState, ActivityOtRequestListBinding>() {

    private val adapter by lazy { OtRequestAdapter() }

    override fun createViewModel() = OtRequestListViewModel()

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.extension_requests)
            refreshLayout.setOnRefreshListener(this@OtRequestListActivity::onRefreshTask)

            // 初始化状态筛选器
            initStatusFilter()

            // 初始化用户选择
            initUserSelect()

            // 初始化提交时间选择
            initSubmitTimeFilter()

            // 初始化处理时间选择
            initProcessedTimeFilter()

            recyclerView.addItemDecoration(RecyclerViewItemSpace(this@OtRequestListActivity))
            adapter.apply {
                bindToRecyclerView(recyclerView)
                disableLoadMoreIfNotFullPage()
                setLoadMoreView(LoadMoreLayout())
                setOnLoadMoreListener(this@OtRequestListActivity::onLoadMoreTask, recyclerView)
                setOnItemClickListener { _, _, position -> onItemClick(position) }
            }
        }
    }

    private fun initStatusFilter() {
        binding.statusSpinner.apply {
            val statusOptions = viewModel.getStatusOptions().map { it.name.replaceAll("_", " ") }
            setDataOptions(statusOptions, isInitFirstOption = false)

            setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    if (position >= 0) {
                        val selectedStatus = if (position >= 0) viewModel.getStatusOptions()[position] else null
                        viewModel.setSelectedStatus(selectedStatus)
                    }
                }
            })
        }
    }

    private fun initUserSelect() { // 用户选择
        binding.processBySelectLayout.setOnClickListener {
            val intent = Intent(this@OtRequestListActivity, UserInfoListActivity::class.java)
            startActivityForResult(intent, UserInfoListActivity.REQUEST_CODE_USER_INFO_LIST)
        }

        // 用户清除
        binding.processByClearIv.setOnClickListener {
            viewModel.setSelectedUser(null)
        }
    }

    private fun initSubmitTimeFilter() { // 设置时间从选择器点击事件
        binding.submitTimeFromLayout.setOnClickListener {
            viewModel.showDatePicker(this, getString(R.string.submit_time_from), 0, true)
        }

        // 设置时间至选择器点击事件
        binding.submitTimeToLayout.setOnClickListener {
            viewModel.showDatePicker(this, getString(R.string.submit_time_to), 0, false)
        }

        // 从时间清除按钮
        binding.submitFromClearIv.setOnClickListener {
            viewModel.clearDateSelection(0, true)
        }

        // 至时间清除按钮
        binding.submitToClearIv.setOnClickListener {
            viewModel.clearDateSelection(0, false)
        }
    }

    private fun initProcessedTimeFilter() { // 设置处理时间从选择器点击事件
        binding.processedTimeFromLayout.setOnClickListener {
            viewModel.showDatePicker(this, getString(R.string.processed_time_from), 1, true)
        }

        // 设置处理时间至选择器点击事件
        binding.processedTimeToLayout.setOnClickListener {
            viewModel.showDatePicker(this, getString(R.string.processed_time_to), 1, false)
        }

        // 处理时间从清除按钮
        binding.processedFromClearIv.setOnClickListener {
            viewModel.clearDateSelection(1, true)
        }

        // 处理时间至清除按钮
        binding.processedToClearIv.setOnClickListener {
            viewModel.clearDateSelection(1, false)
        }
    }

    private fun formatDate(date: Date?): String? {
        return date?.let { DateFormat.format("MM/dd/yyyy", it).toString() }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(OtRequestListUiState::otRequestDetails) {
            adapter.loadMoreComplete()
            adapter.setDiffList(it)
            binding.pendingRequestsTv.setVisibleOrGone(it.isNotEmpty())
            binding.pendingRequestsTv.text = String.format(getString(R.string.xxx_pending_requests),
                it.filter { detail -> detail.status == OtRequestStatus.PENDING }.safeCount().toString())
            adapter.disableLoadMoreIfNotFullPage()
        }
        subscribe(OtRequestListUiState::loadMoreEnd) {
            if (it) {
                adapter.loadMoreEnd(it)
            }
        }
        subscribe(OtRequestListUiState::selectedUser) { user ->
            binding.processByTv.text = user?.userName ?: ""
            binding.processByClearIv.setVisibleOrGone(null != user)
        }
        subscribe(OtRequestListUiState::submitTimeFrom) { date ->
            binding.submitTimeFromTxt.text = formatDate(date)
            binding.submitFromClearIv.setVisibleOrGone(null != date)
        }
        subscribe(OtRequestListUiState::submitTimeTo) { date ->
            binding.submitTimeToTxt.text = formatDate(date)
            binding.submitToClearIv.setVisibleOrGone(null != date)
        }
        subscribe(OtRequestListUiState::processedTimeFrom) { date ->
            binding.processedTimeFromTxt.text = formatDate(date)
            binding.processedFromClearIv.setVisibleOrGone(null != date)
        }
        subscribe(OtRequestListUiState::processedTimeTo) { date ->
            binding.processedTimeToTxt.text = formatDate(date)
            binding.processedToClearIv.setVisibleOrGone(null != date)
        }
    }

    override fun showProgress(show: Boolean, cancelable: Boolean) {
        binding.refreshLayout.isRefreshing = show
    }

    override fun showProgress(show: Boolean) {
        binding.apply {
            refreshLayout.post { refreshLayout.isRefreshing = show }
        }
    }

    private fun onLoadMoreTask() {
        viewModel.onLoadMoreTask()
    }

    private fun onRefreshTask() {
        viewModel.onRefreshTask()
    }

    private fun onItemClick(position: Int) {
        adapter.getItem(position)?.id?.let {
            OtRequestDetailActivity.startActivityForResult(this, it)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == OtRequestDetailActivity.REQUEST_CODE_OT_REQUEST_DETAIL && resultCode == Activity.RESULT_OK && data != null) {
            val otRequestId = data.getLongExtra(OtRequestDetailActivity.OT_REQUEST_ID, 0)
            viewModel.removeOtRequest(otRequestId)
        } else if (requestCode == UserInfoListActivity.REQUEST_CODE_USER_INFO_LIST && resultCode == Activity.RESULT_OK && data != null) {
            (data.getSerializableExtra(UserInfoEntity.TAG) as? UserInfoEntity)?.let {
                viewModel.setSelectedUser(it)
            }
        }
    }
}