package com.unis.wms.ot_control

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.customer.widget.core.ActivityBundleHolder
import com.linc.platform.core.MainEnv
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.BaseFragment
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.reactivemvi.mvvm.kotlin.extensions.universalParam
import com.unis.wms.R
import com.unis.wms.common.FragmentContainerActivity
import com.unis.wms.home.ToHomeHandler
import com.unis.wms.ot_control.extend_shift_request.ExtendShiftRequestFragment
import com.unis.wms.ot_control.request_status.OtRequestStatusConfigEntity
import com.unis.wms.ot_control.request_status.RequestStatusFragment
import com.unis.wms.ot_control.work_hours.WorkHoursAlertFragment
import com.unis.wms.ot_control.worker_assignment.WorkerAssignmentFragment
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.Serializable

/**
 * <AUTHOR> on 2024/12/10
 */
class OtControlActivity : FragmentContainerActivity<OtControlViewModel, OtControlUiState>() {

    data class Param(
        val otControlConfigEntity: OtControlConfigEntity,
    ) : UniversalActivityParam

    companion object {
        fun startActivity(context: Context, otControlConfigEntity: OtControlConfigEntity) {
            val intent = Intent()
            val clazz = OtControlActivity::class.java
            intent.setClass(context, clazz)
            val param = Param(otControlConfigEntity = otControlConfigEntity)
            ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
            context.startActivity(intent)
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(OtControlUiState::otControlTitle) {
            it?.let { otControlTitle ->
                otControlTitle.title?.let {titleResId ->
                    if (otControlTitle.canBack) {
                        initToolBar(binding.toolbar, titleResId)
                    } else {
                        initToolBarNoBack(binding.toolbar, getString(titleResId))
                    }
                }
            }
        }
    }

    override fun createViewModel(): OtControlViewModel {
        val param = universalParam.let {
            it as Param
        }
        return OtControlViewModel(OtControlDataState(otControlConfigEntity = param.otControlConfigEntity))
    }

    override fun initView(savedInstanceState: Bundle?) {
        onEvents()
        addBackPressedCallback(::manageBackPress)
    }

    override fun getInitFragment(): BaseFragment {
       return ExtendShiftRequestFragment.newInstance()
    }

    private fun onEvents() {
        onEvent<OtControlEvent.UpdateOtControlStep> {
            when (step) {
                is OtControlStep.WorkerAssignment -> showWorkerAssignmentFragment(step.loginResultEntity)
                OtControlStep.ExtendShiftRequest -> showExtendShiftRequestFragment()
                is OtControlStep.RequestStatus -> showRequestStatusFragment(step.configEntity)
                OtControlStep.WorkHoursAlert -> showWorkHoursAlertFragment()
            }
        }
        onEvent<OtControlEvent.EndWork> {
            logout()
        }
    }

    private fun showWorkerAssignmentFragment(loginResultEntity: LoginResultEntity) {
        addFragment(WorkerAssignmentFragment.newInstance(loginResultEntity))
        initToolBarNoBack(binding.toolbar, getString(R.string.worker_assignment))
    }

    private fun showExtendShiftRequestFragment() {
        addFragment(ExtendShiftRequestFragment.newInstance())
        initToolBar(binding.toolbar, R.string.extend_shift_request)
    }

    private fun showRequestStatusFragment(configEntity: OtRequestStatusConfigEntity) {
        addFragment(RequestStatusFragment.newInstance(configEntity))
        initToolBarNoBack(binding.toolbar, getString(R.string.extension_request_status))
    }

    private fun showWorkHoursAlertFragment() {
        addFragment(WorkHoursAlertFragment.newInstance())
        initToolBarNoBack(binding.toolbar, getString(R.string.work_hours_alert))
    }

    private fun logout() {
        ToHomeHandler.getHandler().clearLoginData(this, idmUserId)
        MainEnv.startLoginActivity()
    }

    fun manageBackPress(): Boolean {
        if (!canBackPress()) {
            return true
        }
        val backStackEntryCount = supportFragmentManager.backStackEntryCount
        if (backStackEntryCount > 1) { // print label fragment is last fragment and when back then finish
            supportFragmentManager.popBackStack()
            updateTitleByFragment()
            return true
        }
        if (isWorkerAssignmentFragment()) {//WorkerAssignmentFragment page back to clear cache login data to desk
            ToHomeHandler.getHandler().clearLoginData(this, idmUserId)
        }
        finish()
        return false
    }

    private fun updateTitleByFragment(){
        CoroutineScope(Dispatchers.Main).launch {
            delay(200)
            when(supportFragmentManager.findFragmentById(binding.frameLayoutContainer.id)){
                is WorkerAssignmentFragment -> viewModel.updateToolbarTitle(OtControlTitle(R.string.worker_assignment, false))
                is ExtendShiftRequestFragment ->  viewModel.updateToolbarTitle(OtControlTitle(R.string.extend_shift_request, true))
                is RequestStatusFragment ->  viewModel.updateToolbarTitle(OtControlTitle(R.string.extension_request_status, false))
                is WorkHoursAlertFragment ->  viewModel.updateToolbarTitle(OtControlTitle(R.string.work_hours_alert, false))
            }
        }
    }

    private fun canBackPress(): Boolean {
        return when(supportFragmentManager.findFragmentById(binding.frameLayoutContainer.id)){
            is RequestStatusFragment ->  false
            is WorkHoursAlertFragment ->  false
            else -> true
        }
    }

    private fun isWorkerAssignmentFragment(): Boolean {
        return supportFragmentManager.findFragmentById(binding.frameLayoutContainer.id) is WorkerAssignmentFragment
    }

}