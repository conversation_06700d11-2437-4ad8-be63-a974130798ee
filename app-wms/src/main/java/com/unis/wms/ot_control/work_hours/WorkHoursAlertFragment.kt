package com.unis.wms.ot_control.work_hours

import android.os.Bundle
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setVisible
import com.unis.platform.ot_control.models.OtCountType
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.wms.R
import com.unis.wms.databinding.FragmentWorkHoursAlertBinding
import com.unis.wms.ot_control.OtControlViewModel

/**
 * <AUTHOR> on 2024/12/11
 */
class WorkHoursAlertFragment : ReactiveFragment<WorkHoursAlertViewModel, WorkHoursAlertUiState, FragmentWorkHoursAlertBinding>() {
    companion object {
        fun newInstance() = WorkHoursAlertFragment()
    }

    private val activityViewModel by lazy { getActivityViewModel<OtControlViewModel>()!! }
    override fun createViewModel() = WorkHoursAlertViewModel(activityViewModel = activityViewModel)

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            extendShiftBtn.setOnClickListener {
                viewModel.toRequestExtendShift()
            }
            endWorkBtn.setOnClickListener {
                showConfirmEndWorkDialog()
            }
            viewModel.setState(facility2?.address?.state)
        }
    }

    private fun showConfirmEndWorkDialog() {
        context ?: return
        CenterDialog.confirm(
            context = context!!,
            message = getString(R.string.are_you_sure_you_want_to_end_work),
            positiveClick = {
                viewModel.endWork()
            },
        ).show()
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showState()
        showTodayWorkHours()
        showWeeklyHours()
    }

    private fun ReactiveViewScope.showState() =
        subscribe(WorkHoursAlertUiState::state) {
            binding?.apply {
                stateTv.text = it
            }
        }

    private fun ReactiveViewScope.showTodayWorkHours() =
        subscribe(WorkHoursAlertUiState::todayWorkHours) {
            binding?.apply {
                todayWorkHoursTv.text = String.format(getString(R.string.text_xx_hours), it)
                if (viewModel.getOtCountType() == OtCountType.DAILY) {
                    statusLl.setVisible()
                    statusTv.text = String.format(getString(R.string.work_hours_alert_tips), it.toString())
                }
            }
        }

    private fun ReactiveViewScope.showWeeklyHours() =
        subscribe(WorkHoursAlertUiState::weeklyWorkHours) {
            binding?.apply {
                weeklyHoursTv.text = String.format(getString(R.string.text_xx_hours), it)
                if (viewModel.getOtCountType() == OtCountType.WEEKLY) {
                    statusLl.setVisible()
                    statusTv.text = String.format(getString(R.string.work_hours_alert_tips_weekly), it.toString())
                }
            }
        }
}