package com.unis.wms.ot_control.list

import com.linc.platform.utils.TimeUtil
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemOtRequestBinding
import com.unis.wms.ot_control.OtControlHelper

/**
 * <AUTHOR> on 2024/12/11
 */
class OtRequestAdapter: BaseBindingDifferQuickAdapter<OtRequestDetailEntity, ItemOtRequestBinding>() {
    override fun convert(helper: BaseBindingViewHolder<ItemOtRequestBinding>?, item: OtRequestDetailEntity?) {
        helper?.apply {
            binding.apply {
                item?.let {
                    userNameTv.text = it.laborUserName
                    employeeIdTv.text = "ID:"+it.laborUserId
                    extensionDurationTv.text = String.format(getString(R.string.extension_request_duration), OtControlHelper.formatDuration(context, it.duration))
                    teamShiftName.text = it.teamName
                    shiftTv.text = it.laborTypeName
                    timeTv.text = TimeUtil.formatDataString(
                        TimeUtil.local2Default(it.createdTime),
                        TimeUtil.FORMAT_FULL_T_FORMAT,
                        TimeUtil.FORMAT_H_MM
                    )
                    dateTv.text = if (TimeUtil.isToday(it.createdTime)) {
                        getString(R.string.today)
                    } else {
                        TimeUtil.formatDataString(
                            TimeUtil.local2Default(it.createdTime),
                            TimeUtil.FORMAT_FULL_T_FORMAT,
                            TimeUtil.FORMAT_MM_DD_YYYY
                        )
                    }
                }
            }
        }
    }

    override fun areContentsTheSame(oldItem: OtRequestDetailEntity, newItem: OtRequestDetailEntity): Boolean {
        return oldItem.id == newItem.id
                && oldItem.status == newItem.status
                && oldItem.processedBy == newItem.processedBy
                && oldItem.processedTime == newItem.processedTime
                && oldItem.createdBy == newItem.createdBy
                && oldItem.createdTime == newItem.createdTime
    }

    override fun areItemsTheSame(oldItem: OtRequestDetailEntity, newItem: OtRequestDetailEntity): Boolean {
        return oldItem.id == newItem.id
                && oldItem.status == newItem.status
                && oldItem.processedBy == newItem.processedBy
                && oldItem.processedTime == newItem.processedTime
                && oldItem.createdBy == newItem.createdBy
                && oldItem.createdTime == newItem.createdTime
    }
}