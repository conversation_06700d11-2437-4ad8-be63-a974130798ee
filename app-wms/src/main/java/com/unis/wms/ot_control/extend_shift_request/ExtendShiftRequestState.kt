package com.unis.wms.ot_control.extend_shift_request

import com.unis.platform.ot_control.models.OTControlSettingEntity
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState

/**
 * <AUTHOR> on 2024/12/10
 */
data class ExtendShiftRequestDataState(
    val state: String? = null,
    val todayWorkHours: Double = 0.0,
    val weeklyWorkHours: Double = 0.0,
    val extensionHours: Int = 0,
    val extensionMinutes: Int = 0,
    val requestType: OtRequestType = OtRequestType.EXTENDED_HOURS,
    val otControlSettings: List<OTControlSettingEntity> = listOf(),
) : ReactiveDataState


data class ExtendShiftRequestUiState(
    val state: String? = null,
    val todayWorkHours: Double = 0.0,
    val weeklyWorkHours: Double = 0.0,
    val requestType: OtRequestType = OtRequestType.EXTENDED_HOURS,
) : ReactiveUiState