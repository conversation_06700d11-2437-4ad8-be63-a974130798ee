package com.unis.wms.ot_control

import com.google.gson.annotations.SerializedName
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.platform.ot_control.models.OtCountType
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent
import com.unis.wms.ot_control.request_status.OtRequestStatusConfigEntity
import java.io.Serializable

/**
 * <AUTHOR> on 2024/12/10
 */
data class OtControlDataState(
    val otControlConfigEntity: OtControlConfigEntity,
    val otCountType: OtCountType = OtCountType.DAILY,
) : ReactiveDataState


data class OtControlUiState(val otControlTitle: OtControlTitle? = null) : ReactiveUiState

interface OtControlEvent{
    data class UpdateOtControlStep(val step: OtControlStep) : UiEvent
    object EndWork : UiEvent
}

sealed interface OtControlStep{
    data class WorkerAssignment(val loginResultEntity: LoginResultEntity): OtControlStep
    object ExtendShiftRequest: OtControlStep
    data class RequestStatus(val configEntity: OtRequestStatusConfigEntity): OtControlStep
    object WorkHoursAlert: OtControlStep
}

data class OtControlTitle(val title: Int?, val canBack: Boolean)

data class OtControlConfigEntity(
    @SerializedName("otControlStep")
    val otControlStep: OtControlStep,

    @SerializedName("otCountType")
    var otCountType: OtCountType? = null,
) : Serializable