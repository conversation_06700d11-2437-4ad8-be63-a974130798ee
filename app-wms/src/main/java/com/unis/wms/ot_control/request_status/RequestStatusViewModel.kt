package com.unis.wms.ot_control.request_status

import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.platform.ot_control.models.OtRequestUpdateEntity
import com.unis.platform.ot_control.models.OtSubmitTypeEntity
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.wms.R
import com.unis.wms.ot_control.OtControlStep
import com.unis.wms.ot_control.OtControlTitle
import com.unis.wms.ot_control.OtControlViewModel

/**
 * <AUTHOR> on 2024/12/11
 */
class RequestStatusViewModel(
    initialDataState: RequestStatusDataState,
    initialUiState: RequestStatusUiState = RequestStatusUiState(),
    private val activityViewModel: OtControlViewModel
) : ReactiveViewModel<RequestStatusDataState, RequestStatusUiState>(initialDataState, initialUiState) {

    private val repository = Repository()

    init {
        autoUpdateDataToUi()
        getOtRequest()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(RequestStatusDataState::otRequestDetail, RequestStatusUiState::otRequestDetail) {
            it
        }
    }

    private fun updateToolbarTitleByRequestStatus(status: OtRequestStatus?) {
        val otControlTitle = when (status) {
            OtRequestStatus.PENDING -> OtControlTitle(R.string.extension_request_status, false)
            OtRequestStatus.REQUESTED -> OtControlTitle(R.string.extension_request, false)
            OtRequestStatus.APPROVED -> OtControlTitle(R.string.extension_request_approved, false)
            OtRequestStatus.APPROVER_REJECTED -> OtControlTitle(R.string.extension_request_rejected, false)
            OtRequestStatus.OT_REACH -> OtControlTitle(R.string.extension_completion, false)
            else -> { OtControlTitle(R.string.extension_request_status, false) }
        }
        activityViewModel.updateToolbarTitle(otControlTitle)
    }

    private fun getOtRequest() {
        launch {
            request(repository.getOtRequest(getOtRequestId()), success = {
                it?.let { otRequest ->
                    if (isOtReach()) {
                        setOtRequestDetail(otRequest.copy(status = OtRequestStatus.OT_REACH))
                    } else if (otRequest.submitType == OtSubmitTypeEntity.ASSIGNMENT && otRequest.status == OtRequestStatus.APPROVED) {
                        setOtRequestDetail(otRequest.copy(status = OtRequestStatus.REQUESTED))
                    } else {
                        setOtRequestDetail(otRequest)
                    }
                }
            })
        }
    }

    fun setOtRequestDetail(otRequestDetail: OtRequestDetailEntity?) {
        setDataState { copy(otRequestDetail = otRequestDetail) }
        updateToolbarTitleByRequestStatus(otRequestDetail?.status)
    }

    fun startExtendedShift() {
        launch {
            request(repository.updateOtRequest(getOtRequestId(), OtRequestUpdateEntity().apply {
                this.id = getOtRequestId()
                this.status = OtRequestStatus.START
            }), success = {
                fireEvent { RequestStatusEvent.StartedExtendShift }
            })
        }
    }

    fun endExtendedShift() {
        launch {
            request(repository.updateOtRequest(getOtRequestId(), OtRequestUpdateEntity().apply {
                this.id = getOtRequestId()
                this.status = OtRequestStatus.END
            }), success = {
            })
        }
    }

    fun extendShiftRequest() {
        activityViewModel.updateOtControlStep(OtControlStep.ExtendShiftRequest)
    }

    fun endWork(isLaborReject: Boolean = false) {
        launch {
            if (isLaborReject) {
                val result = requestAwait(repository.updateOtRequest(getOtRequestId(), OtRequestUpdateEntity().apply {
                    this.id = getOtRequestId()
                    this.status = OtRequestStatus.LABOR_REJECTED
                }))
                if (result.isFailure) return@launch
            }
            activityViewModel.endWork()
        }
    }

    fun getOtRequestDetail() = dataState.otRequestDetail

    private fun getOtRequestId() = dataState.configEntity.otRequestId

    private fun isOtReach() = dataState.configEntity.isOtReach

}

private class Repository : BaseRepository() {

    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun getOtRequest(id: Long) = requestV2({otControlApi.getOtRequest(id)})

    fun updateOtRequest(otRequestId: Long, update: OtRequestUpdateEntity) = requestV2({otControlApi.updateOtRequest(otRequestId, update)})

}