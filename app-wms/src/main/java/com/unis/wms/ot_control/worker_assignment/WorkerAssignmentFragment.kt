package com.unis.wms.ot_control.worker_assignment

import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import androidx.recyclerview.widget.LinearLayoutManager
import com.customer.widget.LabelSpinner
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.common.CenterDialog
import com.linc.platform.core.LocalPersistence
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.wms.databinding.FragmentWorkerAssignmentBinding
import com.unis.wms.home.ToHomeHandler

/**
 * <AUTHOR> on 2024/12/11
 */
class WorkerAssignmentFragment : ReactiveFragment<WorkerAssignmentViewModel, WorkerAssignmentUiState, FragmentWorkerAssignmentBinding>() {

    companion object {
        const val USER_ID = "user-id"
        fun newInstance(loginResultEntity: LoginResultEntity) = newFragmentInstance<WorkerAssignmentFragment>(
            LoginResultEntity.TAG to loginResultEntity)

        fun newInstance(userId: String) = newFragmentInstance<WorkerAssignmentFragment>(
            USER_ID to userId
        )
    }

    private var listener: OnConfirmWorkerAssignmentListener? = null

    private val mAdapter by lazy {
        WorkerAssignmentAdapter {
            viewModel.removeOperationType(it.id.toString())
        }
    }

    override fun createViewModel(): WorkerAssignmentViewModel {
        val loginResultEntity = arguments?.getSerializable(LoginResultEntity.TAG) as? LoginResultEntity
        val userId = arguments?.getString(USER_ID)
        return WorkerAssignmentViewModel(WorkerAssignmentDataState(loginResultEntity = loginResultEntity, userId = userId))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            operationList.layoutManager = LinearLayoutManager(context)
            operationList.adapter = mAdapter
            addOperationBtn.setOnClickListener {
                viewModel.updateOperationType {
                    CallbackDialogV1.showSingleChoice(context!!, items = it, itemTitleMapper = { item -> item.operationType ?: "" })
                }
            }
            confirmBtn.setOnClickListener {
                context?: return@setOnClickListener
                viewModel.confirmWorkerAssignment { teamId, laborTypeId ->
                    listener?.onConfirmWorkerAssignment(teamId, laborTypeId)
                    viewModel.getLoginResultEntity()?.let {
                        //For OT Request use
                        it.userInfo.teamId = teamId
                        it.userInfo.laborTypeId = laborTypeId
                        ToHomeHandler.getHandler().toHome(context!!, it) {
                            activity?.finish()
                        }
                    } ?: run {
                        val iamEntity = LocalPersistence.getIamEntity(context!!)
                        iamEntity.teamId = teamId
                        iamEntity.laborTypeId = laborTypeId
                        LocalPersistence.setIamEntity(context!!, iamEntity)
                    }
                }
            }

            teamSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    viewModel.selectedTeam(position)
                }
            })

            laborSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    viewModel.selectedLabor(position)
                }
            })
        }
        onEvent()
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(WorkerAssignmentUiState::operations) {
            mAdapter.setDiffList(it)
        }
    }

    private fun onEvent() {
        onEvent<WorkerAssignmentEvent.InitTeamsData> {
            binding?.apply {
                if (teams.size == 1) {
                    teamSpinner.setDataOptions(teams, true)
                    return@apply
                }
                if (selectedIndex != -1) {
                    teamSpinner.setDataOptions(teams, teams[selectedIndex])
                } else {
                    teamSpinner.setDataOptions(teams, false)
                }
            }
        }

        onEvent<WorkerAssignmentEvent.InitLaborData> {
            binding?.apply {
                if (labors.size == 1) {
                    laborSpinner.setDataOptions(labors, true)
                    return@apply
                }
                if (selectedIndex != -1) {
                    laborSpinner.setDataOptions(labors, labors[selectedIndex])
                } else {
                    laborSpinner.setDataOptions(labors, false)
                }
            }
        }

        onEvent<WorkerAssignmentEvent.ShowAlertDialogEvent> {
            context?: return@onEvent
            CenterDialog.alert(context = context!!, message = message).show()
        }
    }

    fun setOnConfirmWorkerAssignmentListener(listener: OnConfirmWorkerAssignmentListener) {
        this.listener = listener
    }

    interface OnConfirmWorkerAssignmentListener {
        fun onConfirmWorkerAssignment(teamId: Long, laborTypeId: Long)
    }
}