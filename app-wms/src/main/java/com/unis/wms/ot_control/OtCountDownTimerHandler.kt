package com.unis.wms.ot_control

import android.app.ActivityManager
import android.content.Context
import com.unis.platform.ot_control.models.OtCountDownTimerRemainTimeEntity
import com.unis.platform.ot_control.models.OtCountType
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.wms.ot_control.request_status.OtRequestStatusConfigEntity
import com.unis.wms.uitl.CountDownTimerUtil

class OtCountDownTimerHandler private constructor() {

    private val viewModel by lazy { OtCountDownTotalTimeViewModel() }
    private val countDownTimerMap = mutableMapOf<String, CountDownTimerUtil>()
    private val infoClockViewModel = InfoClockViewModel()
    private val otCountDownTotalTimeViewModel = OtCountDownTotalTimeViewModel()

    companion object {
        private const val OT_COUNT_DOWN_TIMER_TYPE_DAILY_OR_OT = "daily_or_ot"
        private const val OT_COUNT_DOWN_TIMER_TYPE_WEEKLY = "weekly"
        @Volatile
        private var instance: OtCountDownTimerHandler? = null

        fun getInstance(): OtCountDownTimerHandler =
            instance ?: synchronized(this) {
                instance ?: OtCountDownTimerHandler().also { instance = it }
            }
    }

    fun createCountDownTimer(context: Context, userId: String, onSuccess: (() -> Unit)? = null, onFailure: ((String) -> Unit)? = null) {
        viewModel.getOtCountDownTimerTotalTime(userId, {
            it?.let {
                startDailyOrOtCountDownTimer(context, userId, it)
                startWeeklyCountDownTimer(context, userId, it)
                onSuccess?.invoke()
            }?: onSuccess?.invoke()
        }, {
            onFailure?.invoke(it)
        })
    }

    fun createCountDownTimerOrRemove(context: Context, allowOTControl: Boolean, userId: String, onSuccess: (() -> Unit)? = null, onFailure: ((String) -> Unit)? = null) {
        if (allowOTControl) {
            createCountDownTimer(context, userId, onSuccess, onFailure)
        } else {
            removeAllCountDownTimer()
        }
    }

    private fun startDailyOrOtCountDownTimer(
        context: Context,
        userId: String,
        remainTimeEntity: OtCountDownTimerRemainTimeEntity,
    ) {
        val remainTime = if (remainTimeEntity.getRemainTimeOfDailyWork() > 0L) {
            remainTimeEntity.getRemainTimeOfDailyWork()
        } else if (remainTimeEntity.getRemainTimeOfOTWork() > 0L) {
            remainTimeEntity.getRemainTimeOfOTWork()
        } else if (remainTimeEntity.getRemainTimeOfOTWork() == 0L || remainTimeEntity.getRemainTimeOfDailyWork() == 0L){
            0
        } else {
            -1
        }
        if (remainTime > 0L) {//un-reach ot
            if (hasCountDownTimer(OT_COUNT_DOWN_TIMER_TYPE_DAILY_OR_OT)) {
                getCountDownTimer(OT_COUNT_DOWN_TIMER_TYPE_DAILY_OR_OT)?.stop()
            }
            val countDownTimer = CountDownTimerUtil(remainTime*1000, onFinish = {
                if (isOnTopForOtControlActivity(context)) {//Current page is OtControlActivity, no need to show OtControlActivity
                    return@CountDownTimerUtil
                }
                infoClockViewModel.punchOut(context) {//punch out and show OtControlActivity
                    startOtRequestView(context, userId, OtCountType.DAILY)
                }
            })
            countDownTimerMap[OT_COUNT_DOWN_TIMER_TYPE_DAILY_OR_OT] = countDownTimer
            countDownTimer.start()
            return
        }
        if (remainTime == 0L && !isOnTopForOtControlActivity(context)) {//reach ot
            startOtRequestView(context, userId, OtCountType.DAILY)
        }
    }

    private fun startWeeklyCountDownTimer(
        context: Context,
        userId: String,
        remainTimeEntity: OtCountDownTimerRemainTimeEntity,
    ) {
        val remainTime = remainTimeEntity.getRemainTimeOfWeeklyWork()
        if (remainTime > 0L) {//un-reach ot
            if (hasCountDownTimer(OT_COUNT_DOWN_TIMER_TYPE_WEEKLY)) {
                getCountDownTimer(OT_COUNT_DOWN_TIMER_TYPE_WEEKLY)?.stop()
            }
            val countDownTimer = CountDownTimerUtil(remainTime*1000, onFinish = {
                if (isOnTopForOtControlActivity(context)) {//Current page is OtControlActivity, no need to show OtControlActivity
                    return@CountDownTimerUtil
                }
                infoClockViewModel.punchOut(context) {//punch out and show OtControlActivity
                    startOtRequestView(context, userId, OtCountType.WEEKLY)
                }
            })
            countDownTimerMap[OT_COUNT_DOWN_TIMER_TYPE_WEEKLY] = countDownTimer
            countDownTimer.start()
            return
        }
        if (remainTime == 0L && !isOnTopForOtControlActivity(context)) {//reach ot
            startOtRequestView(context, userId, OtCountType.WEEKLY)
        }
    }

    private fun startOtRequestView(context: Context, userId: String, otCountType: OtCountType) {
        otCountDownTotalTimeViewModel.getToDayOtRequest(userId) {
            if (it?.id == null) {
                OtControlActivity.startActivity(context, OtControlConfigEntity(OtControlStep.WorkHoursAlert, otCountType))
                return@getToDayOtRequest
            }
            OtControlActivity.startActivity(context, OtControlConfigEntity(OtControlStep.RequestStatus(
                OtRequestStatusConfigEntity(it.id!!, it.status == OtRequestStatus.START)
            )))
        }
    }

    fun removeAllCountDownTimer() {
        if (countDownTimerMap.isNotEmpty()) {
            countDownTimerMap.forEach {
                it.value.stop()
            }
            countDownTimerMap.clear()
        }
    }

    private fun hasCountDownTimer(type: String): Boolean {
        return countDownTimerMap.containsKey(type)
    }

    private fun getCountDownTimer(type: String): CountDownTimerUtil? {
        return countDownTimerMap[type]
    }

    private fun isOnTopForOtControlActivity(context: Context): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val appTasks = activityManager.appTasks
        if (appTasks.isNotEmpty()) {
            val taskInfo = appTasks[0].taskInfo
            return taskInfo?.topActivity?.className == OtControlActivity::class.java.name
        }
        return false
    }
}