package com.unis.wms.ot_control.request_detail

import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OTControlSettingQueryEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.platform.ot_control.models.OtRequestUpdateEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingQueryEntity
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R

/**
 * <AUTHOR> on 2024/12/10
 */
class OtRequestDetailViewModel(
    initialDataState: OtRequestDetailDataState,
    initialUiState: OtRequestDetailUiState = OtRequestDetailUiState(),
) : ReactiveViewModel<OtRequestDetailDataState, OtRequestDetailUiState>(initialDataState, initialUiState) {

    private val repository = Repository()

    init {
        autoUpdateDataToUi()
        getEmployeeOtRequestDetail()
        loadOtControlSettings()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(OtRequestDetailDataState::otRequestDetail, OtRequestDetailUiState::otRequestDetail) {
            it
        }

        mapDataToUi(OtRequestDetailDataState::operationTypes, OtRequestDetailUiState::operationTypes) {
            it
        }
    }

    private fun getEmployeeOtRequestDetail() {
        val otRequestId = getOtRequestId()
        launch {
            request(repository.getOtRequest(otRequestId), success = {
                setDataState { copy(otRequestDetail = it) }

            })

            val detailResult = requestAwait(repository.getOtRequest(otRequestId))
            if (detailResult.isFailure || null == detailResult.getOrNull()) return@launch
            val requestDetailEntity = detailResult.getOrNull()
            setDataStateAwait { copy(otRequestDetail = requestDetailEntity) }
            val teamLaborResult = requestAwait(
                repository.searchTeamLabors(
                    TeamLaborsSettingQueryEntity(
                        teamId = requestDetailEntity!!.teamId, laborUserId = requestDetailEntity.laborUserId)))
            if (teamLaborResult.isFailure || teamLaborResult.getOrNull().isNullOrEmpty()) return@launch
            val teamLaborsSettingEntity = teamLaborResult.getOrNull()!!.first()
            setDataStateAwait { copy(operationTypes = teamLaborsSettingEntity.operationTypes) }
        }
    }

    private fun loadOtControlSettings() {
        launch {
            request(repository.getOtControlSettings(), success = {
                it?.let {
                    setDataState { copy(otControlSettings = it.list) }
                }
            })
        }
    }

    fun approveRequest() {
        val duration = getExtensionHours() * 60 + getExtensionMinutes()
        if (duration <= 0) {
            showToast(R.string.msg_please_select_extension_duration)
            return
        }
        val otRequestId = getOtRequestId()
        launch {
            request(repository.updateOtRequest(otRequestId, OtRequestUpdateEntity().apply {
                this.id = otRequestId
                this.duration = duration
                this.status = OtRequestStatus.APPROVED
            }), success = {
                fireEvent { OtRequestDetailEvent.OtRequestStatusChanged(otRequestId, OtRequestStatus.APPROVED) }
            })
        }
    }

    fun rejectRequest() {
        val otRequestId = getOtRequestId()
        launch {
            request(repository.updateOtRequest(otRequestId, OtRequestUpdateEntity().apply {
                this.id = otRequestId
                this.status = OtRequestStatus.APPROVER_REJECTED
            }), success = {
                fireEvent { OtRequestDetailEvent.OtRequestStatusChanged(otRequestId, OtRequestStatus.APPROVER_REJECTED) }
            })
        }
    }

    fun setExtensionHours(hours: Int) {
        setDataState { copy(extensionHours = hours) }
    }

    fun setExtensionMinutes(minutes: Int) {
        setDataState { copy(extensionMinutes = minutes) }
    }

    fun setRequestType(requestType: OtRequestType) {
        setDataState { copy(requestType = requestType) }
    }

    private fun getOtRequestId() = dataState.otRequestId

    fun getExtensionHours() = dataState.extensionHours

    fun getExtensionMinutes() = dataState.extensionMinutes

    fun getOtRequestDetail() = dataState.otRequestDetail

    fun getOtControlSettings() = dataState.otControlSettings
}

private class Repository : BaseRepository() {
    val otControlApi by apiServiceLazy<OtControlApi>()

    fun getOtRequest(otRequestId: Long) = requestV2({ otControlApi.getOtRequest(otRequestId) })

    fun updateOtRequest(otRequestId: Long, update: OtRequestUpdateEntity) = requestV2({ otControlApi.updateOtRequest(otRequestId, update) })

    fun getOtControlSettings() = requestV2({ otControlApi.searchOtControlSettings(OTControlSettingQueryEntity().apply { this.pageSize = 1000 }) })

    fun searchTeamLabors(search: TeamLaborsSettingQueryEntity) = requestV2({ otControlApi.searchTeamLabors(search) })
}