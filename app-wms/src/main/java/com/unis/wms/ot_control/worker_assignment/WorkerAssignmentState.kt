package com.unis.wms.ot_control.worker_assignment

import com.unis.platform.iam.model.LoginResultEntity
import com.unis.platform.ot_control.models.LaborEntity
import com.unis.platform.ot_control.models.OperationTypeSettingEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingEntity
import com.unis.platform.ot_control.models.TeamSettingEntity
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

/**
 * <AUTHOR> on 2024/12/10
 */
data class WorkerAssignmentDataState(
    val loginResultEntity: LoginResultEntity? = null,
    val userId: String? = null,
    val operations: List<String> = listOf(),
    val operationMap: Map<String, OperationTypeSettingEntity>? = null,
    val teams: List<TeamSettingEntity> = listOf(),
    val labors: List<LaborEntity> = listOf(),
    val selectTeam: TeamSettingEntity? = null,
    val selectLabor: LaborEntity? = null,
    val teamLabors: TeamLaborsSettingEntity? = null
) : ReactiveDataState


data class WorkerAssignmentUiState(val operations: List<OperationTypeSettingEntity> = listOf()) : ReactiveUiState

interface WorkerAssignmentEvent {
    data class InitTeamsData(val teams: List<String>, val selectedIndex: Int) : UiEvent
    data class InitLaborData(val labors: List<String>, val selectedIndex: Int) : UiEvent
    data class ShowAlertDialogEvent(val message: String) : UiEvent
}