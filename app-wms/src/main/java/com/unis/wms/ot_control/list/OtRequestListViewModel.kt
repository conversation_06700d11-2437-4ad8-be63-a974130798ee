package com.unis.wms.ot_control.list

import android.content.Context
import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestQueryEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.common.DatePickerDialog
import java.util.Calendar
import java.util.Date

/**
 * <AUTHOR> on 2024/12/10
 */
class OtRequestListViewModel(
    initialDataState: OtRequestListDataState = OtRequestListDataState(),
    initialUiState: OtRequestListUiState = OtRequestListUiState(),
) : ReactiveViewModel<OtRequestListDataState, OtRequestListUiState>(initialDataState, initialUiState) {

    private val repository = Repository()

    init {
        autoUpdateDataToUi()
        loadOtRequests()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(OtRequestListDataState::otRequestDetails, OtRequestListUiState::otRequestDetails) {
            it
        }

        mapDataToUi(OtRequestListDataState::selectedUser, OtRequestListUiState::selectedUser) {
            it
        }

        mapDataToUi(OtRequestListDataState::submitTimeFrom, OtRequestListUiState::submitTimeFrom) {
            it
        }

        mapDataToUi(OtRequestListDataState::submitTimeTo, OtRequestListUiState::submitTimeTo) {
            it
        }

        mapDataToUi(OtRequestListDataState::processedTimeFrom, OtRequestListUiState::processedTimeFrom) {
            it
        }

        mapDataToUi(OtRequestListDataState::processedTimeTo, OtRequestListUiState::processedTimeTo) {
            it
        }
    }

    private fun loadOtRequests(isRefresh: Boolean = true) {
        launch {
            awaitDataState()
            val result = requestAwait(repository.queryOtRequests(generalSearchEntity(isRefresh)), showLoading = isRefresh)
            if (result.isSuccess) {
                result.getOrNull()?.let {
                    val newTaskList: ArrayList<OtRequestDetailEntity> = arrayListOf()
                    if (!isRefresh) {
                        newTaskList.addAll(dataState.otRequestDetails)
                    }
                    newTaskList.addAll(it.list)

                    setDataState { copy(otRequestDetails = newTaskList.toList(), currentPage = it.currentPage, totalPage = it.totalPage) }
                }
            }
        }
    }

    fun removeOtRequest(otRequestId: Long) {
        val toMutableList = dataState.otRequestDetails.toMutableList()
        toMutableList.removeIf { it.id == otRequestId }
        setDataState { copy(otRequestDetails = toMutableList) }
    }

    private fun generalSearchEntity(
        isRefresh: Boolean
    ): OtRequestQueryEntity {
        return OtRequestQueryEntity().apply {
            this.pageSize = 20
            this.currentPage = if (isRefresh) 1 else (dataState.currentPage ?: 0) + 1
            this.status = dataState.selectedStatus

            // 根据选择的提交时间区间设置查询条件
            this.submitTimeFrom = dataState.submitTimeFrom //from before 7 day
            this.submitTimeTo = dataState.submitTimeTo

            // 根据选择的处理时间区间设置查询条件
            this.processedTimeFrom = dataState.processedTimeFrom
            this.processedTimeTo = dataState.processedTimeTo

            // 设置处理人ID
            this.processedBy = dataState.selectedUser?.userName
        }
    }

    fun onLoadMoreTask() {
        if (isLastPaging()) {
            setUiState { copy(loadMoreEnd = true) }
        } else {
            loadOtRequests(isRefresh = false)
        }
    }

    fun onRefreshTask() {
        loadOtRequests(isRefresh = true)
    }

    private fun isLastPaging(): Boolean {
        return dataState.currentPage?.let { it >= (dataState.totalPage ?: 0) } ?: true
    }


    fun setSelectedStatus(status: OtRequestStatus?) {
        setDataState { copy(selectedStatus = status) }
        onRefreshTask()
    }

    fun getStatusOptions(): List<OtRequestStatus> {
        return dataState.statusOptions
    }

    fun setSelectedUser(user: UserInfoEntity?) {
        setDataState { copy(selectedUser = user) }
        onRefreshTask()
    }

    private fun setSubmitTimeFrom(date: Date?) {
        setDataState { copy(submitTimeFrom = date) }
        onRefreshTask()
    }

    private fun setSubmitTimeTo(date: Date?) {
        setDataState { copy(submitTimeTo = date) }
        onRefreshTask()
    }

    private fun setProcessedTimeFrom(date: Date?) {
        setDataState { copy(processedTimeFrom = date) }
        onRefreshTask()
    }

    private fun setProcessedTimeTo(date: Date?) {
        setDataState { copy(processedTimeTo = date) }
        onRefreshTask()
    }


    /**
     * 显示日期选择器
     * @param context 上下文
     * @param title 标题
     * @param filterType 筛选类型，0=提交时间，1=处理时间
     * @param isFromDate 是否是开始日期，true=from，false=to
     */
    fun showDatePicker(context: Context, title: String, filterType: Int, isFromDate: Boolean) {
        val defaultDate = when {
            filterType == 0 && isFromDate -> dataState.submitTimeFrom
            filterType == 0 && !isFromDate -> dataState.submitTimeTo
            filterType == 1 && isFromDate -> dataState.processedTimeFrom
            filterType == 1 && !isFromDate -> dataState.processedTimeTo
            else -> null
        }

        DatePickerDialog(context = context, titleText = title, defaultDate = defaultDate, onDateSelected = { data -> // 设置日期的时间部分
            val calendar = Calendar.getInstance()
            calendar.time = data

            if (isFromDate) { // 开始日期设为当天的 00:00:00
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)

                if (filterType == 0) {
                    if (null != dataState.submitTimeTo && calendar.time > dataState.submitTimeTo) {
                        showToast(R.string.error_start_date_after_end_date)
                        return@DatePickerDialog
                    }
                    setSubmitTimeFrom(calendar.time)
                } else if (filterType == 1) {
                    if (null != dataState.processedTimeTo && calendar.time > dataState.processedTimeTo) {
                        showToast(R.string.error_start_date_after_end_date)
                        return@DatePickerDialog
                    }
                    setProcessedTimeFrom(calendar.time)
                }
            } else { // 结束日期设为当天的 23:59:59
                calendar.set(Calendar.HOUR_OF_DAY, 23)
                calendar.set(Calendar.MINUTE, 59)
                calendar.set(Calendar.SECOND, 59)
                calendar.set(Calendar.MILLISECOND, 999)

                if (filterType == 0) {
                    if (null != dataState.submitTimeFrom && calendar.time < dataState.submitTimeFrom) {
                        showToast(R.string.error_end_date_before_start_date)
                        return@DatePickerDialog
                    }
                    setSubmitTimeTo(calendar.time)
                } else if (filterType == 1) {
                    if (null != dataState.processedTimeFrom && calendar.time < dataState.processedTimeFrom) {
                        showToast(R.string.error_end_date_before_start_date)
                        return@DatePickerDialog
                    }
                    setProcessedTimeTo(calendar.time)
                }
            }
        }).show()
    }

    /**
     * 清除日期选择
     * @param filterType 筛选类型，0=提交时间，1=处理时间
     * @param isFromDate 是否是开始日期，true=from，false=to
     */
    fun clearDateSelection(filterType: Int, isFromDate: Boolean) {
        when {
            filterType == 0 && isFromDate -> setSubmitTimeFrom(null)
            filterType == 0 && !isFromDate -> setSubmitTimeTo(null)
            filterType == 1 && isFromDate -> setProcessedTimeFrom(null)
            filterType == 1 && !isFromDate -> setProcessedTimeTo(null)
        }
    }
}

private class Repository : BaseRepository() {
    val otControlApi by apiServiceLazy<OtControlApi>()

    fun queryOtRequests(queryEntity: OtRequestQueryEntity) = requestV2({ otControlApi.queryOtRequestsByPaging(queryEntity) })
}