package com.unis.wms.ot_control.request_detail

import com.unis.platform.ot_control.models.OTControlSettingEntity
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

/**
 * <AUTHOR> on 2024/12/10
 */
data class OtRequestDetailDataState(
    val otRequestId: Long,
    val extensionHours: Int = 0,
    val extensionMinutes: Int = 0,
    val otRequestDetail: OtRequestDetailEntity? = null,
    val requestType: OtRequestType = OtRequestType.EXTENDED_HOURS,
    val otControlSettings: List<OTControlSettingEntity> = listOf(),
    val operationTypes: List<String>? = null
) : ReactiveDataState

data class OtRequestDetailUiState(
    val otRequestDetail: OtRequestDetailEntity? = null,
    val requestType: OtRequestType = OtRequestType.EXTENDED_HOURS,
    val operationTypes: List<String>? = null
) : ReactiveUiState

interface OtRequestDetailEvent {
    data class OtRequestStatusChanged(val otRequestId: Long, val status: OtRequestStatus) : UiEvent
}