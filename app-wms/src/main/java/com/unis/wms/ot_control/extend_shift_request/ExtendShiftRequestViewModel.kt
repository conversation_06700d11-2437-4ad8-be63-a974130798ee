package com.unis.wms.ot_control.extend_shift_request

import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OTControlSettingQueryEntity
import com.unis.platform.ot_control.models.OtRequestEntity
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.platform.ot_control.models.OtSubmitTypeEntity
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.ot_control.InfoClockViewModel
import com.unis.wms.ot_control.OtControlStep
import com.unis.wms.ot_control.OtControlViewModel
import com.unis.wms.ot_control.request_status.OtRequestStatusConfigEntity

/**
 * <AUTHOR> on 2024/12/11
 */
class ExtendShiftRequestViewModel(
    initialDataState: ExtendShiftRequestDataState = ExtendShiftRequestDataState(),
    initialUiState: ExtendShiftRequestUiState = ExtendShiftRequestUiState(),
    private val activityViewModel: OtControlViewModel
) : ReactiveViewModel<ExtendShiftRequestDataState, ExtendShiftRequestUiState>(initialDataState, initialUiState) {

    private val repository = Repository()
    private val infoClockViewModel = InfoClockViewModel()

    init {
        autoUpdateDataToUi()
        getPunchHistory()
        loadOtControlSettings()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(ExtendShiftRequestDataState::state, ExtendShiftRequestUiState::state){
            it
        }
        mapDataToUi(ExtendShiftRequestDataState::todayWorkHours, ExtendShiftRequestUiState::todayWorkHours){
            it
        }
        mapDataToUi(ExtendShiftRequestDataState::weeklyWorkHours, ExtendShiftRequestUiState::weeklyWorkHours){
            it
        }
        mapDataToUi(ExtendShiftRequestDataState::requestType, ExtendShiftRequestUiState::requestType){
            it
        }
    }

    fun submitShift(extendsReason: String, teamId: Long?, laborTypeId: Long?) {
        val duration = getExtensionHours() * 60 + getExtensionMinutes()
        if (duration <= 0) {
            showToast(R.string.msg_please_select_extension_duration)
            return
        }
        if (extendsReason.isEmpty()) {
            showToast(R.string.hint_please_provide_reason)
            return
        }
        launch {
            request(repository.createOtRequest(OtRequestEntity().apply {
                this.laborUserId = repository.idmUserId
                this.requestType = dataState.requestType
                this.duration = duration
                this.reason = extendsReason
                this.state = dataState.state
                this.teamId = teamId
                this.laborTypeId = laborTypeId
                this.dailyWorkHours = getTodayWorkHours()
                this.weeklyWorkHours = getWeeklyWorkHours()
                this.submitType = OtSubmitTypeEntity.SELF_APPLY
            }), success = {
                it?.let {
                    activityViewModel.updateOtControlStep(OtControlStep.RequestStatus(OtRequestStatusConfigEntity(it.id.toLong())))
                }
            })
        }

    }

    fun setState(state: String?) {
        setDataState { copy(state = state) }
    }

    fun setExtensionHours(hours: Int) {
        setDataState { copy(extensionHours = hours) }
    }

    fun setExtensionMinutes(minutes: Int) {     
        setDataState { copy(extensionMinutes = minutes) }
    }

    fun setRequestType(requestType: OtRequestType) {
        setDataState { copy(requestType = requestType) }
    }

    private fun getPunchHistory() {
        infoClockViewModel.getTodayHoursAndWeeklyHours { todayWorkHours, weeklyWorkHours ->
            setDataState { copy(todayWorkHours = todayWorkHours, weeklyWorkHours = weeklyWorkHours) }
        }
    }

    private fun loadOtControlSettings() {
        launch {
            request(repository.getOtControlSettings(), success = {
                it?.let {
                    setDataState { copy(otControlSettings = it.list) }
                }
            })
        }
    }

    fun getExtensionHours() = dataState.extensionHours

    fun getExtensionMinutes() = dataState.extensionMinutes

    fun getTodayWorkHours() = dataState.todayWorkHours

    fun getWeeklyWorkHours() = dataState.weeklyWorkHours

    fun getOtControlSettings() = dataState.otControlSettings

}

private class Repository : BaseRepository() {

    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun createOtRequest(request: OtRequestEntity) = requestV2({otControlApi.createOtRequest(request)})

    fun getOtControlSettings() = requestV2({otControlApi.searchOtControlSettings(OTControlSettingQueryEntity().apply { this.pageSize = 1000 })})

}