package com.unis.wms.ot_control.worker_assignment

import com.unis.platform.ot_control.models.OperationTypeSettingEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.databinding.ItemEmployeeSkillsBinding

/**
 * <AUTHOR> on 2024/12/12
 */
class WorkerAssignmentAdapter(val remove: (OperationTypeSettingEntity) -> Unit) : BaseBindingDifferQuickAdapter<OperationTypeSettingEntity, ItemEmployeeSkillsBinding>() {
    override fun areContentsTheSame(oldItem: OperationTypeSettingEntity, newItem: OperationTypeSettingEntity): Boolean {
        return oldItem == newItem
    }

    override fun convert(helper: BaseBindingViewHolder<ItemEmployeeSkillsBinding>?, item: OperationTypeSettingEntity) {
        helper?.binding?.apply {
            tvName.text = item.operationType
            btnRemove.setOnClickListener { remove(item) }
        }
    }

    override fun areItemsTheSame(oldItem: OperationTypeSettingEntity, newItem: OperationTypeSettingEntity): Boolean {
        return oldItem.id == newItem.id && oldItem.operationType == newItem.operationType && oldItem.taskTypes == newItem.taskTypes
    }
}