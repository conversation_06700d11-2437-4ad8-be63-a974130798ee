package com.unis.wms.ot_control.request_status

import com.google.gson.annotations.SerializedName
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent
import java.io.Serializable

/**
 * <AUTHOR> on 2024/12/10
 */
data class RequestStatusDataState(
    val configEntity: OtRequestStatusConfigEntity,
    val otRequestDetail: OtRequestDetailEntity? = null,
) : ReactiveDataState


data class RequestStatusUiState(
    val requestStatus: OtRequestStatus? = null,
    val otRequestDetail: OtRequestDetailEntity? = null,
) : ReactiveUiState

interface RequestStatusEvent {

    object StartedExtendShift: UiEvent
}

data class OtRequestStatusConfigEntity(
    @SerializedName("otRequestId")
    val otRequestId: Long,

    @SerializedName("isOtReach")
    var isOtReach: Boolean = false,
) : Serializable {
    companion object {
        val TAG: String = OtRequestStatusConfigEntity::class.java.simpleName
    }
}