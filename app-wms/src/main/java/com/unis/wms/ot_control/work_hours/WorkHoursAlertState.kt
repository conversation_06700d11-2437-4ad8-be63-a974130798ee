package com.unis.wms.ot_control.work_hours

import com.unis.platform.ot_control.models.OtCountType
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState

/**
 * <AUTHOR> on 2024/12/10
 */
data class WorkHoursAlertDataState(
    val state: String? = null,
    val todayWorkHours: Double = 0.0,
    val weeklyWorkHours: Double = 0.0,
    val otCountType: OtCountType = OtCountType.DAILY,
) : ReactiveDataState


data class WorkHoursAlertUiState(
    val state: String? = null,
    val todayWorkHours: Double = 0.0,
    val weeklyWorkHours: Double = 0.0,
) : ReactiveUiState