package com.unis.wms.ot_control

import android.content.Context
import com.unis.platform.ot_control.models.OTControlSettingEntity
import com.unis.platform.ot_control.models.OtControlSettingType
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.wms.R

object OtControlHelper {

    const val THRESHOLD_DAILY_OT = 8
    const val THRESHOLD_DAILY_DT = 12
    const val THRESHOLD_WEEKLY_OT = 40

    fun getHoursList(): List<String> {
        return listOf("0","1","2","3","4","5","6","7","8")
    }

    fun getMinutesList(): List<String> {
        return listOf("0","15","30","45")
    }

    fun minutesToHours(minutes: Int): Double {
        return String.format("%.2f", minutes / 60.0).toDouble()
    }

    fun formatDuration(context: Context?, durationInMinutes: Int?): String {
        if (context == null || durationInMinutes == null) {
            return ""
        }
        val hours = durationInMinutes / 60
        val minutes = durationInMinutes % 60
        return when {
            hours > 0 && minutes > 0 -> "$hours ${context.getString(R.string.text_hours)} $minutes ${context.getString(R.string.text_minutes)}"
            hours > 0 -> "$hours ${context.getString(R.string.text_hours)}"
            minutes > 0 -> "$minutes ${context.getString(R.string.text_minutes)}"
            else -> "0 ${context.getString(R.string.text_minutes)}"
        }
    }

    private fun generateThresholdMap(): Map<OtControlSettingType, Int> {
        val thresholdMap = mutableMapOf<OtControlSettingType, Int>()
        thresholdMap[OtControlSettingType.DAILY_OT] = THRESHOLD_DAILY_OT
        thresholdMap[OtControlSettingType.DAILY_DT] = THRESHOLD_DAILY_DT
        thresholdMap[OtControlSettingType.WEEKLY_OT] = THRESHOLD_WEEKLY_OT
        return thresholdMap
    }

    private fun getThreshold(otControlSettingType: OtControlSettingType): Int {
        val thresholdMap = generateThresholdMap()
        return thresholdMap[otControlSettingType] ?: 0
    }

    /**
     * Determine the request type based on the work hours with extension duration and the ot control setting types
     * @param workHoursWithExtensionDuration The work hours with extension duration (If greater than 40 hours per week, weekly hours + requested OT hours. Otherwise, daily hours + requested OT hours)
     * @param otControlSettingTypes The ot control setting types (combination of DAILY_OT, DAILY_DT, WEEKLY_OT)
     * @return The request type
     */
    fun determineRequestType(workHoursWithExtensionDuration: Double, otControlSettingTypes: List<OTControlSettingEntity>?): OtRequestType {
        if (otControlSettingTypes.isNullOrEmpty()) {
            return OtRequestType.EXTENDED_HOURS
        }
        if (otControlSettingTypes.size == 1) { // One of DAILY_OT, DAILY_DT, WEEKLY_OT
            val otControlSettingType = otControlSettingTypes[0].type
            return when {
                otControlSettingType == OtControlSettingType.DAILY_OT && workHoursWithExtensionDuration > getThreshold(otControlSettingType) -> OtRequestType.DAILY_OT
                otControlSettingType == OtControlSettingType.DAILY_DT && workHoursWithExtensionDuration > getThreshold(otControlSettingType) -> OtRequestType.DAILY_DT
                otControlSettingType == OtControlSettingType.WEEKLY_OT && workHoursWithExtensionDuration > getThreshold(otControlSettingType) -> OtRequestType.WEEKLY_OT
                else -> OtRequestType.EXTENDED_HOURS
            }
        }
        if (otControlSettingTypes.size == 2) { // Any two combinations of DAILY_OT, DAILY_DT, WEEKLY_OT
            val type1 = otControlSettingTypes[0].type
            val type2 = otControlSettingTypes[1].type
            return when {
                type1 == OtControlSettingType.DAILY_OT && type2 == OtControlSettingType.DAILY_DT -> {
                    when {
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_DT) -> OtRequestType.DAILY_DT
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_OT) -> OtRequestType.DAILY_OT
                        else -> OtRequestType.EXTENDED_HOURS
                    }
                }
                type1 == OtControlSettingType.DAILY_OT && type2 == OtControlSettingType.WEEKLY_OT -> {
                    when {
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.WEEKLY_OT) -> OtRequestType.WEEKLY_OT
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_OT) -> OtRequestType.DAILY_OT
                        else -> OtRequestType.EXTENDED_HOURS
                    }
                }
                type1 == OtControlSettingType.DAILY_DT && type2 == OtControlSettingType.WEEKLY_OT -> {
                    when {
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.WEEKLY_OT) -> OtRequestType.WEEKLY_OT
                        workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_DT) -> OtRequestType.DAILY_DT
                        else -> OtRequestType.EXTENDED_HOURS
                    }
                }
                else -> OtRequestType.EXTENDED_HOURS
            }
        }
        if (otControlSettingTypes.size == 3) { // All three types: DAILY_OT, DAILY_DT, WEEKLY_OT
            return when {
                workHoursWithExtensionDuration > getThreshold(OtControlSettingType.WEEKLY_OT) -> OtRequestType.WEEKLY_OT
                workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_DT) -> OtRequestType.DAILY_DT
                workHoursWithExtensionDuration > getThreshold(OtControlSettingType.DAILY_OT) -> OtRequestType.DAILY_OT
                else -> OtRequestType.EXTENDED_HOURS
            }
        }
        return OtRequestType.EXTENDED_HOURS
    }

}