package com.unis.wms.ot_control

import android.content.Context
import com.linc.platform.core.LocalPersistence
import com.linc.platform.http.HttpService
import com.linc.platform.infoclock.InfoClockApi
import com.linc.platform.infoclock.model.PunchInHistoryReqEntry
import com.linc.platform.infoclock.model.PunchInReqEntry
import com.linc.platform.infoclock.model.base.BaseInfoClockRequest
import com.linc.platform.utils.NetWorkUtil
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.TimeUtil
import com.linc.platform.utils.ToastUtil
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OtCountDownTimerQueryEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.BaseVM
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import kotlin.getValue

class InfoClockViewModel: BaseVM() {

    private val repository: InfoClockRepository = InfoClockRepository()

    fun getTodayHoursAndWeeklyHours(success: (Double, Double) -> Unit) {
//        val employeeInfoEntry = LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()) ?: return
//        val weekDateList = TimeUtil.getWeekDateList(System.currentTimeMillis())
//        val reqEntry = BaseInfoClockRequest(
//            PunchInHistoryReqEntry(
//                employeeInfoEntry.employeeId,
//                TimeUtil.getDateToString(weekDateList.first(), TimeUtil.FORMAT_YYYY_MM_DD),
//                TimeUtil.getDateToString(weekDateList.last(), TimeUtil.FORMAT_YYYY_MM_DD),
//                TimeUtil.getTimeZoneOffsetHrs()))
//        launch {
//            request(repository.getEmployeePunchHistory(reqEntry), success = {
//                val todayWorkHours = InfoClockHelper.getToDayHours(employeeInfoEntry, it)
//                val weeklyWorkHours = InfoClockHelper.getWeeklyHours(employeeInfoEntry, it)
//                success.invoke(todayWorkHours, weeklyWorkHours)
//            })
//        }

        launch {
            val result = requestAwait(repository.getOtCountDownTimerTotalTime(repository.idmUserId))
            if (result.isFailure) return@launch
            val useWorkHour = result.getOrNull()?.find { it.userId == repository.idmUserId }
            useWorkHour ?: return@launch
            success.invoke(useWorkHour.dailyWorkHours ?: 0.0, useWorkHour.weeklyWorkHours ?: 0.0)
        }
    }

    fun punchOut(context: Context, success: () -> Unit) {
        checkFacilityIPToPunch(context, success)
    }

    private fun punchOut(isIpCheckSuccess: Boolean, ipAddress: String?, success: () -> Unit) {
        val employeeInfoEntry = LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()) ?: return
        val punchInReqEntry = BaseInfoClockRequest(
            PunchInReqEntry(
                employeeInfoEntry.employeeId,
                employeeInfoEntry.firstName,
                employeeInfoEntry.lastName,
                null,
                TimeUtil.getTimeZoneOffsetHrs(),
                if (isIpCheckSuccess) ipAddress else null,
                if (isIpCheckSuccess) null else LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()).facilityCode,
                true))
        launch {
            request(repository.punchOut(punchInReqEntry), error = {
                ToastUtil.showToast(it.error)
            }, success = {
                success.invoke()
            })
        }
    }

    private fun checkFacilityIPToPunch(context: Context, success: () -> Unit) {
        val ipAddress = NetWorkUtil.getIpAddress(context)
        launch {
            request(repository.getFacilityByIP(ipAddress), error = {
                checkFacilityCodeToPunch(success)
            }, success = {
                punchOut(true, ipAddress, success)
            })
        }
    }

    private fun checkFacilityCodeToPunch(success: () -> Unit) {
        val facilityCode = LocalPersistence.getEmployeeInfoEntry(ResUtil.getContext()).facilityCode
        launch {
            request(repository.checkCanPunchByFacilityCode(facilityCode), error = {
                ToastUtil.showToast(it.error)
            }, success = {
                punchOut(false, null, success)
            })
        }
    }
}

private class InfoClockRepository : BaseRepository() {

    private val infoClockApi = HttpService.createInfoClockService(InfoClockApi::class.java, -1)
    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun getFacilityByIP(ip: String?) = rxRequestInfoClock(infoClockApi.getFacilityByIP(ip))

    fun checkCanPunchByFacilityCode(code: String?) = rxRequestInfoClock(infoClockApi.checkCanPunchByFacilityCode(code))

    fun getEmployeePunchHistory(request: BaseInfoClockRequest<PunchInHistoryReqEntry>) = rxRequestInfoClock(infoClockApi.getEmployeePunchHistory(request))

    fun punchOut(reqEntry: BaseInfoClockRequest<PunchInReqEntry>) = rxRequestInfoClock(infoClockApi.punchOut(reqEntry))

    fun getOtCountDownTimerTotalTime(userId: String) = requestV2({otControlApi.getOtCountDownTimerTotalTime(OtCountDownTimerQueryEntity().apply {
        this.laborUserId = userId
    })})

}