package com.unis.wms.ot_control.request_detail

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.customer.widget.LabelSpinner
import com.customer.widget.core.ActivityBundleHolder
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.customer.widget.extensions.setVisibleOrGone
import com.linc.platform.utils.TimeUtil
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.platform.ot_control.models.OtRequestType
import com.unis.platform.ot_control.models.getDisplayName
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.reactivemvi.mvvm.kotlin.extensions.universalParam
import com.unis.wms.R
import com.unis.wms.databinding.ActivityOtRequestDetailBinding
import com.unis.wms.ot_control.OtControlHelper
import com.unis.wms.ot_control.OtControlHelper.minutesToHours
import java.io.Serializable

/**
 * <AUTHOR> on 2024/12/11
 */
class OtRequestDetailActivity : ReactiveActivity<OtRequestDetailViewModel, OtRequestDetailUiState, ActivityOtRequestDetailBinding>() {

    data class Param(
        val otRequestId: Long,
    ) : UniversalActivityParam

    companion object {
        const val REQUEST_CODE_OT_REQUEST_DETAIL = 0x001
        const val OT_REQUEST_ID = "ot-request-id"
        const val OT_REQUEST_STATUS = "ot-request-status"
        fun startActivityForResult(fragment: Fragment, otRequestId: Long) {
            fragment.context ?: return
            val intent = Intent()
            val clazz = OtRequestDetailActivity::class.java
            intent.setClass(fragment.context!!, clazz)
            val param = Param(otRequestId = otRequestId)
            ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
            fragment.startActivityForResult(intent, REQUEST_CODE_OT_REQUEST_DETAIL)
        }

        fun startActivityForResult(activity: AppCompatActivity, otRequestId: Long) {
            val intent = Intent()
            val clazz = OtRequestDetailActivity::class.java
            intent.setClass(activity, clazz)
            val param = Param(otRequestId = otRequestId)
            ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
            activity.startActivityForResult(intent, REQUEST_CODE_OT_REQUEST_DETAIL)
        }
    }

    override fun createViewModel(): OtRequestDetailViewModel {
        val param = universalParam as Param
        return OtRequestDetailViewModel(OtRequestDetailDataState(otRequestId = param.otRequestId))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.extension_request_details)
            val hoursList = OtControlHelper.getHoursList()
            val minutesList = OtControlHelper.getMinutesList()
            extendHoursSpinner.setDataOptions(hoursList.map { "${it}h" })
            extendMinutesSpinner.setDataOptions(minutesList.map { "${it}m" }, "${minutesList[1]}m")
            extendHoursSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val hours = hoursList[position].toInt()
                    viewModel.setExtensionHours(hours)
                    val weeklyWorkHours = viewModel.getOtRequestDetail()?.dailyWorkHours ?: 0.0
                    val todayHours = viewModel.getOtRequestDetail()?.weeklyWorkHours ?: 0.0
                    val requestType = if (weeklyWorkHours > OtControlHelper.THRESHOLD_WEEKLY_OT) {
                        val workHoursWithExtensionDuration = weeklyWorkHours + hours + minutesToHours(viewModel.getExtensionMinutes())
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    } else {
                        val workHoursWithExtensionDuration = todayHours + hours + minutesToHours(viewModel.getExtensionMinutes())
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    }
                    viewModel.setRequestType(requestType)
                }
            })
            extendMinutesSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val minutes = minutesList[position].toInt()
                    viewModel.setExtensionMinutes(minutes)
                    val weeklyWorkHours = viewModel.getOtRequestDetail()?.dailyWorkHours ?: 0.0
                    val todayHours = viewModel.getOtRequestDetail()?.weeklyWorkHours ?: 0.0
                    val requestType = if (weeklyWorkHours > OtControlHelper.THRESHOLD_WEEKLY_OT) {
                        val workHoursWithExtensionDuration = weeklyWorkHours + viewModel.getExtensionHours() + minutesToHours(minutes)
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    } else {
                        val workHoursWithExtensionDuration = todayHours + viewModel.getExtensionHours() + minutesToHours(minutes)
                        OtControlHelper.determineRequestType(workHoursWithExtensionDuration, viewModel.getOtControlSettings())
                    }
                    viewModel.setRequestType(requestType)
                }
            })
            extendHoursSpinner.setEnable(userInfoEntity.isApprover())
            extendMinutesSpinner.setEnable(userInfoEntity.isApprover())
            approveRequestBtn.setOnClickListener {
                if (userInfoEntity.isApprover()) {
                    viewModel.approveRequest()
                } else {
                    finish()
                }
            }
            rejectRequestBtn.setOnClickListener {
                if (userInfoEntity.isApprover()) {
                    viewModel.rejectRequest()
                } else {
                    finish()
                }
            }
        }
        onOtRequestStatusChangedEvent()
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showOtRequestDetail()
        showRequestType()
        showOperationTypes()
    }

    private fun ReactiveViewScope.showRequestType() = subscribe(OtRequestDetailUiState::requestType) {
        binding.apply {
            requestTypeTv.text = it.getDisplayName()
        }
    }

    private fun ReactiveViewScope.showOtRequestDetail() = subscribe(OtRequestDetailUiState::otRequestDetail) {
        binding.apply {
            it?.let {
                employeeTv.text = it.laborUserName
                teamNameTv.text = it.teamName
                laborTypeTv.text = it.laborTypeName
                operationTypeTv.text = ""
                stateTv.text = it.state
                todayWorkHoursTv.text = it.dailyWorkHours.toString()
                weeklyHoursTv.text = it.weeklyWorkHours.toString()
                viewModel.setRequestType(it.requestType ?: OtRequestType.EXTENDED_HOURS)
                requestTimeTv.text =
                    TimeUtil.formatDataString(TimeUtil.local2Default(it.createdTime), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_MM_DD_YYYY_HH_MM)
                reasonForExtensionTv.text = it.reason
                approveRequestBtn.setVisibleOrGone(userInfoEntity.isApprover() && it.status == OtRequestStatus.PENDING)
                rejectRequestBtn.setVisibleOrGone(userInfoEntity.isOtSupervisorOrApprover() && it.status == OtRequestStatus.PENDING)
                if (userInfoEntity.isOnlySupervisor()) {
                    rejectRequestBtn.text = getString(R.string.text_ok)
                }
                setExtensionDuration(it.duration)
            }
        }
    }

    private fun ReactiveViewScope.showOperationTypes() = subscribe(OtRequestDetailUiState::operationTypes) {
        it?.let {
            binding.apply {
                val joinToString = it.joinToString(separator = " . ")
                operationTypeTv.text = joinToString
            }
        }
    }

    private fun setExtensionDuration(duration: Int?) {
        binding.apply {
            duration?.let {
                val hours = it / 60
                val minutes = it % 60
                val hoursList = OtControlHelper.getHoursList()
                val minutesList = OtControlHelper.getMinutesList()
                extendHoursSpinner.setSelectedPosition(hoursList.indexOf(hours.toString()))
                extendMinutesSpinner.setSelectedPosition(minutesList.indexOf(minutes.toString()))
                viewModel.setExtensionHours(hours)
                viewModel.setExtensionMinutes(minutes)
            }
        }
    }

    private fun onOtRequestStatusChangedEvent() = onEvent<OtRequestDetailEvent.OtRequestStatusChanged> {
        val data = Intent()
        data.putExtra(OT_REQUEST_ID, otRequestId)
        data.putExtra(OT_REQUEST_STATUS, status)
        setResult(Activity.RESULT_OK, data)
        finish()
    }

}