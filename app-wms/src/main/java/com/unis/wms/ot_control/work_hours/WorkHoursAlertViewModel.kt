package com.unis.wms.ot_control.work_hours

import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.wms.ot_control.InfoClockViewModel
import com.unis.wms.ot_control.OtControlStep
import com.unis.wms.ot_control.OtControlViewModel

/**
 * <AUTHOR> on 2024/12/11
 */
class WorkHoursAlertViewModel(
    initialDataState: WorkHoursAlertDataState = WorkHoursAlertDataState(),
    initialUiState: WorkHoursAlertUiState = WorkHoursAlertUiState(),
    private val activityViewModel: OtControlViewModel
) : ReactiveViewModel<WorkHoursAlertDataState, WorkHoursAlertUiState>(initialDataState, initialUiState) {

    private val infoClockViewModel = InfoClockViewModel()

    init {
        autoUpdateDataToUi()
        getPunchHistory()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(WorkHoursAlertDataState::state, WorkHoursAlertUiState::state){
            it
        }
        mapDataToUi(WorkHoursAlertDataState::todayWorkHours, WorkHoursAlertUiState::todayWorkHours){
            it
        }
        mapDataToUi(WorkHoursAlertDataState::weeklyWorkHours, WorkHoursAlertUiState::weeklyWorkHours){
            it
        }
    }

    fun toRequestExtendShift(){
        activityViewModel.updateOtControlStep(OtControlStep.ExtendShiftRequest)
    }

    fun setState(state: String?) {
        setDataState { copy(state = state) }
    }

    private fun getPunchHistory() {
        infoClockViewModel.getTodayHoursAndWeeklyHours { todayWorkHours, weeklyWorkHours ->
            setDataState { copy(todayWorkHours = todayWorkHours, weeklyWorkHours = weeklyWorkHours) }
        }
    }

    fun endWork() {
        activityViewModel.endWork()
    }

    fun getOtCountType() = activityViewModel.getOtCountType()
}