package com.unis.wms.ot_control.worker_assignment

import com.linc.platform.http.ErrorResponse2
import com.linc.platform.utils.ResUtil
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.LaborTypeQueryEntity
import com.unis.platform.ot_control.models.OperationTypeSettingEntity
import com.unis.platform.ot_control.models.ShiftSettingQueryEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingCreateEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingQueryEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingUpdateEntity
import com.unis.platform.ot_control.models.TeamSettingEntity
import com.unis.platform.ot_control.models.TeamSettingQueryEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.requestAllAwait
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.wms.R
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * <AUTHOR> on 2024/12/11
 */
class WorkerAssignmentViewModel(
    initialDataState: WorkerAssignmentDataState = WorkerAssignmentDataState(),
    initialUiState: WorkerAssignmentUiState = WorkerAssignmentUiState(),
) : ReactiveViewModel<WorkerAssignmentDataState, WorkerAssignmentUiState>(initialDataState, initialUiState) {
    private val repository = WorkerAssignmentRepo()

    init {
        mapDataToUi(
            WorkerAssignmentDataState::operations,
            WorkerAssignmentDataState::selectTeam,
            WorkerAssignmentUiState::operations
        ) { operations, selectTeam ->
            selectTeam?.serviceTypeMap?.let {
                operations.mapNotNull { operation -> it[operation] }
            } ?: listOf()
        }
        getTeamsAndLabors()
    }


    fun removeOperationType(item: String) {
        val list = dataState.operations.toMutableList()
        list.remove(item)
        setDataState { copy(operations = list) }
    }

    private fun getTeamsAndLabors() {
        launch {
            val (teamsResult, shiftResult, laborsResult) = requestAllAwait(repository.getTeams(), repository.getShifts(), repository.getLabors())
            if (teamsResult.isFailure || shiftResult.isFailure || laborsResult.isFailure) return@launch
            val teamSettingEntities = teamsResult.getOrNull() ?: listOf()
            val shifts = shiftResult.getOrNull()?.list ?: listOf()
            val labors = laborsResult.getOrNull()?.list ?: listOf()
            if(teamSettingEntities.isEmpty()){
                showSnack(SnackType.ErrorV1(), R.string.no_team_tips)
                return@launch
            }
            if(labors.isEmpty()){
                showSnack(SnackType.ErrorV1(), R.string.no_labor_type_tips)
                return@launch
            }
            val teamLaborsResult = requestAwait(repository.getTeamLabors(TeamLaborsSettingQueryEntity().apply {
                this.laborUserId = getLoginResultEntity()?.userInfo?.userId ?: dataState.userId
            }))
            if (teamLaborsResult.isFailure) return@launch
            val teamLabors = teamLaborsResult.getOrNull() ?: listOf()
            var teamSelectedIndex = -1
            var laborSelectedIndex = -1
            var teamLaborsSettingEntity: TeamLaborsSettingEntity? = null
            if (teamLabors.isNotEmpty()) {
                teamLaborsSettingEntity = teamLabors[0]
                teamSelectedIndex = teamSettingEntities.indexOfFirst { it.id == teamLaborsSettingEntity.teamId }
                laborSelectedIndex = labors.indexOfFirst { it.id == teamLaborsSettingEntity.laborTypeId }
            }
            teamSettingEntities.forEach { teamSettingEntity ->
                val shift = shifts.firstOrNull { it.id == teamSettingEntity.shiftId }
                teamSettingEntity.shift = shift
            }
            val teamShowNames =
                teamSettingEntities.map { team -> team.name + " " + team.shift?.shiftName + " (" + team.shift?.workHourFrom + "-" + team.shift?.workHourTo + ")" }
                    .toList()
            val laborsStrings = labors.map { team -> team.name ?: "" }.toList()
            fireEvent { WorkerAssignmentEvent.InitTeamsData(teamShowNames, teamSelectedIndex) }
            fireEvent { WorkerAssignmentEvent.InitLaborData(laborsStrings, laborSelectedIndex) }
            setDataState { copy(teams = teamSettingEntities.toList(), labors = labors, teamLabors = teamLaborsSettingEntity) }
            initOperationTypes(teamSettingEntities, teamSelectedIndex)
        }
    }

    private fun initOperationTypes(teamSettingEntities: List<TeamSettingEntity>, teamSelectedIndex: Int) {
        if (teamSettingEntities.size == 1) {
            setOperationType(teamSettingEntities[0].serviceTypes?: listOf())
            return
        }
        if (teamSelectedIndex != -1) {
            setOperationType(teamSettingEntities[teamSelectedIndex].serviceTypes?: listOf())
        }
    }

    fun selectedTeam(position: Int) {
        if (position < 0) return
        val teamSettingEntity = dataState.teams[position]
        setDataState { copy(selectTeam = teamSettingEntity) }
        setOperationType(teamSettingEntity.serviceTypes?: listOf())
    }

    fun selectedLabor(position: Int) {
        if (position < 0) return
        val teamSettingEntity = dataState.labors[position]
        setDataState { copy(selectLabor = teamSettingEntity) }
    }

    fun updateOperationType(onSelect: (List<OperationTypeSettingEntity>) -> Flow<OperationTypeSettingEntity?>) {
        if (null == dataState.selectTeam) {
            showSnack(SnackType.ErrorV1(), R.string.please_select_team)
            return
        }
        if (dataState.selectTeam!!.serviceTypes.isNullOrEmpty()) {
            showSnack(SnackType.ErrorV1(), R.string.no_operation_type_tips)
            return
        }
        launch {
            val operationType = onSelect(dataState.selectTeam!!.serviceTypeMap?.values?.toList() ?: listOf()).firstOrNull()?.id?.toString() ?: return@launch
            val toMutableList = dataState.operations.toMutableList()
            if (toMutableList.contains(operationType)) {
                showSnack(SnackType.ErrorV1(), R.string.multiple_operation_type)
                return@launch
            }
            toMutableList.add(operationType)
            setDataState { copy(operations = toMutableList.toList()) }
        }
    }

    fun setOperationType(operationTypes: List<String>) {
        setDataState { copy(operations = operationTypes) }
    }

    fun confirmWorkerAssignment(onSuccess: (Long, Long) -> Unit) {
        validateWorkerAssignment {
            if (isEditWorkerAssignment()) {
                updateWorkerAssignment(onSuccess)
            } else {
                createWorkerAssignment(onSuccess)
            }
        }
    }

    private fun createWorkerAssignment(onSuccess: (Long, Long) -> Unit) {
        val team = dataState.selectTeam
        val labor = dataState.selectLabor
        val operations = dataState.operations
        launch {
            val teamLaborsSettingCreateEntity = TeamLaborsSettingCreateEntity().apply {
                this.teamId = team?.id
                this.laborTypeId = labor?.id
                this.operationTypes = operations
                this.laborUserId = getLoginResultEntity()?.userInfo?.userId ?: dataState.userId
            }
            requestAwait(repository.createTeamLabors(teamLaborsSettingCreateEntity), error = {
                onWorkerAssignmentError(it)
            }).onSuccess {
                onSuccess.invoke(team?.id ?: 0, labor?.id ?: 0)
            }
        }
    }

    private fun updateWorkerAssignment(onSuccess: (Long, Long) -> Unit) {
        val team = dataState.selectTeam
        val labor = dataState.selectLabor
        val operations = dataState.operations
        val teamLaborId = dataState.teamLabors?.id
        teamLaborId?: return
        launch {
            val teamLaborsSettingUpdateEntity = TeamLaborsSettingUpdateEntity().apply {
                this.id = teamLaborId
                this.teamId = team?.id
                this.laborTypeId = labor?.id
                this.operationTypes = operations
                this.laborUserId = getLoginResultEntity()?.userInfo?.userId ?: dataState.userId
            }
            requestAwait(repository.updateTeamLabors(teamLaborId, teamLaborsSettingUpdateEntity), error = {
                onWorkerAssignmentError(it)
            }).onSuccess {
                onSuccess.invoke(team?.id ?: 0, labor?.id ?: 0)
            }
        }
    }

    private fun onWorkerAssignmentError(errorResponse: ErrorResponse2) {
        if (errorResponse.code == 40600) {
            fireEvent { WorkerAssignmentEvent.ShowAlertDialogEvent(ResUtil.getString(R.string.msg_maximum_labor_limit_reached)) }
            return
        }
        showSnack(SnackType.ErrorV1(), errorResponse.error)
    }

    private fun validateWorkerAssignment(action: () -> Unit) {
        val team = dataState.selectTeam
        val labor = dataState.selectLabor
        val operations = dataState.operations
        if (team == null) {
            showSnack(SnackType.ErrorV1(), R.string.please_select_team)
            return
        }
        if (labor == null) {
            showSnack(SnackType.ErrorV1(), R.string.please_select_labor)
            return
        }
        if (operations.isEmpty()) {
            showSnack(SnackType.ErrorV1(), R.string.please_add_operation_type)
            return
        }
        action()
    }

    fun getTeamSettings() = dataState.teams

    private fun isEditWorkerAssignment() = dataState.teamLabors != null

    fun getLoginResultEntity() = dataState.loginResultEntity
}

private class WorkerAssignmentRepo : BaseRepository() {

    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun getTeams() = requestV2({otControlApi.searchTeams(TeamSettingQueryEntity().apply { this.pageSize = 1000 })})

    fun getLabors() = requestV2({otControlApi.searchLabors(LaborTypeQueryEntity().apply { this.pageSize = 1000 })})

    fun getShifts() = requestV2({otControlApi.searchShifts(ShiftSettingQueryEntity().apply { this.pageSize = 1000 })})

    fun getTeamLabors(query: TeamLaborsSettingQueryEntity) = requestV2({otControlApi.searchTeamLabors(query)})

    fun createTeamLabors(teamLaborsSettingCreateEntity: TeamLaborsSettingCreateEntity) = requestV2({otControlApi.createTeamLabors(teamLaborsSettingCreateEntity)})

    fun updateTeamLabors(id: Long, teamLaborsSettingUpdateEntity: TeamLaborsSettingUpdateEntity) = requestV2({otControlApi.updateTeamLabors(id, teamLaborsSettingUpdateEntity)})

}