package com.unis.wms.ot_control

import com.unis.platform.ot_control.models.OtCountType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.wms.R

/**
 * <AUTHOR> on 2024/12/10
 */
class OtControlViewModel(
    initialDataState: OtControlDataState,
    initialUiState: OtControlUiState = OtControlUiState(),
) : ReactiveViewModel<OtControlDataState, OtControlUiState>(initialDataState, initialUiState) {

    init {
        updateOtControlStep(dataState.otControlConfigEntity.otControlStep)
        dataState.otControlConfigEntity.otCountType?.let { setOtCountType(it) }
    }

    fun updateOtControlStep(step: OtControlStep) {
        fireEvent { OtControlEvent.UpdateOtControlStep(step) }
        val otControlTitle = when (step) {
            is OtControlStep.WorkerAssignment -> OtControlTitle(R.string.worker_assignment, false)
            OtControlStep.ExtendShiftRequest -> OtControlTitle(R.string.extend_shift_request, true)
            is OtControlStep.RequestStatus -> OtControlTitle(R.string.extension_request_status, false)
            OtControlStep.WorkHoursAlert -> OtControlTitle(R.string.work_hours_alert, false)
        }
        updateToolbarTitle(otControlTitle)
    }

    fun updateToolbarTitle(otControlTitle: OtControlTitle) {
        setUiState { copy(otControlTitle = otControlTitle) }
    }

    fun endWork(){
        fireEvent { OtControlEvent.EndWork }
    }

    fun setOtCountType(otCountType: OtCountType) {
        setDataState { copy(otCountType = otCountType) }
    }

    fun getOtCountType() = dataState.otCountType

    fun getOtControlConfigEntity() = dataState.otControlConfigEntity
}