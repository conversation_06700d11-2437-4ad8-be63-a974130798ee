package com.unis.wms.ot_control.list

import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import java.util.Date

/**
 * <AUTHOR> on 2024/12/10
 */
data class OtRequestListDataState(
    val otRequestDetails: List<OtRequestDetailEntity> = listOf(),
    val currentPage: Int? = null,
    val totalPage: Int? = null,
    val selectedStatus: OtRequestStatus? = null,
    val selectedUser: UserInfoEntity? = null,
    val submitTimeFrom: Date? = null,
    val submitTimeTo: Date? = null,
    val processedTimeFrom: Date? = null,
    val processedTimeTo: Date? = null,
    val statusOptions: List<OtRequestStatus> = listOf(
        OtRequestStatus.PENDING,
        OtRequestStatus.APPROVED,
        OtRequestStatus.APPROVER_REJECTED,
        OtRequestStatus.LABOR_REJECTED,
        OtRequestStatus.START,
        OtRequestStatus.END),
) : ReactiveDataState


data class OtRequestListUiState(
    val otRequestDetails: List<OtRequestDetailEntity> = listOf(),
    val loadMoreEnd: Boolean = false,
    val selectedUser: UserInfoEntity? = null,
    val submitTimeFrom: Date? = null,
    val submitTimeTo: Date? = null,
    val processedTimeFrom: Date? = null,
    val processedTimeTo: Date? = null,
) : ReactiveUiState