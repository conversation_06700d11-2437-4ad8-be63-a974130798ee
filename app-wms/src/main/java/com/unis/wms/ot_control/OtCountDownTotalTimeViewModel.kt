package com.unis.wms.ot_control

import com.linc.platform.utils.TimeUtil
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.OtCountDownTimerQueryEntity
import com.unis.platform.ot_control.models.OtCountDownTimerRemainTimeEntity
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestQueryEntity
import com.unis.platform.ot_control.models.OtRequestStatus
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.BaseVM
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch

class OtCountDownTotalTimeViewModel : BaseVM() {
    
    private val repository = OtCountDownTotalTimeRepository()

    private var retryCount = 0
    
    fun getOtCountDownTimerTotalTime(userId: String, onSuccess: (OtCountDownTimerRemainTimeEntity?) -> Unit, onFailure: (String) -> Unit) {
        launch {
            request(repository.getOtCountDownTimerTotalTime(userId), error = {
                if (retryCount < 3) {
                    retryCount++
                    getOtCountDownTimerTotalTime(userId, onSuccess, onFailure)
                } else {
                    retryCount = 0
                    onFailure(it.error)
                }
            }, success = {
                retryCount = 0
                if (it.isNullOrEmpty()) {
                    onSuccess(null)
                } else {
                    onSuccess(it[0])
                }
            })
        }
    }

    fun getToDayOtRequest(userId: String, onSuccess: (OtRequestDetailEntity?) -> Unit) {
        launch {
            request(repository.queryToDayOtRequests(userId), success = {
                onSuccess(it?.firstOrNull { v -> v.status != OtRequestStatus.END && v.status != OtRequestStatus.LABOR_REJECTED })
            })
        }
    }

}

private class OtCountDownTotalTimeRepository : BaseRepository() {

    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun getOtCountDownTimerTotalTime(userId: String) = requestV2({otControlApi.getOtCountDownTimerTotalTime(OtCountDownTimerQueryEntity().apply {
        this.laborUserId = userId
    })})

    fun queryToDayOtRequests(userId: String) = requestV2({otControlApi.queryOtRequests(OtRequestQueryEntity().apply {
        this.laborUserId = userId
        this.submitTimeFrom = TimeUtil.toDate(TimeUtil.getDateToString(System.currentTimeMillis().getStartOfDay(), TimeUtil.FORMAT_FULL_T_FORMAT))
        this.submitTimeTo = TimeUtil.toDate(TimeUtil.getDateToString(System.currentTimeMillis().getEndOfDay(), TimeUtil.FORMAT_FULL_T_FORMAT))
    })})

    private fun Long.getStartOfDay(): Long = this - (this % (24 * 60 * 60 * 1000))//today+"00:00:00"

    private fun Long.getEndOfDay(): Long = getStartOfDay() + (24 * 60 * 60 * 1000 - 1)//today+"59:59:59"
    
}