package com.unis.wms.common

import com.linc.platform.core.LocalPersistence
import com.linc.platform.utils.ResUtil
import com.unis.platform.common.model.UserPermissionEntity

class UserPermissionHelper private constructor() {

    companion object {
        private val INSTANCE: UserPermissionHelper = UserPermissionHelper()

        fun getInstance(): UserPermissionHelper = INSTANCE
    }

    private var userPermissions: List<UserPermissionEntity> = listOf()

    private fun loadPermissions() {
        val userInfoEntity = LocalPersistence.getIamEntity(ResUtil.getContext())
        userPermissions = userInfoEntity?.permissions ?: listOf()
    }

    fun hasPermission(permission: String): Boolean {
//        if (userPermissions.isEmpty()) {
//            loadPermissions()
//        }
//        return userPermissions.any { it.name == permission }
        return true
    }

    fun hasAnyPermission(vararg permissions: String): Boolean {
//        if (userPermissions.isEmpty()) {
//            loadPermissions()
//        }
//        return permissions.any { hasPermission(it) }
        return true
    }

    fun hasAllPermissions(vararg permissions: String): Boolean {
        if (userPermissions.isEmpty()) {
            loadPermissions()
        }
        return permissions.all { hasPermission(it) }
    }

    fun hasInboundPermission(): Boolean {
        return hasPermission(UserPermissionConstants.HOME_PAGE_MORE_INBOUND_PUT_AWAY)
    }

    fun hasOutboundPermission(): Boolean {
        return hasAnyPermission(UserPermissionConstants.HOME_PAGE_MORE_OUTBOUND_LP_SN_COLLECTION,
               UserPermissionConstants.HOME_PAGE_MORE_OUTBOUND_PUT_BACK,
               UserPermissionConstants.HOME_PAGE_MORE_OUTBOUND_CLP_BONDING_TASK,
               UserPermissionConstants.HOME_PAGE_MORE_OUTBOUND_CONSOLIDATE_TO_PALLET_TASK)
    }

    fun hasInventoryControlPermission(): Boolean {
        return hasAnyPermission(UserPermissionConstants.HOME_PAGE_MORE_INVENTORY_CONTROL_LP_DETAIL,
               UserPermissionConstants.HOME_PAGE_MORE_INVENTORY_CONTROL_REPLENISHMENT_TASK,
               UserPermissionConstants.HOME_PAGE_MORE_INVENTORY_CONTROL_INVENTORY_SEARCH,
               UserPermissionConstants.HOME_PAGE_MORE_INVENTORY_CONTROL_INVENTORY_MOVEMENT,
               UserPermissionConstants.HOME_PAGE_MORE_INVENTORY_CONTROL_STACK_HEIGHT_COLLECT)
    }

    fun hasAdminPermission(): Boolean {
        return hasAnyPermission(UserPermissionConstants.HOME_PAGE_MORE_ADMIN_PRINT,
               UserPermissionConstants.HOME_PAGE_MORE_ADMIN_PRINTER_TEST,
               UserPermissionConstants.HOME_PAGE_MORE_ADMIN_ITEM_LABEL,
               UserPermissionConstants.HOME_PAGE_MORE_ADMIN_PALLET_LABEL,
               UserPermissionConstants.HOME_PAGE_MORE_ADMIN_LP_LABEL,
               UserPermissionConstants.HOME_PAGE_MORE_ADMIN_LIGHT_SETTING)
    }

    fun clearPermissions() {
        userPermissions = mutableListOf()
    }
}