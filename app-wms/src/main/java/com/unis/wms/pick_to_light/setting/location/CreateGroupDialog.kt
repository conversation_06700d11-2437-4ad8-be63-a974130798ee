package com.unis.wms.pick_to_light.setting.location

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import android.widget.AdapterView
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import com.customer.widget.LabelSpinner
import com.customer.widget.core.LincBaseDialog
import com.linc.platform.utils.ToastUtil
import com.unis.platform.pick_to_light.mode.group.CreatePickToLightGroupEntity
import com.unis.platform.pick_to_light.mode.group.PickToLightDeviceType
import com.unis.platform.pick_to_light.mode.group.PickToLightGroupType
import com.unis.wms.R

/**
 * <AUTHOR> on 2024/12/31
 */
class CreateGroupDialog(private val save: (CreatePickToLightGroupEntity) -> Unit, private val ipScan: String) : LincBaseDialog() {
    companion object {
        fun newInstance(save: (CreatePickToLightGroupEntity) -> Unit, ip: String): CreateGroupDialog {
            return CreateGroupDialog(save, ip)
        }
    }

    private lateinit var saveBtn: AppCompatButton
    private lateinit var typeSpinner: LabelSpinner
    private lateinit var ipTv: AppCompatTextView
    private lateinit var nameEdt: AppCompatEditText
    private lateinit var portEdt: AppCompatEditText
    private lateinit var deviceTypeSpinner: LabelSpinner
    private var groupType: PickToLightGroupType? = null
    private var deviceType: PickToLightDeviceType? = null

    override fun getLayoutId() = R.layout.dialog_create_group

    @SuppressLint("SetTextI18n")
    override fun initView() {
        saveBtn = findView(R.id.save_btn)
        typeSpinner = findView(R.id.type_spinner)
        ipTv = findView(R.id.ip_tv)
        ipTv.text = getString(R.string.ip_mark) + ipScan
        nameEdt = findView(R.id.name_edt)
        portEdt = findView(R.id.port_edt)
        deviceTypeSpinner = findView(R.id.device_type_spinner)
        typeSpinner.setDataOptions(PickToLightGroupType.values().map { it.name }, isInitFirstOption = true)
        typeSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                groupType = PickToLightGroupType.values()[position]
            }
        })
        deviceTypeSpinner.setDataOptions(PickToLightDeviceType.values().map { it.getDisplayName() }, isInitFirstOption = true)
        deviceTypeSpinner.setSelectedListener(object : LabelSpinner.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                deviceType = PickToLightDeviceType.values()[position]
            }
        })
        saveBtn.setOnClickListener {
            if(TextUtils.isEmpty(nameEdt.text.toString())){
                ToastUtil.showToast(R.string.please_input_group_name)
                return@setOnClickListener
            }
            if(TextUtils.isEmpty(portEdt.text.toString())){
                ToastUtil.showToast(R.string.please_input_port)
                return@setOnClickListener
            }
            save(CreatePickToLightGroupEntity().apply {
                this.name = nameEdt.text.toString()
                this.ip = ipScan
                this.port = portEdt.text.toString()
                this.type = groupType
                this.deviceType = <EMAIL>
            })
            dismiss()
        }
    }

    override fun getAnimationStyleRes() = 0
    override fun onStart() {
        super.onStart()
        dialog.window?.setBackgroundDrawableResource(R.drawable.rect_222222_4)
    }

}