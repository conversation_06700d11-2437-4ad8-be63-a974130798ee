package com.unis.wms.pick_task.pick.work.viewmodel

import com.linc.platform.core.LocalPersistence
import com.linc.platform.utils.ResUtil
import com.unis.linc.common.extensions.toException
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.inventoryissue.model.InventoryIssueEntity
import com.unis.platform.inventoryissue.model.InventoryIssueType
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_override.model.LocationOverrideRequestCreateEntity
import com.unis.platform.location_override.model.LocationOverrideRequestEntity
import com.unis.platform.location_override.model.OverrideLocationRequestStatusEntity
import com.unis.platform.location_override.model.getDefaultOverrideReason
import com.unis.platform.location_override.model.toLocationOverrideIssueType
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.pick_to_light.mode.group.PickToLightDeviceType
import com.unis.platform.pick_v2.PickSharedPreferencesUtil
import com.unis.platform.pick_v2.model.PickMode
import com.unis.platform.pick_v2.model.pick.AirRobReportInfo
import com.unis.platform.pick_v2.model.pick.LocationItemSuggestEntity
import com.unis.platform.pick_v2.model.pick.LocationSuggestRequestEntity
import com.unis.platform.pick_v2.model.pick.PickItemSubmitEntity
import com.unis.platform.pick_v2.model.pick.PickSubmitEntity
import com.unis.platform.pick_v2.model.pick.TaskSuggest
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.reactivemvi.mvvm.kotlin.extensions.speak
import com.unis.reactivemvi.mvvm.kotlin.extensions.vibrate
import com.unis.wms.R
import com.unis.wms.pick_task.pick.work.PickStepViewModel
import com.unis.wms.pick_task.pick.work.model.PickStepWorkUiEvent
import com.unis.wms.pick_task.pick.work.model.StepWorkProgress
import com.unis.wms.pick_to_light.base.LightManager
import com.unis.wms.pick_to_light.base.SocketListener
import com.unis.wms.pick_to_light.base.model.BaseReceiveMessage
import com.unis.wms.pick_to_light.base.model.BaseSocketMessage
import com.unis.wms.pick_to_light.base.model.LightMessageType
import com.unis.wms.pick_to_light.base.model.LightSocketMessage

class PickStepWorkViewModel(
    activityViewModel: PickStepViewModel,
) : BasePickStepWorkViewModel(activityViewModel) {

    private val socketEvent = object : SocketListener.SocketEvent() {
        override fun receiveMessage(type: String?, message: BaseReceiveMessage?) {
            pickLightSubmit(message)
        }

        override fun disConnect(type: String?) {
            super.disConnect(type)
            setPickToLightServerStatus(0)
        }

        override fun onConnected(type: String?, host: String?, port: String?) {
            super.onConnected(type, host, port)
            sendLightMessageDoubleCheck()
            setPickToLightServerStatus(1)
        }

        override fun onError(type: String?, message: String?) {
            super.onError(type, message)
            message?.let { showSnack(SnackType.ErrorV1(), it) }
        }

        override fun onSendMessageError(message: BaseSocketMessage?) {
            super.onSendMessageError(message)
            if (message == null) return
            if (message is LightSocketMessage && message.messageType == LightMessageType.OFF) {
                showSnack(SnackType.ErrorV1(), R.string.send_close_light_message_error)
                return
            }
            fireEvent { PickStepWorkUiEvent.ShowResendLightMessageDialog(message) }
        }
    }

    private var isFirstConnect = true

    init {
        setDataState {
            copy(lastPickLocationId = LocalPersistence.getLastPickedLocationId(ResUtil.getContext()).let {
                val splits = it.split(":")
                if (splits.size > 1 && splits[0] == pickTask?.id) splits[1] else null
            })
        }
        startWorkPickStep()
    }

    private fun startWorkPickStep() {
        launch { //1.reset data
            initWorkDataState() //2.get pick step progress
            val isPickOver = getPickStepProgress() //3.check is pick over
            if (isPickOver) {
                fireEvent { PickStepWorkUiEvent.ShowClosePickStepDialog }
                return@launch
            }
            val hasSuggest = getAndSetLocationItemSuggest()
            if (!hasSuggest) {
                showToast(R.string.text_no_suggestion)
            }
            pickStepWorkHandle()
        }
    }

    fun setForceShowSubmitButton() {
        launch {
            val isForce = !dataState.forceShowSubmitBottomAsPickToLight
            setDataStateAwait { copy(forceShowSubmitBottomAsPickToLight = isForce) }
        }
    }

    fun rebuildPickStrategyByTask() {
        launch {
            pickTask?.id ?: return@launch
            val rebuildResult = requestAwait(pickStepRepository.rebuildPickStrategyByTask(pickTask.id!!))
            if(rebuildResult.isFailure) return@launch
            getAndSetLocationItemSuggest()
            pickStepWorkHandle()
        }
    }

    private suspend fun pickStepWorkHandle(isOverride: Boolean = false) {
        fireEvent { PickStepWorkUiEvent.PopAllStepFragmentEvent }
        connectPickToLightServer()
        when {
            dataState.pickToLightData?.pickToLight != null && dataState.hasPickToLightServer && dataState.airRobCarton != null  -> {
                pickToLightAndAirRobProcess()
            }

            dataState.pickToLightData?.pickToLight != null && dataState.hasPickToLightServer && dataState.airRobCarton == null -> {
                pickToLightProcess()
            }

            dataState.pickToLightData?.pickToLight == null && dataState.airRobCarton != null -> {
                pickAirRobProcess()
            }

            else -> {
                normalPickProcess(isOverride)
            }
        }
    }

    private suspend fun normalPickProcess(isOverride: Boolean) {
        stepNavigateHandle(StepWorkProgress.ScanLocation)
        if (dataState.locationSuggest == null) {
            return
        }
        if (dataState.locationSuggest!!.isPickLocation) {
            setDataStateAwait { copy(pickWorkData = dataState.pickWorkData.copy(fromLpId = dataState.locationSuggest?.hlpId)) }
            if (dataState.lastPickLocationId == dataState.locationSuggest?.id || isOverride) {
                stepNavigateHandle(StepWorkProgress.ScanItem)
            }
        } else {
            stepNavigateHandle(StepWorkProgress.ScanLp)
        }
    }

    private suspend fun pickToLightProcess() {
        stepNavigateHandle(StepWorkProgress.ScanLocation)
        sendLocationLightMessage(LightMessageType.ON)
        if (dataState.locationSuggest?.isPickLocation != true) {
            stepNavigateHandle(StepWorkProgress.ScanLp)
            return
        } else {
            setDataStateAwait { copy(pickWorkData = dataState.pickWorkData.copy(fromLpId = dataState.locationSuggest?.hlpId)) }
        }

        if (isNeedScanItem()) {
            stepNavigateHandle(StepWorkProgress.ScanItem)
            return
        } else {
            stepNavigateHandle(StepWorkProgress.ScanItem)
        }

        if (dataState.isNeedCollectLotNo) {
            stepNavigateHandle(StepWorkProgress.ScanLotNo)
            return
        }

        if (isNeedScanSn()) {
            stepNavigateHandle(StepWorkProgress.ScanSN)
            return
        }

        stepNavigateHandle(StepWorkProgress.ScanToLP)
    }

    private suspend fun pickToLightAndAirRobProcess() {
        stepNavigateHandle(StepWorkProgress.ScanLocation)
        sendLocationLightMessage(LightMessageType.ON) //location is pick location
        setDataStateAwait {
            copy(
                selectedPickItemSuggest = dataState.pickItemSuggests?.firstOrNull(),
                pickWorkData = dataState.pickWorkData.copy(fromLpId = dataState.airRobCarton?.lpId))
        }
        stepNavigateHandle(StepWorkProgress.ScanItem)
        if (dataState.isNeedCollectLotNo) {
            stepNavigateHandle(StepWorkProgress.ScanLotNo)
            return
        }
        if (isNeedScanSn()) {
            stepNavigateHandle(StepWorkProgress.ScanSN)
            return
        }
        stepNavigateHandle(StepWorkProgress.ScanToLP)
    }

    private suspend fun pickAirRobProcess() {
        stepNavigateHandle(StepWorkProgress.ScanLocation) //location is pick location
        setDataStateAwait {
            copy(
                selectedPickItemSuggest = dataState.pickItemSuggests?.firstOrNull(),
                pickWorkData = dataState.pickWorkData.copy(fromLpId = dataState.airRobCarton?.lpId))
        }
    }

    fun overrideLocationSuggest(
        specificLocation: LocationEntity?,
        inventoryIssue: InventoryIssueEntity? = null,
        isSkipForNow: Boolean = false,
        isNeedWaitingApproval: Boolean = true,
        overrideRequestResult: LocationOverrideRequestEntity?= null
    ) {
        launch {
            val lastSuggestLocationId = dataState.locationSuggest?.id
            val newLocationItemSuggest: LocationItemSuggestEntity? = when { // Skip For Now
                isSkipForNow -> {
                    if (dataState.pickConfigData.noMoreLocationCanSkipForNow == true) {
                        showToast(R.string.no_new_suggestion)
                        return@launch
                    }
                    val skipResult = requestAwait(
                        pickStepRepository.skipPickLocation(pickTask?.id!!, dataState.locationSuggest?.id ?: "",
                            LocationSuggestRequestEntity().apply {
                                lastPickLocationId = dataState.lastPickLocationId
                                toteCartBarcode = toteCart?.barcode
                            }))
                    if (skipResult.isFailure) return@launch
                    skipResult.getOrNull()
                }

                // User Scanned Another Location
                specificLocation != null -> {
                    if (isNeedWaitingApproval && dataState.locationItemSuggest?.needApprovalBeforeOverrideSuggestedLocation == true) {
                        sendAndWaitLocationOverrideRequest(specificLocation = specificLocation)
                        return@launch
                    }
                    val specificResult = if (specificLocation.isPickLocation) {
                        getSpecificPickLocationItemSuggest(specificLocation)
                    } else {
                        getLocationItemSuggest(specificLocation)
                    }
                    if (specificResult.isFailure) return@launch
                    val suggest = specificResult.getOrNull() ?: LocationItemSuggestEntity()

                    reportLocationOverrideHistory(lastSuggestLocationId, specificLocation.id)

                    if (suggest.location == null) {
                        suggest.copy(location = specificLocation)
                    } else {
                        suggest
                    }
                }

                // User Click Next Location
                inventoryIssue != null -> { //report success
                    if (isNeedWaitingApproval && dataState.locationItemSuggest?.needApprovalBeforeOverrideSuggestedLocation == true) {
                        sendAndWaitLocationOverrideRequest(inventoryIssue = inventoryIssue)
                        return@launch
                    }
                    val reportResult = requestAwait(pickStepRepository.inventoryIssueSubmit(inventoryIssue)).getOrNull() ?: return@launch
                    val locationSuggestRequestEntity = LocationSuggestRequestEntity().apply {
                        lastPickLocationId = dataState.lastPickLocationId
                        issueId = reportResult
                        toteCartBarcode = toteCart?.barcode
                    }
                    val suggestResult = requestAwait(pickStepRepository.rebuildPickStrategy(pickTask?.id!!, locationSuggestRequestEntity))
                    if (suggestResult.isFailure) return@launch

                    reportLocationOverrideHistory(
                        lastSuggestLocationId,
                        suggestResult.getOrNull()?.location?.id,
                        inventoryIssue.issueType,
                        reportResult,
                        overrideRequestResult
                    )

                    PickSharedPreferencesUtil.setPickReportInventoryIssueInfo(taskId = pickTask.id!!, locationId = inventoryIssue.locationId?:"")
                    suggestResult.getOrNull()
                }

                else -> return@launch
            }
            closeLightNotCallBack()
            setDataStateAwait {
                this.initForLocationItemSuggest(newLocationItemSuggest)
            }
            pickStepWorkHandle(specificLocation != null)
        }
    }

    private fun sendAndWaitLocationOverrideRequest(specificLocation: LocationEntity? = null, inventoryIssue: InventoryIssueEntity? = null){
        launch {
            val overrideRequestCreate = LocationOverrideRequestCreateEntity(
                taskId = pickTask?.id,
                taskType = TaskType.PICK,
                taskAssigneeUserId = pickTask?.assigneeUserId,
                status = OverrideLocationRequestStatusEntity.NEW,
                suggestedLocationId = dataState.locationSuggest?.id,
                overrideLocationId = specificLocation?.id,
                overrideReason = inventoryIssue?.issueName ?: "Manual Override"
            )
            val result = requestAwait(pickStepRepository.createLocationOverrideRequest(overrideRequestCreate))
            if (result.isFailure) return@launch
            fireEvent {
                PickStepWorkUiEvent.ShowLocationOverrideWaitingApprovalDialog(
                    specificLocation,
                    inventoryIssue,
                    result.getOrNull()!!
                )
            }
        }
    }

    private suspend fun getSpecificPickLocationItemSuggest(specificLocation: LocationEntity) = requestAwait(
        pickStepRepository.suggestSpecificPickLocation(pickTask?.id!!, specificLocation.id,
            LocationSuggestRequestEntity().apply {
                if (dataState.selectPickMode == PickMode.PICK_TO_TOTE) {
                    toteCartBarcode = toteCart?.barcode
                }
            }
        )
    )

    private fun reportLocationOverrideHistory(
        suggestedLocationId: String?,
        overrideLocationId: String?,
        inventoryIssueType: InventoryIssueType? = null,
        inventoryIssueId: String? = null,
        overrideRequestResult: LocationOverrideRequestEntity?= null
    ) {
        launch {
            suggestedLocationId ?: return@launch
            val overrideIssue = LocationOverrideHistoryCreateEntity().apply {
                this.taskId = dataState.task.id
                this.taskType = TaskType.PICK
                this.taskStepType = TaskStepType.PICK
                this.taskAssigneeUserId = dataState.task.assigneeUserId
                this.suggestedLocationId = suggestedLocationId
                this.overrideLocationId = overrideLocationId
                this.overrideIssueType = inventoryIssueType.toLocationOverrideIssueType()
                this.overrideReason = this.overrideIssueType.getDefaultOverrideReason()
                this.inventoryIssueId = inventoryIssueId
                this.overrideRequestId = overrideRequestResult?.id?.toString()
                this.approvalSupervisorId = overrideRequestResult?.processedBy
            }
            requestAwait(pickStepRepository.reportLocationOverrideHistory(overrideIssue), showLoading = false)
        }
    }

    fun updateOverrideLocationWhenAlreadyReport(specificLocation: LocationEntity) {
        launch {
            val specificResult = if (specificLocation.isPickLocation) {
                getSpecificPickLocationItemSuggest(specificLocation)
            } else {
                getLocationItemSuggest(specificLocation)
            }
            if (specificResult.isFailure) return@launch
            val suggest = specificResult.getOrNull() ?: LocationItemSuggestEntity()
            val newLocationItemSuggest: LocationItemSuggestEntity = if (suggest.location == null) {
                suggest.copy(location = specificLocation)
            } else {
                suggest
            }
            setDataStateAwait {
                this.initForLocationItemSuggest(newLocationItemSuggest)
            }
            pickStepWorkHandle(true)
        }
    }

    fun updateSuggestLocation(location: LocationEntity?) {
        setDataState {
            copy(locationItemSuggest = dataState.locationItemSuggest?.copy(location = location))
        }
    }

    fun submitAndNextLocation() {
        launch {
            awaitDataState()
            runCatching { validatePickItemSubmit() }.onFailure {
                if (dataState.isPickToLight) {
                    vibrate()
                    speak(it.message?:"")
                    sendToteLightMessage(LightMessageType.ON)
                    sendLocationLightMessage(LightMessageType.ON)
                }
                showSnack(SnackType.ErrorV1(), it.message?:"")
                return@launch
            }

            //airRob report check
            var isEmpty = false
            var isNeedEmptyCartonReport = false
            if (dataState.airRobCarton?.containerCode != null && dataState.selectedPickItemSuggest?.remainQty == dataState.pickWorkData.pickingQty) {
                //showDialog
                isNeedEmptyCartonReport = true
                isEmpty = awaitEvent { PickStepWorkUiEvent.ShowCartonCheckDialog() } == true
            }

            val pickWorkData = dataState.pickWorkData
            val pickItemSubmitEntity = PickItemSubmitEntity(
                dataState.selectedPickItemSuggest?.id ?: return@launch,
                pickWorkData.fromLpId ?: return@launch,
                pickWorkData.toLpId ?: return@launch,
                pickWorkData.snList,
                "",
                pickWorkData.pickingQty.toDouble(),
                dataState.pickWorkData.isEntireLpPick,
                dataState.selectedPickItemSuggest?.orderId,
                occupyEquipmentId = dataState.pickWorkData.occupyTote?.id,
                airRobReportInfo = if (dataState.airRobCarton?.containerCode != null && isNeedEmptyCartonReport) {
                    AirRobReportInfo(isEmpty, dataState.airRobCarton?.containerCode!!)
                } else {
                    null
                }
            )
            val pickSubmitEntity = PickSubmitEntity().apply {
                taskSubmit = pickItemSubmitEntity
                taskSuggest = TaskSuggest(toteCartBarcode = toteCart?.barcode, lastPickLocationId = dataState.locationSuggest?.id)
            }

            requestAwait(pickStepRepository.submitPickItemAndNext(pickTask?.id!!, stepId = pickStepId, pickSubmitEntity)).onSuccess { result ->
                closeLightNotCallBack()
                setDataStateAwait { copy(lastPickLocationId = dataState.locationSuggest?.id) }
                LocalPersistence.setLastPickedLocationId(ResUtil.getContext(), "${pickTask.id}:${dataState.locationSuggest?.id}")
                initWorkDataState()
                val isPickOver = initPickProgress(result?.pickProgress)
                if (isPickOver) {
                    fireEvent { PickStepWorkUiEvent.ShowClosePickStepDialog }
                    return@launch
                }
                val suggestData = result?.nextSuggest
                if (suggestData == null) {
                    showToast(R.string.text_no_suggestion)
                    return@launch
                }
                setDataStateAwait {
                    this.initForLocationItemSuggest(suggestData)
                }
                pickStepWorkHandle()
            }.onFailure {
                if (dataState.isPickToLight) {
                    vibrate()
                    speak(getString(R.string.error_pick_to_light_submit_fail))
                }
                sendToteLightMessage(LightMessageType.ON)
                sendLocationLightMessage(LightMessageType.ON)
            }
        }
    }

    @Throws
    private fun validatePickItemSubmit() {
        if (dataState.pickWorkData.fromLpId.isNullOrEmpty()) {
            throw getString(R.string.msg_from_lp_is_empty).toException
        }

        if (dataState.pickWorkData.toLpId.isNullOrEmpty()) {
            throw getString(R.string.msg_to_lp_is_empty).toException
        }
    }

    private fun pickLightSubmit(message: BaseReceiveMessage?) {
        launch {
            if (message == null) return@launch
            if (!LightManager.isValidCloseLightCommand(message.command)) return@launch
            awaitDataState()

            val toteCartIp = toteCart?.ip
            val suggestToteLightId = dataState.pickWorkData.occupyTote?.lightId?: toteCart?.childrenEquipments?.find {
                it.barcode == dataState.selectedPickItemSuggest?.toteSuggest?.firstOrNull()
            }?.lightId
            val toteHasPickToLight = !toteCartIp.isNullOrEmpty() && !suggestToteLightId.isNullOrEmpty()
            val locationLightIp = dataState.pickToLightData?.pickToLightGroup?.ip
            val locationHasPickToLight = !dataState.pickToLightData?.pickToLight?.lightId.isNullOrEmpty() && !locationLightIp.isNullOrEmpty()
            when {
                toteHasPickToLight && locationHasPickToLight -> {
                    if (message.ip == toteCartIp && dataState.isLocationLightClosed) {
                        submitAndNextLocation()
                    } else if (message.ip == locationLightIp) {
                        // 获取设备类型并检查
                        val deviceType = dataState.toteCart?.pickToLightGroup?.deviceType
                        if (deviceType == PickToLightDeviceType.ITEM_PICK_TO_LIGHT_1B4D) {
                            val qty = LightManager.getPickToLightSubmitQty(message.command)
                            if (qty > (dataState.selectedPickItemSuggest?.suggestedQty ?: 0) || qty < 0) {
                                showSnack(SnackType.ErrorV1(), "${getString(R.string.msg_invalid_qty)} , QTY: $qty")
                                speak(getString(R.string.msg_invalid_qty))
                                vibrate()
                                sendToteLightMessage(LightMessageType.ON)
                                sendLocationLightMessage(LightMessageType.ON)
                                return@launch
                            }
                            updatePickingQty(qty)
                        }
                        setDataState { copy(isLocationLightClosed = true) }
                    } else if (message.ip == toteCartIp && !dataState.isLocationLightClosed){
                        showSnack(SnackType.ErrorV1(), R.string.msg_close_location_light)
                        speak(getString(R.string.msg_close_location_light))
                        vibrate()
                        sendToteLightMessage(LightMessageType.ON)
                    }
                }
                else -> {
                    val deviceType = if (dataState.pickToLightData?.pickToLight != null) {
                        dataState.pickToLightData?.pickToLightGroup?.deviceType
                    } else {
                        dataState.toteCart?.pickToLightGroup?.deviceType
                    }
                    if (deviceType == PickToLightDeviceType.ITEM_PICK_TO_LIGHT_1B4D) {
                        val qty = LightManager.getPickToLightSubmitQty(message.command)
                        if (qty > (dataState.selectedPickItemSuggest?.suggestedQty ?: 0) || qty < 0) {
                            showSnack(SnackType.ErrorV1(), "${getString(R.string.msg_invalid_qty)} , QTY: $qty")
                            speak(getString(R.string.msg_invalid_qty))
                            vibrate()
                            sendToteLightMessage(LightMessageType.ON)
                            sendLocationLightMessage(LightMessageType.ON)
                            return@launch
                        }
                        updatePickingQty(qty)
                    }
                    submitAndNextLocation()
                }
            }
        }
    }

    private fun closeLightNotCallBack() {
        sendLocationLightMessage(LightMessageType.OFF)
        sendToteLightMessage(LightMessageType.OFF)
    }

    fun resendLocationLightMessage(message: LightSocketMessage) {
        LightManager.sendMessage(message)
    }

    private fun connectPickToLightServer() {
        if ((dataState.pickToLightData?.pickToLight != null || !toteCart?.ip.isNullOrEmpty())) {
            LightManager.startLightSocket(socketEvent)
        }
    }

    fun setPickToLightServerStatus(status: Int) {
        launch {
            setDataStateAwait { copy(pickToLightServerStatus = status) }
            fireEvent { PickStepWorkUiEvent.UpdateConnectStatus }
        }
    }

    private fun sendLightMessageDoubleCheck() {
        if (!isFirstConnect) return
        if (dataState.pickToLightData?.pickToLight != null && toteCart?.ip.isNullOrEmpty()) {
            sendLocationLightMessage(LightMessageType.ON, true)
        } else if (dataState.currentWorkStep == StepWorkProgress.ScanToLP && !toteCart?.ip.isNullOrEmpty() && dataState.pickToLightData?.pickToLight == null) {
            sendToteLightMessage(LightMessageType.ON, true)
        } else if (dataState.pickToLightData?.pickToLight != null && !toteCart?.ip.isNullOrEmpty()) {
            sendLocationLightMessage(LightMessageType.ON, true)
            sendToteLightMessage(LightMessageType.ON, true)
        }
        isFirstConnect = false
    }

    override fun onCleared() {
        isFirstConnect = true
        sendLocationLightMessage(LightMessageType.OFF)
        sendToteLightMessage(LightMessageType.OFF)
        LightManager.removeSocketEvent(socketEvent)
        super.onCleared()
    }
}