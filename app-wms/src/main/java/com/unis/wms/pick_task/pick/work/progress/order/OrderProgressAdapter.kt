package com.unis.wms.pick_task.pick.work.progress.order

import android.annotation.SuppressLint
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import com.customer.widget.extensions.setVisibleOrGone
import com.linc.platform.utils.ResUtil.format
import com.unis.platform.order.model.OrderStatusEntity
import com.unis.platform.pick_v2.model.progress.ProgressOrderEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemPickStepProgressChildBinding
import com.unis.wms.databinding.ItemProgressOrderBinding

class OrderProgressAdapter : BaseBindingDifferQuickAdapter<ProgressOrderEntity, ItemProgressOrderBinding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseBindingViewHolder<ItemProgressOrderBinding>?, item: ProgressOrderEntity?) {
        helper?.binding?.apply {
            orderTitleTv.text = item?.orderId
            orderStatusTv.text = item?.orderStatus?.name
            val statueTextColor =
                if (OrderStatusEntity.CANCELLED == item?.orderStatus) ContextCompat.getColor(mContext, R.color.dt_color_ec4a44)
                else ContextCompat.getColor(mContext, R.color.white)
            orderStatusTv.setTextColor(statueTextColor)
            orderItemLl.removeAllViews()
            item?.itemLines?.forEach { itemLine ->
                val itemLayout = LayoutInflater.from(mContext).inflate(R.layout.item_pick_step_progress_child, root, false)
                ItemPickStepProgressChildBinding.bind(itemLayout).apply {
                    itemTitleTv.text = itemLine.item.name
                    itemDescriptionTv.setVisibleOrGone(itemLine.item.description?.isNotEmpty() == true)
                    itemDescriptionTv.text = itemLine.item.description
                    val needPickQty = itemLine.requirePickQty.minus(itemLine.pickedQty)
                    itemMessageTv.text = "${format(R.string.text_required, itemLine.requirePickQty)} ," +
                            "${format(R.string.text_picked_qty_count, "${itemLine.pickedQty}  ${itemLine.uomName}")}, " +
                            format(R.string.text_need_pick, if (needPickQty < 0.0) 0.0 else needPickQty)

                }
                orderItemLl.addView(itemLayout)
            }
        }
    }

    override fun areItemsTheSame(oldItem: ProgressOrderEntity, newItem: ProgressOrderEntity) =
        oldItem.orderId == newItem.orderId

    override fun areContentsTheSame(oldItem: ProgressOrderEntity, newItem: ProgressOrderEntity) =
        oldItem.orderId == newItem.orderId
}