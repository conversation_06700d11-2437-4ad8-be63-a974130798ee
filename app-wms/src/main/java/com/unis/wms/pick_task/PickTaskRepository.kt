package com.unis.wms.pick_task

import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.customer_v2.CustomerApiService
import com.unis.platform.item.api.ItemApi
import com.unis.platform.item.model.ItemQueryEntity
import com.unis.platform.pick_v2.PickTaskApiService
import com.unis.platform.pick_v2.model.PickTaskSearchEntity
import com.unis.platform.pick_v2.model.PickTaskUpdateEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy

class PickTaskRepository: BaseRepository() {
    private val pickTaskApiService by apiServiceLazy<PickTaskApiService>()
    private val itemApi by apiServiceLazy<ItemApi>()
    private val customerApiService by apiServiceLazy<CustomerApiService>()

    fun searchItem(itemQueryEntity: ItemQueryEntity) = rxRequest2(itemApi.searchItemByPaging(itemQueryEntity))

    fun searchPickTask(taskId: String) = requestV2({pickTaskApiService.searchPickTask(PickTaskSearchEntity().apply {
        id = taskId
    })})

    fun getPickTask(taskId: String) = requestV2({pickTaskApiService.getPickTask(taskId)})

    fun getPickStep(taskId: String) = requestV2({pickTaskApiService.getPickStep(taskId)})

    fun bindToteCart(taskId: String, toteCart: String) {}

    fun getToteCartDetail(toteCart: String) {

    }

    fun getCustomer(customerId: String) = rxRequest2(customerApiService.getCustomer(customerId))

    fun forceClosePickStep(stepId: String) = requestV2({pickTaskApiService.forceClosePickStep(stepId)})

    fun closeStep(stepId: String) = requestV2({pickTaskApiService.closePickStep(stepId)})

    fun searchPickTaskListByPaging(pickTaskSearchEntity: TaskQueryEntity) = requestV2({pickTaskApiService.searchPickTaskByPaging(pickTaskSearchEntity)})

    fun closeTask(taskId: String) = requestV2({pickTaskApiService.closeTask(taskId)})

    fun forceCloseTask(taskId: String) = requestV2({pickTaskApiService.forceCloseTask(taskId)})

    fun batchUpdateTask(tasks: List<PickTaskUpdateEntity>) = requestV2({pickTaskApiService.batchUpdateTask(tasks)})
}