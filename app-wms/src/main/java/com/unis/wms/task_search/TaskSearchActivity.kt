package com.unis.wms.task_search

import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.customer.widget.QuickScanner
import com.customer.widget.common.CenterDialog
import com.customer.widget.core.ActivityBundleHolder
import com.customer.widget.extensions.setVisibleOrGone
import com.google.gson.Gson
import com.unis.platform.assembly.mode.AssemblyTaskEntity
import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.BaseTaskEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.common.model.task.getDisplayName
import com.unis.platform.general.model.GeneralTaskEntity
import com.unis.platform.internaltransfer.inn.model.TransferInTaskEntity
import com.unis.platform.internaltransfer.out.model.TransferOutTaskEntity
import com.unis.platform.load.model.task.LoadTaskEntity
import com.unis.platform.movement.model.MovementTaskEntity
import com.unis.platform.movement.model.MovementTypeEntity
import com.unis.platform.pack.model.PackTaskEntity
import com.unis.platform.pick_v2.model.PickTaskEntity
import com.unis.platform.put_back.model.PutBackTaskEntity
import com.unis.platform.receive.model.ReceiveTaskEntity
import com.unis.platform.replenishment.model.ReplenishmentTaskEntity
import com.unis.platform.transload.dockcheck.model.DockCheckTaskEntity
import com.unis.platform.transload.loading.model.TransLoadLoadingTaskEntity
import com.unis.platform.transload.receiving.model.TransLoadReceivingTaskEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.SimpleReactiveActivity
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.Message
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.wms.R
import com.unis.wms.assemblytask.work.AssemblyTaskActivity
import com.unis.wms.common.IntentKey
import com.unis.wms.databinding.ActivityTaskSearchBinding
import com.unis.wms.general.work.GeneralWorkActivity
import com.unis.wms.internaltransfer.inn.TransferInTaskActivity
import com.unis.wms.internaltransfer.out.TransferOutTaskActivity
import com.unis.wms.load.LoadTaskActivity
import com.unis.wms.material.receive.MaterialReceiveActivity
import com.unis.wms.material.receive.ReceiveTaskStartEvent
import com.unis.wms.more.consolidatetopallet.ConsolidateToPalletTaskActivity
import com.unis.wms.movement.batchmovement.BatchMovementActivity
import com.unis.wms.movement.work.MovementTaskActivity
import com.unis.wms.outbound_qc_task.OutboundQcTaskActivity
import com.unis.wms.pick_task.PickTaskStepStartEvent
import com.unis.wms.pick_task.order_pick.OrderPickStepActivity
import com.unis.wms.pick_task.pick.work.PickStepActivity
import com.unis.wms.pick_task.sorting_to_wall.SortingToWallActivity
import com.unis.wms.pick_task.stage.StageStepActivity
import com.unis.wms.pick_task.stage_to_wall.StageToWallStepActivity
import com.unis.wms.put_away_task.ui.PutAwayTaskActivity
import com.unis.wms.put_back.emptytask.EmptyPutBackTaskActivity
import com.unis.wms.receive.dockcheckin.DockCheckInActivity
import com.unis.wms.receive.lpsetup.LpSetupActivity
import com.unis.wms.receive.offload.OffloadActivity
import com.unis.wms.receive.offload.OffloadInitPage
import com.unis.wms.receive.snscan.SnScanActivity
import com.unis.wms.receive.stage.ReceiveStageActivity
import com.unis.wms.replenishment.work.ReplenishmentTaskActivity
import com.unis.wms.step_menu.LoadTaskDescribeDialog
import com.unis.wms.step_menu.TaskStepMenuActivity
import com.unis.wms.task_search.advanced.AdvancedSearchActivity
import com.unis.wms.task_search.advanced.AdvancedSearchEntity
import com.unis.wms.transload.dockcheck.DockCheckTaskActivity
import com.unis.wms.transload.loading.TransLoadLoadingTaskActivity
import com.unis.wms.transload.receiving.TransLoadReceivingTaskActivity
import com.unis.wms.task_search.dialog.TaskTypeSelectDialog
import org.greenrobot.eventbus.EventBus
import java.io.Serializable

class TaskSearchActivity : SimpleReactiveActivity<TaskSearchViewModel, TaskSearchDataState, ActivityTaskSearchBinding>() {

    private var adapter: TaskSearchAdapter? = null

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(TaskSearchDataState::taskSearchResult) {
            adapter?.setNewData(it)
        }

        subscribe(TaskSearchDataState::advancedSearch) {
            binding.apply {
                advancedSearchEntryLayout.setVisibleOrGone(it != null)
                taskTypeLayout.setVisibleOrGone(it?.taskType != null)
                taskStatusLayout.setVisibleOrGone(it?.taskStatus != null)
                customerLayout.setVisibleOrGone(it?.customer != null)
                assigneeLayout.setVisibleOrGone(it?.assigneeUser != null)

                taskTypeTxt.text = it?.taskType.getDisplayName()
                taskStatusTxt.text = it?.taskStatus.getDisplayName()
                customerNameTxt.text = it?.customer?.name
                assigneeNameTxt.text = it?.assigneeUser?.userName
            }
            if (it == null) return@subscribe
            viewModel.searchTaskList(TaskQueryEntity().apply {
                taskType = it.taskType
                status = it.taskStatus
                customerId = it.customer?.orgId
                assigneeUserId = it.assigneeUser?.userId
                keyword = it.keyword
                dockId = it.dockId
                entryId = it.entryId
                containerNo = it.containerNo
            })

        }

    }

    override fun createViewModel(): TaskSearchViewModel = TaskSearchViewModel()

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.title_task_search)
            searchView.scanEvent = QuickScanner.OnScanEvent { _, data ->
                viewModel.searchTaskList(data) { taskTypes ->
                    TaskTypeSelectDialog.show(
                        activity = this@TaskSearchActivity,
                        keyword = data,
                        taskTypes = taskTypes?: emptyList()
                    )
                }
            }
            adapter = TaskSearchAdapter({ step->
                if (step.taskId.isNullOrEmpty() || step.taskType == null) return@TaskSearchAdapter
                viewModel.getTaskAndNavigateStep(step)
            }, { item->
                if (item.id.isNullOrEmpty() || item.taskType == null) return@TaskSearchAdapter
                if (idmUserId == item.assigneeUserId) {
                    viewModel.getTaskAndNavigateTask(item.id!!, item.taskType!!)
                } else {
                    viewModel.takeOverTask(item.id!!, item.taskType!!, idmUserId, userInfoEntity.userName) {
                        viewModel.getTaskAndNavigateTask(item.id!!, item.taskType!!)
                    }
                }
            })
            recyclerView.layoutManager = LinearLayoutManager(this@TaskSearchActivity)
            recyclerView.adapter = adapter
            deleteBtn.setOnClickListener {
                viewModel.setAdvancedSearch(null)
            }
            advancedSearchBtn.setOnClickListener {
                AdvancedSearchActivity.startActivity(this@TaskSearchActivity, viewModel.dataState.advancedSearch)
            }
            navigateStep()
            navigateTask()
        }
    }

//    override fun onResume() {
//        super.onResume()
//        viewModel.reloadTaskList()
//    }

    private fun navigateTask() = onEvent<TaskSearchUiEvent.NavigateTask> {
        when (taskType) {
            TaskType.RECEIVE -> {
                val receiveTaskEntity = task as ReceiveTaskEntity
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, receiveTaskEntity, TaskType.RECEIVE)
            }
            TaskType.PUT_AWAY -> {
                PutAwayTaskActivity.startActivity(this@TaskSearchActivity, task.id)
            }
            TaskType.PICK -> {
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, task as PickTaskEntity, TaskType.PICK)
            }
            TaskType.LOAD -> {
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, task as LoadTaskEntity, TaskType.LOAD)
            }
            TaskType.PACK -> {
                ConsolidateToPalletTaskActivity.startActivity(this@TaskSearchActivity, task as PackTaskEntity)
            }
            TaskType.REPLENISH -> {
                ReplenishmentTaskActivity.startActivity(this@TaskSearchActivity, task as ReplenishmentTaskEntity)
            }
            TaskType.MOVEMENT -> {
                val movementTask = task as MovementTaskEntity
                if (task.type == MovementTypeEntity.BATCH_MOVEMENT) {
                    BatchMovementActivity.startActivity(this@TaskSearchActivity, movementTask)
                } else {
                    MovementTaskActivity.startActivity(this@TaskSearchActivity, task)
                }
            }
            TaskType.PUT_BACK -> {
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, task as PutBackTaskEntity, TaskType.PUT_BACK)
            }
            TaskType.GENERAL -> {
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, task as GeneralTaskEntity, TaskType.GENERAL)
            }
            TaskType.INTERNAL_TRANSFER_OUT -> {
                TransferOutTaskActivity.startActivity(this@TaskSearchActivity, task as TransferOutTaskEntity)
            }
            TaskType.INTERNAL_TRANSFER_IN -> {
                TransferInTaskActivity.startActivity(this@TaskSearchActivity, task as TransferInTaskEntity)
            }
            TaskType.TRANSLOAD_RECEIVE -> startTransLoadReceivingStepActivity(task)
            TaskType.TRANSLOAD_LOAD ->  startTransLoadLoadingStepActivity(task)
            TaskType.DOCK_CHECK ->  startDockCheckTaskActivity(task as DockCheckTaskEntity)
            TaskType.ASSEMBLY -> {
                TaskStepMenuActivity.startActivity(this@TaskSearchActivity, task as AssemblyTaskEntity, TaskType.ASSEMBLY)
            }
            else -> {}
        }
    }

    private fun navigateStep() = onEvent<TaskSearchUiEvent.NavigateStep> {
        when (step.taskType) {
            TaskType.PICK -> {
                when (step.stepType) {
                    TaskStepType.PICK -> {
                        PickStepActivity.startActivity(this@TaskSearchActivity, step.taskId.toString())
                    }

                    TaskStepType.STAGE -> {
                        StageStepActivity.startActivity(this@TaskSearchActivity, step.id.toString())
                    }
                    TaskStepType.ORDER_PICK_FROM_WALL -> {
                        val intent = Intent()
                        val clazz = OrderPickStepActivity::class.java
                        intent.setClass(this@TaskSearchActivity, clazz)
                        val param = OrderPickStepActivity.Param(task = task as PickTaskEntity, step = step)
                        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                        startActivity(intent)
                    }
                    TaskStepType.STAGE_TO_WALL -> {
                        val intent = Intent()
                        val clazz = StageToWallStepActivity::class.java
                        intent.setClass(this@TaskSearchActivity, clazz)
                        val param = StageToWallStepActivity.Param(task = task as PickTaskEntity, step = step)
                        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                        startActivity(intent)
                    }
                    TaskStepType.SORTING_TO_WALL-> {
                        val event = PickTaskStepStartEvent().apply {
                            this.stepEntity = step
                            this.taskEntity = task as PickTaskEntity
                        }
                        EventBus.getDefault().postSticky(event)
                        SortingToWallActivity.startActivity(this@TaskSearchActivity)
                    }
                    TaskStepType.LOADING -> {
                        val isAssignedToMe = step.isAssigned(idmUserId)
                        if (isAssignedToMe) {
                            viewModel.showLoadTaskInfo(step.taskId?:"",::showLoadTaskDescribeDialog)
                        } else {
                            viewModel.takeOverTask(step.taskId?:"", TaskType.LOAD, idmUserId, userInfoEntity.userName) {
                                viewModel.getTaskAndNavigateLoadingStep(step.taskId?:"", TaskType.LOAD)
                            }
                        }
                    }
                    else -> {}
                }
            }

            TaskType.RECEIVE -> {
                startReceiveStepActivity(task, step)
            }
            TaskType.TRANSLOAD_RECEIVE -> {
                startTransLoadReceivingStepActivity(task)
            }
            TaskType.TRANSLOAD_LOAD -> {
                startTransLoadLoadingStepActivity(task)
            }

            TaskType.GENERAL -> {
                startGeneralStepActivity(task, step)
            }

            TaskType.PUT_BACK -> {
                startPutBackStepActivity(task, step)
            }
            TaskType.LOAD -> {
                LoadTaskActivity.startActivity(this@TaskSearchActivity, step.taskId!!)
            }
            TaskType.ASSEMBLY -> {
                startAssemblyStepActivity(task, step)
            }
            TaskType.QC -> {
                startOutboundQcStepActivity(task, step)
            }
            else -> {}
        }
    }


    private fun startReceiveStepActivity(task: BaseTaskEntity, step: BaseTaskStepEntity) {
        when (step.stepType) {
            TaskStepType.DOCK_CHECK_IN -> {
                startDockCheckIn(task)
            }
            TaskStepType.OFFLOAD -> {
                startOffload(task)
            }
            TaskStepType.LP_SETUP -> {
                startLpSetup(task)
            }
            TaskStepType.STAGE -> {
                startReceiveStage(task)
            }
            TaskStepType.SN_SCAN -> {
                startSnScan(task)
            }
            TaskStepType.MATERIAL_RECEIVING -> {
                startMaterialReceivingStepActivity(task)
            }
            else -> {}
        }
    }

    private fun startDockCheckIn(task: BaseTaskEntity) {
        val receiveTaskEntity = task as ReceiveTaskEntity
        DockCheckInActivity.startActivityForResult(this@TaskSearchActivity, receiveTaskEntity, receiveTaskEntity.entryId,
            receiveTaskEntity.dockId, receiveTaskEntity.dockName)
    }

    private fun startOffload(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = OffloadActivity::class.java
        intent.setClass(this, clazz)
        val param = OffloadActivity.Param(receiveTaskEntity = task as ReceiveTaskEntity, OffloadInitPage.SELECT_TYPE)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startLpSetup(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = LpSetupActivity::class.java
        intent.setClass(this, clazz)
        val param = LpSetupActivity.Param(receiveTaskEntity = task as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startReceiveStage(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = ReceiveStageActivity::class.java
        intent.setClass(this, clazz)
        val param = ReceiveStageActivity.Param(receiveTaskEntity = task as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startSnScan(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = SnScanActivity::class.java
        intent.setClass(this, clazz)
        val param = SnScanActivity.Param(receiveTaskEntity = task as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startTransLoadReceivingStepActivity(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = TransLoadReceivingTaskActivity::class.java
        intent.setClass(this, clazz)
        val param = TransLoadReceivingTaskActivity.Param(transLoadReceivingTaskEntity = task as TransLoadReceivingTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startMaterialReceivingStepActivity(task: BaseTaskEntity) {
        val startEvent = ReceiveTaskStartEvent().apply {
            taskEntity = task as ReceiveTaskEntity
        }
        EventBus.getDefault().postSticky(startEvent)
        MaterialReceiveActivity.startActivity(this)
    }

    private fun startTransLoadLoadingStepActivity(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = TransLoadLoadingTaskActivity::class.java
        intent.setClass(this, clazz)
        val param = TransLoadLoadingTaskActivity.Param(transLoadLoadingTaskEntity = task as TransLoadLoadingTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startGeneralStepActivity(task: BaseTaskEntity, step: BaseTaskStepEntity) {
        when (step.stepType) {
            TaskStepType.GENERAL -> {
                val intent = Intent()
                val clazz = GeneralWorkActivity::class.java
                intent.setClass(this, clazz)
                val param = GeneralWorkActivity.Param(task = task as GeneralTaskEntity, step = step)
                ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                startActivity(intent)
            }

            else -> {}
        }
    }


    private fun startPutBackStepActivity(task: BaseTaskEntity, step: BaseTaskStepEntity) {
        val intent = Intent()
        val clazz = EmptyPutBackTaskActivity::class.java
        intent.setClass(this, clazz)
        val param = EmptyPutBackTaskActivity.Param(task = task as PutBackTaskEntity, step = step)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startDockCheckTaskActivity(dockCheckTaskEntity: DockCheckTaskEntity) {
        val intent = Intent()
        val clazz = DockCheckTaskActivity::class.java
        intent.setClass(this, clazz)
        val param = DockCheckTaskActivity.Param(dockCheckTaskEntity = dockCheckTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startAssemblyStepActivity(task: BaseTaskEntity, step: BaseTaskStepEntity) {
        // 使用getTaskStep方法获取对应的步骤
        val assemblyPickStep = task.getTaskStep(TaskStepType.ASSEMBLY_PICK)
        val kittingStep = task.getTaskStep(TaskStepType.GENERAL)
        if (step.taskId.isNullOrEmpty()) return
        when (step.stepType) {
            TaskStepType.ASSEMBLY_PICK -> {
                AssemblyTaskActivity.startActivity(this@TaskSearchActivity, step.taskId!!, step.stepType!!)
            }
            TaskStepType.GENERAL -> {
                // 检查ASSEMBLY_PICK步骤是否已完成
                if (assemblyPickStep?.isDone() == true) {
                    AssemblyTaskActivity.startActivity(this@TaskSearchActivity, step.taskId!!, step.stepType!!)
                } else {
                    CenterDialog.alert(
                        context = this@TaskSearchActivity,
                        title = getString(R.string.title_assembly_task),
                        message = getString(R.string.msg_complete_assembly_pick_first),
                        okClick = null
                    ).show()
                }
            }
            TaskStepType.ASSEMBLY_PUTAWAY -> {
                // 检查ASSEMBLY_PICK步骤和GENERAL步骤是否已完成
                if (assemblyPickStep?.isDone() == true && kittingStep?.isDone() == true) {
                    AssemblyTaskActivity.startActivity(this@TaskSearchActivity, step.taskId!!, step.stepType!!)
                } else {
                    val message = when {
                        assemblyPickStep?.isDone() != true -> getString(R.string.msg_complete_assembly_pick_first)
                        kittingStep?.isDone() != true -> getString(R.string.msg_complete_assembly_kitting_first)
                        else -> null
                    }
                    message?.let {
                        CenterDialog.alert(
                            context = this@TaskSearchActivity,
                            title = getString(R.string.title_assembly_task),
                            message = it,
                            okClick = null
                        ).show()
                    }
                }
            }
            else -> {
            }
        }
    }

    private fun startOutboundQcStepActivity(task: BaseTaskEntity, step: BaseTaskStepEntity) {
        if (step.taskId.isNullOrEmpty()) return

        // 如果是Fix步骤，检查Inspection步骤是否完成
        if (step.stepType == TaskStepType.FIX_CONTENT) {
            val inspectionCONTENTStep = task.getTaskStep(TaskStepType.INSPECTION_CONTENT)
            if (inspectionCONTENTStep?.isDone() != true) {
                showSnack(Message.ResourceMessage(R.string.msg_complete_inspection_first), SnackType.ErrorV2())
                return
            }
        }

        OutboundQcTaskActivity.startActivity(this@TaskSearchActivity, step.taskId, step.stepType!!)
    }

    private fun showLoadTaskDescribeDialog(describeEntity: LoadTaskDescribeEntity) = LoadTaskDescribeDialog.show(
        this@TaskSearchActivity,
        describe = describeEntity,
        onConfirm = {
            LoadTaskActivity.startActivity(this@TaskSearchActivity, describeEntity.taskId!!)
        }
    )

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(requestCode == AdvancedSearchActivity.REQUEST_CODE && resultCode == RESULT_OK){
            val advancedSearch = data?.getStringExtra(IntentKey.ADVANCED)
            if (advancedSearch.isNullOrEmpty()) return
            val advanced = Gson().fromJson(advancedSearch, AdvancedSearchEntity::class.java)
            viewModel.setAdvancedSearch(advanced)
        }
    }
}