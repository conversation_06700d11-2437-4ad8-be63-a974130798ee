package com.unis.wms.task_search

import android.view.LayoutInflater
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.linc.platform.core.LocalPersistence
import com.linc.platform.utils.StringUtil
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.taskcenter.model.TaskQueryResultEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemTaskSearchBinding
import com.unis.wms.databinding.ItemTaskStepBinding

class TaskSearchAdapter(
    private val stepAction: (BaseTaskStepEntity) -> Unit,
    private val taskOverAction: (TaskQueryResultEntity) -> Unit
): BaseBindingDifferQuickAdapter<TaskQueryResultEntity, ItemTaskSearchBinding>() {

    override fun convert(holder: BaseBindingViewHolder<ItemTaskSearchBinding>, item: TaskQueryResultEntity) {
        holder.binding.apply {
            initTitle(this, item)
            initContent(this, item)
            initStep(this, item.taskSteps)
            val userId = LocalPersistence.getIamEntity(mContext).userId
            takeOverBtn.text = mContext.getString(if (userId == item.assigneeUserId) R.string.text_enter else R.string.title_take_over)
            if (needShowTaskStep(item)) {
                stepDetailBtn.setVisible()
                stepLayout.setVisible()
                taskStepLl.setVisible()
                taskStepDivider.setVisible()
            } else {
                stepDetailBtn.setGone()
                stepLayout.setGone()
                taskStepLl.setGone()
                taskStepDivider.setGone()
            }
            stepDetailBtn.setOnClickListener{
                taskStepLl.setVisible()
            }
            takeOverBtn.setOnClickListener {
                taskOverAction(item)
            }
        }

    }

    private fun needShowTaskStep(item: TaskQueryResultEntity): Boolean {
        return when (item.taskType) {
            TaskType.TRANSLOAD_RECEIVE -> false
            TaskType.TRANSLOAD_LOAD-> false
            TaskType.DOCK_CHECK -> false
            TaskType.REPLENISH -> false
            TaskType.MOVEMENT -> false
            TaskType.PUT_AWAY -> false
            TaskType.PACK -> false
            TaskType.INTERNAL_TRANSFER_OUT -> false
            TaskType.INTERNAL_TRANSFER_IN -> false
            else -> true
        }
    }

    override fun areItemsTheSame(oldItem: TaskQueryResultEntity, newItem: TaskQueryResultEntity) = oldItem.id == newItem.id

    override fun areContentsTheSame(oldItem: TaskQueryResultEntity, newItem: TaskQueryResultEntity)= oldItem.id == newItem.id

    private fun initTitle(binding: ItemTaskSearchBinding, item: TaskQueryResultEntity) {
        when(item.status) {
            TaskStatus.NEW -> {
                binding.statusImg.setImageResource(R.drawable.ic_status_new)
                binding.statusTxt.text = mContext.getString(R.string.title_load_status_new)
                binding.titleLy.setBackgroundResource(R.color.status_unlock)
            }
            TaskStatus.IN_PROGRESS -> {
                binding.statusImg.setImageResource(R.drawable.ic_filter_btn_progress)
                binding.statusTxt.text = mContext.getString(R.string.title_load_status_progress)
                binding.titleLy.setBackgroundResource(R.color.status_progress)
            }
            TaskStatus.EXCEPTION -> {
                binding.statusImg.setImageResource(R.drawable.ic_error_red_26)
                binding.statusTxt.text = mContext.getString(R.string.title_exception)
                binding.titleLy.setBackgroundResource(R.color.status_progress)
            }
            TaskStatus.CLOSED, TaskStatus.FORCE_CLOSED, TaskStatus.CANCELLED  -> {
                binding.statusImg.setImageResource(R.drawable.ic_filter_btn_done)
                binding.statusTxt.text = mContext.getString(R.string.title_load_status_done)
                binding.titleLy.setBackgroundResource(R.color.status_done)
            } else -> {}
        }

        binding.taskIdTxt.text = item.id?: ""
    }

    private fun initContent(binding: ItemTaskSearchBinding, item: TaskQueryResultEntity) {
        val resId = R.string.text_unknown
        val titleResId = when(item.taskType) {
            TaskType.GENERAL -> R.string.general_task
            TaskType.RECEIVE -> R.string.title_receive_task
            TaskType.PUT_AWAY -> R.string.title_put_away_task
            TaskType.PICK -> R.string.title_pick_task
            TaskType.LOAD-> R.string.title_task_load
            TaskType.REPLENISH -> R.string.title_task_replenishment
            TaskType.MOVEMENT -> R.string.title_inventory_movement_task
            TaskType.PACK -> R.string.title_pack_task
            TaskType.PUT_BACK -> R.string.title_put_back_task
            TaskType.CYCLE_COUNT -> R.string.title_cycle_count
            TaskType.TRANSLOAD_RECEIVE -> R.string.title_transload_receiving
            TaskType.TRANSLOAD_LOAD -> R.string.title_transload_loading
            TaskType.INTERNAL_TRANSFER_IN -> R.string.title_transfer_in_task
            TaskType.INTERNAL_TRANSFER_OUT -> R.string.title_transfer_out_task
            TaskType.TRANSFER_DOCK_LOCATION -> R.string.transfer_dock_location
            TaskType.ASSEMBLY -> R.string.title_assembly_task
            TaskType.QC-> R.string.title_outbound_qc_task
            else -> resId
        }
        binding.apply {
            dockNameTxt.text = item.dockName
            priorityTxt.text = item.priority?.name
            assigneeNameTxt.text = item.assigneeUserName
            taskTypeTxt.text = mContext.getString(titleResId)
            startPeriodTxt.text = StringUtil.dateTimeFormat(item.startTime)
            endPeriodTxt.text = StringUtil.dateTimeFormat(item.endTime)
            rnTxt.text = item.receiptIds?.joinToString(", ", postfix = "")
            orderIdsTxt.text = item.orderIds?.joinToString(", ", postfix = "")
            stepNameTxt.text = getTaskStep(item)
        }
    }

    private fun getTaskStep(item: TaskQueryResultEntity): String {
        return item.taskSteps?.joinToString(", ", postfix = "") { getStepName(it.stepType) } ?:""
    }

    private fun getStepName(stepType: TaskStepType?): String {
        return  when(stepType) {
            TaskStepType.PICK -> mContext.getString(R.string.label_pick_step)
            TaskStepType.DOCK_CHECK_IN -> mContext.getString(R.string.text_dock_check_in)
            TaskStepType.SN_SCAN -> mContext.getString(R.string.label_scan_sn_step)
            TaskStepType.OFFLOAD -> mContext.getString(R.string.label_offload_step)
            TaskStepType.LP_SETUP -> mContext.getString(R.string.label_lp_setup_step)
            TaskStepType.STAGE -> mContext.getString(R.string.label_stage_step)
            TaskStepType.REPLENISH -> mContext.getString(R.string.label_replenish_step)
            TaskStepType.GENERAL -> mContext.getString(R.string.label_general_step)
            TaskStepType.TRANSLOAD_RECEIVE -> mContext.getString(R.string.transload_receiving_step)
            TaskStepType.TRANSLOAD_LOAD -> mContext.getString(R.string.transload_shipping_step)
            TaskStepType.PUT_BACK -> mContext.getString(R.string.label_put_back_step)
            TaskStepType.LOADING -> mContext.getString(R.string.text_loading)
            TaskStepType.ORDER_PICK_FROM_WALL -> mContext.getString(R.string.label_order_pick_from_wall_step)
            TaskStepType.STAGE_TO_WALL -> mContext.getString(R.string.label_stage_to_wall_step)
            TaskStepType.SORTING_TO_WALL-> mContext.getString(R.string.label_sorting_to_wall_step)
            TaskStepType.PACK -> mContext.getString(R.string.label_pack)
            TaskStepType.DROP -> mContext.getString(R.string.text_drop)
            TaskStepType.MATERIAL_RECEIVING -> mContext.getString(R.string.text_material_receive)
            TaskStepType.ASSEMBLY_PICK -> mContext.getString(R.string.title_assembly_pick)
            TaskStepType.ASSEMBLY_PUTAWAY -> mContext.getString(R.string.title_assembly_put_away)
            TaskStepType.INSPECTION_CONTENT -> mContext.getString(R.string.text_inspection)
            TaskStepType.FIX_CONTENT -> mContext.getString(R.string.text_fix)
            else -> ""
        }
    }

    private fun initStep(binding: ItemTaskSearchBinding, taskSteps: List<BaseTaskStepEntity>?) {
        if (taskSteps.isNullOrEmpty()) return
        binding.apply {
            taskStepLl.removeAllViews()
            val allowShowTaskSteps = taskSteps.filter { it.isShawStepLocal }
            for (item in allowShowTaskSteps) {
                val stepLayout = LayoutInflater.from(mContext).inflate(R.layout.item_task_step, taskStepLl, false)
                ItemTaskStepBinding.bind(stepLayout).apply {
                    itemSequenceTv.text = item.stepSequence.toString()
                    stepNameTv.text = getStepName(item.stepType)
                    stepStatusTv.text = when(item.status) {
                        TaskStepStatus.NEW -> mContext.getString(R.string.label_new)
                        TaskStepStatus.IN_PROGRESS -> mContext.getString(R.string.label_in_progress)
                        TaskStepStatus.CLOSED -> mContext.getString(R.string.label_done)
                        TaskStepStatus.FORCE_CLOSED -> mContext.getString(R.string.label_force_close)
                        TaskStepStatus.CANCELLED -> mContext.getString(R.string.label_cancel)
                        else -> ""
                    }
                    assigneeNameTv.text = item.assigneeUserNames?.joinToString(",", postfix = "")
                    root.setOnClickListener { stepAction.invoke(item) }
                }
                taskStepLl.addView(stepLayout)
            }
        }
    }

}