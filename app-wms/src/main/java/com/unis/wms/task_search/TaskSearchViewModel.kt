package com.unis.wms.task_search

import com.customer.widget.common.addToNewList
import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.cyclecount.model.CountModeEntity
import com.unis.platform.cyclecount.model.CountTaskQueryEntity
import com.unis.platform.cyclecount.model.transferToDockCheckTask
import com.unis.platform.internaltransfer.inn.api.TransferInTaskApi
import com.unis.platform.internaltransfer.out.api.TransferOutTaskApi
import com.unis.platform.movement.api.MovementTaskApi
import com.unis.platform.pack.api.PackTaskApi
import com.unis.platform.replenishment.api.ReplenishmentTaskApi
import com.unis.platform.taskcenter.api.TaskCenterApi
import com.unis.platform.taskcenter.model.TakeOverRequest
import com.unis.platform.transload.dockcheck.api.DockCheckApi
import com.unis.platform.transload.dockcheck.model.DockCheckTaskEntity
import com.unis.platform.transload.loading.api.TransLoadLoadingTaskApi
import com.unis.platform.transload.receiving.api.TransLoadReceivingTaskApi
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.assemblytask.work.AssemblyTaskRepository
import com.unis.wms.general.GeneralTaskRepository
import com.unis.wms.load.LoadTaskRepository
import com.unis.wms.outbound_qc_task.OutboundQcTaskRepository
import com.unis.wms.pick_task.PickTaskRepository
import com.unis.wms.put_away_task.PutAwayRepository
import com.unis.wms.put_back.PutBackTaskRepository
import com.unis.wms.receive.ReceiveTaskRepository
import com.unis.wms.task_search.advanced.AdvancedSearchEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOf

class TaskSearchViewModel(
    initialDataState: TaskSearchDataState = TaskSearchDataState()
) : SimpleReactiveViewModel<TaskSearchDataState>(initialDataState) {

    private val taskSearchRepository = TaskSearchRepository()

    fun searchTaskList(keyword: String, onShowConfirmDialog: (List<TaskType>?) -> Flow<List<TaskType>?>) {
        val newKeyword = formatInputPrefix(keyword)
        val query = TaskQueryEntity().apply {
            this.keyword = newKeyword
        }
        launch {
            val taskSearchResult = requestAwait(taskSearchRepository.searchTaskList(query)).getOrNull()
            if (taskSearchResult.isNullOrEmpty()) {
                showToast(R.string.error_task_not_found)
            } else {
                val taskTypeList = taskSearchResult.mapNotNull { it.taskType }.distinct()
                val taskResult = if (taskTypeList.size == 1) {
                    taskSearchResult
                } else {
                    val selectTaskTypes = onShowConfirmDialog(taskTypeList).firstOrNull()?: emptyList()
                    val selectTaskSearchResult = if (selectTaskTypes.isEmpty()) {
                        taskSearchResult
                    } else {
                        taskSearchResult.filter { it.taskType in selectTaskTypes }
                    }
                    selectTaskSearchResult
                }
                taskResult.map { if (it.taskType == TaskType.PICK && it.getTaskStep(TaskStepType.LOADING) != null) {
                    it.taskSteps?.map { step->
                        if (step.stepType == TaskStepType.LOADING) {
                            step.isShawStepLocal = true
                        } else if (step.stepType == TaskStepType.STAGE) {
                            step.isShawStepLocal = false
                        }
                        it
                    }
                } else {
                    it
                }}
                setDataState { copy(taskSearchResult = taskResult, currentKeyword = newKeyword, currentQueryEntity = null) }
            }
        }
    }

    fun searchTaskList(taskQueryEntity: TaskQueryEntity) {
        request(taskSearchRepository.searchTaskList(taskQueryEntity)) {
            if (it.isNullOrEmpty()) {
                showToast(R.string.error_task_not_found)
            }
            setDataState { copy(taskSearchResult = it, currentQueryEntity = taskQueryEntity, currentKeyword = null) }
        }
    }

    fun takeOverTask(taskId: String, type: TaskType, userId: String, assigneeUserName: String, action: () -> Unit) {
        val takeOverRequest = TakeOverRequest().apply {
            this.taskId = taskId
            this.taskType = type
            this.assigneeUserId = userId
        }
        request(taskSearchRepository.takeOverTask(takeOverRequest)) {
            showToast(R.string.msg_task_take_over_success)
            updateAssignee(taskId, userId, assigneeUserName)
            action.invoke()
        }
    }

    private fun updateAssignee(taskId: String, userId: String, assigneeUserName: String) {
        val task = getTaskSearchResultList()?.find { v -> v.id == taskId }
        task?.assigneeUserId = userId
        task?.assigneeUserName = assigneeUserName
        task?.taskSteps?.forEach {
            it.assigneeUserIds = it.assigneeUserIds.addToNewList(userId)
            it.assigneeUserNames = it.assigneeUserNames.addToNewList(assigneeUserName)
        }
    }

    fun getTaskAndNavigateStep(stepEntity: BaseTaskStepEntity) {
        launch {
            val taskId = stepEntity.taskId?: return@launch
            val taskType = stepEntity.taskType?: return@launch
            val task = requestAwait(taskSearchRepository.loadTaskDetail(taskId, taskType)).getOrNull()?: return@launch
            fireEvent { TaskSearchUiEvent.NavigateStep(task, stepEntity) }
        }
    }

    fun getTaskAndNavigateTask(taskId: String, taskType: TaskType) {
        launch {
            if (TaskType.DOCK_CHECK == taskType) {
                getDockCheckTask(taskId) {
                    fireEvent { TaskSearchUiEvent.NavigateTask(taskType, it) }
                }
            } else {
                val task = requestAwait(taskSearchRepository.loadTaskDetail(taskId, taskType)).getOrNull()?: return@launch
                fireEvent { TaskSearchUiEvent.NavigateTask(taskType, task) }
            }
        }
    }

    fun getTaskAndNavigateLoadingStep(taskId: String, taskType: TaskType) {
        launch {
            val task = requestAwait(taskSearchRepository.loadTaskDetail(taskId, taskType)).getOrNull()?: return@launch
            val loadStep = task.getTaskStep(TaskStepType.LOADING)?: return@launch
            fireEvent { TaskSearchUiEvent.NavigateStep(task, loadStep) }
        }
    }

    fun getDockCheckTask(taskId: String, action: (DockCheckTaskEntity) -> Unit) {
        launch {
            val tasks = requestAwait(taskSearchRepository.loadDockCheckTask(taskId)).getOrNull()?:return@launch
            if (tasks.list.isNullOrEmpty()) {
                showSnack(SnackType.ErrorV2(), R.string.msg_get_task_fail)
            } else {
                val countTaskEntity = tasks.list[0]
                action.invoke(countTaskEntity.transferToDockCheckTask() ?: DockCheckTaskEntity())
            }
        }
    }

    fun setAdvancedSearch(advanced: AdvancedSearchEntity?) {
        setDataState { copy(advancedSearch = advanced) }
        if (advanced == null) {
            setDataState { copy(taskSearchResult = null) }
        }
    }

    private fun formatInputPrefix(inputStr: String): String {
        val prefixes = listOf("TASK-", "RN-", "DN-", "ET-")
        return prefixes.firstOrNull { inputStr.lowercase().startsWith(it.lowercase()) }?.let {
            inputStr.replaceFirst(it, it, true)
        } ?: inputStr
    }

    fun showLoadTaskInfo(taskId: String, describeDialog: (LoadTaskDescribeEntity)-> Flow<Boolean>) {
        launch {
            val loadDescribe = requestAwait(taskSearchRepository.getLoadDescribe(taskId)).getOrNull()?: return@launch
            describeDialog(loadDescribe).firstOrNull()
        }
    }

    private fun getTaskSearchResultList() = dataState.taskSearchResult

    inner class TaskSearchRepository: BaseRepository() {

        private val taskCenterApi by apiServiceLazy<TaskCenterApi>()

        private val pickTaskRepository by lazy { PickTaskRepository() }
        private val putAwayRepository by lazy { PutAwayRepository() }
        private val generalTaskRepository by lazy { GeneralTaskRepository() }
        private val putBackTaskRepository by lazy { PutBackTaskRepository() }
        private val receiveTaskRepository by lazy { ReceiveTaskRepository() }
        private val loadTaskRepository by lazy { LoadTaskRepository() }
        private val replenishmentTaskApi by apiServiceLazy<ReplenishmentTaskApi>()
        private val movementTaskApi by apiServiceLazy<MovementTaskApi>()
        private val transloadReceiveTaskApi by apiServiceLazy<TransLoadReceivingTaskApi>()
        private val transLoadLoadTaskApi by apiServiceLazy<TransLoadLoadingTaskApi>()
        private val packTaskApi by apiServiceLazy<PackTaskApi>()
        private val dockCheckApi by apiServiceLazy<DockCheckApi>()
        private val transferOutTaskApi by apiServiceLazy<TransferOutTaskApi>()
        private val transferInTaskApi by apiServiceLazy<TransferInTaskApi>()
        private val assemblyTaskRepository by lazy { AssemblyTaskRepository() }
        private val outboundQcTaskRepository by lazy { OutboundQcTaskRepository()}

        fun loadTaskDetail(taskId: String, taskType: TaskType) = when (taskType) {
            TaskType.PICK -> pickTaskRepository.getPickTask(taskId)
            TaskType.GENERAL -> generalTaskRepository.getTask(taskId)
            TaskType.RECEIVE -> receiveTaskRepository.getTask(taskId)
            TaskType.PUT_AWAY -> putAwayRepository.getPutAwayTask(taskId)
            TaskType.LOAD -> loadTaskRepository.getLoadTask(taskId)
            TaskType.REPLENISH -> rxRequest2(replenishmentTaskApi.getTask(taskId))
            TaskType.MOVEMENT -> rxRequest2(movementTaskApi.getTask(taskId))
            TaskType.PUT_BACK -> putBackTaskRepository.getTask(taskId)
            TaskType.CYCLE_COUNT -> TODO()
            TaskType.TRANSLOAD_RECEIVE -> rxRequest2(transloadReceiveTaskApi.getTransLoadReceivingTask(taskId))
            TaskType.TRANSLOAD_LOAD -> rxRequest2(transLoadLoadTaskApi.getTransLoadLoadingTask(taskId))
            TaskType.PACK -> rxRequest2(packTaskApi.getTask(taskId))
            TaskType.INTERNAL_TRANSFER_OUT -> rxRequest2(transferOutTaskApi.getTask(taskId))
            TaskType.INTERNAL_TRANSFER_IN -> rxRequest2(transferInTaskApi.getTask(taskId))
            TaskType.DOCK_CHECK -> TODO()
            TaskType.SPOT_CHECK -> TODO()
            TaskType.TRANSFER_DOCK_LOCATION -> TODO()
            TaskType.ASSEMBLY -> assemblyTaskRepository.getTask(taskId)
            TaskType.QC -> outboundQcTaskRepository.getTask(taskId)
        }

        fun loadDockCheckTask(taskId: String) =
            rxRequest2(dockCheckApi.searchDockCheckTaskByPaging(CountTaskQueryEntity().apply {
                this.id = taskId
                this.mode = CountModeEntity.DOCK_CHECK
            }))

        fun searchTaskList(query: TaskQueryEntity) = requestV2({
            taskCenterApi.searchTask(query.apply { pageSize = 100 })
        })

        fun takeOverTask(takeOverRequest: TakeOverRequest) = requestV2({
            taskCenterApi.takeOverTask(takeOverRequest)
        })

        fun getLoadDescribe(taskId: String) = loadTaskRepository.getLoadTaskDescribe(taskId)
    }
}