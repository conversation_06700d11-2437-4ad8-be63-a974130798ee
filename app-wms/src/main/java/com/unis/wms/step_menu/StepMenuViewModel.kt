package com.unis.wms.step_menu

import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.receive.model.ReceiveTaskEntity
import com.unis.platform.taskcenter.model.TakeOverRequest
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.common.extensions.deepCopy
import com.unis.wms.step_menu.model.StepMenuDataState
import com.unis.wms.step_menu.model.StepMenuUiState
import com.unis.wms.step_menu.model.TaskStepUiEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

class StepMenuViewModel(
    initialDataState: StepMenuDataState = StepMenuDataState(),
    initialUiState: StepMenuUiState = StepMenuUiState()
) : ReactiveViewModel<StepMenuDataState, StepMenuUiState>(initialDataState, initialUiState) {

    private val stepMenuRepository by lazy { StepMenuRepository() }

    init {
        mapDataToUi(StepMenuDataState::task, StepMenuUiState::task) { it }
        mapDataToUi(StepMenuDataState::taskStepList, StepMenuUiState::taskStepList) {
            it?.filter { step -> step.isShawStepLocal }
        }
    }

    fun loadTaskStep(taskId: String, taskType: TaskType) {
        launch {
            val request = stepMenuRepository.loadTaskDetail(taskId, taskType) ?: return@launch
            val taskResult = requestAwait(request)
            fireEvent { TaskStepUiEvent.RefreshEnd }
            if (taskResult.isSuccess) {
                val task = taskResult.getOrNull() ?: return@launch
                val loadStep = task.getTaskStep(TaskStepType.LOADING)
                val stageStep = task.getTaskStep(TaskStepType.STAGE)
                val allowDirectedLoad = taskType == TaskType.PICK && loadStep != null
                val allowDirectedLoadOptionMenu = allowDirectedLoad && ((stageStep == null || stageStep.isNew()) && loadStep?.isNew() == true)
                val taskStepList = if (allowDirectedLoad) {
                    if (allowDirectedLoadOptionMenu) {
                        task.taskSteps?.map {
                            if (it.stepType == TaskStepType.LOADING) {
                                it.isShawStepLocal = dataState.isTurnOnDirectedLoad == true
                            } else if (it.stepType == TaskStepType.STAGE) {
                                it.isShawStepLocal = dataState.isTurnOnDirectedLoad != true
                            }
                            it
                        }
                    } else {
                        task.taskSteps?.map {
                            if (loadStep?.isNew() != true) {
                                if (it.stepType == TaskStepType.LOADING) {
                                    it.isShawStepLocal = true
                                } else if (it.stepType == TaskStepType.STAGE) {
                                    it.isShawStepLocal = false
                                }
                            } else {
                                if (it.stepType == TaskStepType.LOADING) {
                                    it.isShawStepLocal = false
                                } else if (it.stepType == TaskStepType.STAGE) {
                                    it.isShawStepLocal = true
                                }
                            }

                            it
                        }
                    }
                } else {
                    task.taskSteps
                }

                setDataStateAwait { copy(task = task, taskType = taskType, taskStepList = taskStepList, allowDirectedLoadOptionMenu = allowDirectedLoadOptionMenu) }
                fireEvent { TaskStepUiEvent.RefreshMenu }
                val isStepAllOver = task.taskSteps?.map { step -> step.status }?.all { status ->
                    status == TaskStepStatus.CLOSED
                            || status == TaskStepStatus.CANCELLED || status == TaskStepStatus.FORCE_CLOSED
                } == true
                val isForceCloseStep =
                    task.taskSteps?.map { step -> step.status }?.any { status -> status == TaskStepStatus.FORCE_CLOSED } == true
                val isNewOrProgress = task.status == TaskStatus.NEW || task.status == TaskStatus.IN_PROGRESS
                if (isNewOrProgress && isStepAllOver) {
                    fireEvent {
                        if (isForceCloseStep) TaskStepUiEvent.ShowForceCloseTaskDialog(getString(R.string.msg_force_close_task))
                        else TaskStepUiEvent.ShowCloseTaskDialog
                    }
                }
            }
        }
    }

    fun updatePageByDirectLoad(isTurnOn: Boolean) {
        launch {
            val loadingStep = dataState.taskStepList?.find { it.stepType == TaskStepType.LOADING }.deepCopy() ?: return@launch
            val stageStep = dataState.taskStepList?.find { it.stepType == TaskStepType.STAGE }.deepCopy() ?: return@launch
            if (isTurnOn) {
                loadingStep.isShawStepLocal = true
                stageStep.isShawStepLocal = false
            } else {
                loadingStep.isShawStepLocal = false
                stageStep.isShawStepLocal = true
            }
            val taskStepList = dataState.taskStepList?.toMutableList()?.apply {
                removeIf {
                    it.stepType == TaskStepType.LOADING || it.stepType == TaskStepType.STAGE
                }
                add(loadingStep)
                add(stageStep)
            }
            setDataStateAwait { copy(taskStepList = taskStepList, isTurnOnDirectedLoad = isTurnOn) }
            fireEvent { TaskStepUiEvent.RefreshMenu }
        }
    }

    fun closeTask() {
        if (dataState.task?.id == null || dataState.taskType == null) return
        val requestClose = stepMenuRepository.closeTask(dataState.task?.id!!, dataState.taskType!!)
        if (requestClose == null) {
            showToast(R.string.unable_close_task)
            return
        }
        request(requestClose, success = {
            showSnack(SnackType.SuccessV1(), getString(R.string.close_success))
            fireEvent { TaskStepUiEvent.FinishActivity }
        })
    }

    fun forceCloseTask(msg: String) {
        if (dataState.task?.id == null || dataState.taskType == null) return
        val requestForceClose = stepMenuRepository.forceCloseTask(dataState.task?.id!!, dataState.taskType!!)
        if (requestForceClose == null) {
            showToast(R.string.unable_force_close_task)
            return
        }
        request(requestForceClose) {
            showSnack(SnackType.SuccessV1(), getString(R.string.close_success))
            fireEvent { TaskStepUiEvent.FinishActivity }
        }
    }

    fun checkWorkerTotalHours(action: (Boolean) -> Unit) {
        if (dataState.taskType != TaskType.RECEIVE) {
            return action(false)
        }
        val receiveTaskEntity = dataState.task as? ReceiveTaskEntity
        if (receiveTaskEntity?.customerId.isNullOrEmpty()) return action(false)
        launch {
            requestAwait(stepMenuRepository.getCustomer(receiveTaskEntity?.customerId!!)).onSuccess {
                action(it?.inboundSetting?.collectTotalHours ?: false)
            }
        }
    }

    fun getTask() = dataState.task

    fun taskTakeOver(taskId: String,taskType: TaskType, showDialog: () -> Flow<String?>) {
        launch {
            val message = showDialog().firstOrNull() ?: return@launch
            val takeOverRequest = TakeOverRequest().apply {
                this.taskId = taskId
                this.taskType = taskType
                this.assigneeUserId = stepMenuRepository.idmUserId
            }
            requestAwait(stepMenuRepository.takeOverTask(takeOverRequest)).onSuccess {
                loadTaskStep(taskId, taskType)
            }
        }
    }

    fun showLoadTaskInfo(taskId: String, describeDialog: (LoadTaskDescribeEntity)-> Flow<Boolean>) {
        launch {
            val loadDescribe = requestAwait(stepMenuRepository.getLoadDescribe(taskId)).getOrNull()?: return@launch
            describeDialog(loadDescribe).firstOrNull()
        }
    }
}