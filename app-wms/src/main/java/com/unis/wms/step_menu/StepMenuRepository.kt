package com.unis.wms.step_menu

import com.unis.platform.common.model.task.TaskType
import com.unis.platform.customer_v2.CustomerApiService
import com.unis.platform.taskcenter.api.TaskCenterApi
import com.unis.platform.taskcenter.model.TakeOverRequest
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.wms.assemblytask.work.AssemblyTaskRepository
import com.unis.wms.general.GeneralTaskRepository
import com.unis.wms.load.LoadTaskRepository
import com.unis.wms.outbound_qc_task.OutboundQcTaskRepository
import com.unis.wms.pick_task.PickTaskRepository
import com.unis.wms.put_away_task.PutAwayRepository
import com.unis.wms.put_back.PutBackTaskRepository
import com.unis.wms.receive.ReceiveTaskRepository

class StepMenuRepository : BaseRepository() {
    private val taskCenterApi by apiServiceLazy<TaskCenterApi>()

    private val pickTaskRepository by lazy { PickTaskRepository() }
    private val putAwayRepository by lazy { PutAwayRepository() }
    private val generalTaskRepository by lazy { GeneralTaskRepository() }
    private val putBackTaskRepository by lazy { PutBackTaskRepository() }
    private val receiveTaskRepository by lazy { ReceiveTaskRepository() }
    private val loadTaskRepository by lazy { LoadTaskRepository() }
    private val assemblyTaskRepository by lazy { AssemblyTaskRepository() }
    private val customerApi by apiServiceLazy<CustomerApiService>()
    private val outboundQcTaskRepository by lazy { OutboundQcTaskRepository() }

    fun loadTaskDetail(taskId: String, taskType: TaskType) = when (taskType) {
        TaskType.PICK -> pickTaskRepository.getPickTask(taskId)
        TaskType.GENERAL -> generalTaskRepository.getTask(taskId)
        TaskType.RECEIVE -> receiveTaskRepository.getTask(taskId)
        TaskType.PUT_AWAY -> putAwayRepository.getPutAwayTask(taskId)
        TaskType.LOAD -> loadTaskRepository.getLoadTask(taskId)
        TaskType.REPLENISH -> null
        TaskType.MOVEMENT -> null
        TaskType.PUT_BACK -> putBackTaskRepository.getTask(taskId)
        TaskType.CYCLE_COUNT -> null
        TaskType.TRANSLOAD_RECEIVE -> null
        TaskType.TRANSLOAD_LOAD -> null
        TaskType.PACK -> null
        TaskType.INTERNAL_TRANSFER_OUT -> null
        TaskType.INTERNAL_TRANSFER_IN -> null
        TaskType.DOCK_CHECK -> null
        TaskType.SPOT_CHECK -> null
        TaskType.TRANSFER_DOCK_LOCATION -> null
        TaskType.ASSEMBLY -> assemblyTaskRepository.getTask(taskId)
        TaskType.QC -> outboundQcTaskRepository.getTask(taskId)
    }

    fun closeTask(taskId: String, taskType: TaskType) = when(taskType) {
        TaskType.PICK -> pickTaskRepository.closeTask(taskId)
        TaskType.GENERAL -> generalTaskRepository.closeTask(taskId)
        TaskType.RECEIVE -> receiveTaskRepository.closeTask(taskId)
        TaskType.PUT_AWAY -> putAwayRepository.closeTask(taskId)
        TaskType.REPLENISH -> null
        TaskType.MOVEMENT -> null
        TaskType.PUT_BACK -> putBackTaskRepository.closeTask(taskId)
        TaskType.CYCLE_COUNT -> null
        TaskType.TRANSLOAD_RECEIVE -> null
        TaskType.TRANSLOAD_LOAD -> null
        TaskType.PACK -> null
        TaskType.LOAD -> loadTaskRepository.closeTask(taskId)
        TaskType.INTERNAL_TRANSFER_OUT -> null
        TaskType.INTERNAL_TRANSFER_IN -> null
        TaskType.DOCK_CHECK -> null
        TaskType.SPOT_CHECK -> null
        TaskType.TRANSFER_DOCK_LOCATION -> null
        TaskType.ASSEMBLY -> assemblyTaskRepository.closeTask(taskId)
        TaskType.QC -> outboundQcTaskRepository.closeTask(taskId)
    }

    fun forceCloseTask(taskId: String, taskType: TaskType) = when(taskType) {
        TaskType.PICK -> pickTaskRepository.forceCloseTask(taskId)
        TaskType.GENERAL -> null
        TaskType.RECEIVE -> receiveTaskRepository.forceCloseTask(taskId)
        TaskType.PUT_AWAY -> putAwayRepository.closeTask(taskId)
        TaskType.LOAD -> null
        TaskType.REPLENISH -> null
        TaskType.MOVEMENT -> null
        TaskType.PUT_BACK -> null
        TaskType.CYCLE_COUNT -> null
        TaskType.TRANSLOAD_RECEIVE -> null
        TaskType.TRANSLOAD_LOAD -> null
        TaskType.PACK -> null
        TaskType.INTERNAL_TRANSFER_OUT -> null
        TaskType.INTERNAL_TRANSFER_IN -> null
        TaskType.DOCK_CHECK -> null
        TaskType.SPOT_CHECK -> null
        TaskType.TRANSFER_DOCK_LOCATION -> null
        TaskType.ASSEMBLY -> null
        TaskType.QC -> null
    }

    fun takeOverTask(takeOverRequest: TakeOverRequest) = requestV2({
        taskCenterApi.takeOverTask(takeOverRequest)
    })

    fun getCustomer(customerId: String) = rxRequest2(customerApi.getCustomer(customerId))

    fun getLoadDescribe(taskId: String) = loadTaskRepository.getLoadTaskDescribe(taskId)
}