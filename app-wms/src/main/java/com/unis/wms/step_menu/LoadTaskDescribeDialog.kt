package com.unis.wms.step_menu

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.linc.platform.utils.ResUtil
import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDialog
import com.unis.wms.R
import com.unis.wms.databinding.DialogLoadInfoBinding
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class LoadTaskDescribeDialog(
    private val describe: LoadTaskDescribeEntity,
    private val onConfirm: (LoadTaskDescribeEntity) -> Unit,
    private val onCancel: () -> Unit = {},
    private val onDismiss: () -> Unit = {}
) : BaseBindingDialog<DialogLoadInfoBinding>() {

    private val currentData = describe
    private val adapter = LoadTaskDescribeAdapter()

    @SuppressLint("SetTextI18n")
    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            // 设置RecyclerView
            val lpCounts = describe.orderLps?.sumOf { it.lpIds?.size ?: 0 }?: 0
            describeTv.text = "${ResUtil.format(R.string.label_dock, describe.dockName)}, ${getString(R.string.label_lp_counts)}: $lpCounts"
            setupRecyclerView()

            // 设置按钮点击事件
            setupButtons()
            dialog.setOnDismissListener { onDismiss() }
        }
    }

    override fun onStart() {
        dialog.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setGravity(Gravity.CENTER)
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val width = (screenWidth * 0.9).toInt()
            val params = attributes
            params.width = width
            params.height = WindowManager.LayoutParams.WRAP_CONTENT
            params.gravity = Gravity.CENTER
            attributes = params
        }
        super.onStart()
    }

    private fun setupRecyclerView() {
        binding?.apply {
            recyclerView.layoutManager = LinearLayoutManager(context)
            recyclerView.adapter = adapter
            adapter.setNewData(currentData.orderLps)
        }
    }

    private fun setupButtons() {
        binding?.apply {
            positiveButton.setOnClickListener {
                onConfirm(currentData)
                dismiss()
            }
        }
    }

    companion object {
        fun show(
            activity: FragmentActivity,
            describe: LoadTaskDescribeEntity,
            onConfirm: (LoadTaskDescribeEntity) -> Unit = {},
            onCancel: () -> Unit = {}
        ) = callbackFlow {
            val dialog = LoadTaskDescribeDialog(
                describe = describe,
                onConfirm = { data ->
                    onConfirm(data)
                    trySend(true)
                },
                onCancel = {
                    onCancel()
                    trySend(false)
                },
                onDismiss = {
                    close()
                }
            )
            dialog.show(activity.supportFragmentManager, "LoadTaskDescribeDialog")
            awaitClose { dialog.apply { dismiss() } }
        }
    }
} 