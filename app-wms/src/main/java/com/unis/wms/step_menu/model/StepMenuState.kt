package com.unis.wms.step_menu.model

import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.BaseTaskEntity
import com.unis.platform.common.model.task.TaskType
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

data class StepMenuDataState (
    val task: BaseTaskEntity? = null,
    val taskType: TaskType? = null,
    val taskStepList: List<BaseTaskStepEntity>? = null,
    val isTurnOnDirectedLoad: Boolean? = true,
    val allowDirectedLoadOptionMenu: Boolean? = null,
): ReactiveDataState

data class StepMenuUiState (
    val task: BaseTaskEntity? = null,
    val taskStepList: List<BaseTaskStepEntity>? = null,
    val refreshDirectedLoadOptionMenu: Boolean? = false
): ReactiveUiState {
}

interface TaskStepUiEvent {
    object RefreshEnd : UiEvent
    object ShowCloseTaskDialog: UiEvent
    data class ShowForceCloseTaskDialog(val msg: String): UiEvent
    object FinishActivity: UiEvent
    object RefreshMenu: UiEvent
}