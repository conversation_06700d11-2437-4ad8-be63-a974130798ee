package com.unis.wms.step_menu

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.customer.widget.common.CenterDialog
import com.customer.widget.core.ActivityBundleHolder
import com.data_collection.DCActionConstants
import com.data_collection.models.ActionType
import com.data_collection.models.SendDataModel
import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.BaseTaskEntity
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.general.model.GeneralTaskEntity
import com.unis.platform.pick_v2.model.PickTaskEntity
import com.unis.platform.put_back.model.PutBackTaskEntity
import com.unis.platform.receive.model.ReceiveTaskEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.common.CallbackDialog
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.Message
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.viewLifecycleScope
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.wms.R
import com.unis.wms.assemblytask.work.AssemblyTaskActivity
import com.unis.wms.databinding.ActivityStepMenuBinding
import com.unis.wms.general.work.GeneralWorkActivity
import com.unis.wms.load.LoadTaskActivity
import com.unis.wms.material.receive.MaterialReceiveActivity
import com.unis.wms.material.receive.ReceiveTaskStartEvent
import com.unis.wms.outbound_qc_task.OutboundQcTaskActivity
import com.unis.wms.pick_task.PickTaskStepStartEvent
import com.unis.wms.pick_task.order_pick.OrderPickStepActivity
import com.unis.wms.pick_task.pick.work.PickStepActivity
import com.unis.wms.pick_task.sorting_to_wall.SortingToWallActivity
import com.unis.wms.pick_task.stage.StageStepActivity
import com.unis.wms.pick_task.stage_to_wall.StageToWallStepActivity
import com.unis.wms.put_back.emptytask.EmptyPutBackTaskActivity
import com.unis.wms.receive.WorkerTotalHoursDialog
import com.unis.wms.receive.dockcheckin.DockCheckInActivity
import com.unis.wms.receive.lpsetup.LpSetupActivity
import com.unis.wms.receive.offload.OffloadActivity
import com.unis.wms.receive.offload.OffloadInitPage
import com.unis.wms.receive.snscan.SnScanActivity
import com.unis.wms.receive.stage.ReceiveStageActivity
import com.unis.wms.step_menu.model.StepMenuDataState
import com.unis.wms.step_menu.model.StepMenuStartEvent
import com.unis.wms.step_menu.model.StepMenuUiState
import com.unis.wms.step_menu.model.TaskStepUiEvent
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.Serializable

class TaskStepMenuActivity : ReactiveActivity<StepMenuViewModel, StepMenuUiState, ActivityStepMenuBinding>(), SwipeRefreshLayout.OnRefreshListener {

    companion object{
        fun startActivity(context: Context, task: BaseTaskEntity, taskType: TaskType) {
            val startEvent = StepMenuStartEvent().apply {
                this.task = task
                this.taskType = taskType
            }
            EventBus.getDefault().postSticky(startEvent)
            val intent = Intent(context, TaskStepMenuActivity::class.java)
            context.startActivity(intent)
        }
    }

    private var task: BaseTaskEntity = BaseTaskEntity()
    private var taskType: TaskType = TaskType.GENERAL
    private var assetId: String? = null
    private val adapter by lazy { StepMenuAdapter() }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(StepMenuUiState::taskStepList) {
            adapter.setDiffList(it)
        }
        subscribe(StepMenuUiState::refreshDirectedLoadOptionMenu) {
            invalidateOptionsMenu()
        }
    }

    override fun createViewModel(): StepMenuViewModel = StepMenuViewModel(StepMenuDataState(task, taskType))

    override fun initView(savedInstanceState: Bundle?) {
        EventBus.getDefault().register(this@TaskStepMenuActivity)
        binding.apply {
            initToolBar(toolbar, task.id)
            refreshListSrl.setOnRefreshListener(this@TaskStepMenuActivity)
            val emptyView = LayoutInflater.from(this@TaskStepMenuActivity).inflate(R.layout.layout_empty_list, null)
            adapter.emptyView = emptyView
            adapter.bindToRecyclerView(taskStepRv)
            adapter.setOnItemClickListener { adapter, _, position ->
                if (position < 0) return@setOnItemClickListener
                val item = adapter.data[position]
                navigateToStepMenu(item as BaseTaskStepEntity)
                EventBus.getDefault().post(
                    SendDataModel(
                        actionType = ActionType.CLICK, actionKey = DCActionConstants.TASK_STEP, value = item.stepType?.name))
            }
        }
        refreshEndEvent()
        showCloseTaskDialog()
        showForceCloseTaskDialog()
        onEvent<TaskStepUiEvent.FinishActivity> {
            finish()
        }
        onEvent<TaskStepUiEvent.RefreshMenu> {
            invalidateOptionsMenu()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEvent(event: StepMenuStartEvent) {
        task = event.task
        taskType = event.taskType
        assetId = event.task.assetId
    }

    override fun onResume() {
        super.onResume()
        viewModel.loadTaskStep(task.id?: "", taskType)
    }

    override fun onRefresh() {
        viewModel.loadTaskStep(task.id?: "", taskType)
    }

    private fun refreshEndEvent() = onEvent<TaskStepUiEvent.RefreshEnd> {
        binding.refreshListSrl.isRefreshing = false
    }

    private fun showCloseTaskDialog() = onEvent<TaskStepUiEvent.ShowCloseTaskDialog> {
        CenterDialog.confirm(this@TaskStepMenuActivity, message = getString(R.string.msg_all_step_confirm_close_task), positiveClick = {
            if (viewModel.dataState.taskType == TaskType.RECEIVE) {
                collectWorkerTotalHoursAfterCloseTask()
            } else {
                viewModel.closeTask()
            }
        }).show()
    }

    private fun showForceCloseTaskDialog() = onEvent<TaskStepUiEvent.ShowForceCloseTaskDialog> {
        CenterDialog.confirm(this@TaskStepMenuActivity, message = msg, positiveClick = {
            viewModel.forceCloseTask(msg)
        }).show()
    }

    private fun navigateToStepMenu(step: BaseTaskStepEntity) {
        if (step.taskId == null) return
        when (viewModel.dataState.taskType) {
            TaskType.PICK -> {
                when (step.stepType) {
                    TaskStepType.PICK -> {
                        PickStepActivity.startActivity(this@TaskStepMenuActivity, step.taskId.toString(), assetId)
                    }

                    TaskStepType.STAGE -> {
                        StageStepActivity.startActivity(this@TaskStepMenuActivity, step.id.toString())
                    }
                    TaskStepType.ORDER_PICK_FROM_WALL -> {
                        val intent = Intent()
                        val clazz = OrderPickStepActivity::class.java
                        intent.setClass(this, clazz)
                        val param = OrderPickStepActivity.Param(task = viewModel.getTask() as PickTaskEntity, step = step)
                        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                        startActivity(intent)
                    }
                    TaskStepType.STAGE_TO_WALL -> {
                        val intent = Intent()
                        val clazz = StageToWallStepActivity::class.java
                        intent.setClass(this, clazz)
                        val param = StageToWallStepActivity.Param(task = viewModel.getTask() as PickTaskEntity, step = step)
                        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                        startActivity(intent)
                    }
                    TaskStepType.SORTING_TO_WALL-> {
                        val event = PickTaskStepStartEvent().apply {
                            this.stepEntity = step
                            this.taskEntity = viewModel.getTask() as PickTaskEntity
                        }
                        EventBus.getDefault().postSticky(event)
                        SortingToWallActivity.startActivity(this@TaskStepMenuActivity)
                    }
                    TaskStepType.LOADING -> {
                        val isAssignedToMe = step.isAssigned(idmUserId)
                        if (isAssignedToMe) {
                            viewModel.showLoadTaskInfo(step.taskId?:"",::showLoadTaskDescribeDialog)
                        } else {
                           viewModel.taskTakeOver(step.taskId?:"", TaskType.LOAD,::showTaskTakeOverDialog)
                        }
                    }
                    else -> {}
                }
            }

            TaskType.RECEIVE -> {
                startReceiveStepActivity(step)
            }
            TaskType.TRANSLOAD_RECEIVE -> {
                startTransLoadReceivingStepActivity()
            }
            TaskType.TRANSLOAD_LOAD -> {
                startTransLoadLoadingStepActivity()
            }

            TaskType.GENERAL -> {
                startGeneralStepActivity(step)
            }

            TaskType.PUT_BACK -> {
                startPutBackStepActivity(step)
            }
            TaskType.LOAD -> {
                LoadTaskActivity.startActivity(this@TaskStepMenuActivity, step.taskId!!, assetId)
            }
            TaskType.ASSEMBLY -> {
                startAssemblyStepActivity(step)
            }
            TaskType.QC -> {
                startOutboundQcStepActivity(step)
            }
            else -> {}
        }
    }

    private fun startReceiveStepActivity(step: BaseTaskStepEntity) {
        when (step.stepType) {
            TaskStepType.DOCK_CHECK_IN -> {
                startDockCheckIn(task)
            }
            TaskStepType.OFFLOAD -> {
                startOffload()
            }
            TaskStepType.LP_SETUP -> {
                startLpSetup()
            }
            TaskStepType.STAGE -> {
                startReceiveStage(task)
            }
            TaskStepType.SN_SCAN -> {
                startSnScan()
            }
            TaskStepType.MATERIAL_RECEIVING -> {
                startMaterialReceive()
            }
            else -> {}
        }
    }

    private fun startMaterialReceive() {
        val startEvent = ReceiveTaskStartEvent().apply {
            taskEntity = viewModel.getTask() as ReceiveTaskEntity
        }
        EventBus.getDefault().postSticky(startEvent)
        MaterialReceiveActivity.startActivity(this)
    }

    private fun startDockCheckIn(task: BaseTaskEntity) {
        val receiveTaskEntity = task as ReceiveTaskEntity
        DockCheckInActivity.startActivityForResult(this@TaskStepMenuActivity, receiveTaskEntity, receiveTaskEntity.entryId,
            receiveTaskEntity.dockId, receiveTaskEntity.dockName, assetId)
    }

    private fun startOffload() {
        val intent = Intent()
        val clazz = OffloadActivity::class.java
        intent.setClass(this, clazz)
        val param = OffloadActivity.Param(receiveTaskEntity = viewModel.getTask() as ReceiveTaskEntity, OffloadInitPage.SELECT_TYPE)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startLpSetup() {
        val intent = Intent()
        val clazz = LpSetupActivity::class.java
        intent.setClass(this, clazz)
        val param = LpSetupActivity.Param(receiveTaskEntity = viewModel.getTask() as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startReceiveStage(task: BaseTaskEntity) {
        val intent = Intent()
        val clazz = ReceiveStageActivity::class.java
        intent.setClass(this, clazz)
        val param = ReceiveStageActivity.Param(receiveTaskEntity = task as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }


    private fun startSnScan() {
        val intent = Intent()
        val clazz = SnScanActivity::class.java
        intent.setClass(this, clazz)
        val param = SnScanActivity.Param(receiveTaskEntity = viewModel.getTask() as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startTransLoadReceivingStepActivity() {
        val intent = Intent()
        val clazz = LpSetupActivity::class.java
        intent.setClass(this, clazz)
        val param = LpSetupActivity.Param(receiveTaskEntity = viewModel.getTask() as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startTransLoadLoadingStepActivity() {
        val intent = Intent()
        val clazz = LpSetupActivity::class.java
        intent.setClass(this, clazz)
        val param = LpSetupActivity.Param(receiveTaskEntity = viewModel.getTask() as ReceiveTaskEntity)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startGeneralStepActivity(step: BaseTaskStepEntity) {
        when (step.stepType) {
            TaskStepType.GENERAL -> {
                val intent = Intent()
                val clazz = GeneralWorkActivity::class.java
                intent.setClass(this, clazz)
                val param = GeneralWorkActivity.Param(task = viewModel.getTask() as GeneralTaskEntity, step = step)
                ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
                startActivity(intent)
            }

            else -> {}
        }
    }


    private fun startPutBackStepActivity(step: BaseTaskStepEntity) {
        val intent = Intent()
        val clazz = EmptyPutBackTaskActivity::class.java
        intent.setClass(this, clazz)
        val param = EmptyPutBackTaskActivity.Param(task = viewModel.getTask() as PutBackTaskEntity, step = step)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun startAssemblyStepActivity(step: BaseTaskStepEntity) {
        // 使用getTaskStep方法获取对应的步骤
        val assemblyPickStep = viewModel.getTask()?.getTaskStep(TaskStepType.ASSEMBLY_PICK)
        val kittingStep = viewModel.getTask()?.getTaskStep(TaskStepType.GENERAL)
        if (step.taskId.isNullOrEmpty()) return
        when (step.stepType) {
            TaskStepType.ASSEMBLY_PICK -> {
                AssemblyTaskActivity.startActivity(this@TaskStepMenuActivity, step.taskId!!, step.stepType!!)
            }
            TaskStepType.GENERAL -> {
                // 检查ASSEMBLY_PICK步骤是否已完成
                if (assemblyPickStep?.isDone() == true) {
                    AssemblyTaskActivity.startActivity(this@TaskStepMenuActivity, step.taskId!!, step.stepType!!)
                } else {
                    CenterDialog.alert(
                        context = this@TaskStepMenuActivity,
                        title = getString(R.string.title_assembly_task),
                        message = getString(R.string.msg_complete_assembly_pick_first),
                        okClick = null
                    ).show()
                }
            }
            TaskStepType.ASSEMBLY_PUTAWAY -> {
                // 检查ASSEMBLY_PICK步骤是否已完成
                if (assemblyPickStep?.isDone() == true && kittingStep?.isDone() == true) {
                    AssemblyTaskActivity.startActivity(this@TaskStepMenuActivity, step.taskId!!, step.stepType!!)
                } else {
                    val message = when {
                        assemblyPickStep?.isDone() != true -> getString(R.string.msg_complete_assembly_pick_first)
                        kittingStep?.isDone() != true -> getString(R.string.msg_complete_assembly_kitting_first)
                        else -> null
                    }
                    message?.let {
                        CenterDialog.alert(
                            context = this@TaskStepMenuActivity,
                            title = getString(R.string.title_assembly_task),
                            message = it,
                            okClick = null
                        ).show()
                    }
                }
            }
            else -> {
            }
        }
    }

    private fun startOutboundQcStepActivity(step: BaseTaskStepEntity) {
        if (step.taskId.isNullOrEmpty()) return
        
        // 如果是Fix步骤，检查Inspection步骤是否完成
        if (step.stepType == TaskStepType.FIX_CONTENT) {
            val inspectionCONTENTStep = viewModel.getTask()?.getTaskStep(TaskStepType.INSPECTION_CONTENT)
            if (inspectionCONTENTStep?.isDone() != true) {
                showSnack(Message.ResourceMessage(R.string.msg_complete_inspection_first), SnackType.ErrorV2())
                return
            }
        }
        
        OutboundQcTaskActivity.startActivity(this@TaskStepMenuActivity, step.taskId, step.stepType!!)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        if (!task.isDone()) {
            menu.clear()
            menuInflater.inflate(R.menu.activity_task_step_menu_action, menu)
            if (viewModel.dataState.allowDirectedLoadOptionMenu == true) {
                if (viewModel.dataState.isTurnOnDirectedLoad != true) {
                    menu.add(getString(R.string.label_turn_on_load))
                } else {
                    menu.add(getString(R.string.label_turn_off_load))
                }
            }
        }
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_close_task) {
            if (viewModel.dataState.taskType == TaskType.RECEIVE) {
                collectWorkerTotalHoursAfterCloseTask()
            } else {
                viewModel.closeTask()
            }
        } else if (item.title == getString(R.string.label_turn_on_load)) {
            viewModel.updatePageByDirectLoad(true)
        } else if (item.title == getString(R.string.label_turn_off_load)) {
            viewModel.updatePageByDirectLoad(false)
        }

        EventBus.getDefault().post(
            SendDataModel(
                actionType = ActionType.CLICK, actionKey = DCActionConstants.RIGHT_TOP_MENU, value = item.title?.toString()))
        return super.onOptionsItemSelected(item)
    }

    private fun collectWorkerTotalHoursAfterCloseTask() {
        viewModel.checkWorkerTotalHours {
            if (it) {
                val dialog =
                    WorkerTotalHoursDialog.newInstance(WorkerTotalHoursDialog.WorkerTotalHoursParam(taskId = viewModel.getTask()?.id!!, customerId = (viewModel.getTask() as ReceiveTaskEntity).customerId))
                dialog.setConfirmListener {
                    viewModel.closeTask()
                }
                dialog.show(supportFragmentManager, "WorkerTotalHoursDialog")
            } else {
                viewModel.closeTask()
            }
        }
    }

    private fun showTaskTakeOverDialog() = CallbackDialog.showSingleInput(
        this@TaskStepMenuActivity,
        title = getString(R.string.title_take_over),
        hint = getString(R.string.send_message_to_owner)
    )

    private fun showLoadTaskDescribeDialog(describeEntity: LoadTaskDescribeEntity) = LoadTaskDescribeDialog.show(
        this@TaskStepMenuActivity,
        describe = describeEntity,
        onConfirm = {
            LoadTaskActivity.startActivity(this@TaskStepMenuActivity, describeEntity.taskId!!, assetId)
        }
    )

    override fun onDestroy() {
        EventBus.getDefault().unregister(this@TaskStepMenuActivity)
        super.onDestroy()
    }
}