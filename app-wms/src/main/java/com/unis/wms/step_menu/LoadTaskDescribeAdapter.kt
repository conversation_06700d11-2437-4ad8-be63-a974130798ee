package com.unis.wms.step_menu

import android.annotation.SuppressLint
import com.linc.platform.utils.ResUtil
import com.unis.platform.common.model.LoadLpEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemLoadDescribeBinding

class LoadTaskDescribeAdapter : BaseBindingQuickAdapter<LoadLpEntity, ItemLoadDescribeBinding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseBindingViewHolder<ItemLoadDescribeBinding>?, item: LoadLpEntity?) {
        helper?.binding?.apply {
            item?.let { data ->
                orderTv.text = "${ResUtil.getString(R.string.title_order)} ${data.orderId ?: ""}"
                unloadLpTv.text = "${ResUtil.getString(R.string.text_unload_lp)}: ${data.lpIds?.joinToString(", ")?:""}"
            }
        }
    }
} 