package com.unis.wms.tasklist

import com.customer.widget.common.addToNewList
import com.linc.platform.toolset.lpputaway.api.PutAwayTaskApi
import com.unis.platform.asset.api.AssetApi
import com.unis.platform.asset.model.AssetCheckInEntity
import com.unis.platform.asset.model.AssetQueryEntity
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.BaseTaskEntity
import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.receive.api.ReceiveTaskApi
import com.unis.platform.receive.model.ReceiveTaskUpdateEntity
import com.unis.platform.replenishment.model.ReplenishmentTaskEntity
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.wms.receive.ReceiveTaskRepository
import com.unis.wms.tasklist.model.TaskListDataState
import com.unis.wms.tasklist.model.TaskListUiEvent
import com.unis.wms.tasklist.model.TaskListUiState
import com.unis.platform.pick_v2.model.PickTaskUpdateEntity
import com.unis.platform.load.model.LoadTaskUpdateEntity
import com.unis.platform.movement.api.MovementTaskApi
import com.unis.platform.movement.model.MovementTaskUpdateEntity
import com.unis.platform.put_away.PutAwayApiService
import com.unis.platform.put_away.model.PutAwayTaskUpdateEntity
import com.unis.platform.replenishment.api.ReplenishmentTaskApi
import com.unis.platform.replenishment.model.ReplenishmentTaskUpdateEntity
import com.unis.wms.load.LoadTaskRepository
import com.unis.wms.pick_task.PickTaskRepository

abstract class BaseTaskListViewModel<T : BaseTaskEntity>(
    initialDataState: TaskListDataState<T> = TaskListDataState(),
    initialUiState: TaskListUiState<T> = TaskListUiState()
) : ReactiveViewModel<TaskListDataState<T>, TaskListUiState<T>>(initialDataState, initialUiState) {

    private val baseTaskListRepository = BaseTaskListRepository()
    private val receiveTaskRepository = ReceiveTaskRepository()
    private val loadTaskRepository = LoadTaskRepository()
    private val pickTaskRepository = PickTaskRepository()

    protected var curNewPageNo = 1
    protected var curProgressPageNo = 1
    protected var curDonePageNo = 1

    private lateinit var activityViewModel: TaskListParentViewModel
    private var newTaskTotalPage: Int = 0
    private var progressTaskTotalPage: Int = 0
    private var closeTaskTotalPage: Int = 0


    init {
        mapDataToUi()
    }

    fun initData(activityViewModel: TaskListParentViewModel) {
        this.activityViewModel = activityViewModel
    }

    private fun mapDataToUi() {
        mapDataToUi(TaskListDataState<T>::newTaskList, TaskListUiState<T>::newTaskList) { it }
        mapDataToUi(TaskListDataState<T>::progressTaskList, TaskListUiState<T>::progressTaskList) { it }
        mapDataToUi(TaskListDataState<T>::closeTaskList, TaskListUiState<T>::closeTaskList) { it }
    }

    fun loadNewTaskList(isLoadMore: Boolean) {
        if (isLastPaging(TaskStatus.NEW) && isLoadMore) {
            fireEvent { TaskListUiEvent.LoadMoreEnd }
            return
        }
        launch {
            if (isLoadMore) {
                curNewPageNo++
            } else {
                curNewPageNo = 1
            }
            val result = getNewTaskList()
            if (result == null) {
                fireEvent { TaskListUiEvent.RefreshEnd }
                return@launch
            }
            var claimedAssetId: String? = dataState.assetId
            if (isNeedShowForklift() && !dataState.hasLoadBindAsset) {
                val assetSearchResponse = requestAwait(baseTaskListRepository.searchAsset()).getOrNull()
                assetSearchResponse?.let {
                    if (it.isNotEmpty()) {
                        val claimedAsset = it[0]
                        claimedAssetId = claimedAsset.assetId
                        setDataState { copy(assetId = claimedAssetId) }
                    }
                }
            }
            newTaskTotalPage = result.totalPage
            
            // Set assetId to tasks
            val tasksWithAsset = result.list.map { task ->
                task.apply { assetId = claimedAssetId }
            }
            
            if (isLoadMore) {
                val resultList = dataState.newTaskList?.addToNewList(tasksWithAsset)
                setDataState { copy(newTaskList = resultList) }
                activityViewModel.setNewCount(result.totalCount)
                fireEvent { TaskListUiEvent.LoadMoreComplete }
            } else {
                setDataState { copy(newTaskList = tasksWithAsset) }
                activityViewModel.setNewCount(result.totalCount)
                fireEvent { TaskListUiEvent.RefreshEnd }
            }
        }
    }

    fun loadProgressTaskList(isLoadMore: Boolean) {
        if (isLastPaging(TaskStatus.IN_PROGRESS) && isLoadMore) {
            fireEvent { TaskListUiEvent.LoadMoreEnd }
            return
        }
        launch {
            if (isLoadMore) {
                curProgressPageNo++
            } else {
                curProgressPageNo = 1
            }
            val result = getProgressTaskList()
            if (result == null) {
                fireEvent { TaskListUiEvent.RefreshEnd }
                return@launch
            }
            var claimedAssetId: String? = dataState.assetId
            if (isNeedShowForklift() && !dataState.hasLoadBindAsset) {
                val assetSearchResponse = requestAwait(baseTaskListRepository.searchAsset()).getOrNull()
                assetSearchResponse?.let {
                    if (it.isNotEmpty()) {
                        val claimedAsset = it[0]
                        claimedAssetId = claimedAsset.assetId
                        setDataState { copy(assetId = claimedAssetId) }
                    }
                }
            }
            progressTaskTotalPage = result.totalPage
            
            // Set assetId to tasks
            val tasksWithAsset = result.list.map { task ->
                task.apply { assetId = claimedAssetId }
            }
            
            if (isLoadMore) {
                val resultList = dataState.progressTaskList?.addToNewList(tasksWithAsset)
                setDataState { copy(progressTaskList = resultList) }
                activityViewModel.setProgressCount(result.totalCount)
                fireEvent { TaskListUiEvent.LoadMoreComplete }
            } else {
                setDataState { copy(progressTaskList = tasksWithAsset) }
                activityViewModel.setProgressCount(result.totalCount)
                fireEvent { TaskListUiEvent.RefreshEnd }
            }
        }
    }

    fun loadDoneTaskList(isLoadMore: Boolean) {
        if (isLastPaging(TaskStatus.CLOSED) && isLoadMore) {
            fireEvent { TaskListUiEvent.LoadMoreEnd }
            return
        }
        launch {
            if (isLoadMore) {
                curDonePageNo++
            } else {
                curDonePageNo = 1
            }
            val result = getDoneTaskList()
            if (result == null) {
                fireEvent { TaskListUiEvent.RefreshEnd }
                return@launch
            }
            closeTaskTotalPage = result.totalPage
            if (isLoadMore) {
                val resultList = dataState.closeTaskList?.addToNewList(result.list)
                setDataState { copy(closeTaskList = resultList) }
                activityViewModel.setCloseCount(result.totalCount)
                fireEvent { TaskListUiEvent.LoadMoreComplete }
            } else {
                setDataState { copy(closeTaskList = result.list) }
                activityViewModel.setCloseCount(result.totalCount)
                fireEvent { TaskListUiEvent.RefreshEnd }
            }
        }
    }

    private fun isLastPaging(status: TaskStatus): Boolean {
        return when (status) {
            TaskStatus.NEW -> {
                curNewPageNo >= newTaskTotalPage
            }

            TaskStatus.IN_PROGRESS -> {
                curProgressPageNo >= progressTaskTotalPage
            }

            TaskStatus.CLOSED, TaskStatus.FORCE_CLOSED -> {
                curDonePageNo >= closeTaskTotalPage
            }

            else -> {
                false
            }
        }
    }

    fun removeAsset(status: TaskStatus) {
        launch {
            val assetId = dataState.assetId ?: return@launch
            requestAwait(baseTaskListRepository.checkInAsset(assetId)).onSuccess {
                clearClaimForkliftByTaskStatus(status)

                // Notify adapter of data changes
                fireEvent { TaskListUiEvent.NotifyDataChanged(assetId = "") }
            }
        }
    }

    fun saveClaimForklift(assetId: String, status: TaskStatus) {

        saveClaimForkliftByTaskStatus(assetId, status)
        if (status == TaskStatus.IN_PROGRESS) {
            val taskIds = dataState.progressTaskList?.mapNotNull { it.id }
            if (!taskIds.isNullOrEmpty()) {
                updateTasksForkliftId(assetId, taskIds)
            }
        }

        // Notify adapter of data changes
        fireEvent { TaskListUiEvent.NotifyDataChanged(assetId = assetId) }
    }

    fun saveClaimForkliftByTaskStatus(assetId: String, status: TaskStatus) {
        // Update data state
        setDataState {
            copy(
                assetId = assetId,
                hasLoadBindAsset = true
            )
        }
        when (status) {
            TaskStatus.NEW -> {
                dataState.newTaskList?.let { list ->
                    val updatedList = list.map { task ->
                        task.apply { this.assetId = assetId }
                    }
                    setDataState { copy(newTaskList = updatedList) }
                }
            }

            TaskStatus.IN_PROGRESS -> {
                dataState.progressTaskList?.let { list ->
                    val updatedList = list.map { task ->
                        task.apply { this.assetId = assetId }
                    }
                    setDataState { copy(progressTaskList = updatedList) }
                }
            }

            else -> {

            }
        }
    }

    fun clearClaimForkliftByTaskStatus(status: TaskStatus) {
        // Update data state
        setDataState {
            copy(
                assetId = null,
                hasLoadBindAsset = false
            )
        }
        when (status) {
            TaskStatus.NEW -> {
                dataState.newTaskList?.let { list ->
                    val updatedList = list.map { task ->
                        task.apply { this.assetId = null }
                    }
                    setDataState { copy(newTaskList = updatedList) }
                }
            }

            TaskStatus.IN_PROGRESS -> {
                dataState.progressTaskList?.let { list ->
                    val updatedList = list.map { task ->
                        task.apply { this.assetId = null }
                    }
                    setDataState { copy(progressTaskList = updatedList) }
                }
            }

            else -> {

            }
        }
    }

    abstract suspend fun getNewTaskList(): PageResponseEntity<T>?

    abstract suspend fun getProgressTaskList(): PageResponseEntity<T>?

    abstract suspend fun getDoneTaskList(): PageResponseEntity<T>?

    fun filterTask(status: TaskStatus, predicate: (T) -> Boolean): List<T>? {
        return when (status) {
            TaskStatus.NEW -> dataState.newTaskList?.filter { predicate.invoke(it) }
            TaskStatus.IN_PROGRESS -> dataState.progressTaskList?.filter { predicate.invoke(it) }
            TaskStatus.CLOSED -> dataState.closeTaskList?.filter { predicate.invoke(it) }
            else -> listOf()
        }
    }

    open fun isNeedShowForklift() = false

    private fun updateTasksForkliftId(assetId: String, taskIds: List<String>) {
        return when (getTaskType()) {
            TaskType.RECEIVE -> batchUpdateReceiveTask(assetId, taskIds)
            TaskType.PICK -> batchUpdatePickTask(assetId, taskIds)
            TaskType.LOAD -> batchUpdateLoadTask(assetId, taskIds)
            TaskType.PUT_AWAY -> batchUpdatePutAwayTask(assetId, taskIds)
            TaskType.MOVEMENT -> batchUpdateMovementTask(assetId, taskIds)
            TaskType.REPLENISH -> batchUpdateReplenishTask(assetId, taskIds)
            else -> {}
        }
    }

    private fun batchUpdateReceiveTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { ReceiveTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(receiveTaskRepository.batchUpdateTask(updateEntities))
        }
    }

    private fun batchUpdatePickTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { PickTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(pickTaskRepository.batchUpdateTask(updateEntities))
        }
    }

    private fun batchUpdateLoadTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { LoadTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(loadTaskRepository.batchUpdateTask(updateEntities))
        }
    }   

    private fun batchUpdatePutAwayTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { PutAwayTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(baseTaskListRepository.batchUpdatePutAwayTask(updateEntities))
        }
    }   

    private fun batchUpdateMovementTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { MovementTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(baseTaskListRepository.batchUpdateMovementTask(updateEntities))
        }
    }   

    private fun batchUpdateReplenishTask(assetId: String, taskIds: List<String>) {
        val updateEntities = taskIds.map { ReplenishmentTaskUpdateEntity(id = it, forkliftId = assetId) }
        launch {
            requestAwait(baseTaskListRepository.batchUpdateReplenishmentTask(updateEntities))
        }
    }

    private fun getTaskType(): TaskType? {
        // Check new tasks first
        if (!dataState.newTaskList.isNullOrEmpty()) {
            val taskSteps = dataState.newTaskList!![0].taskSteps
            if (!taskSteps.isNullOrEmpty()) {
                return taskSteps[0].taskType
            }
        }
        
        // Then check in progress tasks
        if (!dataState.progressTaskList.isNullOrEmpty()) {
            val taskSteps = dataState.progressTaskList!![0].taskSteps
            if (!taskSteps.isNullOrEmpty()) {
                return taskSteps[0].taskType
            }
        }

        // Finally check done tasks
        if (!dataState.closeTaskList.isNullOrEmpty()) {
            val taskSteps = dataState.closeTaskList!![0].taskSteps
            if (!taskSteps.isNullOrEmpty()) {
                return taskSteps[0].taskType
            }
        }

        return null
    }
}

private class BaseTaskListRepository : BaseRepository() {

    private val assetApi by apiServiceLazy<AssetApi>()
    private val putAwayTaskApi by apiServiceLazy<PutAwayApiService>()
    private val movementTaskApi by apiServiceLazy<MovementTaskApi>()
    private val replenishmentTaskApi by apiServiceLazy<ReplenishmentTaskApi>()

    fun searchAsset() = requestV2({assetApi.searchAsset(
        AssetQueryEntity(assigneeId = idmUserId)
    )})

    fun checkInAsset(assetId: String) = requestV2({assetApi.checkInAsset(assetId, AssetCheckInEntity(checkInReason = "Forklift Return"))})
    
    fun batchUpdatePutAwayTask(tasks: List<PutAwayTaskUpdateEntity>) = requestV2({putAwayTaskApi.batchUpdateTask(tasks)})

    fun batchUpdateMovementTask(tasks: List<MovementTaskUpdateEntity>) = requestV2({movementTaskApi.batchUpdateTask(tasks)})

    fun batchUpdateReplenishmentTask(tasks: List<ReplenishmentTaskUpdateEntity>) = requestV2({replenishmentTaskApi.batchUpdateTask(tasks)})

}