package com.unis.wms.uitl

import android.os.CountDownTimer

class CountDownTimerUtil(
    private val totalTime: Long,
    private val interval: Long = 1000,
    private val onTick: ((Long) -> Unit)? = null,
    private val onFinish: (() -> Unit)? = null
) {
    private var countDownTimer: CountDownTimer? = null
    private var isPaused = false
    private var remainingTime: Long = totalTime

    fun start() {
        if (countDownTimer != null) {
            return
        }
        isPaused = false
        createTimer(remainingTime)
        countDownTimer?.start()
    }

    fun pause() {
        if (countDownTimer != null) {
            isPaused = true
            countDownTimer?.cancel()
            countDownTimer = null
        }
    }

    fun resume() {
        if (!isPaused) {
            return
        }
        start()
    }

    fun stop() {
        isPaused = false
        remainingTime = totalTime
        countDownTimer?.cancel()
        countDownTimer = null
    }

    private fun createTimer(millisInFuture: Long) {
        countDownTimer = object : CountDownTimer(millisInFuture, interval) {
            override fun onTick(millisUntilFinished: Long) {
                remainingTime = millisUntilFinished
                onTick?.invoke(millisUntilFinished)
            }

            override fun onFinish() {
                if (remainingTime <= interval) {
                    remainingTime = 0
                    isPaused = false
                    countDownTimer = null
                    onFinish?.invoke()
                }
            }
        }
    }

    fun getRemainingTime(): Long = remainingTime

    fun isPaused(): Boolean = isPaused

    fun isRunning(): Boolean = countDownTimer != null
}