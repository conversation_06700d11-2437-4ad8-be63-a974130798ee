package com.unis.wms.uitl

import com.google.gson.annotations.SerializedName
import com.linc.platform.http.HttpService
import com.linc.platform.utils.StringUtil
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import java.io.Serializable

class WebSocketClient {

    companion object {

        const val TYPE_TRANSLOAD_LOADING_PALLET = "TRANSLOAD_LOADING_PALLET"
        const val TYPE_OT_CONTROL = "OT_CONTROL"

        private const val TAG = "WebSocketClient"
        private const val SUBSCRIBE_MESSAGE_EVENT = "subscribe-message"
        private const val PATH = "/socket.io/"

        @JvmStatic
        fun newInstance(type: String) = WebSocketClient().apply {
            this.type = type
        }
    }

    private lateinit var config: WebSocketConfig
    private var type: String = "TYPE"
    private var socket: Socket? = null

    private val options by lazy {
        IO.Options().apply {
            reconnection = true
            reconnectionAttempts = 3
            reconnectionDelay = 1000
            transports = arrayOf("websocket")
            path = PATH
        }
    }

    private var severUrl = "wss://stage.logisticsteam.com"

    private var prodSeverUrl = "wss://wise.logisticsteam.com"

    private var messageListener: WebSocketMessageListener? = null

    private val connectEvent: Emitter.Listener = Emitter.Listener {
        socket?.emit(SUBSCRIBE_MESSAGE_EVENT, type, HttpService.getFacilityId(), config.clientId)
        messageListener?.onConnectSuccess()
    }

    private val disConnectEvent: Emitter.Listener = Emitter.Listener {
        messageListener?.onDisConnected()
    }

    private val connectErrorEvent: Emitter.Listener = Emitter.Listener {
        if (it.isNullOrEmpty()) {
            messageListener?.onFail("connect error")
        } else {
            val exception = it[0]
            if (exception is Exception) {
                messageListener?.onFail(exception.message)
                return@Listener
            }
            messageListener?.onFail("connect error")
        }
    }

    private val receiveMessageEvent: Emitter.Listener = Emitter.Listener {
        val jsonStr = it[0].toString()
        messageListener?.onResult(jsonStr)
    }

    private fun createSocket() {
        val severUrl = if (EnvUtil.isProdService()) {
            prodSeverUrl
        } else {
            severUrl
        }
        options.auth = mapOf(
            "token" to "Basic d2lzZWJvdDp1aW9wNzg5MA==",
            "wise-facility-id" to HttpService.getFacilityId(),
            "wise-company-id" to StringUtil.getTenantId())
        socket = IO.socket(severUrl, options)
        socket?.on(Socket.EVENT_CONNECT, connectEvent)
        socket?.on(Socket.EVENT_CONNECT_ERROR, connectErrorEvent)
        socket?.on(Socket.EVENT_DISCONNECT, disConnectEvent)

    }

    fun setServerUrl(url: String) {
        severUrl = url
    }

    fun connect(config: WebSocketConfig, listener: WebSocketMessageListener?) {
        this.config = config
        createSocket()
        socket?.apply {
            messageListener = listener
            on(type, receiveMessageEvent)
            socket = connect()
        }
    }

    private fun reConnect() {
        connect(config, messageListener)
    }

    private fun stopReceiveMessage() {
        socket?.off(type)
    }

    fun resumeReceiveMessage() {
        socket?.on(type, receiveMessageEvent)
    }

    fun disconnect() {
        socket?.disconnect()
        stopReceiveMessage()
        socket = null
    }

    fun isConnected(): Boolean {
        return socket?.connected() ?: false
    }

    fun stop() {
        stopReceiveMessage()
    }

    interface WebSocketMessageListener {
        fun onResult(result: String)
        fun onConnectSuccess()
        fun onFail(message: String?)

        fun onDisConnected()
    }

}

data class WebSocketConfig(
    val clientId: String,
)

data class WebSocketResponseEntity<T>(
    @SerializedName("companyId")
    val companyId: String,
    @SerializedName("facilityId")
    val facilityId: String,
    @SerializedName("method")
    val method: String,
    @SerializedName("data")
    val data: T,
    @SerializedName("clientId")
    val clientId: String,
    @SerializedName("type")
    val type: String
): Serializable


