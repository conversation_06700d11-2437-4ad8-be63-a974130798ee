package com.unis.wms.outbound_qc_task.inspection.adapter

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.text.Editable
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import com.customer.widget.AfterTextWatcher
import com.customer.widget.extensions.setVisibleOrGone
import com.unis.platform.outbound_qc_task.model.ContentResultType
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemOutboundQcInspectionBinding
import com.unis.wms.photo.UploadPhotoBean
import com.unis.wms.photo.UploadPhotoByAdapterCallBack
import com.unis.wms.photo.initLoadParamInfo

/**
 * 质检项目列表适配器
 */
class QcInspectionItemAdapter(
    private val updateComment: (position: Int, String) -> Unit = { _, _ -> },
    private val addOrUpdatePhotos: (item: QCInspectionStepContentViewEntity, List<UploadPhotoBean>) -> Unit = { _, _ -> },
    private val deleteCountSheetPhotos: (QCInspectionStepContentViewEntity, List<UploadPhotoBean>, Boolean) -> Unit,
    private val onGoodClicked: (item: QCInspectionStepContentViewEntity) -> Unit = { _ -> },
    private val onBadClicked: (item: QCInspectionStepContentViewEntity) -> Unit = { _ -> },
    private val onExpandAction: (item: QCInspectionStepContentViewEntity, isExpand: Boolean) -> Unit = { _, _ -> }
) : BaseBindingDifferQuickAdapter<QCInspectionStepContentViewEntity, ItemOutboundQcInspectionBinding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseBindingViewHolder<ItemOutboundQcInspectionBinding>?, item: QCInspectionStepContentViewEntity?) {
        item ?: return
        helper?.binding?.apply {
            // 设置项目序号
            tvItemNumber.text = (helper.layoutPosition).toString()

            // 设置检查项名称
            tvItemName.text = item.content ?: ""
            
            // 设置目标值（如果有）
            tvItemTarget.text = item.checkValue?:""
            tvItemTarget.setVisibleOrGone(!item.checkValue.isNullOrEmpty())

            // 设置Pass/Fail按钮状态
            val textColor = ContextCompat.getColor(root.context, R.color.white)
            val passSelectBg = R.drawable.rect_214d36_border_color_bg_light_left_8
            val failSelectBg = R.drawable.rect_4f0000_border_5e5f5f_right_8
            val passSelectTextColor = ContextCompat.getColor(root.context, R.color.dt_color_56c288)
            val failSelectTextColor = ContextCompat.getColor(root.context, R.color.dt_color_ec4a44)
            val passNormalBg = R.drawable.rect_bg_color_dark_border_bg_color_light_left_8
            val failNormalBg = R.drawable.rect_bg_color_light_border_bg_color_dark_right_8
            
            when (item.resultType) {
                ContentResultType.PASS -> {
                    tvGood.setBackgroundResource(passSelectBg)
                    tvGood.setTextColor(passSelectTextColor)
                    tvBad.setBackgroundResource(failNormalBg)
                    tvBad.setTextColor(textColor)
                    ivStatusIcon.setImageResource(R.drawable.ic_check_circle_green_1)
                }
                ContentResultType.FAIL -> {
                    tvGood.setBackgroundResource(passNormalBg)
                    tvGood.setTextColor(textColor)
                    tvBad.setBackgroundResource(failSelectBg)
                    tvBad.setTextColor(failSelectTextColor)
                    ivStatusIcon.setImageResource(R.drawable.ic_error_info)
                }
                else -> {
                    tvGood.setBackgroundResource(passNormalBg)
                    tvGood.setTextColor(textColor)
                    tvBad.setBackgroundResource(failNormalBg)
                    tvBad.setTextColor(textColor)
                    ivStatusIcon.setImageResource(0)
                }
            }

            ivStatusIcon.setVisibleOrGone(!item.isExpanded)
            llStatus.setVisibleOrGone(item.isExpanded)

            // 设置展开/折叠图标的旋转角度
            if (ivExpand.tag == null) {
                ivExpand.tag = item.isExpanded
            } else {
                ivExpand.tag as Boolean
            }
            ivExpand.rotation = if (item.isExpanded) 0f else 180f

            ivExpand.setOnClickListener {
                // 使用Tag来记录当前的展开状态
                val currentExpanded = if (ivExpand.tag == null) {
                    item.isExpanded
                } else {
                    ivExpand.tag as Boolean
                }
                // 创建旋转动画
                createRotateAnimation(ivExpand, currentExpanded)
                // 保存新状态到Tag中
                ivExpand.tag = !currentExpanded
                // 调用展开/折叠回调
                onExpandAction.invoke(item, !item.isExpanded)
            }

            etComment.setText(item.comment)
            etComment.addTextChangedListener(object : AfterTextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    updateComment.invoke(helper.layoutPosition - headerLayoutCount, s.toString())
                }
            })

            // 根据结果显示或隐藏评论区域
            clComment.setVisibleOrGone(item.isShowComment && item.isExpanded)

            // 设置照片上传组件
            uploadPhoto.apply {
                setVideoParam(item.content, null, false)

                initLoadParamInfo()

                // 初始化照片列表
                initPhotoListByAdapter(item.photos, item.content)
                
                setUploadPhotoByAdapterCallBack(object : UploadPhotoByAdapterCallBack {
                    override fun addOrUpdatePhotos(photoBean: List<UploadPhotoBean>, parentItemKey: Any?) {
                        (parentItemKey as? String)?.let { content ->
                            data.find { it?.content == content }?.let { item ->
                                addOrUpdatePhotos.invoke(item, photoBean)
                            }
                        }
                    }

                    override fun deletePhotos(
                        photos: List<UploadPhotoBean>?, isRemoveAll: Boolean, parentItemKey: Any?
                    ) {
                        (parentItemKey as? String)?.let { content ->
                            data.find { it?.content == content }?.let { item ->
                                deleteCountSheetPhotos.invoke(item, photos?: emptyList(), isRemoveAll)
                            }
                        }
                    }
                })
            }
            
            // 添加Pass/Fail按钮点击事件
            tvGood.setOnClickListener {
                onGoodClicked.invoke(item)
            }
            
            tvBad.setOnClickListener {
                onBadClicked.invoke(item)
            }
        }
    }

    /**
     * 创建旋转动画
     * @param view 需要旋转的视图
     * @param isExpanded 当前是否展开
     */
    private fun createRotateAnimation(view: android.view.View, isExpanded: Boolean) {
        val startRotation = if (isExpanded) 180f else 0f
        val endRotation = if (isExpanded) 0f else 180f
        
        // 创建旋转动画
        val rotateAnimator = ObjectAnimator.ofFloat(view, "rotation", startRotation, endRotation)
        rotateAnimator.duration = 300 // 300毫秒完成动画
        rotateAnimator.interpolator = DecelerateInterpolator() // 减速插值器，使动画更自然
        
        // 开始动画
        rotateAnimator.start()
    }

    /**
     * 判断两个项是否相同
     * 通过比较内容来确定是否是同一个项目
     */
    override fun areItemsTheSame(oldItem: QCInspectionStepContentViewEntity, newItem: QCInspectionStepContentViewEntity): Boolean {
        // 通过内容标识来确定是否是同一个质检项
        return oldItem.content == newItem.content
    }

    /**
     * 判断两个项的内容是否相同
     * 详细比较每个属性，确定内容是否有变化
     */
    override fun areContentsTheSame(oldItem: QCInspectionStepContentViewEntity, newItem: QCInspectionStepContentViewEntity): Boolean {
        return oldItem.content == newItem.content &&
               oldItem.resultType == newItem.resultType &&
               oldItem.comment == newItem.comment &&
               oldItem.photos == newItem.photos &&
               oldItem.checkValue == newItem.checkValue
    }
}

data class QCInspectionStepContentViewEntity(
    val content: String? = null,
    val resultType: ContentResultType? = null,
    var comment: String? = null,
    val photos: List<UploadPhotoBean>? = null,
    val checkValue: String? = null,
    val isExpanded: Boolean = true
) {
    val isShowComment: Boolean = resultType == ContentResultType.FAIL
}