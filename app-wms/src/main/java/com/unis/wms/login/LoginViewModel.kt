package com.unis.wms.login

import android.text.TextUtils
import com.customer.widget.common.safeCount
import com.linc.platform.core.LocalPersistence
import com.linc.platform.home.UpdateApi
import com.linc.platform.http.DownloadInterceptor
import com.linc.platform.http.HttpService
import com.linc.platform.http.TokenInfoEntity
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.LoginByScanEmployeeBarCodeEntry
import com.linc.platform.infoclock.InfoClockApi
import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry
import com.linc.platform.infoclock.model.InfoClockFacilityEntry
import com.linc.platform.infoclock.model.LoginByEmployeeIdEntry
import com.linc.platform.infoclock.model.PunchConfirmUserNameReqEntry
import com.linc.platform.infoclock.model.base.BaseInfoClockRequest
import com.linc.platform.publicdata.PublicDataApi
import com.linc.platform.publicdata.model.PublicDataReqEntry
import com.linc.platform.utils.AESUtil
import com.linc.platform.utils.Logger
import com.linc.platform.utils.NetWorkUtil
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.TimeUtil
import com.linc.platform.utils.ToastUtil
import com.linc.platform.utils.UpdateManager
import com.data_collection.DataCollectionManager
import com.unis.platform.db.WMSDBManager
import com.unis.platform.facility_v2.FacilityApiService
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.facility_v2.mode.FacilitySearchEntity
import com.unis.platform.iam.api.IamApi
import com.unis.platform.iam.model.LoginByQrcodeEntity
import com.unis.platform.iam.model.LoginEntity
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.platform.iam.model.UpdateUserEntity
import com.unis.platform.iam.model.UpdateUserProfileEntity
import com.unis.platform.iam.model.UserInfoEntity
import com.unis.platform.iam.model.UserStatusEntity
import com.unis.platform.ot_control.api.OtControlApi
import com.unis.platform.ot_control.models.TeamSettingQueryEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.requestAllAwait
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.wms.R
import com.unis.wms.common.ServerAddressManager
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.observers.DisposableObserver
import io.reactivex.rxjava3.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import java.util.regex.Pattern

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/7/17
 */
class LoginViewModel(
    initialDataState: LoginDataState = LoginDataState(),
    initialUiState: LoginUiState = LoginUiState(),
) : ReactiveViewModel<LoginDataState, LoginUiState>(initialDataState, initialUiState) {

    private val FILE_NAME = "wms-debug.apk"
    private val repository: Repository = Repository()
    private var updateDownloadApi = HttpService.createService(UpdateApi::class.java, DownloadInterceptor(::handlerDownloadProgress))

    private fun handlerDownloadProgress(current: Long, length: Long, isDone: Boolean) {
        launch(context = Dispatchers.Main) {
            fireEvent { LoginEvent.UpdateProgressStep(current, length, isDone) }
        }
    }

    fun login() {
        val userName = getUserName()
        val psw = getPsw()
        if (TextUtils.isEmpty(userName)) {
            showSnack(SnackType.ErrorV1(), R.string.error_invalid_account)
            return
        }
        if (TextUtils.isEmpty(psw)) {
            showSnack(SnackType.ErrorV1(), R.string.error_incorrect_password)
            return
        }
        val loginEntity = LoginEntity().apply {
            this.username = userName
            this.password = psw
        }
        launch {
            requestAwait(repository.loginByPassword(loginEntity)).onSuccess {
                it?.let {
                    parseLoginResult(it, null)
                } ?: showSnack(SnackType.ErrorV1(), R.string.error_incorrect_password_or_account)
            }
        }
    }

    fun switchUserTenant(tenantId: String, loginResultEntity: LoginResultEntity) {
        launch {
            val userId = loginResultEntity.userInfo.userId
            requestAwait(repository.switchUserTenant(userId, tenantId)).onSuccess {
                it?.profile?.let { profile -> loginResultEntity.userInfo.profile = profile }
                fireEvent { LoginEvent.SwitchUserTenantSuccess(tenantId, loginResultEntity) }
            }
        }
    }

    fun loginByQrCode(qrcode: String) {
        LocalPersistence.setEmployeeInfoEntry(ResUtil.getContext(), null)
        val employeeLoginBarcode = getEmployeeIdByQRCode(qrcode)
        if (TextUtils.isEmpty(employeeLoginBarcode)) {
            showSnack(SnackType.ErrorV1(), R.string.toast_login_invalid_qrcode)
            return
        }
        launch {
            val loginByQrCodeEntry = BaseInfoClockRequest(LoginByEmployeeIdEntry(employeeLoginBarcode), 1, 0)
            val result = requestAwait(repository.getUserInfoByEmployeeId(loginByQrCodeEntry), error = {
                showSnack(SnackType.ErrorV1(), it.error)
            })
            if (result.isFailure) return@launch
            result.getOrNull()?.let {
                if ("not found" == it.employeeId || it.IsActive != true) {
                    showSnack(SnackType.ErrorV1(), R.string.toast_login_invalid_employee_id)
                    return@launch
                }
                getFacilityByIPCheckToLogin(it)
            }
        }
    }

    private fun getFacilityByIPCheckToLogin(employeeEntry: InfoClockEmployeeViewEntry) {
        launch {
            val result = requestAwait(repository.getFacilityByIP(NetWorkUtil.getIpAddress(ResUtil.getContext())), error = {
                val errorMessage: String = it.error
                val ipCheckError = if (!TextUtils.isEmpty(errorMessage) && errorMessage.contains("Ip")) errorMessage.replace(
                    "Not found facility ", "") else ""
                setDataState { copy(ipCheckError = ipCheckError) }
                loginTempByEmployeeId(employeeEntry)
            })
            if (result.isFailure) return@launch
            result.getOrNull()?.let {
                if (isTempEmployee(employeeEntry)) {
                    getTempUserLastPunchTimeAndNameToLogin(employeeEntry, it)
                } else {
                    checkWiseFacilityToLogin(employeeEntry, it)
                }
            }
        }
    }

    private fun loginTempByEmployeeId(employeeEntry: InfoClockEmployeeViewEntry) {
        val loginByQrCodeEntry = LoginByQrcodeEntity(qrcode = employeeEntry.employeeId)
        launch {
            val result = requestAwait(repository.loginByQrCode(loginByQrCodeEntry))
            if (result.isFailure) return@launch
            val loginResultEntity = result.getOrNull()
            if (TextUtils.isEmpty(loginResultEntity?.userInfo?.tenantId)) {
                return@launch
            }
            loginResultEntity?.userInfo?.let { userInfoEntity ->
                if (!hasSettingFacility(userInfoEntity)) {
                    showSnack(SnackType.ErrorV1(), R.string.hint_please_assign_facility_for_user)
                    return@launch
                }
                LocalPersistence.setIamEntity(ResUtil.getContext(), loginResultEntity.userInfo) //更新 header
                val tokenInfoEntity = TokenInfoEntity(
                    accessToken = loginResultEntity.accessToken, refreshToken = loginResultEntity.refreshToken, expiresAt = loginResultEntity.expiresAt)
                val facilityEntity = getUserFacilityEntity(userInfoEntity)
                HttpService.updateTokenInfo(tokenInfoEntity)
                HttpService.updateFacilityId(facilityEntity?.id)
                repository.updateFacilityApi()
                searchFacilitiesFromEmployee(employeeEntry, loginResultEntity)
            }
        }
    }


    private fun getTempUserLastPunchTimeAndNameToLogin(
        employeeEntry: InfoClockEmployeeViewEntry, infoClockFacilityEntry: InfoClockFacilityEntry
    ) {
        launch {
            val reqEntry = BaseInfoClockRequest(employeeEntry.employeeId, 0, 0)
            val result = requestAwait(repository.getLastPunchHistory(reqEntry), error = null)
            result.getOrNull()?.let {
                if (TextUtils.isEmpty(it.punchInTime) || System.currentTimeMillis() - TimeUtil.getStringToDate(
                        it.punchInTime, TimeUtil.FORMAT_FULL_T_FORMAT) >= 8 * 60 * 60 * 1000
                ) {
                    employeeEntry.firstName = ""
                    employeeEntry.lastName = ""
                } else {
                    employeeEntry.firstName = it.firstName
                    employeeEntry.lastName = it.lastName
                }
            }
            checkWiseFacilityToLogin(employeeEntry, infoClockFacilityEntry)
        }
    }

    private fun checkWiseFacilityToLogin(
        infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry, infoClockFacilityEntry: InfoClockFacilityEntry
    ) {
        val publicDataReqEntry = PublicDataReqEntry(arrayOf("facilityCodes", "punchConfigs"))
        launch {
            setDataStateAwait { copy(curWiseFacilityCode = null, disableAutoLoginToWISE = false) }
            val loginByQrCodeEntry = LoginByQrcodeEntity(qrcode = infoClockEmployeeViewEntry.employeeId)
            val resultLogin = requestAwait(repository.loginByQrCode(loginByQrCodeEntry))
            if (resultLogin.isFailure) return@launch
            val facilityEntity = getUserFacilityEntity(resultLogin.getOrNull()?.userInfo)
            LocalPersistence.setIamEntity(ResUtil.getContext(), resultLogin.getOrNull()?.userInfo)
            HttpService.updateFacilityId(facilityEntity?.id)
            val result = requestAwait(repository.getWmsPublicData(publicDataReqEntry))
            LocalPersistence.setIamEntity(ResUtil.getContext(), null)
            HttpService.updateFacilityId(null)
            result.getOrNull()?.let {
                if (it.punchConfigs != null && it.punchConfigs!!.disableAutoLoginToWISE != null) {
                    setDataStateAwait { copy(disableAutoLoginToWISE = it.punchConfigs!!.disableAutoLoginToWISE) }
                }
                if (it.facilityCodes.safeCount() > 0 && !TextUtils.isEmpty(infoClockFacilityEntry.facilityCode) && it.facilityCodes!!.contains(
                        infoClockFacilityEntry.facilityCode!!)
                ) {
                    infoClockEmployeeViewEntry.facilityCode = infoClockFacilityEntry.facilityCode
                    infoClockEmployeeViewEntry.facility = infoClockFacilityEntry.facilityName
                    setDataStateAwait { copy(curWiseFacilityCode = infoClockFacilityEntry.facilityCode) }
                }
            }
            LocalPersistence.setEmployeeInfoEntry(ResUtil.getContext(), infoClockEmployeeViewEntry)
            if (dataState.curWiseFacilityCode == null || isTempEmployee(infoClockEmployeeViewEntry) || dataState.disableAutoLoginToWISE == true) {
                fireEvent { LoginEvent.ShowEmployeeInfoConfirmDialog(infoClockEmployeeViewEntry, false) }
            } else {
                employeeInfoConfirm(infoClockEmployeeViewEntry)
            }
        }
    }

    private fun searchFacilitiesFromEmployee(employeeEntry: InfoClockEmployeeViewEntry, loginResultEntry: LoginResultEntity) {
        val searchEntry = FacilitySearchEntity()
        searchEntry.ids = loginResultEntry.userInfo.profile?.facilityIds
        launch {
            val result = requestAwait(repository.searchFacility(searchEntry))
            if (result.isSuccess) {
                val facilityEntryList = result.getOrNull()
                if (facilityEntryList.safeCount() <= 0) {
                    ToastUtil.showToast(R.string.text_facility_not_found)
                    return@launch
                }
                fireEvent { LoginEvent.ShowSelectFacilityDialog(employeeEntry, facilityEntryList!!) }
            }
            LocalPersistence.setIamEntity(ResUtil.getContext(), null) //更新 header
            HttpService.updateTokenInfo(null)
            HttpService.updateFacilityId(null)
        }
    }

    fun onSelectFacility(employeeEntry: InfoClockEmployeeViewEntry?, facilityEntry: FacilityEntity?) {
        val accountingCode = facilityEntry?.accountingCode
        accountingCode ?: return
        launch {
            val result = requestAwait(repository.checkCanPunchByFacilityCode(accountingCode), error = {
                fireEvent { LoginEvent.ShowFacilityCheckByCodeError(getFacilityCheckByCodeError(it.error)) }
            })
            if (result.isFailure) return@launch
            result.getOrNull()?.let {
                if (isTempEmployee(employeeEntry)) {
                    getTempUserLastPunchTimeAndNameToLogin(employeeEntry!!, it)
                } else {
                    checkWiseFacilityToLogin(employeeEntry!!, it)
                }
            }
        }
    }


    fun employeeInfoConfirm(employeeEntry: InfoClockEmployeeViewEntry) {
        LocalPersistence.setEmployeeInfoEntry(ResUtil.getContext(), employeeEntry)
        if (dataState.loginResultEntity != null) {
            updateUserInfo(dataState.loginResultEntity!!, employeeEntry)
            return
        }
        if (dataState.curWiseFacilityCode == null || dataState.disableAutoLoginToWISE == true) {
            jumpTimesheet(employeeEntry)
        } else {
            loginByQrCodeConfirm(employeeEntry)
        }
    }

    private fun loginByQrCodeConfirm(infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry) {
        val loginByQrCodeEntry = LoginByQrcodeEntity(qrcode = infoClockEmployeeViewEntry.employeeId)
        launch {
            val result = requestAwait(repository.timeOutLoginByQrCode(loginByQrCodeEntry))
            if (result.isFailure) {
                showSnack(SnackType.ErrorV1(), result.exceptionOrNull()?.message ?: "")
                jumpTimesheet(infoClockEmployeeViewEntry)
                return@launch
            }
            parseLoginResult(result.getOrNull()!!, infoClockEmployeeViewEntry)
        }
    }


    fun punchConfirmUserName(employeeEntry: InfoClockEmployeeViewEntry) {
        launch {
            val punchConfirmUserNameReqEntry = BaseInfoClockRequest(PunchConfirmUserNameReqEntry(employeeEntry.employeeId), 0, 0)
            requestAwait(repository.punchConfirmUserName(punchConfirmUserNameReqEntry), error = {
                showSnack(SnackType.ErrorV1(), it.error)
            })
            employeeInfoConfirm(employeeEntry)
        }
    }


    private fun updateUserInfo(loginResultEntry: LoginResultEntity, infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry) {
        launch {
            val updateUserEntry = UpdateUserEntity(firstName = infoClockEmployeeViewEntry.firstName, lastName = infoClockEmployeeViewEntry.lastName)
            val result = requestAwait(repository.updateUser(loginResultEntry.userInfo.userId, updateUserEntry))
            if (result.isFailure) {
                jumpTimesheet(infoClockEmployeeViewEntry)
                return@launch
            }
            if (loginResultEntry.userInfo.profile?.wmsUserType == "Temp") {
                val profileEntity = UpdateUserProfileEntity(userId = loginResultEntry.userInfo.userId, confirmed = false)
                val resultProfile = requestAwait(repository.updateUserProfile(profileEntity))
                if (resultProfile.isFailure) {
                    jumpTimesheet(infoClockEmployeeViewEntry)
                    return@launch
                }
            }
            loadUserInfo(loginResultEntry, infoClockEmployeeViewEntry)
        }
    }

    private fun loadUserInfo(loginResultEntry: LoginResultEntity, infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry?) {
        launch {
            val result = requestAwait(repository.getUser(loginResultEntry.userInfo.userId))
            if (result.isFailure) {
                jumpTimesheet(infoClockEmployeeViewEntry)
                return@launch
            }
            val userViewEntry = result.getOrNull()
            if (userViewEntry == null || userViewEntry.userStatus == UserStatusEntity.INACTIVE) {
                showSnack(SnackType.ErrorV1(), R.string.msg_user_disable_please_contact_manager)
                jumpTimesheet(infoClockEmployeeViewEntry)
            } else if (!hasSettingFacility(userViewEntry)) {
                showSnack(SnackType.ErrorV1(), R.string.hint_please_assign_facility_for_user)
                jumpTimesheet(infoClockEmployeeViewEntry)
            } else {
                onLoginSuccess(loginResultEntry)
            }
        }
    }

    fun getUserFacilityEntity(userInfo: UserInfoEntity?): FacilityEntity? {
        userInfo ?: return null
        val localFacility = WMSDBManager.facilityManager.getOneByUserId(userInfo.userId)
        if (localFacility != null) {
            return localFacility
        }
        return userInfo.profile?.defaultFacility ?: userInfo.profile?.facilities?.get(0)
    }

    private fun parseLoginResult(loginResultEntity: LoginResultEntity, infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry?) {
        if (!hasSettingFacility(loginResultEntity.userInfo)) {
            showSnack(SnackType.ErrorV1(), R.string.hint_please_assign_facility_for_user)
            return
        }
        launch {
            val tokenInfoEntity = TokenInfoEntity(
                accessToken = loginResultEntity.accessToken,
                refreshToken = loginResultEntity.refreshToken,
                expiresAt = loginResultEntity.expiresAt
            )
            val facilityEntity = getUserFacilityEntity(loginResultEntity.userInfo)
            HttpService.updateTokenInfo(tokenInfoEntity)
            HttpService.updateFacilityId(facilityEntity?.id)
            if (facilityEntity?.facilitySettingEntity?.allowOTControl == true) {
                val (teamsResult, userPayTypeResult) = requestAllAwait(repository.getTeams(), repository.getUserPayType(loginResultEntity.userInfo.userId))
                val teamSettings = teamsResult.getOrNull()?.list?: listOf()
                loginResultEntity.userInfo.isSupervisor = teamSettings.any { it.supervisorIds?.contains(loginResultEntity.userInfo.userId) ?: false }
                loginResultEntity.userInfo.isOTApprover = teamSettings.any { it.otApproverIds?.contains(loginResultEntity.userInfo.userId) ?: false }
                loginResultEntity.userInfo.isUpperApprover = teamSettings.any { it.upperApproverIds?.contains(loginResultEntity.userInfo.userId) ?: false }
                loginResultEntity.userInfo.payType = userPayTypeResult.getOrNull()?.payType
            }
            LocalPersistence.setIamEntity(ResUtil.getContext(), loginResultEntity.userInfo) //更新 header
            if (infoClockEmployeeViewEntry != null) {
                setDataState { copy(loginResultEntity = loginResultEntity) }
                if (!infoClockEmployeeViewEntry.isTempUser() && (!infoClockEmployeeViewEntry.hasConfirmedLoginName() || loginResultEntity.userInfo.profile?.confirm == true)) {
                    fireEvent { LoginEvent.ShowEmployeeInfoConfirmDialog(infoClockEmployeeViewEntry, true) }
                } else {
                    updateUserInfo(loginResultEntity, infoClockEmployeeViewEntry)
                }
            } else {
                val userInfo = loginResultEntity.userInfo
                if (facilityEntity?.facilitySettingEntity?.allowOTControl == false || userInfo.enableSkipWorkerAssignment()) {
                    onLoginSuccess(loginResultEntity)
                    return@launch
                }
                val employeeId =
                    userInfo.externalInfo?.get("employeeId") ?: return@launch showSnack(SnackType.ErrorV1(), R.string.no_employee_id_tips)
                val loginByQrCodeEntry = BaseInfoClockRequest(LoginByEmployeeIdEntry(employeeId.toString()), 1, 0)
                request(repository.getUserInfoByEmployeeId(loginByQrCodeEntry), success = {
                    LocalPersistence.setEmployeeInfoEntry(ResUtil.getContext(), it)
                    onLoginSuccess(loginResultEntity)
                })
            }
        }
    }



    private fun hasSettingFacility(userInfoEntity: UserInfoEntity): Boolean {
        return userInfoEntity.profile?.let {
            (it.defaultFacility != null) || !it.facilities.isNullOrEmpty()
        } ?: false
    }

    private fun onLoginSuccess(loginResultEntity: LoginResultEntity) {
        fireEvent { LoginEvent.LoginSuccess(loginResultEntity) }
    }

    private fun getUserName() = dataState.userAccount

    private fun getPsw() = dataState.psw

    fun setUserAccount(userAccount: String?) {
        setDataState { copy(userAccount = userAccount) }
        setUiState { copy(loginButtonEnable = !TextUtils.isEmpty(userAccount) && !TextUtils.isEmpty(dataState.psw)) }
        LocalPersistence.saveUserAccount(ResUtil.getContext(), AESUtil.encode(userAccount))
        fireEvent { LoginEvent.LoginButtonStatus(uiState.loginButtonEnable) }
    }

    fun setPassword(psw: String?) {
        setDataState { copy(psw = psw) }
        setUiState { copy(loginButtonEnable = !TextUtils.isEmpty(psw) && !TextUtils.isEmpty(dataState.userAccount)) }
        LocalPersistence.savePassword(ResUtil.getContext(), AESUtil.encode(psw))
        fireEvent { LoginEvent.LoginButtonStatus(uiState.loginButtonEnable) }
    }

    fun setRememberPassword(isRememberPsw: Boolean?) {
        LocalPersistence.saveIsRememberPassword(ResUtil.getContext(), isRememberPsw ?: false)
    }

    fun setServerName(serverName: String?) {
        val serverAddress = ServerAddressManager.getInstance().getServerAddressByName(serverName)
        val infoClockServerAddress = ServerAddressManager.getInstance().getInfoClockServerAddressByName(serverName)
        LocalPersistence.saveServerAddr(ResUtil.getContext(), serverAddress)
        HttpService.updateServerUrl(serverAddress)
        HttpService.setBaseInfoClockUrl(infoClockServerAddress)
        LocalPersistence.saveInfoClockServerAddr(ResUtil.getContext(), infoClockServerAddress)
        val dcServerAddress = ServerAddressManager.getInstance().getDCServerAddress(serverName)
        LocalPersistence.saveDCServerAddr(ResUtil.getContext(), dcServerAddress)
        DataCollectionManager.instance().updateDcDomain(dcServerAddress)
    }

    fun updateApi() {
        repository.updateIamApi()
        repository.updateLoadVersionApi()
        repository.updateOtherApi()
        updateDownloadApi = HttpService.createService(UpdateApi::class.java, DownloadInterceptor(::handlerDownloadProgress))
    }

    fun isNeedUpdate(currentVersionCode: Int): Boolean {
        val versionEntry = dataState.versionEntry
        versionEntry ?: return true
        return currentVersionCode < versionEntry.versionCode
    }

    fun checkUpdate(versionCode: Int) {
        launch {
            requestAwait(repository.loadVersion()).onSuccess {
                setDataState { copy(versionEntry = it) }
                if (it != null && versionCode < it.versionCode) {
                    fireEvent { LoginEvent.ShowVersionUpdateDialog(it) }
                    return@launch
                }
                fireEvent { LoginEvent.EnableCheckUpdate(true) }
            }.onFailure {
                fireEvent { LoginEvent.EnableCheckUpdate(true) }
            }
        }
    }

    fun doUpdate(filePath: String) {
        var name = FILE_NAME
        val versionEntry = dataState.versionEntry
        if (versionEntry != null && !TextUtils.isEmpty(versionEntry.name)) {
            name = versionEntry.name
        }
        launch {
            fireEvent { LoginEvent.UpdateProgressStep(0, 0, false) }
            updateDownloadApi.downloadFile(name).map { responseBody ->
                UpdateManager.writeFileToSD(filePath, responseBody.body())
            }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(object : DisposableObserver<Boolean>() {
                override fun onComplete() {
                }

                override fun onError(e: Throwable) {
                    Logger.handleException(e)
                    fireEvent { LoginEvent.EnableCheckUpdate(true) }
                }

                override fun onNext(aBoolean: Boolean) {
                    if (aBoolean) {
                        fireEvent { LoginEvent.LaunchInstall(filePath) }
                    }
                    fireEvent { LoginEvent.EnableCheckUpdate(true) }
                }
            })
        }
    }

    private fun getEmployeeIdByQRCode(qrCode: String): String {
        val regex = "(?i)EmployeeID=\\[(.+)]"
        val pattern = Pattern.compile(regex)
        val matcher = pattern.matcher(qrCode)
        if (matcher.find()) {
            return matcher.group(1)
        }
        return ""
    }

    private fun isTempEmployee(infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry?): Boolean {
        return infoClockEmployeeViewEntry != null && infoClockEmployeeViewEntry.isTempUser()
    }

    private fun jumpTimesheet(infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry?) {
        if (infoClockEmployeeViewEntry != null) {
            fireEvent { LoginEvent.ToTimesheet }
        }
    }

    private fun getFacilityCheckByCodeError(error: String): String {
        var errorMsg = error
        if (!TextUtils.isEmpty(dataState.ipCheckError)) {
            errorMsg = """
            $errorMsg
            ${dataState.ipCheckError}
            """.trimIndent()
        }
        return errorMsg.replace("InfoClockApi:", "")
    }
}

private class Repository : BaseRepository() {

    private var idmApi = HttpService.createService(IdmApi::class.java)
    private var iamApi = HttpService.createService(IamApi::class.java)
    private var updateApi = HttpService.createService(UpdateApi::class.java)
    private var infoClockApi = HttpService.createInfoClockService(InfoClockApi::class.java, -1)
    private var infoClockApiTimeout = HttpService.createInfoClockService(InfoClockApi::class.java, 5)
    private var iamApiTimeout = HttpService.createService(IamApi::class.java, 5)

    private var publicDataApi = HttpService.createService(PublicDataApi::class.java, 5)
    private var facilityApi = HttpService.createService(FacilityApiService::class.java, listOf())
    private val facilityApiService by apiServiceLazy<FacilityApiService>()
    private val otControlApi by apiServiceLazy<OtControlApi>()

    fun updateIamApi() {
        this.iamApi = HttpService.createService(IamApi::class.java)
    }

    fun updateLoadVersionApi() {
        this.updateApi = HttpService.createService(UpdateApi::class.java)
    }

    fun updateFacilityApi() {
        facilityApi = HttpService.createService(FacilityApiService::class.java, listOf())
    }

    fun updateOtherApi() {
        infoClockApi = HttpService.createInfoClockService(InfoClockApi::class.java, -1)
        infoClockApiTimeout = HttpService.createInfoClockService(InfoClockApi::class.java, 5)
        iamApiTimeout = HttpService.createService(IamApi::class.java, 5)
        publicDataApi = HttpService.createService(PublicDataApi::class.java, 5)
        facilityApi = HttpService.createService(FacilityApiService::class.java)
    }

    fun loginByPassword(loginEntity: LoginEntity) = rxRequest2(iamApi.loginByPassword(loginEntity))

    fun switchUserTenant(userId: String, tenantId: String) = rxRequest2(iamApi.switchUserTenant(userId, tenantId))

    fun loginByScanEmployeeBarCode(loginRequestEntry: LoginByScanEmployeeBarCodeEntry) = rxRequest(idmApi.loginByScanEmployeeBarCode(loginRequestEntry))

    fun getUserInfo(idmUserId: String) = rxRequest(idmApi.getUserInfo(idmUserId))

    fun loadVersion() = rxRequest(updateApi.loadVersion())

    fun getUserInfoByEmployeeId(employee: BaseInfoClockRequest<LoginByEmployeeIdEntry>) = rxRequestInfoClock(infoClockApi.getUserInfoByEmployeeId(employee))

    fun getFacilityByIP(ipAddress: String?) = rxRequestInfoClock(infoClockApi.getFacilityByIP(ipAddress))

    fun getLastPunchHistory(request: BaseInfoClockRequest<String>) = rxRequestInfoClock(infoClockApiTimeout.getLastPunchHistory(request))

    fun getWmsPublicData(request: PublicDataReqEntry) = rxRequest(publicDataApi.getWmsPublicData(request))

    fun punchConfirmUserName(request: BaseInfoClockRequest<PunchConfirmUserNameReqEntry>) = rxRequestInfoClock(infoClockApi.punchConfirmUserName(request))

    fun updateUser(userId: String, request: UpdateUserEntity) = rxRequest2(iamApi.updateIamUser(userId, request))

    fun getUser(userId: String) = rxRequest2(iamApi.getUser(userId))

    fun loginByQrCode(request: LoginByQrcodeEntity) = rxRequest2(iamApi.loginByQRCode(request))

    fun searchFacility(searchEntry: FacilitySearchEntity) = rxRequest2(facilityApi.searchFacility(searchEntry))

    fun checkCanPunchByFacilityCode(facilityCode: String) = rxRequestInfoClock(infoClockApi.checkCanPunchByFacilityCode(facilityCode))

    fun timeOutLoginByQrCode(request: LoginByQrcodeEntity) = rxRequest2(iamApiTimeout.loginByQRCode(request))

    fun updateUserProfile(request: UpdateUserProfileEntity) = rxRequest2(iamApi.updateUserProfile(request))

    fun getFacility(id: String) = rxRequest2(facilityApiService.getFacility(id))

    fun getTeams() = requestV2({otControlApi.searchTeamsByPaging(TeamSettingQueryEntity().apply { this.pageSize = 1000 })})

    fun getUserPayType(userId: String) = requestV2({otControlApi.getUserPayType(userId)})
}