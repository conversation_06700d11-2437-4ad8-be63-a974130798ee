package com.unis.wms.login

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import com.customer.widget.IScanDialog
import com.customer.widget.ScanDialogFactory
import com.customer.widget.VersionUpdateProgressDialog
import com.customer.widget.common.CenterDialog
import com.customer.widget.scanner.decoder.DriverLicense
import com.customer.widget.util.CommUtil
import com.customer.widget.util.GlobalConfig
import com.data_collection.DataCollectionManager
import com.linc.platform.core.LocalPersistence
import com.linc.platform.core.RxBus
import com.linc.platform.http.HttpService
import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry
import com.linc.platform.utils.AESUtil
import com.linc.platform.utils.PermissionUtil
import com.linc.platform.utils.ToastUtil
import com.linc.platform.utils.UpdateManager
import com.unis.platform.common.model.company.CompanyEntity
import com.unis.platform.db.WMSDBManager
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.iam.model.LoginResultEntity
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.wms.BuildConfig
import com.unis.wms.R
import com.unis.wms.common.ServerAddressManager
import com.unis.wms.databinding.ActivityLoginBinding
import com.unis.wms.fcm.RegistrationService
import com.unis.wms.home.ToHomeHandler
import com.unis.wms.home.time_sheet.PunchActivity
import com.unis.wms.ot_control.OtControlActivity
import com.unis.wms.ot_control.OtControlConfigEntity
import com.unis.wms.ot_control.OtControlStep
import com.unis.wms.uitl.EnvUtil

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/7/30
 */
class LoginActivity : ReactiveActivity<LoginViewModel, LoginUiState, ActivityLoginBinding>() {

    companion object {
        const val USER_ACCOUNT_WATCHER = 0
        const val PSW_WATCHER = 1
    }

    private var enableCheckUpdate: Boolean = true
    private val versionUpdateProgressDialog by lazy { VersionUpdateProgressDialog(this) }

    override fun createViewModel(): LoginViewModel = LoginViewModel()

    override fun initView(savedInstanceState: Bundle?) {
        PermissionUtil.requestAllPermission(this)
        binding.apply {
            edtUserAccount.addTextChangedListener(LoginTextWatcher(USER_ACCOUNT_WATCHER))
            edtPsw.addTextChangedListener(LoginTextWatcher(PSW_WATCHER))
            llServerName.setOnClickListener {
                removeUserAccountOrPswFocus()
                updateServerInfoDialog()
            }
            cbRememberPassword.setOnCheckedChangeListener { _, isChecked ->
                removeUserAccountOrPswFocus()
                viewModel.setRememberPassword(isChecked)
            }
            showAccountAndPswInfo()
            cbShowPassword.setOnClickListener {
                showOrHidePassword()
            }
            btnLogin.setOnClickListener {
                removeUserAccountOrPswFocus()
                login()
            }
            btnLoginByScanCode.setOnClickListener {
                removeUserAccountOrPswFocus()
                showScanBarcodeDialog()
            }
            llLoginContainer.setOnClickListener {
                removeUserAccountOrPswFocus()
            }
            showServerName(ServerAddressManager.getInstance().getAddressName(LocalPersistence.getServerAddr(appContext)))
            HttpService.updateCompanyIds("") //set company id empty when login
            HttpService.updateFacilityId("") //set facility id empty when login
            HttpService.updateFacility(null)
        }
        onUserAlreadyLoggedInAnotherDeviceDialogEvent()
        onLoginSuccessEvent()
        onSwitchUserTenantSuccessEvent()
        onShowVersionUpdateDialogEvent()
        onLaunchInstallEvent()
        onEnableCheckUpdateEvent()
        onUpdateProgressStepEvent()
        onShowEmployeeInfoConfirmDialogEvent()
        onToTimeSheetEvent()
        onShowSelectFacilityDialogEvent()
        DataCollectionManager.instance().updateDeviceInfo()
    }

    override fun onResume() {
        RxBus.reset()
        super.onResume()
        if (!BuildConfig.DEBUG && EnvUtil.isSameEnvAndService() && enableCheckUpdate) {
            enableCheckUpdate = false
            viewModel.checkUpdate(BuildConfig.VERSION_CODE)
            HttpService.removeChromeHttpTracker()
        }
        setVersionName()
        registrationId(false)
        ToHomeHandler.getHandler().clearLoginData(appContext, idmUserId)
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        updateLoginButtonStatus()
    }

    private fun ReactiveViewScope.updateLoginButtonStatus() {
        subscribe(LoginUiState::loginButtonEnable) {
            binding.apply {
                btnLogin.isEnabled = it
            }
        }
    }

    private fun updateServerInfoDialog() {
        val serverNameList = ServerAddressManager.getInstance().serverNameList
        val defaultServerName = ServerAddressManager.getInstance().getAddressName(HttpService.getBaseUrl())
        CenterDialog.singleChoiceGrid(this,
            title = getString(R.string.title_dialog_serveraddr),
            items = serverNameList,
            defaultChose = defaultServerName,
            itemTitleMapper = { it },
            positiveClick = {
                updateServerInfo(it)
            }).show()
    }

    private fun updateServerInfo(serverName: String?) {
        viewModel.setServerName(serverName)
        viewModel.updateApi()
        showServerName(serverName)
        showAccountAndPswInfo()
    }

    private fun showAccountAndPswInfo() {
        binding.apply {
            cbRememberPassword.isChecked = LocalPersistence.getIsRememberPassword(appContext)
            if (cbRememberPassword.isChecked) {
                val userAccount = AESUtil.decode(LocalPersistence.getUserAccount(appContext))
                val password = AESUtil.decode(LocalPersistence.getPassword(appContext))
                edtUserAccount.setText(userAccount)
                edtPsw.setText(password)
                viewModel.setUserAccount(userAccount)
                viewModel.setPassword(password)
            } else {
                edtUserAccount.setText("")
                edtPsw.setText("")
            }
        }
    }

    private fun showServerName(serverName: String?) {
        binding.apply { tvServerName.text = serverName }
    }

    private fun setVersionName() {
        var versionName = String.format(getString(R.string.title_system_version), BuildConfig.VERSION_NAME)
        if (BuildConfig.DEBUG) {
            versionName += "-debug-version"
        }
        binding.apply {
            tvVersion.text = versionName
        }
    }

    private fun showOrHidePassword() {
        binding.apply {
            if (cbShowPassword.isChecked) {
                edtPsw.setTransformationMethod(HideReturnsTransformationMethod.getInstance())
            } else {
                edtPsw.setTransformationMethod(PasswordTransformationMethod.getInstance())
            }
            edtPsw.setSelection(edtPsw.getText().length)
        }
    }

    private fun login() {
//        if (!BuildConfig.DEBUG && EnvUtil.isSameEnvAndService() && viewModel.isNeedUpdate(BuildConfig.VERSION_CODE)) {
//            viewModel.checkUpdate(BuildConfig.VERSION_CODE)
//            return
//        }
        viewModel.login()
    }

    private fun showScanBarcodeDialog() {
        val scanDialog = ScanDialogFactory.createScanDialog(this)
        scanDialog.setSquareViewFinder(true)
        scanDialog.setOnBarcodeResult(object : IScanDialog.OnBarcodeResult {
            override fun onBarcode(barcode: String) {
//                if (!BuildConfig.DEBUG && EnvUtil.isSameEnvAndService() && viewModel.isNeedUpdate(BuildConfig.VERSION_CODE)) {
//                    viewModel.checkUpdate(BuildConfig.VERSION_CODE)
//                    return
//                }
                viewModel.loginByQrCode(barcode)
            }

            override fun onDriverLicense(driverLicense: DriverLicense) {}
            override fun onDismiss() {}
        })
        scanDialog.show(supportFragmentManager, IScanDialog::class.java.simpleName)
    }

    private fun registrationId(isRegister: Boolean) {
        val intent = Intent(this, RegistrationService::class.java)
        intent.putExtra(RegistrationService.ACTION_IS_REGISTER, isRegister)
        startService(intent)
    }

    private fun removeUserAccountOrPswFocus() {
        binding.apply {
            if (edtUserAccount.isInputFocused()) {
                edtUserAccount.removeFocus()
                CommUtil.hideKeyBoard(edtUserAccount.getInputTextView())
            }
            if (edtPsw.isInputFocused()) {
                edtPsw.removeFocus()
                CommUtil.hideKeyBoard(edtPsw.getInputTextView())
            }
        }
    }

    inner class LoginTextWatcher(private val who: Int) : TextWatcher {

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            when (who) {
                USER_ACCOUNT_WATCHER -> viewModel.setUserAccount(s.toString().trim())
                PSW_WATCHER -> viewModel.setPassword(s.toString().trim())
            }
        }

    }

    private fun onUserAlreadyLoggedInAnotherDeviceDialogEvent() = onEvent<LoginEvent.UserAlreadyLoggedInAnotherDeviceDialog> {
        CenterDialog.alert(context = this@LoginActivity, message = errMsg, okClick = {
            viewModel.login()
        }).show()
    }

    private fun onLoginSuccessEvent() = onEvent<LoginEvent.LoginSuccess> {
        val tenantIds = loginResultEntity.userInfo.tenantIds
        if (!tenantIds.isNullOrEmpty() && tenantIds.size > 1) {
            showMultiTenantDialog(tenantIds, loginResultEntity)
            return@onEvent
        }
        handleLoginResult(loginResultEntity)
    }

    private fun onSwitchUserTenantSuccessEvent() = onEvent<LoginEvent.SwitchUserTenantSuccess> {
        loginResultEntity.userInfo.selectedTenantId = tenantId
        handleLoginResult(loginResultEntity)
    }

    private fun handleLoginResult(loginResultEntity: LoginResultEntity) {
        if (viewModel.getUserFacilityEntity(loginResultEntity.userInfo)?.facilitySettingEntity?.allowOTControl == false
            || loginResultEntity.userInfo.enableSkipWorkerAssignment()) {
            ToHomeHandler.getHandler().toHome(this, loginResultEntity) {
                finish()
            }
        } else {
            OtControlActivity.startActivity(this@LoginActivity, OtControlConfigEntity(otControlStep = OtControlStep.WorkerAssignment(loginResultEntity)))
            finish()
        }
    }

    private fun showMultiTenantDialog(tenantIds: List<String>, loginResultEntity: LoginResultEntity) {
        CenterDialog.singleChoiceList(
            context = this@LoginActivity,
            title = getString(R.string.title_select_tenant),
            items = tenantIds,
            itemTitleMapper = {
                it
            },
            defaultChose = tenantIds[0],
            positiveClick = {
                viewModel.switchUserTenant(it!!, loginResultEntity)
            }
        ).show()
    }

    private fun onShowVersionUpdateDialogEvent() = onEvent<LoginEvent.ShowVersionUpdateDialog> {
        CenterDialog.alert(context = this@LoginActivity,
            title = getString(R.string.title_alarm),
            message = getString(R.string.text_update),
            cancelable = false,
            canceledOnTouchOutside = false,
            okClick = {
                viewModel.doUpdate(GlobalConfig.getUpdateFilePath(this@LoginActivity))
            }).show()
    }

    private fun onEnableCheckUpdateEvent() = onEvent<LoginEvent.EnableCheckUpdate> {
        <EMAIL> = enableCheckUpdate
    }

    private fun onLaunchInstallEvent() = onEvent<LoginEvent.LaunchInstall> {
        UpdateManager.installApkForResult(this@LoginActivity, filePath)
    }

    private fun onUpdateProgressStepEvent() = onEvent<LoginEvent.UpdateProgressStep> {
        if (!isFinishing) {
            runOnUiThread {
                if (!versionUpdateProgressDialog.isShowing()) {
                    versionUpdateProgressDialog.show(current, length)
                } else {
                    versionUpdateProgressDialog.updateProgress(current, length)
                }

                if (isDone && versionUpdateProgressDialog.isShowing()) {
                    versionUpdateProgressDialog.dismiss()
                }
            }
        }
    }

    private fun onShowEmployeeInfoConfirmDialogEvent() = onEvent<LoginEvent.ShowEmployeeInfoConfirmDialog> {
        showEmployeeInfoConfirmDialog(infoClockEmployeeViewEntry, isOnlyConfirmOnce)
    }

    private fun onToTimeSheetEvent() = onEvent<LoginEvent.ToTimesheet> {
        registrationId(true)
        startActivityForResult(Intent(this@LoginActivity, PunchActivity::class.java), PunchActivity.RESULT_CODE)
    }

    private fun onShowSelectFacilityDialogEvent() = onEvent<LoginEvent.ShowSelectFacilityDialog> {
        selectFacilityByEmployee(infoClockEmployeeViewEntry, facilities)
    }

    private fun showEmployeeInfoConfirmDialog(infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry?, isOnlyConfirmOnce: Boolean) {
        val infoConfirmDialog = LoginEmployeeInfoConfirmDialog()
        infoConfirmDialog.setEmployeeResultEntry(infoClockEmployeeViewEntry)
        infoConfirmDialog.setInfoConfirmListener { userInfo: InfoClockEmployeeViewEntry ->
            if (isOnlyConfirmOnce) {
                viewModel.punchConfirmUserName(userInfo)
            } else {
                viewModel.employeeInfoConfirm(userInfo)
            }
        }
        infoConfirmDialog.show(supportFragmentManager, LoginEmployeeInfoConfirmDialog::class.java.simpleName)
    }

    private fun selectFacilityByEmployee(employeeEntry: InfoClockEmployeeViewEntry, facilityEntries: List<FacilityEntity>) {
        CenterDialog.singleChoiceList(this,
            items = facilityEntries,
            itemTitleMapper = { it.name },
            positiveClick = { viewModel.onSelectFacility(employeeEntry, it) }).show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UpdateManager.REQUEST_CODE_PACKAGE_INSTALL) {
            UpdateManager.installApk(this@LoginActivity, GlobalConfig.getUpdateFilePath(this@LoginActivity))
        }

        if (requestCode == PunchActivity.RESULT_CODE && resultCode == PunchActivity.RESULT_CODE) {
            val loginResultEntry = data!!.getSerializableExtra(PunchActivity.KEY_LOGIN_RESULT, LoginResultEntity::class.java)
            if (loginResultEntry != null) { // Clock in first scenario
                handleLoginResult(loginResultEntry)
            } else {
                ToastUtil.showToast(getString(R.string.login_fail))
            }
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 1000) {
             DataCollectionManager.instance().updateDeviceInfo()
        }
    }
}