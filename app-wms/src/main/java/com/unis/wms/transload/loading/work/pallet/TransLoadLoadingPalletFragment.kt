package com.unis.wms.transload.loading.work.pallet

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.customer.widget.RecyclerViewItemSpace
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.customer.widget.extensions.setVisibleOrGone
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.unis.platform.transload.loading.model.TransLoadLoadingRfidResultEntity
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.wms.R
import com.unis.wms.databinding.FragmentTransloadLoadingPalletBinding
import com.unis.wms.transload.gps.GpsBindActivity
import com.unis.wms.transload.gps.GpsBoundListActivity
import com.unis.wms.transload.loading.TransLoadLoadingTaskViewModel
import com.unis.wms.uitl.WebSocketClient
import com.unis.wms.uitl.WebSocketConfig
import com.unis.wms.uitl.WebSocketResponseEntity
import com.data_collection.DCActionConstants
import com.data_collection.models.ActionType
import com.data_collection.models.SendDataModel
import org.greenrobot.eventbus.EventBus

class TransLoadLoadingPalletFragment :
    ReactiveFragment<TransLoadLoadingPalletViewModel, TransLoadLoadingPalletUiState, FragmentTransloadLoadingPalletBinding>(),
    BaseQuickAdapter.OnItemChildClickListener {

    private val activityViewModel by lazy { getActivityViewModel<TransLoadLoadingTaskViewModel>()!! }
    private val adapter by lazy { LoadingPalletAdapter(::onPhotoChanged) }
    private val socketClient by lazy { WebSocketClient.newInstance(WebSocketClient.TYPE_TRANSLOAD_LOADING_PALLET) }

    companion object {
        fun newInstance() = newFragmentInstance<TransLoadLoadingPalletFragment>()
    }

    override fun createViewModel(): TransLoadLoadingPalletViewModel {
        return TransLoadLoadingPalletViewModel(activityViewModel = activityViewModel)
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            transloadPalletListRv.layoutManager = LinearLayoutManager(context)
            transloadPalletListRv.addItemDecoration(RecyclerViewItemSpace(context))
            transloadPalletListRv.adapter = adapter
            adapter.onItemChildClickListener = this@TransLoadLoadingPalletFragment
            scanPalletScanner.setInputFocus()
            scanPalletScanner.setScanEvent { _, data ->
                viewModel.scannedPallet(data.toString().trim(), loadUnloadSwitchButton.isChecked)
            }
            labelLoadTv.setOnClickListener {
                loadUnloadSwitchButton.isChecked = true
            }
            labelUnloadTv.setOnClickListener {
                loadUnloadSwitchButton.isChecked = false
            }
            nextProBtn.setOnClickListener {
                viewModel.startNextPro()
            }
            bindGpsTv.setOnClickListener {
                viewModel.getCurrentProNo()?.let {
                    GpsBindActivity.startActivity(this@TransLoadLoadingPalletFragment, it)
                }
            }
            gpsTv.setOnClickListener {
                viewModel.getCurrentProNo()?.let {
                    GpsBoundListActivity.startActivity(this@TransLoadLoadingPalletFragment, it)
                }
            }
            socketClient.connect(WebSocketConfig(viewModel.getTaskId()!!), object : WebSocketClient.WebSocketMessageListener {
                override fun onResult(result: String) {
                    val gson = GsonBuilder().serializeNulls().create()
                    try {
                        val type = object : TypeToken<WebSocketResponseEntity<TransLoadLoadingRfidResultEntity>>() {}.type
                        val resultEntity = gson.fromJson<WebSocketResponseEntity<TransLoadLoadingRfidResultEntity>>(result, type)
                        activity?.runOnUiThread {
                            showRfidAlertDialog(resultEntity.data)
                        }
                    } catch (e: JsonSyntaxException) {
                        e.printStackTrace()
                    }
                }

                override fun onConnectSuccess() {
                }

                override fun onFail(message: String?) {
                }

                override fun onDisConnected() {
                }

            })
        }
        onShowAlertDialogEvent()
        onShowBindGpsOrGpsEvent()
    }

    private fun showRfidAlertDialog(result: TransLoadLoadingRfidResultEntity) {
        result.palletNos?.let {
            val palletNosMessage = it.joinToString("\n")
            val message = String.format(getString(R.string.the_following_pallets_do_not_belong_to_task_xxx), result.taskId) + "\n\n" + palletNosMessage
            CenterDialog.alert(
                context = context!!, message = message, okText = getString(R.string.text_cancel)).show()
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showLoadInfo()
        showPalletProgress()
        showLoadedPalletProgressList()
    }

    override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        val loadingPalletAdapter = adapter as LoadingPalletAdapter
        val palletNo = loadingPalletAdapter.getItem(position)?.palletNo
        CenterDialog.confirm(
            context = context!!,
            message = String.format(getString(R.string.msg_please_cofirm_to_unload_pallet), palletNo),
            negativeText = getString(R.string.text_cancel),
            positiveText = getString(R.string.text_unload),
            positiveClick = {
                viewModel.unload(palletNo)
            },
        ).show()
        EventBus.getDefault().post(
            SendDataModel(
                actionType = ActionType.CLICK, actionKey = DCActionConstants.LIST_ITEM_UNLOAD_PALLET, value = palletNo))
    }

    private fun ReactiveViewScope.showLoadInfo() = subscribe(
        TransLoadLoadingPalletUiState::currentLoadNo,
        TransLoadLoadingPalletUiState::currentOrderNo,
        TransLoadLoadingPalletUiState::currentShouldLoadProNo) { loadNo, orderNo, proNo ->
        binding?.apply {
            loadNo?.let {
                loadTv.text = it
                loadTv.setVisible()
            } ?: loadTv.setGone()

            orderNo?.let {
                orderTv.text = it
                orderTv.setVisible()
            } ?: orderTv.setGone()

            proNo?.let {
                proTv.text = it
                proTv.setVisible()
            } ?: proTv.setGone()
        }
    }

    private fun ReactiveViewScope.showPalletProgress() = subscribe(TransLoadLoadingPalletUiState::palletProgress) {
        binding?.apply {
            val progress = getString(R.string.text_pallet_progress)
            if (it == null) {
                palletProgressTv.text = String.format(progress, 0, 0)
            } else {
                palletProgressTv.text = String.format(progress, it.doneCount, it.totalCount)
            }
            val isAllDone = ((it?.doneCount ?: 0) > 0) && it?.doneCount == it?.totalCount
            scanPalletScanner.setVisibleOrGone(!isAllDone)
            nextProBtn.setVisibleOrGone(isAllDone)
        }
    }

    private fun ReactiveViewScope.showLoadedPalletProgressList() = subscribe(TransLoadLoadingPalletUiState::loadedPalletList) {
        binding?.apply {
            adapter.setDiffList(it)
        }
    }

    private fun onShowAlertDialogEvent() = onEvent<TransLoadLoadingPalletEvent.ShowAlertDialog> {
        context ?: return@onEvent
        CenterDialog.alert(context = context!!, message = msg).show()
    }

    private fun onShowBindGpsOrGpsEvent() = onEvent<TransLoadLoadingPalletEvent.ShowBindGpsOrGps> {
        context ?: return@onEvent
        showBindGpsOrGps(showBindGps)
    }

    private fun onPhotoChanged(palletNo: String, newPhotoList: List<String>) {
        viewModel.updatePhoto(palletNo, newPhotoList)
    }

    override fun onBackPressed(): Boolean {
        if (viewModel.isCompleteCurrentProLoad()) {
            viewModel.startNextPro()
            return true
        }
        return super.onBackPressed()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == GpsBindActivity.REQUEST_CODE_GPS_BIND && resultCode == GpsBindActivity.RESULT_CODE_GPS_BIND && data != null) {
            val hasBoundGps = data.getBooleanExtra(GpsBindActivity.RESULT_GPS_BIND, false)
            showBindGpsOrGps(!hasBoundGps)
        }
        if (requestCode == GpsBoundListActivity.REQUEST_CODE_GPS_BOUND_LIST && resultCode == GpsBoundListActivity.RESULT_CODE_GPS_BOUND_LIST && data != null) {
            val hasBoundGps = data.getBooleanExtra(GpsBoundListActivity.RESULT_GPS_BOUND_LIST, false)
            showBindGpsOrGps(!hasBoundGps)
        }
    }

    private fun showBindGpsOrGps(showBindGps: Boolean) {
        binding?.apply {
            bindGpsTv.setVisibleOrGone(showBindGps)
            gpsTv.setVisibleOrGone(!showBindGps)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        socketClient.disconnect()
    }

}