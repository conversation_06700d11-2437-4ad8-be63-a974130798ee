package com.unis.wms.stepstatus

import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.customer.widget.extensions.setVisibleOrGone
import com.unis.platform.asset.model.AssetQueryEntity
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.reactivemvi.common.CallbackDialog
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.viewLifecycleScope
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.reactivemvi.mvvm.kotlin.extensions.popBack
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import java.io.Serializable
import com.unis.wms.stepstatus.StepStatusUiState.*
import com.unis.wms.stepstatus.StepStatusUiEvent.Success
import com.unis.wms.R
import com.unis.wms.databinding.FragmentStepStatusBinding

class StepStatusFragment : ReactiveFragment<StepStatusViewModel, StepStatusUiState, FragmentStepStatusBinding>() {

    companion object {
        private const val PARAM_STEP = "step"
        private const val PARAM_STATUS = "status"
        private const val PERMISSION_CONFIG = "permissionConfig"
        private const val EXTRA_DATA = "extraData"
        const val TAG ="StepStatusFragment"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            step: BaseTaskStepEntity,
            config: StepStatusPermissionConfig = StepStatusPermissionConfig(),
            newStatus: TaskStepStatus = step.status?: TaskStepStatus.CLOSED,
            extraData: StepStatusExtra = StepStatusExtra()
        ) = newFragmentInstance<StepStatusFragment>(
            PARAM_STEP to step, 
            PARAM_STATUS to newStatus, 
            PERMISSION_CONFIG to config,
            EXTRA_DATA to extraData
        )
    }

    private var onOperateSuccessListener: OnOperateSuccess? = null
    private var onProcessOperateSuccessListener: OnProcessOperateSuccess? = null
    private val extraData: StepStatusExtra by lazy { getBundleObject(EXTRA_DATA) }

    override fun createViewModel(): StepStatusViewModel {
        val initialState = StepStatusState(
            getBundleObject(PARAM_STEP),
            getBundleObject(PARAM_STATUS),
            getBundleObject(PERMISSION_CONFIG)
        )
        return StepStatusViewModel(initialState)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val config = getBundleObject(PERMISSION_CONFIG) as StepStatusPermissionConfig
        setHasOptionsMenu(config.allowShowMenu)
    }

    override fun onCreateOptionsMenu(menu: Menu?, inflater: MenuInflater?) {
        if (!viewModel.dataState.permissionConfig.allowShowMenu) {
            menu?.clear()
        }
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        // Operate success event
        onEvent<Success> { onOperateSuccess(result) }
        onEvent<StepStatusUiEvent.ProcessSuccess> {
            onProcessOperateSuccess(result, process)
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        // UI status for showing
        subscribe(StepStatusUiState::statusForShowing) {
            when (it) {
                UiStatus.Uninitialized -> {}//show nothing
                UiStatus.Start -> showStartView()
                is UiStatus.TakeOver -> showTakeOverView(it.allowTakeOver)
                is UiStatus.Done -> showDoneView(it.allowReopen)
            }
        }
    }

    private fun showStartView() {
        binding?.apply {
            startTakeoverButton.let {
                it.setVisible()
                it.text = getString(R.string.text_start)
                it.setOnClickListener { 
                    if (extraData.assetId.isNullOrEmpty()) {
                        viewModel.searchCurrentUserAsset { assetId ->
                            viewModel.startStep(false, assetId)
                        }
                    } else {
                        viewModel.startStep(false, extraData.assetId)
                    }
                }
                it.isEnabled = true
            }
            doneLayout.setGone()
        }
    }

    private fun showTakeOverView(allowTakeOver: Boolean) {
        binding?.apply {
            startTakeoverButton.let {
                it.setVisible()
                it.text = getString(R.string.text_take_over)
                it.setOnClickListener {
                    viewLifecycleScope.launch {
                        val message = showTakeOverMessageDialog().firstOrNull() ?: return@launch
                        viewModel.takeOverStep(message)
                    }
                }
                it.isEnabled = allowTakeOver
            }
            doneLayout.setGone()
        }
    }

    private fun showTakeOverMessageDialog() = CallbackDialog.showSingleInput(
        context,
        title = getString(R.string.title_take_over),
        hint = getString(R.string.send_message_to_owner)
    )


    private fun showDoneView(allowReopen: Boolean) {
        binding?.apply {
            startTakeoverButton.setGone()
            doneLayout.setVisible()
            reopenButton.setVisibleOrGone(allowReopen)
            reopenButton.setOnClickListener { viewModel.startStep(true, extraData.assetId) }
        }
    }

    private fun onOperateSuccess(result: StepStatusOperateResult) {
        popBack()
        onOperateSuccessListener?.onSuccess(result)
    }

    fun interface OnOperateSuccess {
        fun onSuccess(result: StepStatusOperateResult)
    }

    fun setOnOperateSuccessListener(listener: OnOperateSuccess) {
        this.onOperateSuccessListener = listener
    }

    private fun onProcessOperateSuccess(result: StepStatusOperateResult, process: String) {
        popBack()
        onProcessOperateSuccessListener?.onSuccess(result, process)
    }

    fun interface OnProcessOperateSuccess {
        fun onSuccess(result: StepStatusOperateResult, process: String)
    }

    fun setOnProcessOperateSuccessListener(listener: OnProcessOperateSuccess) {
        this.onProcessOperateSuccessListener = listener
    }

}

data class StepStatusPermissionConfig(
    // Allow Take Over when user is not assignee of step
    val allowTakeOver: Boolean = true,
    // Allow reopen step when step is closed
    val allowReopen: Boolean = true,
    // Allow start step when step is closed
    val allowShowMenu: Boolean = false
) : Serializable

data class StepStatusExtra(
    val assetId: String? = null
) : Serializable

sealed interface StepStatusOperateResult {
    object Start : StepStatusOperateResult
    object TakeOver : StepStatusOperateResult
    object Reopen : StepStatusOperateResult
}

sealed interface ProcessStepStatusOperateResult {
}
