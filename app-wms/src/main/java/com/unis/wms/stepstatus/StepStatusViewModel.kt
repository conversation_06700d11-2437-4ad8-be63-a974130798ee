package com.unis.wms.stepstatus

import com.linc.platform.core.LocalPersistence
import com.linc.platform.utils.ResUtil
import com.unis.platform.asset.api.AssetApi
import com.unis.platform.asset.model.AssetQueryEntity
import com.unis.platform.common.model.step.StepTakeOverEntity
import com.unis.platform.common.model.step.TaskStepApiService
import com.unis.platform.common.model.step.TaskStepStatus
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskStartEntity
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.common.model.tasknode.TaskResultEntity
import com.unis.platform.load.model.task.LoadWorkFlowProcess
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import com.unis.wms.stepstatus.StepStatusUiEvent.Success
import com.unis.wms.stepstatus.StepStatusUiState.UiStatus

/**
 * <AUTHOR>
 * @date 2022/06
 */
class StepStatusViewModel(
    initialState: StepStatusState,
    initialUiState: StepStatusUiState = StepStatusUiState()
) : ReactiveViewModel<StepStatusState, StepStatusUiState>(initialState, initialUiState) {

    private val repository: StepStatusRepo = StepStatusRepo()

    private val stepId get() = dataState.stepEntry.id

    init {
        when (dataState.status) {
            TaskStepStatus.NEW, TaskStepStatus.IN_PROGRESS -> showStartOrTakeOverView()
            TaskStepStatus.CANCELLED, TaskStepStatus.CLOSED, TaskStepStatus.FORCE_CLOSED -> showDoneView()
            else -> {}
        }
    }

    private fun showStartOrTakeOverView() {
        val isAssignee = dataState.stepEntry.assigneeUserIds?.contains(repository.idmUserId)?: false
        if (isAssignee) {
            // Start
            setUiState { copy(statusForShowing = UiStatus.Start) }
        } else {
            // Take over
            setUiState { copy(statusForShowing = UiStatus.TakeOver(dataState.permissionConfig.allowTakeOver)) }
        }
    }

    private fun showDoneView() {
        setUiState { copy(statusForShowing = UiStatus.Done(dataState.permissionConfig.allowReopen)) }
    }

    fun searchCurrentUserAsset(action: (String?) -> Unit) {
        request(repository.searchAsset(AssetQueryEntity().apply {
            this.assigneeId = repository.idmUserId
        }), error = {
            action.invoke(null)
        }, success = {
            if (it.isNullOrEmpty()) {
                action.invoke(null)
            } else {
                action.invoke(it[0].assetId)
            }
        })
    }

    fun startStep(isReopen: Boolean, forkliftId: String?) {
        if (dataState.stepEntry.taskId == null || dataState.stepEntry.stepType == null) return
        val taskStartEntity = TaskStartEntity(forkliftId)
        request(repository.startStep(dataState.stepEntry.taskId!!, stepId.toString(), dataState.stepEntry.taskType!!, dataState.stepEntry.stepType!!, taskStartEntity, isReopen)) {
            showToast(R.string.msg_success)
            if (dataState.stepEntry.stepType == TaskStepType.LOADING) {
                val result = if (isReopen) StepStatusOperateResult.Reopen else StepStatusOperateResult.Start
                fireEvent { StepStatusUiEvent.ProcessSuccess(result, if (isReopen) {
                    ""
                } else {
                    val taskResultEntity = it as TaskResultEntity
                    taskResultEntity.currentNodeResults?.firstOrNull()?.nodeName?:""
                }) }
            } else {
                val result = if (isReopen) StepStatusOperateResult.Reopen else StepStatusOperateResult.Start
                fireEvent { Success(result) }
            }
        }
    }

    fun takeOverStep(message: String) {
        val takeOverEntry = StepTakeOverEntity(
                taskId = dataState.stepEntry.taskId!!,
                stepId = <EMAIL>!!,
                assigneeTo = LocalPersistence.getUserId(ResUtil.getContext())
                )
        request(repository.takeOverStep(takeOverEntry)) {
            showToast(R.string.msg_success)
            if (dataState.stepEntry.stepType == TaskStepType.LOADING) {
                fireEvent { StepStatusUiEvent.ProcessSuccess(StepStatusOperateResult.TakeOver, LoadWorkFlowProcess.TAKE_OVER) }
            } else {
                fireEvent { Success(StepStatusOperateResult.TakeOver) }
            }
        }
    }
}

class StepStatusRepo : BaseRepository() {

    private val taskStepApi by apiServiceLazy<TaskStepApiService>()
    private val assetApi by apiServiceLazy<AssetApi>()

    fun startStep(taskId: String, stepId: String, taskType: TaskType, stepType: TaskStepType, taskStartEntity: TaskStartEntity, isReopen: Boolean = false) =
        if (isReopen) {
            when (stepType) {
                TaskStepType.PICK -> requestV2({ taskStepApi.reopenPickStep(taskId, stepId) })
                //STAGE Step is for receive/pick task
                TaskStepType.STAGE -> if (taskType == TaskType.PICK) requestV2({ taskStepApi.reopenPickStep(taskId, stepId) }) else requestV2({ taskStepApi.reopen(stepId) })
                else -> requestV2({ taskStepApi.reopen(stepId) })
            }
        } else {
            when (stepType) {
                TaskStepType.PICK -> requestV2({ taskStepApi.startPickStep(taskId, stepId, taskStartEntity) })
                TaskStepType.DOCK_CHECK_IN -> requestV2({ taskStepApi.startDockCheckInStep(stepId, taskStartEntity) })
                TaskStepType.OFFLOAD -> requestV2({ taskStepApi.startOffloadStep(stepId) })
                TaskStepType.LP_SETUP -> requestV2({ taskStepApi.startLpSetupStep(stepId) })
                TaskStepType.SN_SCAN -> requestV2({ taskStepApi.startSnScanStep(stepId) })
                TaskStepType.COLLECT -> requestV2({ taskStepApi.startMovementTaskAndStep(taskId, taskStartEntity) })
                //STAGE Step is for receive/pick task 
                TaskStepType.STAGE -> if (taskType == TaskType.RECEIVE) requestV2({ taskStepApi.startStep(stepId) }) else requestV2({ taskStepApi.startStageStep(stepId) })
                TaskStepType.PUT_AWAY -> requestV2({ taskStepApi.startPutAwayTaskAndStep(taskId, taskStartEntity) })
                TaskStepType.REPLENISH -> requestV2({ taskStepApi.startReplenishmentTaskAndStep(taskId, stepId, taskStartEntity) })
                TaskStepType.TRANSLOAD_RECEIVE -> requestV2({ taskStepApi.startTransLoadReceivingTaskAndStep(taskId) })
                TaskStepType.TRANSLOAD_LOAD -> requestV2({ taskStepApi.startTransLoadLoadingTaskAndStep(taskId) })
                TaskStepType.LOADING -> requestV2({ taskStepApi.startLoadTaskAndStep(taskId, taskStartEntity) })
                TaskStepType.ORDER_PICK_FROM_WALL -> requestV2({ taskStepApi.startOrderPickFromWallStep(taskId, stepId) })
                TaskStepType.STAGE_TO_WALL -> requestV2({ taskStepApi.startStageToWallStep(taskId, stepId) })
                TaskStepType.SORTING_TO_WALL -> requestV2({ taskStepApi.startSortingToWallStep(stepId) })
                TaskStepType.PACK -> requestV2({ taskStepApi.startPackTaskAndStep(taskId) })
                TaskStepType.INTERNAL_TRANSFER_OUT -> requestV2({ taskStepApi.startTransferOutTask(taskId) })
                TaskStepType.INTERNAL_TRANSFER_IN -> requestV2({ taskStepApi.startTransferInTask(taskId) })
                TaskStepType.MATERIAL_RECEIVING -> requestV2({ taskStepApi.startStep(stepId) })
                TaskStepType.INSPECTION_CONTENT -> requestV2({ taskStepApi.startInspectionStep(stepId) })
                TaskStepType.FIX_CONTENT -> requestV2({ taskStepApi.startFixStep(stepId) })
                else -> requestV2({ taskStepApi.startStep(stepId) })
            }
        }

    fun takeOverStep(entry: StepTakeOverEntity) = requestV2({taskStepApi.takeOverStep(entry)})

    fun searchAsset(query: AssetQueryEntity) = requestV2({assetApi.searchAsset(query)})
}

