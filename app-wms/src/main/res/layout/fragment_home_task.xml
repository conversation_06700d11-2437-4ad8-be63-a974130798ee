<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingBottom="10dp"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/tv_task_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="30sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="11111" />

        <TextView
            android:id="@+id/tv_task_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_a0a0a0"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@+id/tv_task_title"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:text="11111" />
        <ImageView
            android:id="@+id/locating_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_wifi_blue_24dp"
            android:layout_marginRight="10dp"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_task_title"
            app:layout_constraintBottom_toTopOf="@+id/tv_task_title"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_facility"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="15dp"
            android:drawableStart="@drawable/ic_location_purple_9"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="11111" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="15dp"
            android:drawableStart="@drawable/ic_baseline_calendar_today_15"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="11111" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/color_434444" />

    <LinearLayout
        android:id="@+id/claim_forklift_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_373737_4"
        android:padding="10dp"
        android:gravity="center_vertical"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:text="@string/claim_forklift"/>

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_baseline_keyboard_arrow_right_24"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/task_action_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_373737_4"
        android:padding="10dp"
        android:gravity="center_vertical"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:text="@string/text_task_action"/>

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_baseline_keyboard_arrow_right_24"/>

    </LinearLayout>


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:text="@string/home_pending_task"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ot_request_cl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ot_request_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="OT Request"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ot_badge_tv"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="end|top"
                android:background="@drawable/bg_red_badge"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="1"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintLeft_toRightOf="@+id/ot_request_tv"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/sl_task"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_task"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"/>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.appcompat.widget.LinearLayoutCompat>