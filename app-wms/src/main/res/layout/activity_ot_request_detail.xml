<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.customer.widget.CenterTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/employee"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/employee_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="@string/employee" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/team_name"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/team_name_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="@string/employee" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/labor_type"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/labor_type_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="@string/employee" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/operation_type"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/operation_type_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="@string/operation_type" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:background="@drawable/rect_2e2e2e_4"
                android:orientation="vertical">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_baseline_access_time_24"
                        android:drawablePadding="5dp"
                        android:text="@string/today_work_hours"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/today_work_hours_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_baseline_access_time_24"
                        android:drawablePadding="5dp"
                        android:text="@string/weekly_hours"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/weekly_hours_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_state"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/state_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:paddingHorizontal="16dp"
                android:paddingVertical="10dp"
                android:background="@drawable/rect_661f8bea_4"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/extension_duration"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_marginTop="10dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.customer.widget.LabelSpinner
                        android:id="@+id/extend_hours_spinner"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                    <com.customer.widget.LabelSpinner
                        android:id="@+id/extend_minutes_spinner"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.appcompat.widget.LinearLayoutCompat>
            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:text="@string/request_type"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/request_type_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:textColor="@color/colorAccent"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="@drawable/rect_661f8bea_4"
                    tools:text="@string/operation_type" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/request_time"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/request_time_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="2025-02-15" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/reason_for_extension"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/reason_for_extension_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                tools:text="@string/hint_please_provide_reason"
                android:textSize="15sp"
                android:padding="10dp"
                android:textColor="@color/white"
                android:layout_marginHorizontal="16dp"
                android:background="@drawable/rect_646566_4" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>

    <com.customer.widget.StateButton
        android:id="@+id/approve_request_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/text_approve_request"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/colorAccent"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/colorAccent"
        app:pressedTextColor="@color/white"
        app:unableBackgroundColor="@color/colorAccent_disable"
        app:unableTextColor="@color/white_o30"
        tools:visibility="visible"/>

    <com.customer.widget.StateButton
        android:id="@+id/reject_request_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/text_reject_request"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/accent_red_v1"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/accent_red_v1"
        app:pressedTextColor="@color/white"
        tools:visibility="visible" />

</LinearLayout>