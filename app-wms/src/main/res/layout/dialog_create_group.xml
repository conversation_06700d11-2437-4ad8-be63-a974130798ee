<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="15dp"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/ip_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textColor="@color/white"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/name_edt"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="@string/please_input_group_name"
        style="@style/editInputStyle"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/port_edt"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:hint="@string/please_input_port"
        style="@style/editInputStyle"/>

    <com.customer.widget.LabelSpinner
        android:id="@+id/type_spinner"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"/>

    <com.customer.widget.LabelSpinner
        android:id="@+id/device_type_spinner"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/save_btn"
        style="@style/buttonV1"
        android:layout_marginTop="10dp"
        android:text="@string/btn_save" />
</LinearLayout>