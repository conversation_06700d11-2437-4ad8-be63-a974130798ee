<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/rect_page_background_r4"
    android:minWidth="320dp"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
    android:paddingTop="@dimen/page_vertical_margin_v1"
    android:paddingBottom="10dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_lp_qty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_lp_qty_mark"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/lp_qty_edit"
        android:layout_width="120dp"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/rect_color_bg_light_r4"
        android:imeOptions="actionDone"
        android:inputType="number"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/title_lp_qty"
        app:layout_constraintStart_toEndOf="@id/title_lp_qty"
        app:layout_constraintTop_toTopOf="@id/title_lp_qty"
        tools:text="3"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/lp_qty_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/title_lp_qty"
        app:layout_constraintStart_toEndOf="@id/title_lp_qty"
        app:layout_constraintTop_toTopOf="@id/title_lp_qty"
        tools:text="7" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/edit_button"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:background="@null"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingHorizontal="12dp"
        android:src="@drawable/ic_pen_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_receiving_qty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/receiving_qty"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_lp_qty" />

    <FrameLayout
        android:id="@+id/ll_receive_qty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/title_receiving_qty"
        app:layout_constraintTop_toTopOf="@id/title_receiving_qty">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/rc_qty_edit"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/rect_color_bg_light_r4"
            android:imeOptions="actionDone"
            android:inputType="number"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/title_receiving_qty"
            app:layout_constraintStart_toEndOf="@id/title_receiving_qty"
            app:layout_constraintTop_toTopOf="@id/title_receiving_qty"
            tools:text="700"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/receiving_qty_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="700" />

    </FrameLayout>


    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/edit_button_rc"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:background="@null"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:paddingHorizontal="12dp"
        android:src="@drawable/ic_pen_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title_receiving_qty"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/receiving_tv_unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="EA"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/ll_receive_qty"
        app:layout_constraintStart_toEndOf="@id/ll_receive_qty"
        app:layout_constraintTop_toTopOf="@id/ll_receive_qty" />

    <TextView
        android:id="@+id/tv_full_pallet_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/receive_full_pallet"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_receiving_qty"/>

    <TextView
        android:id="@+id/tv_partial_pallet_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/receive_partial_pallet"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_full_pallet_count"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_lp_subtype"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/lp_sub_type"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_partial_pallet_count"/>

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkbox_pallet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:button="@drawable/checkbox_style_white"
        android:text="@string/title_pallet"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/tv_lp_subtype"
        app:layout_constraintTop_toTopOf="@id/tv_lp_subtype"
        app:layout_constraintBottom_toBottomOf="@id/tv_lp_subtype"/>

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkbox_carton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:button="@drawable/checkbox_style_white"
        android:text="@string/title_carton"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/checkbox_pallet"
        app:layout_constraintTop_toTopOf="@id/checkbox_pallet"
        app:layout_constraintBottom_toBottomOf="@id/checkbox_pallet"/>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="22dp"
        android:background="@drawable/divider_grey_656565"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_lp_subtype" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_to_receive_qty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:text="@string/to_receive_qty"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/to_receive_qty_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@id/title_to_receive_qty"
        app:layout_constraintTop_toTopOf="@id/title_to_receive_qty"
        tools:text="700 EA" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/negative_button"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/button_height_v1"
        android:layout_marginEnd="12dp"
        android:background="@null"
        android:text="@string/btn_cancel"
        android:textColor="@color/colorAccent"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/positive_button"
        app:layout_constraintTop_toBottomOf="@id/title_to_receive_qty" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/positive_button"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/button_height_v1"
        android:background="@null"
        android:text="@string/btn_confirm"
        android:textColor="@color/colorAccent"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_to_receive_qty" />

</androidx.constraintlayout.widget.ConstraintLayout>