<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rect_2e2e2e_4"
    android:layout_marginHorizontal="16dp"
    android:layout_marginTop="10dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_baseline_my_account_24" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginStart="5dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="16sp"
                tools:text="sss" />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/employee_id_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="sss" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/time_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="16sp"
                tools:text="sss" />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/date_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="sss" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </LinearLayout>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/team_shift_name"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="sss" />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/shift_tv"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="end"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="sss" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/extension_duration_tv"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:drawablePadding="5dp"
            android:drawableStart="@drawable/ic_baseline_access_time_24"
            android:textSize="14sp"
            tools:text="sss" />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_arrow_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow_right_white"/>
    </androidx.appcompat.widget.LinearLayoutCompat>
</LinearLayout>