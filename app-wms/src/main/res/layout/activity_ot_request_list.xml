<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.customer.widget.CenterTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <!-- 可滚动的内容区域（标题+筛选器） -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_collapseMode="parallax">

                    <!-- 标题部分 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/pending_requests_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            tools:text="@string/xxx_pending_requests" />
                    </LinearLayout>

                    <!-- 筛选条件容器 -->
                    <LinearLayout
                        android:id="@+id/filter_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <com.customer.widget.LabelSpinner
                            android:id="@+id/status_spinner"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:labelSpinnerText="@string/label_status" />

                        <!-- 用户选择区域 -->
                        <LinearLayout
                            android:id="@+id/process_by_select_layout"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:layout_marginTop="15dp"
                            android:background="@drawable/rect_bg_input_8"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/process_by_tv"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:hint="@string/processed_by"
                                android:paddingStart="15dp"
                                android:textColor="@color/white"
                                android:textColorHint="@color/hint_color"
                                android:textSize="15sp"
                                tools:ignore="RtlSymmetry" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/process_by_clear_iv"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:paddingHorizontal="16dp"
                                android:src="@drawable/ic_clear_white" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/ic_arrow_right" />

                        </LinearLayout>

                        <!-- 提交时间选择区域 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:id="@+id/submit_time_from_layout"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:background="@drawable/rect_bg_input_8"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/submit_time_from_txt"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:hint="@string/submit_time_from"
                                    android:paddingStart="15dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/hint_color"
                                    android:textSize="15sp"
                                    tools:ignore="RtlSymmetry" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/submit_from_clear_iv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:paddingHorizontal="10dp"
                                    android:src="@drawable/ic_clear_white" />

                            </LinearLayout>

                            <View
                                android:layout_width="20dp"
                                android:layout_height="1dp"
                                android:layout_marginHorizontal="10dp"
                                android:background="@color/colorAccent" />

                            <LinearLayout
                                android:id="@+id/submit_time_to_layout"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:background="@drawable/rect_bg_input_8"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/submit_time_to_txt"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:hint="@string/submit_time_to"
                                    android:paddingStart="15dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/hint_color"
                                    android:textSize="15sp"
                                    tools:ignore="RtlSymmetry" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/submit_to_clear_iv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:paddingHorizontal="10dp"
                                    android:src="@drawable/ic_clear_white" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- 处理时间选择区域 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:layout_marginBottom="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:id="@+id/processed_time_from_layout"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:background="@drawable/rect_bg_input_8"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/processed_time_from_txt"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:hint="@string/processed_time_from"
                                    android:paddingStart="15dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/hint_color"
                                    android:textSize="15sp"
                                    tools:ignore="RtlSymmetry" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/processed_from_clear_iv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:paddingHorizontal="10dp"
                                    android:src="@drawable/ic_clear_white" />

                            </LinearLayout>

                            <View
                                android:layout_width="20dp"
                                android:layout_height="1dp"
                                android:layout_marginHorizontal="10dp"
                                android:background="@color/colorAccent" />

                            <LinearLayout
                                android:id="@+id/processed_time_to_layout"
                                android:layout_width="0dp"
                                android:layout_height="48dp"
                                android:layout_weight="1"
                                android:background="@drawable/rect_bg_input_8"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/processed_time_to_txt"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:hint="@string/processed_time_to"
                                    android:paddingStart="15dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/hint_color"
                                    android:textSize="15sp"
                                    tools:ignore="RtlSymmetry" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/processed_to_clear_iv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:paddingHorizontal="10dp"
                                    android:src="@drawable/ic_clear_white" />

                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                app:layoutManager="LinearLayoutManager" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>

