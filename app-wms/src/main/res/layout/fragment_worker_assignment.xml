<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_222222"
    android:orientation="vertical">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:gravity="center_vertical"
        android:layout_marginVertical="16dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:text="@string/team_name_mark"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <com.customer.widget.LabelSpinner
            android:id="@+id/team_spinner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"/>
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:text="@string/labor_type_mark"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <com.customer.widget.LabelSpinner
            android:id="@+id/labor_spinner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"/>
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/operation_type_mark"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/add_operation_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="@drawable/ic_add_round_blue" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/operation_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.customer.widget.StateButton
        android:id="@+id/confirm_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/btn_confirm"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/colorAccent"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/colorAccent"
        app:pressedTextColor="@color/white"
        app:unableBackgroundColor="@color/colorAccent_disable"
        app:unableTextColor="@color/white_o30" />
</LinearLayout>