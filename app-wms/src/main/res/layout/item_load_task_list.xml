<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
    android:paddingVertical="10dp">

    <LinearLayout
        android:id="@+id/task_title_layout_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_color_bg_light_top_4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_task_id"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.8"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_body3_v1"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_dock"
            style="@style/textSubtitleV1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_body3_v1"
            android:textStyle="bold"
            android:gravity="end"
            tools:text="Dock" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/state_complete_tv"
            style="@style/textSubtitleV1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rect_214d36_r2"
            android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
            android:paddingVertical="@dimen/page_horizontal_margin_half_v1"
            android:text="@string/btn_complete"
            android:textColor="@color/accent_green_v1"
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_task_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_color_bg_medium_bottom_4"
        android:orientation="vertical"
        android:paddingHorizontal="20dp">

        <LinearLayout
            android:id="@+id/forklift_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/content_padding_left">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/asset_type_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:text="@string/text_forklift"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/asset_id_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:text="Asset-1"/>

            <ImageView
                android:id="@+id/remove_asset_iv"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_baseline_remove_24"
                android:layout_marginStart="8dp"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/text_load_no" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_load_no"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorBgLight" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/text_create_by" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_create_by"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorBgLight" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            >

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:paddingVertical="@dimen/chip_vertical_padding_v1"
                android:text="@string/title_priority" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_521b17_r2"
                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                android:paddingVertical="@dimen/page_horizontal_margin_half_v1"
                android:textColor="@color/color_ef4134"
                android:textSize="@dimen/text_size_body4_v1"
                android:textStyle="bold"
                android:gravity="center_horizontal"/>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_note" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/note_tv"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="2"
                android:ellipsize="end"/>

        </LinearLayout>

    </LinearLayout>


</LinearLayout>