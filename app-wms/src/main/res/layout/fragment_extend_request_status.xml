<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/status_ll"
        android:layout_width="match_parent"
        android:layout_marginTop="10dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:padding="10dp"
        tools:background="@drawable/rect_fff6e6_r4">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/status_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            tools:src="@drawable/ic_baseline_access_time_24" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/status_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/extended_yellow_700"
            tools:text="@string/pending_tips" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/pending_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="visible">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/request_time_1"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/pending_request_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/extension_time"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/pending_extension_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:drawablePadding="5dp"
                android:text="@string/label_status"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_fff6e6_r40"
                android:paddingHorizontal="10dp"
                android:paddingVertical="5dp"
                android:text="@string/text_pending"
                android:textColor="@color/extended_yellow_700"
                android:textSize="14sp"
                android:textStyle="bold" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="50dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/waiting_for_supervisor_approval"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="@string/pending_tips_1"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/approved_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/approved_time"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/approved_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/extension_duration"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/approved_extension_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:drawablePadding="5dp"
                android:text="@string/approved_by"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/approved_by_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/text_pending" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/rect_661f8bea_4"
            android:padding="10dp"
            android:text="@string/approved_tips"
            android:textColor="@color/color_18a0fb" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/start_extended_shift_ll"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:background="@drawable/rect_4caf50_4"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_access_time"
                android:drawablePadding="10dp"
                android:text="@string/start_extended_shift"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/requested_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/requested_time"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/requested_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/extension_duration"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/requested_extension_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:drawablePadding="5dp"
                android:text="@string/requested_by"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/requested_by_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/requested_by" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/requested_start_extended_shift_ll"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:background="@drawable/rect_4caf50_4"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_access_time"
                android:drawablePadding="10dp"
                android:text="@string/start_extended_shift"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <com.customer.widget.StateButton
            android:id="@+id/requested_end_work_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:text="@string/end_work"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/grey_ECECEC"
            app:normalTextColor="@color/gray_929292"
            app:pressedBackgroundColor="@color/grey_ECECEC"
            app:pressedTextColor="@color/gray_929292" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/rejected_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/response_time"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/rejected_time_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:text="@string/rejected_by"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/rejected_by_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/rejected_end_work_ll"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:background="@drawable/rect_646566_4"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_access_time"
                android:drawablePadding="10dp"
                android:text="@string/end_work"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/completed_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_access_time"
                android:drawablePadding="5dp"
                android:text="@string/extension_duration"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/completed_extension_duration_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/today" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.customer.widget.StateButton
            android:id="@+id/completed_request_other_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:text="@string/request_additional_extension"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/colorAccent"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/colorAccent"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/colorAccent_disable"
            app:unableTextColor="@color/white_o30" />

        <com.customer.widget.StateButton
            android:id="@+id/completed_end_work_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
            android:layout_marginBottom="10dp"
            android:text="@string/end_work"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/grey_ECECEC"
            app:normalTextColor="@color/gray_929292"
            app:pressedBackgroundColor="@color/grey_ECECEC"
            app:pressedTextColor="@color/gray_929292" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>