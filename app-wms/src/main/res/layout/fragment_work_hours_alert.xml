<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/status_ll"
        android:layout_width="match_parent"
        android:layout_marginTop="10dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:padding="10dp"
        android:background="@drawable/rect_fff6e6_r4"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/status_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/extended_yellow_700"
            tools:text="@string/work_hours_alert_tips" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_access_time"
            android:drawablePadding="5dp"
            android:text="@string/today_work_hours"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/today_work_hours_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="@string/today" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_access_time"
            android:drawablePadding="5dp"
            android:text="@string/weekly_hours"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/weekly_hours_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="@string/today" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/text_state"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/state_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="@string/today" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.customer.widget.StateButton
        android:id="@+id/extend_shift_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/extend_shift"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/colorAccent"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/colorAccent"
        app:pressedTextColor="@color/white"
        app:unableBackgroundColor="@color/colorAccent_disable"
        app:unableTextColor="@color/white_o30" />

    <com.customer.widget.StateButton
        android:id="@+id/end_work_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/end_work"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/grey_ECECEC"
        app:normalTextColor="@color/gray_929292"
        app:pressedBackgroundColor="@color/grey_ECECEC"
        app:pressedTextColor="@color/gray_929292" />
</LinearLayout>