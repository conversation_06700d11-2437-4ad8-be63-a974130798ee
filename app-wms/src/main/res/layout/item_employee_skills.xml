<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:layout_marginTop="5dp"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:maxLines="1"
        android:textColor="@color/white"
        tools:text="Material 1fffffffffffffffffff"/>

    <androidx.appcompat.widget.AppCompatButton
        style="@style/filterButtonStyle"
        android:id="@+id/btn_remove"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:text="@string/btn_remove"/>
</LinearLayout>