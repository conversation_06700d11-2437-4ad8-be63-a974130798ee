<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:background="@drawable/rect_2e2e2e_4"
                android:orientation="vertical">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_baseline_access_time_24"
                        android:drawablePadding="5dp"
                        android:text="@string/today_work_hours"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/today_work_hours_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/ic_baseline_access_time_24"
                        android:drawablePadding="5dp"
                        android:text="@string/weekly_hours"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/weekly_hours_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_state"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/state_tv"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="@string/today" />
                </androidx.appcompat.widget.LinearLayoutCompat>
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="@string/extension_duration"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <com.customer.widget.LabelSpinner
                    android:id="@+id/extend_hours_spinner"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <com.customer.widget.LabelSpinner
                    android:id="@+id/extend_minutes_spinner"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/request_type"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/extended_hours_tv"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_ot_request_type_selector"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/text_extended_hours"
                    android:textColor="@color/selector_white_accent_selected"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/daily_ot_tv"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginTop="10dp"
                    android:layout_marginStart="10dp"
                    android:background="@drawable/bg_ot_request_type_selector"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/text_daily_ot"
                    android:textColor="@color/selector_white_accent_selected"
                    android:textSize="16sp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/daily_dt_tv"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_ot_request_type_selector"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/text_daily_dt"
                    android:textColor="@color/selector_white_accent_selected"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/weekly_ot_tv"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:layout_marginTop="10dp"
                    android:layout_marginStart="10dp"
                    android:background="@drawable/bg_ot_request_type_selector"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="@string/text_weekly_ot"
                    android:textColor="@color/selector_white_accent_selected"
                    android:textSize="16sp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/reason_for_extension"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/extends_reason_edt"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/rect_white_t4"
                android:gravity="top"
                android:hint="@string/hint_please_provide_reason"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textColorHint="@color/gray_929292"
                android:textSize="15sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>

    <com.customer.widget.StateButton
        android:id="@+id/submit_shift_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/submit_shift"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/colorAccent"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/colorAccent"
        app:pressedTextColor="@color/white"
        app:unableBackgroundColor="@color/colorAccent_disable"
        app:unableTextColor="@color/white_o30" />

    <com.customer.widget.StateButton
        android:id="@+id/extend_shift_cancel_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginBottom="10dp"
        android:text="@string/text_cancel"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/grey_ECECEC"
        app:normalTextColor="@color/gray_929292"
        app:pressedBackgroundColor="@color/grey_ECECEC"
        app:pressedTextColor="@color/gray_929292" />
</LinearLayout>