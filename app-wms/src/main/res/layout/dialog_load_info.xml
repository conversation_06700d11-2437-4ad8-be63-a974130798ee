<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/rect_color_bg_medium_r4"
    android:backgroundTint="@color/page_background_v1"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingVertical="8dp"
        android:text="@string/title_load_task_describe"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/color_393939" />

    <TextView
        style="@style/labelTextLargeWhite"
        android:id="@+id/describe_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Dock:DOCK11, LP Count: 100"/>


    <com.unis.wms.task_action.pick.LimitHeightRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        tools:itemCount="3"
        tools:listitem="@layout/item_load_describe" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <com.customer.widget.StateButton
            android:id="@+id/positive_button"
            style="@style/stateButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:text="@string/btn_next"
            app:normalBackgroundColor="@color/color_8B5CF6"
            app:pressedBackgroundColor="@color/color_C4B5FD" />
    </LinearLayout>
</LinearLayout> 