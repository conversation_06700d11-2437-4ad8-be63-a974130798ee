package com.customer.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.appcompat.widget.AppCompatSpinner
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.data_collection.models.ActionType
import com.data_collection.models.SendDataModel
import com.data_collection.utils.ActionUtils
import org.greenrobot.eventbus.EventBus


/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2023/6/12
 */
class LabelSpinner @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    private val requireFlagTv: AppCompatTextView
    private val labelTextTv: AppCompatTextView
    private val labelSpinner: AppCompatSpinner

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.LabelSpinner)
        val isShowRequireFlag = ta.getBoolean(R.styleable.LabelSpinner_isShowRequireFlag, false)
        val needLabelText = ta.getBoolean(R.styleable.LabelSpinner_needLabelText, true)
        val labelSpinnerText = ta.getString(R.styleable.LabelSpinner_labelSpinnerText)
        inflate(context, R.layout.layout_label_spinner, this)
        requireFlagTv = findViewById(R.id.require_flag_tv)
        labelTextTv = findViewById(R.id.label_text_tv)
        labelSpinner = findViewById(R.id.label_spinner)
        requireFlagTv.visibility = if (isShowRequireFlag) VISIBLE else GONE
        labelTextTv.visibility = if (needLabelText) VISIBLE else GONE
        if (TextUtils.isEmpty(labelSpinnerText)) {
            labelTextTv.visibility = GONE
        }
        labelTextTv.text = labelSpinnerText
        ta.recycle()
    }

    fun setDataOptions(dataOptions: List<String>) {
        setDataOptions(dataOptions, "", true)
    }

    fun setDataOptions(dataOptions: List<String>, isInitFirstOption: Boolean) {
        setDataOptions(dataOptions, "", isInitFirstOption)
    }

    fun setDataOptions(dataOptions: List<String>, selectItem: String?) {
        setDataOptions(dataOptions, selectItem, false)
    }

    private fun setDataOptions(dataOptions: List<String>, selectItem: String?, isInitFirstOption: Boolean = false) {
        val options = dataOptions.toMutableList()
        options.add(0, "") // set spinner default value to empty
        val spinnerAdapter: ArrayAdapter<String> = object : ArrayAdapter<String>(
            context, R.layout.label_spinner_item, R.id.text_spinner_tv, options) {
            override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
                val v: View
                if (position == 0) { // set spinner default value to empty
                    val tv = TextView(context)
                    tv.visibility = GONE
                    tv.height = 0
                    v = tv
                } else {
                    v = super.getDropDownView(position, null, parent)
                }
                return v
            }
        }
        spinnerAdapter.setDropDownViewResource(R.layout.label_spinner_dropdown_item)
        labelSpinner.adapter = spinnerAdapter
        if (!TextUtils.isEmpty(selectItem)) {
            var index = 0
            for (item in options) {
                if (item != selectItem) {
                    index++
                } else {
                    break
                }
            }
            labelSpinner.setSelection(index)
        } else {
            if (isInitFirstOption) labelSpinner.setSelection(1)
        }
    }

    fun getSelectedItem(): String = labelSpinner.selectedItem.toString()

    fun getSelectedPosition(): Int = labelSpinner.selectedItemPosition

    fun clear() {
        labelSpinner.setSelection(0)
    }

    fun setLabelText(labelText: String) {
        labelTextTv.text = labelText
    }

    fun setLabelTextVisibility(visibility: Int) {
        labelTextTv.visibility = visibility
        requireFlagTv.visibility = visibility
    }

    fun setSelectedListener(listener: OnItemSelectedListener) {
        labelSpinner.onItemSelectedListener = object : OnItemSelectedListener, AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) { //for list option add empty item, and it hide, so position need -1
                listener.onItemSelected(parent, view, position - 1, id)
                EventBus.getDefault().post(
                    SendDataModel(
                        actionType = ActionType.CLICK,
                        actionKey = ActionUtils.getViewActionId(this@LabelSpinner),
                        value = labelSpinner.selectedItem.toString()))
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
            }

        }
    }

    fun setSelectedPosition(position: Int) { //for list option add empty item, and it hide, so position need +1
        labelSpinner.setSelection(position + 1)
    }

    fun setEnable(enable: Boolean) {
        labelSpinner.isEnabled = enable
    }

    fun isEnable(): Boolean = labelSpinner.isEnabled

    interface OnItemSelectedListener {
        fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long)
    }
}