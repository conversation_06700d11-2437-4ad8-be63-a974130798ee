package com.customer.widget.core;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.customer.widget.QMUIProgress;
import com.customer.widget.photo.PhotoWidget;
import com.unis.platform.util.SwitchLangUtils;
import com.customer.widget.util.TtsUtils;
import com.linc.platform.aspectj.PageDestroy;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.core.UserRole;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.localconfig.FacilityConfigPresenter;
import com.linc.platform.localconfig.FacilityConfigPresenterImpl;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.data_collection.DCActionConstants;
import com.data_collection.models.ActionType;
import com.data_collection.models.SendDataModel;
import com.data_collection.models.UpdatePageEvent;
import com.unis.platform.db.WMSDBManager;
import com.unis.platform.facility_v2.mode.FacilityEntity;
import com.unis.platform.iam.model.UserInfoEntity;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class LincBaseActivity extends AppCompatActivity {
    protected Context appContext;
    protected UserRole userRole;
    protected FacilityConfigPresenter facilityConfigPresenter;
    protected TtsUtils ttsUtils;

    protected QMUIProgress progress;
    protected int activePhotoWidgetId = 0;
    protected HashMap<Integer, PhotoWidget> photoWidgets = new HashMap<>();

    protected String barcodeStr = "";
    protected Handler handler = new Handler();
    protected Runnable delayRun = () -> onBarcodeScanDone(barcodeStr.replaceAll("\r|rn", "").trim());
    private boolean enabledBundleBus;
    private WorkIdleMonitor workIdleMonitor;

    public void setCurtActivePhotoWidget(int widgetId) {
        this.activePhotoWidgetId = widgetId;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        appendBundleIfNeeded();
        SwitchLangUtils.applyContextAndApplication(this);
        appContext = getApplicationContext();
        userRole = new UserRole(LocalPersistence.getUserName(appContext),
                LocalPersistence.getUserId(appContext));

        facilityConfigPresenter = FacilityConfigPresenterImpl.getInstance();

        if (supportVoice()) {
            ttsUtils = TtsUtils.getInstance();
            ttsUtils.init(this);
        }
        ResUtil.setCurrentPage(getClass().getName(), null);
        if (isEnableCollect()) {
            EventBus.getDefault().post(new UpdatePageEvent(this.getClass().getSimpleName().replace("Activity", ""), true));
            EventBus.getDefault().post(
                    SendDataModel.Companion.createPageData(
                            ActionType.PAGE_ENTER,
                            ActionType.PAGE_ENTER.getActionTypeStr(),
                            this.getClass().getSimpleName().replace("Activity", "")));
        }
    }

    public void onUserInteraction() {
        super.onUserInteraction();
        if (workIdleMonitor != null) {
            workIdleMonitor.setLastWorkTime();
        }
    }

    private void appendBundleIfNeeded() {
        Class<?> clazz = this.getClass();
        if (ActivityBundleHolder.contains(clazz)) {
            Bundle bundle = ActivityBundleHolder.getAndClear(clazz);
            getIntent().putExtras(bundle);
        }
    }

    @Override
    protected void attachBaseContext(@Nullable Context newBase) {
        super.attachBaseContext(SwitchLangUtils.wrapContext(newBase));
    }

    @Override
    protected void onStart() {
        if (enabledBundleBus) EventBus.getDefault().register(this);
        if (enableManualTimer()) {
            if (workIdleMonitor == null) {
                workIdleMonitor = new WorkIdleMonitor(this::onWorkIdle);
            }
        }
        super.onStart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (supportVoice() && !ttsUtils.checkTtsAvailable()) {
            ttsUtils = TtsUtils.getInstance();
            ttsUtils.init(this);
        }
        ResUtil.setCurrentPage(getClass().getName(), null);
        if (isEnableCollect()) {
            EventBus.getDefault().post(new UpdatePageEvent(this.getClass().getSimpleName().replace("Activity", ""), true));
        }

    }

    @Override
    protected void onStop() {
        if (enabledBundleBus && EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onStop();
    }

    protected void initToolBar(Toolbar toolbar, String title) {
        if (toolbar == null) {
            return;
        }
        toolbar.setTitle(title);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        toolbar.setNavigationOnClickListener(v -> {
            if (v != null && v.isPressed()) {
                EventBus.getDefault().post(new SendDataModel(ActionType.CLICK, DCActionConstants.LEFT_TOP_PAGE_BACK));
                onBackPressed();
            }
        });
    }

    protected void initToolBar(Toolbar toolbar, @StringRes int title) {
        if (toolbar == null) {
            return;
        }
        toolbar.setTitle(title);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        toolbar.setNavigationOnClickListener(v -> {
            if (v != null && v.isPressed()) {
                onBackPressed();
                EventBus.getDefault().post(new SendDataModel(ActionType.CLICK, DCActionConstants.LEFT_TOP_PAGE_BACK));
            }
        });
    }

    protected void initToolBarNoBack(Toolbar toolbar, String title) {
        if (toolbar == null) {
            return;
        }
        toolbar.setTitle(title);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        }
    }

    protected void initToolBarWithSubTitle(Toolbar toolbar, @StringRes int title, String subTitle) {
        initToolBarWithSubTitle(toolbar, getString(title), subTitle);
    }

    protected void initToolBarWithSubTitle(Toolbar toolbar, String title, String subTitle) {
        if (toolbar == null) {
            return;
        }
        toolbar.setTitle(title);
        if (StringUtil.isNotEmpty(subTitle)) {
            toolbar.setSubtitle(subTitle);
        } else {
            toolbar.setSubtitle("");
        }
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        toolbar.setNavigationOnClickListener(v -> {
            if (v != null && v.isPressed()) {
                onBackPressed();
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult(requestCode, resultCode, data);
        if (photoWidgets.containsKey(this.activePhotoWidgetId) && resultCode != 0 && data != null) {
            PhotoWidget target = photoWidgets.get(activePhotoWidgetId);
            target.onActivityResult(requestCode, resultCode, data);
        }

        if (supportVoice()) {
            ttsUtils.onTtsActivityResult(this, requestCode, resultCode);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (supportKeyDownScanEvent()
                && keyCode != KeyEvent.KEYCODE_BACK
                && keyCode != KeyEvent.KEYCODE_DEL
                && event.getUnicodeChar() > 0) {
            Character barcode = (char) event.getUnicodeChar();
            barcodeStr += barcode;
            handler.removeCallbacks(delayRun);
            handler.postDelayed(delayRun, 100);
        }
        return super.onKeyDown(keyCode, event);
    }

    public void addPhotoWidget(PhotoWidget widget) {
        int sort = photoWidgets.size() + 1;
        widget.setSort(sort);
        photoWidgets.put(sort, widget);
    }

    public List<PhotoWidget> getPhotoWidgets() {
        List<PhotoWidget> photoWidgetList = new ArrayList<>();
        for (int i = 0; i < this.photoWidgets.size(); i++) {
            for (PhotoWidget photoWidget : photoWidgets.values()) {
                photoWidgetList.add(photoWidget);
            }
        }
        return photoWidgetList;
    }

    public void speak(int msg) {
        speak(getString(msg));
    }

    public void speak(String msg) {
        if (ttsUtils != null) {
            ttsUtils.speak(msg);
        }
    }

    public void showToast(String msg) {
        ToastUtil.showToast(msg);
    }

    public void showToast(String msg, Object... params) {
        try {
            ToastUtil.showToast(String.format(msg, params));
        } catch (Exception e) {
            ToastUtil.showToast(msg);
        }
    }

    public void showToast(int msg, Object... params) {
        showToast(getString(msg), params);
    }

    public void showToast(int msg) {
        showToast(getString(msg));
    }

    public void speakAndToast(String msg) {
        speak(msg);
        showToast(msg);
    }

    public void speakAndToast(int msg) {
        speakAndToast(getString(msg));
    }

    public void speakAndToast(int msg, Object... params) {
        try {
            speakAndToast(String.format(getString(msg), params));
        } catch (Exception e) {
            speakAndToast(getString(msg));
        }
    }

    @PageDestroy
    @Override
    protected void onDestroy() {
        if (supportVoice()) {
            ttsUtils.onTtsDestroy();
        }
        super.onDestroy();
        if (enableManualTimer()) {
            if (workIdleMonitor != null) {
                workIdleMonitor.onStop();
            }
        }
        if (isEnableCollect()) {
            EventBus.getDefault().post(new UpdatePageEvent(this.getClass().getSimpleName().replace("Activity", ""), false));
            EventBus.getDefault().post(
                    SendDataModel.Companion.createPageData(
                            ActionType.PAGE_CLOSE,
                            ActionType.PAGE_CLOSE.getActionTypeStr(),
                            this.getClass().getSimpleName().replace("Activity", "")));
        }
    }

    public String getIdmUserId() {
        return LocalPersistence.getUserId(appContext);
    }

    public UserInfoEntity getUserInfoEntity() {
        return LocalPersistence.getIamEntity(appContext);
    }

    public String getFacilityId() {
        FacilityEntity facilityEntity = getFacility2();
        return facilityEntity == null ? "" : facilityEntity.getId();
    }

    public String getFacilityName() {
        FacilityEntity facilityEntity = getFacility2();
        return facilityEntity == null ? "" : facilityEntity.getName();
    }

    @Deprecated
    // use getFacility2
    public FacilityEntry getFacility() {
        return FacilityConfigPresenterImpl.getInstance().getFacility(LocalPersistence.getUserId(getApplicationContext()));
    }

    public FacilityEntity getFacility2() {
        return WMSDBManager.INSTANCE.getFacilityManager().getOneByUserId(LocalPersistence.getUserId(getApplicationContext()));
    }

    public <T extends View> T findView(int viewId) {
        return (T) this.findViewById(viewId);
    }

    protected void enabledBundleBus(boolean enabledBundleBus) {
        this.enabledBundleBus = enabledBundleBus;
    }

    protected boolean supportKeyDownScanEvent() {
        return false;
    }

    public boolean supportVoice() {
        return false;
    }

    protected void onBarcodeScanDone(String barcodeStr) {
        this.barcodeStr = "";
    }

    public <T extends Object> T getIntentObject(String name) {
        return (T) this.getIntent().getSerializableExtra(name);
    }

    public void showProgress(boolean show) {
        showProgress(show, true);
    }

    public void showProgress(boolean show, boolean cancelable) {
        if (isFinishing()) {
            return;
        }
        if (progress == null) progress = QMUIProgress.create(this);
        progress.showProgress(show, cancelable);
    }

    public void hideInputSoft(View rootView) {
        InputMethodManager imm = (InputMethodManager) this.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && imm.isActive()) {
            imm.hideSoftInputFromWindow(rootView.getWindowToken(), 0);
        }
    }

    @SuppressLint("MissingPermission")
    public void vibrate(long duration) {
        Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
        vibrator.vibrate(duration);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    protected boolean enableManualTimer() {
        return false;
    }

    protected void onWorkIdle() {
    }

    public boolean isEnableCollect() {
        return true;
    }
}
