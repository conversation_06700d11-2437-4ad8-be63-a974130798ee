<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_purple">#9B51F5</color>
    <color name="accent_purple_o40">#669B51F5</color>
    <color name="accent_purple_o80">#CC9B51F5</color>
    <color name="accent_purple_o50">#8057378C</color>
    <color name="color_C4B5FD">#C4B5FD</color>
    <color name="color_8B5CF6">#8B5CF6</color>
    <color name="color_AE8FF8_o09">#17AE8FF8</color>
    <color name="color_262626">#262626</color>
    <color name="color_2c2c2c">#2c2c2c</color>
    <color name="color_393939">#393939</color>
    <color name="color_525252">#525252</color>
    <color name="color_ff8d8d8d">#FF8D8D8D</color>
    <color name="color_9e9e9e">#9e9e9e</color>

    <!--主要的主题色 start-->
    <color name="colorAccent">@color/accent_purple</color>
    <color name="colorAccent_o40">@color/accent_purple_o40</color>
    <color name="colorAccent_o80">@color/accent_purple_o80</color>
    <color name="colorAccent_disable">@color/accent_purple_o50</color>
    <color name="page_background_v1">@color/color_262626</color>
    <color name="panel_body_v1">@color/color_393939</color>
    <color name="panel_header_v1">@color/color_525252</color>
    <color name="title_bar_background_color">@color/color_2c2c2c</color>
    <!--page_background_v1上层,和title bar同层的背景色-->
    <color name="colorBgDark">@color/color_2c2c2c</color>
    <!--page_background_v1上层，colorBgDark同层的背景色-->
    <color name="colorBgMedium">@color/color_393939</color>
    <!--colorBgMedium上层的背景色-->
    <color name="colorBgLight">@color/color_525252</color>
    <color name="text_hint_v1">@color/color_ff8d8d8d</color>
    <color name="colorTextSecondary">@color/color_9e9e9e</color>
    <color name="colorBgItem">@color/color_323232</color>
    <color name="text_blue_color">@color/accent_purple</color>
    <color name="text_purple_color">@color/colorAccent</color>
    <color name="text_white_color">@color/white</color>
    <color name="text_grey_color">@color/color_9e9e9e</color>
    <!--主要的主题色 end-->

    <color name="color_switch_track_on">@color/colorAccent</color>
    <color name="color_switch_track_off">#E3E3E3</color>
    <color name="color_switch_thumb_on">#FFFFFF</color>
    <color name="color_switch_thumb_stroke">#CFD1CF</color>
    <color name="color_bg_input">@color/color_2e2e2e</color>
    <color name="color_delete_can">#E52628</color>

    <color name="colorPrimary">#212121</color>
    <color name="colorPrimaryDark">#000000</color>
    <color name="colorBackground">#E2E2E2</color>
    <color name="color_primary_text">#FF353535</color>
    <color name="white">@android:color/white</color>
    <color name="green_focused">#388E3C</color>
    <color name="new_grey">#999999</color>
    <color name="colorBgEnd">#530a0a</color>
    <color name="red">@android:color/holo_red_dark</color>
    <color name="gray_line">#CCC</color>
    <color name="black">#000000</color>

    <!-- copy from sync -->
    <color name="sync_purple">@color/accent_purple</color>
    <color name="widget_tabtophoto_bg">#FFFFE1</color>

    <!-- Sync控件 TabToPhoto -->
    <color name="sync_widget_tabtophoto_bg">#FFFFE1</color>
    <color name="sync_blue_light2">#33B5E5</color>
    <color name="divider_gray">#616161</color>
    <color name="yard_check_gray">#908b8b</color>
    <color name="status_error">#E53935</color>
    <color name="status_progress">#000000</color>
    <color name="status_lock">#BDBDBD</color>
    <color name="status_done">@color/accent_purple</color>
    <color name="status_unlock">#9575CD</color>
    <color name="status_new">#9575CD</color>
    <color name="status_tab_selected">#689F38</color>
    <color name="status_tab_unselect">#000000</color>
    <color name="door_checkin_blue">#0288D1</color>
    <color name="shipping_account_green">#9CCC65</color>
    <color name="exception_red">#cf2a27</color>
    <color name="done_green">#b6d7a8</color>
    <color name="inprogress_black">#333333</color>
    <color name="pick_green">#009e0f</color>
    <color name="text_grey">#666666</color>
    <color name="pick_task_blue">#96bdf0</color>
    <color name="pick_task_grey">#A0A0A0</color>
    <color name="bg_load_tab_unselected">#000000</color>
    <color name="damage_light_blue">#9fc5f8</color>
    <color name="hint_color">#CFD8DC</color>
    <color name="load_task_list_gray">#F9F9F9</color>

    <color name="scan_mark_color">#********</color>
    <color name="scan_laser_color">#4CAF50</color>
    <color name="sync_widget_tabtophoto_tab_pressed">#AAAAA1</color>

    <!-- squarecamera begin -->
    <color name="squarecamera__red">#F0001D</color>
    <color name="squarecamera__pressed_red">#B3F0001D</color>
    <color name="squarecamera__white">#FFFFFF</color>
    <color name="squarecamera__pressed_white">#99FFFFFF</color>
    <!-- squarecamera end -->

    <!-- scanner begin -->
    <color name="viewfinder_mask">#********</color>
    <color name="viewfinder_laser">#ffcc0000</color>
    <color name="viewfinder_border">#ffafed44</color>
    <!-- scanner end -->

    <!-- scanner v1 begin -->
    <color name="viewfinder_v1_mask">#00000000</color>
    <color name="viewfinder_v1_laser">#ffcc0000</color>
    <color name="viewfinder_v1_border">@color/colorAccent</color>
    <!-- scanner v1 end -->

    <!-- replenish begin -->
    <color name="replenish_grey">#424242</color>
    <color name="replenish_scan_location_grey">#616161</color>
    <color name="replenish_teal">#64FFDA</color>
    <color name="replenish_red">#B71C1C</color>
    <!-- replenish end -->
    <color name="count_text_bg">#ea0606</color>
    <color name="count_btn_bg_normal">#00ffffff</color>
    <color name="count_btn_bg_pressed">#DF363E</color>
    <color name="count_btn_text_normal">#b6b6b6</color>
    <color name="count_btn_text_pressed">#ffffff</color>

    <color name="take_photo_btn_blue">@color/colorAccent</color>
    <color name="take_photo_btn_red">#D63933</color>
    <color name="btn_bg_stroke_gray">#CECECE</color>
    <color name="prompt_err_red">#E52628</color>
    <color name="prompt_suc_green">#39AC6E</color>
    <color name="dialog_title_black">#444545</color>
    <color name="dialog_content_black">#989A9C</color>
    <color name="text_employee_id_yellow_bg">#FFF6E6</color>
    <color name="text_employ_id_yellow">#CE7129</color>

    <color name="edit_enable_false_hint_v1">#777879</color>
    <color name="color_48b976">#48b976</color>
    <color name="color_872e2d">#872e2d</color>
    <color name="color_646566">#646566</color>
    <color name="color_f4f4f4">#f4f4f4</color>

    <color name="color_ffc13f">#ffc13f</color>
    <color name="color_da2d23">#da2d23</color>
    <color name="color_black_70">#4D000000</color>
    <color name="color_373838">#373838</color>
    <color name="color_ffb1b1">#FFB1B1</color>
    <color name="color_01530d">#01530D</color>
    <color name="color_262e41">#262E41</color>
    <color name="color_323232">#323232</color>
    <color name="color_67b973">#67B973</color>
    <color name="color_cecece">#CECECE</color>
    <color name="color_713f12">#713F12</color>
    <color name="color_facc15">#FACC15</color>
    <color name="primary_grey_g700">#FF777879</color>

    <color name="color_2e2e2e">#2e2e2e</color>
    <color name="color_2a2a2a">#2a2a2a</color>
    <color name="color_8f8f8f">#8f8f8f</color>

    <color name="color_575757">#575757</color>
    <color name="color_434444">#434444</color>
    <color name="color_6e6e6e">#6e6e6e</color>
    <color name="color_1a1a1a">#1a1a1a</color>

    <color name="color_53565a">#53565a</color>
    <color name="color_999b9d">#999b9d</color>
    <color name="color_4f4f50">#4f4f50</color>
    <color name="color_c4c4c4">#c4c4c4</color>

    <color name="accent_green_v1">#56C288</color>
    <color name="accent_red_v1">#EF4134</color>
    <color name="card_v1">@color/colorBgDark</color>

    <color name="color_222222">#222222</color>
    <color name="color_C4C4C4">#C4C4C4</color>
    <color name="color_373737">#373737</color>
    <color name="color_6f6f6f">#6f6f6f</color>
    <color name="color_a0a0a0">#a0a0a0</color>
    <color name="color_595959">#595959</color>
    <color name="color_88898B">#88898B</color>

    <color name="color_ef4134">#EF4134</color>
    <color name="color_4c4e4e">#4C4E4e</color>
    <color name="color_333333">#333333</color>
    <color name="color_521b17">#521B17</color>
    <color name="color_897915">#897915</color>
    <color name="color_f0cc4d">#F0CC4D</color>
    <color name="color_214d36">#214D36</color>
    <color name="color_6c6c6c">#6C6C6C</color>
    <color name="color_4c535d">#4C535D</color>
    <color name="color_393e46">#393E46</color>
    <color name="color_740604">#740604</color>
    <color name="color_E52628">#E52628</color>
    <color name="color_5E5F5F">#5E5F5F</color>

    <color name="white_o75">#BFFFFFFF</color>
    <color name="white_o30">#4DFFFFFF</color>
    <color name="color_3B3C3C">#3B3C3C</color>
    <color name="color_CECECE">#CECECE</color>
    <color name="color_B3B4B5">#B3B4B5</color>
    <color name="color_777879">#777879</color>
    <color name="gray_777879c">#777879</color>
    <color name="gray_989A9C">#989A9C</color>
    <color name="bg_dialog_v1">#FF1E1E1E</color>
    <color name="primary_grey_g900">#FF444545</color>
    <color name="primary_grey_g100">#FFECECEC</color>
    <color name="bg_red_v1">#FF9A3F3A</color>
    <color name="green_56C288">#56C288</color>
    <color name="gray_656565">#656565</color>
    <color name="gray_cccc">#cccccc</color>
    <color name="gray_929292">#929292</color>
    <color name="color_2FC2E6">#2FC2E6</color>
    <color name="color_382FC2E6">#382FC2E6</color>
    <color name="color_9645D2">#9645D2</color>
    <color name="extended_yellow_100">#FFFFF6E6</color>
    <color name="extended_yellow_700">#FFebac29</color>
    <color name="color_18a0fb">#18A0FB</color>
    <color name="task_light_green">#b6d7a8</color>
</resources>