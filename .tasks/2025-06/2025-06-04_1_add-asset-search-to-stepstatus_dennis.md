# Context
Task file name: 2025-06-04_1
Created at: 2025-06-04_09:32:02
Created by: dennis
Issue: —

# Task Description
在StepStatusViewModel中添加一个Kotlin高级函数，用于在启动步骤时搜索资产。具体要求：
1. 参考ClaimForkliftDialog中的searchAsset实现方式
2. 当start时，如果assetId为空，调用此函数执行搜索
3. 如果search Asset失败，也要正常执行start
4. 保持现有start功能的完整性

# Background Info
需要修改StepStatusViewModel.kt，添加资产搜索功能，类似于ClaimForkliftDialog.kt中的实现。实现应该是非阻塞的，并且要优雅地处理失败情况。

# Task Analysis
- 目的：为StepStatusViewModel添加资产搜索能力
- 需要解决的问题：
  - 需要在步骤启动前集成资产搜索
  - 必须优雅处理搜索失败的情况
  - 保持现有启动功能的完整性
- 实现目标：
  - 创建资产搜索的高级函数
  - 与现有的startStep函数集成
  - 适当处理成功/失败情况

# Steps to take
1. 分析ClaimForkliftDialog的searchAsset实现
2. 在StepStatusViewModel中创建类似的函数
3. 修改startStep以在assetId为空时使用新函数
4. 添加适当的错误处理
5. 测试实现

# Current step: 4

# Original task template
[Task File Template content as shown above]

# Original steps
[Steps to Follow content as shown above]

# Notes
—

# Task Progress
- 2025-06-04_09:32:02: 任务分析完成，开始实现
- 2025-06-04_09:32:02: 已实现searchAsset函数和修改startStep函数
- 2025-06-04_09:32:02: 代码修改已完成，等待用户确认

# Final Review
[待完成] 