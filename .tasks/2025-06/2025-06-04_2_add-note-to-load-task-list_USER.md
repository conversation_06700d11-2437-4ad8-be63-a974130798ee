# Context
Task file name: 2025-06-04_2
Created at: 2025-06-04_16:46:26
Created by: USER
Issue: —

# Task Description
Add a "note" display to the `item_load_task_list.xml` layout so that each load task item can show a note or remark.

# Background Info (Optional)
—
# Task Analysis
- Purpose of the task: To display a "note" field in the load task list item UI, providing additional information or remarks for each task.
- Issues identified:
  - The current layout does not have a UI element for displaying a note.
  - Users may not be able to see important remarks or notes associated with each load task.
- Why it needs resolution:
  - Displaying notes can improve user awareness and context for each task.
  - It enhances the usability and informativeness of the task list.
- Implementation details and goals:
  - Add a new TextView (or similar) to the layout to display the note.
  - Ensure the new element fits visually and does not disrupt the existing layout.
  - The note should be clearly labeled (e.g., "Note:") and styled consistently with the rest of the UI.
  - If string resources are needed (e.g., for the label "Note"), follow the multilingual string resource management rules.
- Other useful reference details:
  - The layout file is located at: `app-wms/src/main/res/layout/item_load_task_list.xml`
  - The string resource for "Note" may need to be added if not already present, using the automated command line process for multilingual support.

# Steps to take
- Add a "note" TextView to the layout file.
- Add a string resource for the "Note" label using the automated process if not present.
- Ensure the new UI element is styled and positioned appropriately.
- Test the layout to confirm the note displays correctly.
- DO NOT REMOVE

# Current step: 1

# Original task template
# Context
Task file name: [TASK_FILE_NAME]
Created at: [DATETIME]
Created by: [USER_NAME]
Issue: [JIRA_ISSUE_NUMBER]

# Task Description
[A detailed description based on the [TASK] given by the user.]

# Background Info (Optional)
[A detailed background information about the task and related context. If none, add "—".]

# Task Analysis
- Purpose of the [TASK].
- Issues identified, including:
  - Problems caused.
  - Why it needs resolution.
  - Implementation details and goals.
- Other useful reference details.

# Steps to take
[List of actionable steps for the task]
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Current step: [The number of the current step]

# Original task template
[The ENTIRE unedited "Task File Template"]
- Copy and paste the ENTIRE unedited "Task File Template" into this section, __including all the details__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Original steps
[The ENTIRE unedited "Steps to Follow" section]
- Copy and paste the ENTIRE unedited "Steps to Follow" section into this section, __including all the details under each step__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Notes
—

# Task Progress
- Updates must include:
  - Mandatory:
    - 2025-06-04_16:46:26.
    - SUCCESSFUL/UNSUCCESSFUL, after user confirmation
  - Optional:
    - Findings, solutions, blockers, and results.
    - All updates must be logged chronologically.

# Final Review
[To be filled in only after task completion.] 