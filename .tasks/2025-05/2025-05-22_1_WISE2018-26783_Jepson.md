# Context
Task file name: 2025-05-22_1_WISE2018-26783_Je<PERSON>on
Created at: 2025-05-22_14:53:59
Created by: Jepson
Issue: WISE2018-26783

# Task Description
在 reactivemvi 模块下创建一个MVP模式下的Presenter，并且需要绑定Android中的Lifecycle进行生命周期管理。

# Background Info (Optional)
项目当前已有MVP基础类实现，但需要增强Presenter以支持Android Lifecycle组件的集成，以便更好地管理组件的生命周期。

# Task Analysis
- 目的是创建一个支持生命周期管理的MVP Presenter
- 识别的问题：
  - 现有的BasePresenter缺少与Android Lifecycle组件的直接集成
  - 当前的实现依赖于Activity/Fragment手动管理Presenter的生命周期
  - 需要一种更自动化的方式来响应生命周期事件
- 实现目标：
  - 创建一个LifecyclePresenter类，继承自BasePresenter
  - 实现LifecycleObserver接口，以响应生命周期事件
  - 提供方便的方法，允许在特定生命周期状态下执行任务

# Steps to take
1. 查阅现有的MVP相关代码，了解当前实现
2. 检查现有的Lifecycle相关代码和依赖
3. 创建LifecycleAwarePresenter接口，定义生命周期感知的方法
4. 创建LifecyclePresenter类，实现上述接口并提供基础实现
5. 更新MVPBaseActivity和MVPBaseFragment以使用新的生命周期感知Presenter
6. 测试新的Presenter实现
7. 完成文档注释
DO NOT REMOVE

# Current step: 7

# Original task template
```markdown
# Context
Task file name: [TASK_FILE_NAME]
Created at: [DATETIME]
Created by: [USER_NAME]
Issue: [JIRA_ISSUE_NUMBER]

# Task Description
[A detailed description based on the [TASK] given by the user.]

# Background Info (Optional)
[A detailed background information about the task and related context. If none, add "—".]

# Task Analysis
- Purpose of the [TASK].
- Issues identified, including:
  - Problems caused.
  - Why it needs resolution.
  - Implementation details and goals.
- Other useful reference details.

# Steps to take
[List of actionable steps for the task]
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Current step: [The number of the current step]

# Original task template
[The ENTIRE unedited "Task File Template"]
- Copy and paste the ENTIRE unedited "Task File Template" into this section, __including all the details__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Original steps
[The ENTIRE unedited "Steps to Follow" section]
- Copy and paste the ENTIRE unedited "Steps to Follow" section into this section, __including all the details under each step__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Notes
[Iteration notes during the task. If none, add "—".]

# Task Progress
- Updates must include:
  - Mandatory:
    - [DATETIME].
    - SUCCESSFUL/UNSUCCESSFUL, after user confirmation
  - Optional:
    - Findings, solutions, blockers, and results.
    - All updates must be logged chronologically.

# Final Review
[To be filled in only after task completion.]
```
DO NOT REMOVE

# Original steps
### **1. Task Analysis**
1. Examine the [TASK], related code, and functionality step-by-step.
2. Identify issues and document findings in "Task Analysis."
3. Confirm with the user before proceeding.
   - Combine any iterative changes here.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Wait for user confirmation that your analysis is complete, if not, iterate on this

---

### **2. Task File Creation**
1.  **Determine Project Root:** Identify the root directory of the project associated with this `task-framework.mdc` file. Let this be `[PROJECT_ROOT]`.
   * IMPORTANT: Always use the current working directory where the `task-framework.mdc` file is located as the project root.
   * The task file MUST be created within the SAME project where this `task-framework.mdc` file is located.
   * DO NOT create task files in any other project directory that might be open.
   * Before proceeding, verify the project path with `pwd` to confirm you're in the correct project directory.
2.  **Determine Directory Name and Path:**
    *   Use the [DATETIME] value (format: `yyyy-MM-dd_HH:mm:ss`) to extract the year and month in `YYYY-MM` format by taking the first 7 characters of [DATETIME] (e.g., `2024-07`). This value is `[DIRECTORY_NAME]`.
    *   Construct the target directory path: `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`.
    *   Ensure this directory exists. If it does not exist, **you must create it**.
3.  **Determine Task File Name Prefix (`[TASK_FILE_NAME]`):**
    *   Use the [DATETIME] value to extract the current date in `YYYY-MM-DD` format by taking the first 10 characters of [DATETIME] (e.g., `2024-07-19`).
    *   Determine the next sequential number for tasks created *on this date* within the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`). To achieve this:
        *   List the contents of the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`).
        *   Count how many existing file names within that directory begin with the current date prefix (`YYYY-MM-DD_`).
        *   The sequential number for the new file is this count plus 1.
    *   Combine the date extracted from [DATETIME] and the sequential number using an underscore: `YYYY-MM-DD_SEQUENTIAL_NUMBER` (e.g., `2024-07-19_1`). This combined value is `[TASK_FILE_NAME]`.
4.  **Determine User Name (`[USER_NAME]`):** Obtain the user's name (e.g., by checking Git configuration via `git config user.name` or asking the user if necessary). This value is `[USER_NAME]`.
5.  **Construct Full File Path:** Assemble the final, complete path for the task file using the determined components:
    `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]/[TASK_FILE_NAME]_[TASK_IDENTIFIER]_[USER_NAME].md`
    *(Example: `/path/to/project/.tasks/2024-07/2024-07-19_1_fix-login-bug_JaneDoe.md`)*
6.  **Create and Populate File:**
    *   Create an empty file at the constructed path.
    *   Populate this new file using the "Task File Template". Fill in all placeholders accurately, using the information gathered ([DATETIME], [USER_NAME], [JIRA_ISSUE_NUMBER], [TASK_FILE_NAME] itself for the context section) and incorporating the analysis from Step 1 and user input ([TASK] description, [BACKGROUND INFO]). **Crucially, ensure the 'Context' section is fully populated with:**
        *   `Task file name: [TASK_FILE_NAME]`
        *   `Created at: [DATETIME]`
        *   `Created by: [USER_NAME]`
        *   `Issue: [JIRA_ISSUE_NUMBER]`
7.  **Confirm with User:** Present the fully constructed file path and the proposed initial content of the task file to the user for confirmation.

> BEFORE CONTINUING:
> 1.  Show the proposed full path for the [TASK FILE].
> 2.  Show the proposed initial content for the [TASK FILE].
> 3.  Wait for user confirmation on both the path and the content before proceeding.

---

### **3. Iterate on the Task**
1. Analyze code context fully before changes.
2. Log all progress under "Task Progress" in the [TASK FILE].
3. For each change:
   - Seek user confirmation on updates.
   - Mark changes as SUCCESSFUL or UNSUCCESSFUL in the log.
4. Analyze updates under "Task Progress" to ensure you don't repeat previous mistakes or unsuccessful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. If you believe that you have finished the task, consult the user before continuing to the next step

---

### **4. Final Review**
1. Complete the "Final Review" in the [TASK FILE].
2. Summarize all successful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Confirm with the user before concluding the task

---

> # User Input:
> **[TASK]:** `<DESCRIBE YOUR TASK>`
> **[BACKGROUND INFO]:** `<ENTER BACKGROUND INFORMATION OR LINK TO FILE CONTAINING THE DETAILS (OPTIONAL)>`
DO NOT REMOVE

# Notes
—

# Task Progress
- 2025-05-22_14:53:59
  - 已完成任务分析
  - 创建了任务文件
  - SUCCESSFUL

- 2025-05-22_15:05:20
  - 已创建LifecycleAwarePresenter接口，定义了生命周期感知的方法
  - 已创建LifecyclePresenter类，实现了LifecycleAwarePresenter接口
  - 已创建MVPLifecycleBaseActivity和MVPLifecycleBaseFragment类
  - 添加了示例实现，包括SampleView接口、SamplePresenter类、SampleActivity和SampleFragment类
  - 创建了README.md文档，详细说明了如何使用LifecyclePresenter及相关组件
  - SUCCESSFUL

# Final Review
已成功实现生命周期感知的MVP Presenter架构。这个实现满足了初始需求，对Android Lifecycle组件进行了集成，使Presenter能够正确地响应生命周期事件。主要成就包括：

1. 创建了LifecycleAwarePresenter接口，定义了生命周期感知的方法，如whenAtLeast、whenCreated等
2. 实现了LifecyclePresenter类，提供了上述方法的具体实现，并增加了生命周期事件回调
3. 提供了MVPLifecycleBaseActivity和MVPLifecycleBaseFragment基类，便于在Activity和Fragment中使用
4. 创建了详细的示例实现，展示了如何使用这些新组件
5. 创建了详细的文档，包括使用方法、关键优势和示例代码

这个实现有效地解决了以下问题：
- 自动化了生命周期管理，减少了手动处理的复杂性
- 确保只在适当的生命周期状态下执行操作，避免常见的生命周期相关问题
- 与现有的MVP架构无缝集成，同时提供了更现代的生命周期管理方式
- 充分利用了Kotlin协程和Android Architecture Components的功能 