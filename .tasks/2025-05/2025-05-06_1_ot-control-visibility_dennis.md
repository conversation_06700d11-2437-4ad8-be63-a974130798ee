# Context
Task file name: 2025-05-06_1
Created at: 2025-05-06_10:17:52
Created by: dennis
Issue: 

# Task Description
Conditionally hide OT Control features based on facility settings in HomeTaskFragment and add a getFacility method to HomeTaskViewModel.

# Background Info (Optional)
Currently, the HomeTaskFragment always attempts to query OT requests and shows the OT request control UI element if the user is an OT supervisor or approver. However, we need to add an additional condition to check if the facility settings allow OT control before displaying these features.

# Task Analysis
- Purpose of the task is to ensure that OT Control features are only displayed when both:
  1. The user has appropriate permissions (OT supervisor or approver)
  2. The facility settings allow OT control (allowOTControl is true)
- Issues identified, including:
  - Unnecessary API calls when facility doesn't support OT control
  - UI elements showing when they should be hidden based on facility settings
  - Missing getFacility method in the HomeTaskViewModel
- Implementation details and goals:
  1. Modify HomeTaskViewModel to check facility settings before querying OT requests
  2. Update HomeTaskFragment to hide UI elements when facility settings don't allow OT control
  3. Add a getFacility method to HomeTaskViewModel that returns the repository's facilityEntity

# Steps to take
1. Add getFacility method to HomeTaskViewModel to return repository.facilityEntity
2. Modify the init block in HomeTaskViewModel to check facility settings before querying OT requests
3. Update HomeTaskFragment to check facility settings before showing OT-related UI elements
4. Test the changes to ensure proper functionality
- DO NOT REMOVE

# Current step: 4

# Original task template
```markdown
# Context
Task file name: [TASK_FILE_NAME]
Created at: [DATETIME]
Created by: [USER_NAME]
Issue: [JIRA_ISSUE_NUMBER]

# Task Description
[A detailed description based on the [TASK] given by the user.]

# Background Info (Optional)
[A detailed background information about the task and related context. If none, add "—".]

# Task Analysis
- Purpose of the [TASK].
- Issues identified, including:
  - Problems caused.
  - Why it needs resolution.
  - Implementation details and goals.
- Other useful reference details.

# Steps to take
[List of actionable steps for the task]
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Current step: [The number of the current step]

# Original task template
[The ENTIRE unedited "Task File Template"]
- Copy and paste the ENTIRE unedited "Task File Template" into this section, __including all the details__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Original steps
[The ENTIRE unedited "Steps to Follow" section]
- Copy and paste the ENTIRE unedited "Steps to Follow" section into this section, __including all the details under each step__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Notes
[Iteration notes during the task. If none, add "—".]

# Task Progress
- Updates must include:
  - Mandatory:
    - [DATETIME].
    - SUCCESSFUL/UNSUCCESSFUL, after user confirmation
  - Optional:
    - Findings, solutions, blockers, and results.
    - All updates must be logged chronologically.

# Final Review
[To be filled in only after task completion.]
```
- DO NOT REMOVE

# Original steps
### **1. Task Analysis**
1. Examine the [TASK], related code, and functionality step-by-step.
2. Identify issues and document findings in "Task Analysis."
3. Confirm with the user before proceeding.
   - Combine any iterative changes here.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Wait for user confirmation that your analysis is complete, if not, iterate on this

---

### **2. Task File Creation**
1.  **Determine Project Root:** Identify the root directory of the project associated with this `task-framework.mdc` file. Let this be `[PROJECT_ROOT]`.
   * IMPORTANT: Always use the current working directory where the `task-framework.mdc` file is located as the project root.
   * The task file MUST be created within the SAME project where this `task-framework.mdc` file is located.
   * DO NOT create task files in any other project directory that might be open.
   * Before proceeding, verify the project path with `pwd` to confirm you're in the correct project directory.
2.  **Determine Directory Name and Path:**
    *   Use the [DATETIME] value (format: `yyyy-MM-dd_HH:mm:ss`) to extract the year and month in `YYYY-MM` format by taking the first 7 characters of [DATETIME] (e.g., `2024-07`). This value is `[DIRECTORY_NAME]`.
    *   Construct the target directory path: `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`.
    *   Ensure this directory exists. If it does not exist, **you must create it**.
3.  **Determine Task File Name Prefix (`[TASK_FILE_NAME]`):**
    *   Use the [DATETIME] value to extract the current date in `YYYY-MM-DD` format by taking the first 10 characters of [DATETIME] (e.g., `2024-07-19`).
    *   Determine the next sequential number for tasks created *on this date* within the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`). To achieve this:
        *   List the contents of the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`).
        *   Count how many existing file names within that directory begin with the current date prefix (`YYYY-MM-DD_`).
        *   The sequential number for the new file is this count plus 1.
    *   Combine the date extracted from [DATETIME] and the sequential number using an underscore: `YYYY-MM-DD_SEQUENTIAL_NUMBER` (e.g., `2024-07-19_1`). This combined value is `[TASK_FILE_NAME]`.
4.  **Determine User Name (`[USER_NAME]`):** Obtain the user's name (e.g., by checking Git configuration via `git config user.name` or asking the user if necessary). This value is `[USER_NAME]`.
5.  **Construct Full File Path:** Assemble the final, complete path for the task file using the determined components:
    `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]/[TASK_FILE_NAME]_[TASK_IDENTIFIER]_[USER_NAME].md`
    *(Example: `/path/to/project/.tasks/2024-07/2024-07-19_1_fix-login-bug_JaneDoe.md`)*
6.  **Create and Populate File:**
    *   Create an empty file at the constructed path.
    *   Populate this new file using the "Task File Template". Fill in all placeholders accurately, using the information gathered ([DATETIME], [USER_NAME], [JIRA_ISSUE_NUMBER], [TASK_FILE_NAME] itself for the context section) and incorporating the analysis from Step 1 and user input ([TASK] description, [BACKGROUND INFO]). **Crucially, ensure the 'Context' section is fully populated with:**
        *   `Task file name: [TASK_FILE_NAME]`
        *   `Created at: [DATETIME]`
        *   `Created by: [USER_NAME]`
        *   `Issue: [JIRA_ISSUE_NUMBER]`
7.  **Confirm with User:** Present the fully constructed file path and the proposed initial content of the task file to the user for confirmation.

> BEFORE CONTINUING:
> 1.  Show the proposed full path for the [TASK FILE].
> 2.  Show the proposed initial content for the [TASK FILE].
> 3.  Wait for user confirmation on both the path and the content before proceeding.

---

### **3. Iterate on the Task**
1. Analyze code context fully before changes.
2. Log all progress under "Task Progress" in the [TASK FILE].
3. For each change:
   - Seek user confirmation on updates.
   - Mark changes as SUCCESSFUL or UNSUCCESSFUL in the log.
4. Analyze updates under "Task Progress" to ensure you don't repeat previous mistakes or unsuccessful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. If you believe that you have finished the task, consult the user before continuing to the next step

---

### **4. Final Review**
1. Complete the "Final Review" in the [TASK FILE].
2. Summarize all successful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Confirm with the user before concluding the task

---

> # User Input:
> **[TASK]:** `<DESCRIBE YOUR TASK>`
> **[BACKGROUND INFO]:** `<ENTER BACKGROUND INFORMATION OR LINK TO FILE CONTAINING THE DETAILS (OPTIONAL)>`
- DO NOT REMOVE

# Notes
—

# Task Progress
- 2025-05-06_10:17:52: Task file created. Ready to start implementation.
- 2025-05-06_10:30:10: Added getFacility method to HomeTaskViewModel that returns repository.facilityEntity. SUCCESSFUL
- 2025-05-06_10:30:40: Modified init block in HomeTaskViewModel to check facility settings before querying OT requests. SUCCESSFUL 
- 2025-05-06_10:31:15: Updated HomeTaskFragment to check facility settings before showing OT-related UI elements. SUCCESSFUL
- 2025-05-06_10:31:45: Modified queryOtRequests method to check facility settings before making the API call. SUCCESSFUL
- 2025-05-06_10:32:20: Fixed visibility condition in the subscribeToUiState method in HomeTaskFragment. SUCCESSFUL

# Final Review
All steps have been completed successfully:

1. Added getFacility method to HomeTaskViewModel that returns repository.facilityEntity.
2. Modified the HomeTaskViewModel to check facility settings before querying OT requests.
3. Updated the HomeTaskFragment to check facility settings before showing OT-related UI elements.
4. Modified queryOtRequests method to check facility settings before making the API call.

The implementation now ensures that OT Control features are only shown and queried when both:
1. The user has appropriate permissions (OT supervisor or approver)
2. The facility settings allow OT control (allowOTControl is true) 