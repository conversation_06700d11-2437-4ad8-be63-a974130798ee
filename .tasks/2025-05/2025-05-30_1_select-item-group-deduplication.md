# Context
Task file name: 2025-05-30_1_select-item-group-deduplication
Created at: 2025-05-30_09:42:09
Created by: AI Assistant
Issue: WMS-ANDROID-2

# Task Description
Implement deduplication for selectItemGroup in CollectItemInfoViewModel to prevent duplicate group IDs when adding new item groups.

# Background Info
In the CollectItemInfoViewModel, when updating item specifications, there's a potential issue where the same item group ID could be added multiple times to the groupIds list. This happens in the getUpdateItemSpecRequest() method where new group IDs are added without checking for duplicates.

# Task Analysis
- Purpose: Prevent duplicate item group IDs in the groupIds list
- Issues identified:
  - Current implementation uses addToNewList() which doesn't check for duplicates
  - This could lead to redundant group IDs in the database
  - Need to ensure each group ID appears only once in the list
- Implementation goals:
  - Modify the group ID addition logic to prevent duplicates
  - Maintain existing functionality while adding deduplication
  - Ensure backward compatibility

# Steps to take
1. [x] Analyze current implementation of group ID addition
2. [x] Implement deduplication logic for group IDs
3. [ ] Test the changes to ensure no duplicates are added
4. [ ] Verify backward compatibility
5. [ ] Update documentation if needed

DO NOT REMOVE

# Current step: 3

# Original task template
[The ENTIRE unedited "Task File Template"]
- Copy and paste the ENTIRE unedited "Task File Template" into this section, __including all the details__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Original steps
[The ENTIRE unedited "Steps to Follow" section]
- Copy and paste the ENTIRE unedited "Steps to Follow" section into this section, __including all the details under each step__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Notes
Implementation details:
- Replaced addToNewList() with explicit deduplication logic
- Added null safety checks for groupIds
- Used mutableListOf() for handling the list operations
- Added contains() check before adding new group ID

# Task Progress
- 2025-05-30_09:42:09: Task file created and initial analysis completed
- 2025-05-30_09:42:09: Implemented deduplication logic for group IDs
- 2025-05-30_09:42:09: Steps 1 and 2 completed successfully

# Final Review
[To be filled in only after task completion.] 