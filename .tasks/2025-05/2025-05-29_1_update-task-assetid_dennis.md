# Context
Task file name: 2025-05-29_1_update-task-assetid
Created at: 2025-05-29_15:33:32
Created by: dennis
Issue: N/A

# Task Description
Add a method in BaseTaskListViewModel.kt to update assetId to forkliftId for various task types including:
- ReceiveTask
- PickTask
- LoadTask
- PutawayTask
- MovementTask
- ReplenishmentTask

# Background Info
The BaseTaskListViewModel currently handles task lists and asset management. We need to add functionality to update the assetId field to forkliftId for specific task types.

# Task Analysis
- Purpose: Add a method to update assetId to forkliftId for specific task types
- Current Implementation:
  - BaseTaskListViewModel handles task lists and asset management
  - Has methods for saving and clearing forklift claims
  - Currently updates assetId directly
- Required Changes:
  - Add a new method to handle the assetId to forkliftId conversion
  - Support multiple task types
  - Maintain existing functionality

# Steps to take
1. [x] Analyze the current task types and their inheritance structure
2. [x] Create a new method to handle the assetId to forkliftId conversion
3. [x] Implement the conversion logic for each task type
4. [x] Add appropriate error handling and validation
5. [x] Test the implementation with different task types

# Current step: 5

# Original task template
[Task File Template content as shown in the framework]

# Original steps
[Steps to Follow content as shown in the framework]

# Notes
Implementation details:
1. Added updateTaskForkliftId method to handle individual task updates
2. Added updateTasksForkliftId method to handle list of tasks
3. Updated loadNewTaskList and loadProgressTaskList to use new functionality
4. Updated saveClaimForkliftByTaskStatus to use new functionality
5. Maintained existing assetId functionality while adding forkliftId support
6. Created Kotlin data classes for task update entities:
   - LoadTaskUpdateEntity
   - MovementTaskUpdateEntity
   - ReplenishmentTaskUpdateEntity
   - PickTaskUpdateEntity
7. Updated entity classes to implement Serializable interface
8. Added batch update endpoints to respective task APIs:
   - ReceiveTaskApi: PUT "wms/inbound/receive-task/batch-update"
   - MovementTaskApi: PUT "wms/inventory/movement-task/batch-update"
   - ReplenishmentTaskApi: PUT "wms/outbound/replenishment-task/batch-update"
   All endpoints return Response<BaseResponse<Void>>

# Task Progress
- 2025-05-29_15:33:32: Starting task analysis and implementation
- 2025-05-29_15:35:00: Added new methods for forkliftId updates
- 2025-05-29_15:36:00: Updated existing methods to use new functionality
- 2025-05-29_15:37:00: Implementation complete, ready for review
- 2025-05-29_15:38:00: Created and updated Kotlin data classes for task entities
- 2025-05-29_15:39:00: Updated entity classes to implement Serializable
- 2025-05-29_15:40:00: Added batch update endpoints to task APIs

# Final Review
Implementation completed with the following changes:
1. Added two new methods:
   - updateTaskForkliftId: Updates forkliftId for individual tasks
   - updateTasksForkliftId: Updates forkliftId for lists of tasks
2. Updated existing methods to use new functionality:
   - loadNewTaskList
   - loadProgressTaskList
   - saveClaimForkliftByTaskStatus
3. Maintained backward compatibility with existing assetId functionality
4. Added support for all required task types:
   - ReceiveTask
   - PickTask
   - LoadTask
   - PutawayTask
   - MovementTask
   - ReplenishmentTask
5. Created Kotlin data classes for task update entities:
   - LoadTaskUpdateEntity: Serializable data class with load-specific fields
   - MovementTaskUpdateEntity: Serializable data class with movement-specific fields
   - ReplenishmentTaskUpdateEntity: Serializable data class with replenishment-specific fields
   - PickTaskUpdateEntity: Serializable data class with pick-specific fields
6. All entity classes now implement Serializable interface for better data handling
7. Added batch update endpoints to task APIs:
   - ReceiveTaskApi: PUT endpoint for batch updating receive tasks
   - MovementTaskApi: PUT endpoint for batch updating movement tasks
   - ReplenishmentTaskApi: PUT endpoint for batch updating replenishment tasks
   All endpoints follow consistent pattern with Response<BaseResponse<Void>> return type 