package com.linc.platform.putaway.work;

import static com.linc.platform.putaway.work.PutAwayDataHandler.buildPutAwayLPDatas;
import static com.linc.platform.putaway.work.PutAwayDataHandler.buildPutAwayLPDatasByStep;
import static com.linc.platform.putaway.work.PutAwayDataHandler.getSelectedToPutAwayLPIds;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.R;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationItemCheckEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.putaway.work.base.PutAwayBaseService;
import com.linc.platform.toolset.lpputaway.model.PutAwayLPDetailEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayLPResultEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayLocationSuggestExclusionRequestEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayStepUpdateEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayStepViewEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayTaskEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.ResUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class PutAwayAction implements PutAwayBaseService {
    private FacilityEntry facility;
    private PutAwayTaskEntry task;
    private PutAwayStepViewEntry step;

    private List<PutAwayLPData> putAwayLPDatas;
    private LocationEntry putAwayLocation;
    private LocationEntry suggestLocation;
    private LocationEntry searchOrSuggestLocation;

    private int getSuggestLocationTimes = 0;
    private List<LocationEntry> abandonedSuggestLocations = new ArrayList<>();
    private List<String> customerIds = new ArrayList<>();

    private List<String> suggestLocationRecords = new ArrayList<>();

    /**
     * task & step & facility
     */

    public FacilityEntry getFacility() {
        return facility;
    }

    public void setFacility(FacilityEntry facility) {
        this.facility = facility;
    }

    public PutAwayTaskEntry getTask() {
        return task;
    }

    public void setTask(PutAwayTaskEntry task) {
        this.task = task;
    }

    public PutAwayStepViewEntry getStep() {
        return step;
    }

    public void setStep(PutAwayStepViewEntry step) {
        this.step = step;
        setPutAwayLPDatasByStep(step);
    }

    public boolean useDockCheckingNO() {
        return facility != null && facility.useDockCheckingNo;
    }

    @Override
    public boolean locationCheckingNOIsNotEmpty() {
        return !TextUtils.isEmpty(putAwayLocation.checkingNo);
    }

    public boolean isStepDone() {
        return step.status == StepStatusEntry.FORCE_CLOSED || step.status == StepStatusEntry.DONE;
    }

    public boolean isStepNotOwner(String userId) {
        return !Lists.ensureNotNull(step.assigneeUserIds).contains(userId);
    }

    public boolean isStepNew() {
        return step.status == StepStatusEntry.NEW;
    }

    public List<PutAwayLPData> getPutAwayLPDatas() {
        return putAwayLPDatas;
    }

    public void setPutAwayLPDatas(List<PutAwayLPDetailEntry> lpList, List<InventoryEntry> inventoryEntries, CustomerViewEntry customerViewEntry) {
        this.putAwayLPDatas = buildPutAwayLPDatas(lpList, inventoryEntries, customerViewEntry, isContainNotAllowSelectAllCustomerIds());
    }

    public void addPutAwayLPDatas(PutAwayLPResultEntry lpResult, List<InventoryEntry> inventoryEntries, CustomerViewEntry customerViewEntry) {
        this.putAwayLPDatas = Lists.ensureNotNull(putAwayLPDatas);
        this.putAwayLPDatas.addAll(0, buildPutAwayLPDatas(lpResult.lpList, inventoryEntries, customerViewEntry, isContainNotAllowSelectAllCustomerIds()));
    }

    public boolean isContainNotAllowSelectAllCustomerIds() {
        List<String> notAllowSelectAllCustomerIds = new ArrayList<>();
        if (getFacility() != null && getFacility().notAllowSelectAllAtPutawayTaskCustomerIds != null) {
            notAllowSelectAllCustomerIds = getFacility().notAllowSelectAllAtPutawayTaskCustomerIds;
        }
        return Stream.of(Lists.ensureNotNull(gePutAwayCustomerIds())).anyMatch(notAllowSelectAllCustomerIds::contains);
    }

    public void updatePutAwayLPDatas(List<PutAwayLPData> putAwayLPDatas) {
        this.putAwayLPDatas = putAwayLPDatas;
    }

    public void setPutAwayLPDatasByStep(PutAwayStepViewEntry step) {
        this.putAwayLPDatas = buildPutAwayLPDatasByStep(step);
    }

    public List<String> gePutAwayCustomerIds() {
        return customerIds;
    }

    public void addPutAwayCustomers(List<String> customerIds) {
        if (CollectionUtil.isNotNullOrEmpty(customerIds)) {
            Stream.of(customerIds).forEach(customerId -> {
                if(!this.customerIds.contains(customerId)) {
                    this.customerIds.add(customerId);
                }
            });
        }
    }

    public void setPutAwayCustomers(List<String> customerIds) {
        if (CollectionUtil.isNotNullOrEmpty(customerIds)) {
            this.customerIds = customerIds;
        }
    }

    public void selectAllLP(boolean isSelected) {
        for (PutAwayLPData putAwayLPData : putAwayLPDatas) {
            putAwayLPData.isSelected = isSelected;
        }
    }

    public void setLPSelectStatus(int position, boolean isSelected) {
        if (position < 0 || position >= putAwayLPDatas.size()) return;
        putAwayLPDatas.get(position).isSelected = isSelected;
    }

    public String verifyLPIsSelected() {
        if (PutAwayDataHandler.isEmptySelectedLP(putAwayLPDatas)) {
            return "No LP selected for suggest";
        }

        return "";
    }

    public List<String> getSuggestLocationCriteria() {
        return PutAwayDataHandler.getSelectedToPutAwayLPIds(putAwayLPDatas);
    }

    public PutAwayLocationSuggestExclusionRequestEntry getSuggestLocationWithExcludeCriteria() {
        PutAwayLocationSuggestExclusionRequestEntry requestEntry = new PutAwayLocationSuggestExclusionRequestEntry();
        requestEntry.lpIds = PutAwayDataHandler.getSelectedToPutAwayLPIds(putAwayLPDatas);
        requestEntry.excludeLocationIds = this.suggestLocationRecords;
        return requestEntry;
    }

    public LocationEntry getPutAwayLocation() {
        return putAwayLocation;
    }

    public void setPutAwayLocation(LocationEntry putAwayLocation) {
        this.putAwayLocation = putAwayLocation;
    }

    public LocationEntry getSuggestLocation() {
        return this.suggestLocation;
    }

    public void setSuggestLocation(LocationEntry suggestLocation) {
        if (!suggestLocationRecords.contains(suggestLocation.id)) {
            suggestLocationRecords.add(suggestLocation.id);
        }
        this.suggestLocation = suggestLocation;
    }

    public LocationEntry getSearchOrSuggestLocation() {
        return this.searchOrSuggestLocation;
    }

    public void setSearchOrSuggestLocation(LocationEntry searchOrSuggestLocation) {
        this.searchOrSuggestLocation = searchOrSuggestLocation;
    }

    public String verifySubmitData() {
        if (PutAwayDataHandler.isEmptySelectedLP(putAwayLPDatas)) {
            return "No LP selected";
        }

        if (putAwayLocation == null) {
            return "No Location selected";
        }

        // P&D location can only hold one LP
        if (putAwayLocation.isBufferZone() &&
                !CollectionUtil.isSingleItemCollection(getSelectedToPutAwayLPIds(putAwayLPDatas))
        ) {
            return ResUtil.getString(R.string.error_p_d_location_can_only_hold_one_lp);
        }

        return "";
    }

    public LocationItemCheckEntry buildLocationItemCheckEntry() {
        LocationItemCheckEntry checkEntry = new LocationItemCheckEntry();
        checkEntry.lpIds = PutAwayDataHandler.getSelectedToPutAwayLPIds(putAwayLPDatas);
        return checkEntry;
    }

    public PutAwayStepUpdateEntry buildPutAwayStepUpdateEntry() {
        PutAwayStepUpdateEntry updateEntry = new PutAwayStepUpdateEntry();
        updateEntry.locationId = putAwayLocation.id;
        if (putAwayLocation.isBufferZone()) {
            updateEntry.reservedForBufferZoneLocationId = putAwayLocation.belongToLocationId;
        }
        updateEntry.lpIds = PutAwayDataHandler.getSelectedToPutAwayLPIds(putAwayLPDatas);
        return updateEntry;
    }

    public void clear() {
        putAwayLocation = null;
        searchOrSuggestLocation = null;
        putAwayLPDatas.clear();
        getSuggestLocationTimes = 0;
        abandonedSuggestLocations.clear();
        suggestLocationRecords.clear();
    }

    public boolean notAllowOverridePutAwayLocation() {
        return facility != null && facility.notAllowOverridePutAwaySuggestLocation && CollectionUtil.isNotNullOrEmpty(facility.notAllowOverridePutAwaySuggestLocationCustomerIds);
    }

    public boolean allowGetSuggestWhenNotAllowOverride() {
        return facility != null && facility.enablePasswordApprovalAfterNumberOfAttempts != null && getSuggestLocationTimes < facility.enablePasswordApprovalAfterNumberOfAttempts;
    }

    public boolean notAllowPutawayWithMultipleCustomers() {
        return facility != null
                && facility.notAllowPutawayWithMultipleCustomers;
    }

    public void addAttemptGetSuggestLocationTimes() {
        getSuggestLocationTimes++;
        if (suggestLocation != null) {
            abandonedSuggestLocations.add(suggestLocation);
        }
    }

    public List<LocationEntry> getAbandonedSuggestLocations() {
        return abandonedSuggestLocations;
    }
}
