package com.linc.platform.infoclock.model

import com.google.gson.annotations.SerializedName

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/13
 */
data class PunchInReqEntry(@SerializedName("EmployeeId") var employeeId: String?,
                           @SerializedName("FirstName") var firstName: String?,
                           @SerializedName("LastName") var lastName: String?,
                           @SerializedName("Photo") var photo: String?,
                           @SerializedName("TimeZone") var timeZone: Int,
                           @SerializedName("IPAddress") var ipAddress: String?,
                           @SerializedName("Facility") var facility: String? = null,
                           @SerializedName("IsAutoPunchOt") var isAutoPunchOt: Boolean = false)