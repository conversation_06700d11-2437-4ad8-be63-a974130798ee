package com.linc.platform.infoclock

import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry
import com.linc.platform.infoclock.model.PunchInDetailEntry
import com.linc.platform.infoclock.model.PunchInHistoryEntry
import com.linc.platform.utils.TimeUtil
import java.math.RoundingMode

object InfoClockHelper {

    fun getToDayHours(employeeInfoEntry: InfoClockEmployeeViewEntry, punchList: List<PunchInDetailEntry>?): Double {
        val list = punchList?.filter { entry ->
            isTodayPunch(entry) && entry.firstName == employeeInfoEntry.firstName && entry.lastName == employeeInfoEntry.lastName
        }?.toList()
        return list?.sumOf { history -> history.getWorkHour() }?.toBigDecimal()?.setScale(2, RoundingMode.DOWN)?.toDouble() ?: 0.00
    }

    fun getWeeklyHours(employeeInfoEntry: InfoClockEmployeeViewEntry, weeklyPunchList: List<PunchInDetailEntry>?): Double {
        val inHistoryList: ArrayList<PunchInHistoryEntry> = ArrayList()
        val weekCycle = TimeUtil.getWeekDateList(System.currentTimeMillis())
        for (date in weekCycle) {
            val history = PunchInHistoryEntry(date)
            weeklyPunchList?.run {
                history.punchInInList = filter { entry ->
                    TimeUtil.getDateToString(
                        date,
                        TimeUtil.FORMAT_MM_DD_YYYY) == entry.punchDay && entry.firstName == employeeInfoEntry.firstName && entry.lastName == employeeInfoEntry.lastName
                }
            }
            inHistoryList.add(history)
        }
        return inHistoryList.sumOf { history -> history.getWorkHours() }.toBigDecimal().setScale(2, RoundingMode.DOWN)?.toDouble() ?: 0.00
    }

    private fun isTodayPunch(entry: PunchInDetailEntry): Boolean {// contain cross day punch
        val toDay = TimeUtil.getDateToString(
            System.currentTimeMillis(),
            TimeUtil.FORMAT_MM_DD_YYYY
        )
        val toDay2 = TimeUtil.getDateToString(
            System.currentTimeMillis(),
            TimeUtil.FORMAT_YYYY_MM_DD
        )
        val punchInDay = TimeUtil.getDateToString(
            TimeUtil.getStringToDate(entry.punchInTime, TimeUtil.FORMAT_FULL_T_FORMAT),
            TimeUtil.FORMAT_YYYY_MM_DD
        )
        val punchOutDay = TimeUtil.getDateToString(
            TimeUtil.getStringToDate(entry.punchOutTime, TimeUtil.FORMAT_FULL_T_FORMAT),
            TimeUtil.FORMAT_YYYY_MM_DD
        )
        return toDay == entry.punchDay || toDay2 == punchInDay || toDay2 == punchOutDay
    }


}