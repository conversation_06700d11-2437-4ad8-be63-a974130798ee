package com.linc.platform.pick.presenter.impl;

import static com.linc.platform.utils.CollectionUtil.isNullOrEmpty;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.linc.platform.R;
import com.linc.platform.baseapp.dal.LocationDal;
import com.linc.platform.baseapp.model.LocationSearchEntry;
import com.linc.platform.common.BarcodeType;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.common.lp.LPDal;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.api.OrderApi;
import com.linc.platform.foundation.model.ItemSNValidateRuleEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.foundation.model.LpEntry;
import com.linc.platform.foundation.model.OrderTypeEntry;
import com.linc.platform.http.HttpService;
import com.linc.platform.inventory.dal.InventoryDal;
import com.linc.platform.inventory.model.InventoryDetailEntry;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventory.model.InventorySearchEntry;
import com.linc.platform.pack.model.LPViewEntry;
import com.linc.platform.pick.dal.NewPickTaskDal;
import com.linc.platform.pick.dal.OperateWorkItemDal;
import com.linc.platform.pick.dal.PickTaskCenterDal;
import com.linc.platform.pick.model.ItemReturnToInventoryEntry;
import com.linc.platform.pick.model.OrderShippingSNSearchViewEntry;
import com.linc.platform.pick.model.OrderShippingSNViewEntry;
import com.linc.platform.pick.model.PickStrategy;
import com.linc.platform.pick.model.PickStrategyRebuildForReturnEntity;
import com.linc.platform.pick.model.PickTaskSearchEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.newpick.BarcodeTypeEntry;
import com.linc.platform.pick.presenter.ReturnPickedPresenter;
import com.linc.platform.pick.view.ReturnPickedView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.CustomerConfigUtil;
import com.linc.platform.utils.LPUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.linc.platform.utils.TrackingNumberHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by Gavin
 */

public class ReturnPickedPresenterImpl extends BaseApiDal implements ReturnPickedPresenter {
    public static final int SCANNER_TYPE_UPC = 0;
    public static final int SCANNER_TYPE_FROM_LP_OR_SN_OR_TRACKING = 1;
    public static final int SCANNER_TYPE_TO_LP = 2;
    public static final int SCANNER_TYPE_TO_LOCATION = 3;
    public static final int SCANNER_TYPE_SN = 4;
    public static final int SCANNER_TYPE_SOID = 10;
    public static final int SCANNER_TYPE_TRACKING = 11;

    public static final int SN_ERROR_NOT_A_SN = 5;
    public static final int SN_ERROR_WRONG_SN = 6;
    public static final int SN_ERROR_DUPLICATE_SN = 7;
    public static final int SN_ERROR_INVALID_LENGTH_SN = 8;
    public static final int SN_ERROR_INVALID_FORMAT_SN = 9;

    private ReturnPickedView view;
    private StepBaseEntry stepBaseEntry;
    private ItemSpecAPI itemSpecAPI;
    private OrderApi orderApi;
    private LocationDal locationDal;
    private LPDal lpDal;
    private OperateWorkItemDal operateWorkItemDal;
    private PickTaskCenterDal pickTaskCenterDal;
    private PickTaskViewEntry pickTaskViewEntry;
    private InventoryEntry returnInventoryEntry;
    private ItemSpecEntry returnItemSpecEntry;
    private NewPickTaskDal newPickTaskDal;
    private InventoryDal inventoryDal;
    private String scannedSNOrTracking = "";
    private final FunctionHelpPresenterImpl mFunctionHelpPresenterImpl;
    private String itemValidateRegex;

    public ReturnPickedPresenterImpl(ReturnPickedView returnPickedView, PickTaskViewEntry pickTaskViewEntry, StepBaseEntry stepBaseEntry) {
        this.view = returnPickedView;
        this.pickTaskViewEntry = pickTaskViewEntry;
        this.stepBaseEntry = stepBaseEntry;
        newPickTaskDal = new NewPickTaskDal();
        inventoryDal = new InventoryDal();
        itemSpecAPI = HttpService.createService(ItemSpecAPI.class);
        orderApi = HttpService.createService(OrderApi.class);
        lpDal = new LPDal();
        locationDal = new LocationDal();
        operateWorkItemDal = new OperateWorkItemDal();
        pickTaskCenterDal = new PickTaskCenterDal();
        mFunctionHelpPresenterImpl = new FunctionHelpPresenterImpl();
    }

    @Override
    public boolean isNeedCollectSOID() {
        return returnInventoryEntry != null && (CustomerConfigUtil.isNeedCollectSoId(returnInventoryEntry.customer, returnInventoryEntry.order) ||
                CustomerConfigUtil.isNeedPrintSoId(returnInventoryEntry.customer, returnInventoryEntry.order));
    }

    @Override
    public void getReturnItemDetail(String itemSpecId) {
        execute(itemSpecAPI.get(itemSpecId), itemSpecEntry -> {
            if (itemSpecEntry != null) {
                setReturnItemSpecEntry(itemSpecEntry);
            }
        }, errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()));
    }

    @Override
    public boolean validateSnSuccess(List<String> snList, List<String> scannedSn) {
        if (isNotSNBarcode(scannedSn)) {
            view.showSNError(SN_ERROR_NOT_A_SN);
            return false;
        }
        if (notInReturnInventorySNList(scannedSn)) {
            view.showSNError(SN_ERROR_WRONG_SN);
            return false;
        }
        if (scannedDuplicateSn(snList, scannedSn)) {
            view.showSNError(SN_ERROR_DUPLICATE_SN);
            return false;
        }
        if (snLengthUnequalPrevious(snList, scannedSn)) {
            view.showSNError(SN_ERROR_INVALID_LENGTH_SN);
            return false;
        }
        if (notMatchCustomerSnRegex(scannedSn)) {
            view.showSNError(SN_ERROR_INVALID_FORMAT_SN);
            return false;
        }
        return true;
    }

    @Override
    public String validateAddTracking(List<String> scannedList, String scannedTracking) throws Exception {
        String tracking = TrackingNumberHelper.interceptByCarrier(pickTaskViewEntry.orders, scannedTracking);

        if (scannedList.contains(tracking)) {
            throw new Exception(ResUtil.getString(R.string.msg_duplicate));
        }

        if (Stream.of(returnInventoryEntry.trackingEntryMap.keySet()).noneMatch(trackingNumber -> trackingNumber.equalsIgnoreCase(tracking))) {
            throw new Exception(ResUtil.format(R.string.error_scanned_tracking_not_match, tracking));
        }
        return tracking;
    }

    @Override
    public InventoryEntry getReturnInventoryEntry() {
        return returnInventoryEntry;
    }

    @Override
    public void setReturnInventoryEntry(InventoryEntry returnInventoryEntry) {
        this.returnInventoryEntry = returnInventoryEntry;
    }

    @Override
    public ItemSpecEntry getReturnItemSpecEntry() {
        return returnItemSpecEntry;
    }

    @Override
    public void setReturnItemSpecEntry(ItemSpecEntry itemSpecEntry) {
        this.returnItemSpecEntry = itemSpecEntry;
    }

    @Override
    public boolean isHasSn() {
        return !TextUtils.isEmpty(returnInventoryEntry.sn) || CollectionUtil.isNotNullOrEmpty(returnInventoryEntry.snList);
    }

    @Override
    public boolean isNoInventorySelectedToReturn() {
        return returnInventoryEntry == null;
    }

    @Override
    public void getBarcodeType(String barcode) {
        if (isPickToTracking() && !LPUtil.isLP(barcode)) {
            // When pickToTracking, only LP and tracking# are allowed to be scanned. So if this barcode
            // is not LP, we can treat it as tracking#.
            barcode = TrackingNumberHelper.interceptByCarrier(pickTaskViewEntry.orders, barcode);
        }
        newPickTaskDal.getBarcodeType(barcode, barcodeTypeEntries -> {
                    if (CollectionUtil.isNotNullOrEmpty(barcodeTypeEntries)) {
                        //相同SN有可能会有AVAILABLE和SHIPPED状态两条库存记录
                        List<BarcodeTypeEntry> distinctBarcodeTypeEntries = Stream.of(barcodeTypeEntries)
                                .distinctBy(barcodeTypeEntry -> barcodeTypeEntry.type + barcodeTypeEntry.value).collect(Collectors.toList());

                        view.getBarcodeTypeSuccess(distinctBarcodeTypeEntries);
                    } else {
                        view.barcodeTypeNotFound();
                    }
                },
                errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()));
    }

    @Override
    public void getReturnInventoriesByBarcodeType(BarcodeTypeEntry barcodeTypeEntry) {
        final SuccessHandler<Pair<InventoryDetailEntry, String>> inventoriesSuccessHandler = result -> {
            InventoryDetailEntry inventoryDetailEntry = result.first;
            String lpId = result.second;
            if (inventoryDetailEntry == null || CollectionUtil.isNullOrEmpty(inventoryDetailEntry.inventories)) {
                view.onGetTaskPickedInventoriesSuccess(new ArrayList<>());
                return;
            }

            List<InventoryEntry> pickedInventories = buildPickedInventories(barcodeTypeEntry, lpId, inventoryDetailEntry);
            List<InventoryEntry> inventoryEntries = CollectionUtil.spiltPickedInventoryEntries(pickedInventories, pickTaskViewEntry.pickHistories);

            if (Stream.of(Lists.ensureNotNull(inventoryEntries)).noneMatch(v -> v.itemSpecEntry.requireCollectSnOnShipping)) {
                view.onGetTaskPickedInventoriesSuccess(inventoryEntries);
                return;
            }

            loadOrderShippingSNEntries(inventoryEntries, shippingSNEntries -> {
                fillOrderShippingSN(inventoryEntries, shippingSNEntries);
                view.onGetTaskPickedInventoriesSuccess(inventoryEntries);
            });
        };
        InventorySearchEntry searchEntry = new InventorySearchEntry();
        searchEntry.pickTaskId = stepBaseEntry.taskId;
        if (barcodeTypeEntry.type == BarcodeType.TRACKING_NO) {
            getHlpByTrackingNumber(barcodeTypeEntry.value, lpViewEntry -> {
                inventoryDal.searchInventoryDetail(searchEntry,
                        inventoryDetailEntry -> inventoriesSuccessHandler.onSuccess(Pair.create(inventoryDetailEntry, lpViewEntry.id)));
            });
        } else {
            inventoryDal.searchInventoryDetail(searchEntry, inventoryDetailEntry -> {
                inventoriesSuccessHandler.onSuccess(Pair.create(inventoryDetailEntry, barcodeTypeEntry.isLPType() ? barcodeTypeEntry.value : null));
            });
        }
    }

    private void getHlpByTrackingNumber(String trackingNumber, SuccessHandler<LPViewEntry> successHandler) {
        lpDal.searchByTrackingNumber(trackingNumber, lpViewEntries -> {
            if (CollectionUtil.isNullOrEmpty(lpViewEntries)) {
                ToastUtil.showToast(R.string.message_invalid_tracking_number);
            } else {
                successHandler.onSuccess(lpViewEntries.get(0));
            }
        });
    }

    private List<InventoryEntry> buildPickedInventories(BarcodeTypeEntry barcodeTypeEntry, @Nullable String lpId, InventoryDetailEntry inventoryDetailEntry) {
        List<InventoryEntry> pickedInventories = new ArrayList<>();

        // Scan tracking# or HLP
        if (barcodeTypeEntry.type == BarcodeType.TRACKING_NO ||
                (barcodeTypeEntry.type == BarcodeType.HLP && lpId != null && inventoryDetailEntry.lpMap != null
                        && inventoryDetailEntry.lpMap.get(lpId) != null && StringUtil.isNotEmpty(inventoryDetailEntry.lpMap.get(lpId).lpTrackingNo))) {
            Stream.of(inventoryDetailEntry.inventories).forEach(inventory -> {
                LpEntry lp = inventoryDetailEntry.lpMap.get(inventory.lpId);
                if (lp != null) {
                    inventory.lpTrackingNo = lp.lpTrackingNo;
                }
            });
            InventoryEntry pickedTrackingInventory = Stream.of(inventoryDetailEntry.inventories)
                    .filter(inventory -> inventory.lpId.equalsIgnoreCase(lpId))
                    .findSingle().orElse(null);
            if (pickedTrackingInventory != null) {
                pickedInventories = Stream.of(inventoryDetailEntry.inventories)
                        .filter(inventory -> inventory.itemSpecId.equals(pickedTrackingInventory.itemSpecId) && TextUtils.equals(inventory.unitId, pickedTrackingInventory.unitId)).toList();
                Stream.of(pickedInventories).forEach(inventory -> inventory.isItemVerified = true);
                setScannedSNOrTracking(pickedTrackingInventory.lpTrackingNo);
            }
            return pickedInventories;
        }

        switch (barcodeTypeEntry.type) {
            case CLP:
            case ILP:
            case TLP:
            case HLP:
            case SLP:
                pickedInventories = Stream.of(inventoryDetailEntry.inventories).filter(inventory -> inventory.lpId.equals(lpId)).collect(Collectors.toList());
                break;
            case SN:
                InventoryEntry pickedSNInventory = Stream.of(inventoryDetailEntry.inventories).filter(inventory -> StringUtil.isNotEmpty(inventory.sn) && inventory.sn.equalsIgnoreCase(barcodeTypeEntry.value)).findSingle().orElse(null);

                if (pickedSNInventory != null) {
                    pickedInventories = Stream.of(inventoryDetailEntry.inventories)
                            .filter(inventory -> inventory.lpId.equals(pickedSNInventory.lpId) && inventory.itemSpecId.equals(pickedSNInventory.itemSpecId))
                            .collect(Collectors.toList());
                    Stream.of(pickedInventories).forEach(entry -> entry.isItemVerified = true);
                    setScannedSNOrTracking(barcodeTypeEntry.value);
                }
                break;
            default:
                pickedInventories = new ArrayList<>();
        }

        return pickedInventories;
    }

    private void loadOrderShippingSNEntries(List<InventoryEntry> inventories, SuccessHandler<List<OrderShippingSNViewEntry>> handler) {
        if (CollectionUtil.isNullOrEmpty(inventories)) {
            handler.onSuccess(new ArrayList<>());
            return;
        }

        OrderShippingSNSearchViewEntry entry = new OrderShippingSNSearchViewEntry();
        entry.lpIds = Stream.of(inventories).map(v -> v.lpId).toList();

        operateWorkItemDal.searchOrderShippingSN(entry, handler, error -> ToastUtil.showErrorToast(error.getErrorMessage()));
    }

    private void fillOrderShippingSN(List<InventoryEntry> inventories, List<OrderShippingSNViewEntry> shippingSNEntries) {
        if (isNullOrEmpty(inventories) || isNullOrEmpty(shippingSNEntries)) return;

        for (InventoryEntry inventory : inventories) {
            for (OrderShippingSNViewEntry shippingSNEntry : shippingSNEntries) {
                if (inventory.lpId.equals(shippingSNEntry.lpId) && inventory.itemSpecId.equals(shippingSNEntry.itemSpecId) && inventory.orderId.equals(shippingSNEntry.orderId)) {
                    inventory.snList.add(shippingSNEntry.sn);
                }
            }
        }
    }

    @Override
    public String getScannedSNOrTracking() {
        return scannedSNOrTracking;
    }

    private void setScannedSNOrTracking(String value) {
        this.scannedSNOrTracking = value;
    }

    private boolean notInReturnInventorySNList(List<String> scannedSn) {
        return returnInventoryEntry != null && Stream.of(scannedSn).anyMatch(sn -> !returnInventoryEntry.snList.contains(sn));
    }

    private boolean isNotSNBarcode(List<String> scannedSn) {
        return returnItemSpecEntry != null
                && Stream.of(scannedSn).anyMatch(sn -> sn.equals(returnItemSpecEntry.upcCode) || sn.equals(returnItemSpecEntry.upcCodeCase));
    }

    private boolean notMatchCustomerSnRegex(List<String> scannedSn) {
        if(pickTaskViewEntry == null){
            return false;
        }
        String itemRule=itemValidateRegex;
        if(TextUtils.isEmpty(itemRule)){
            if(pickTaskViewEntry.customerEntry != null && !TextUtils.isEmpty(pickTaskViewEntry.customerEntry.defaultItemSNValidateRegex)){
                itemRule=pickTaskViewEntry.customerEntry.defaultItemSNValidateRegex;
            }
        }
        if(!TextUtils.isEmpty(itemRule)){
            String finalItemRule = itemRule;
            return Stream.of(scannedSn).anyMatch(sn -> !sn.matches(finalItemRule));
        }
        return false;
    }

    private boolean snLengthUnequalPrevious(List<String> snList, List<String> scannedSn) {
        return CollectionUtil.isNotNullOrEmpty(snList)
                && Stream.of(scannedSn).anyMatch(sn -> snList.get(0).length() != sn.length());
    }

    private boolean scannedDuplicateSn(List<String> snList, List<String> scannedSn) {
        return CollectionUtil.isNotNullOrEmpty(scannedSn)
                && Stream.of(scannedSn).anyMatch(snList::contains);
    }

    @Override
    public void onLpAutoComplete(String lpId) {
        lpDal.idAutoComplete(Collections.singletonList(lpId), lpIds -> {
            if (Lists.ensureNotNull(lpIds).size() == 1) {
                getReturnToLp(lpIds.get(0));
            }
        });
    }

    private void getReturnToLp(String lpId) {
        lpDal.get(lpId, lpViewEntry -> view.onGetReturnToLpSuccess(lpViewEntry));
    }

    @Override
    public void getReturnToLocation(String locationName) {
        LocationSearchEntry searchEntry = new LocationSearchEntry();
        searchEntry.name = locationName;
        locationDal.search(searchEntry, locationEntries -> view.onGetReturnToLocationSuccess(locationEntries));
    }

    @Override
    public void getOriginalLocation(String originalReceiveLPId) {
        if (TextUtils.isEmpty(originalReceiveLPId)) {
            view.onGetOriginalLocationSuccess(null);
            return;
        }
        lpDal.getLpStatus(originalReceiveLPId, lpDetail -> view.onGetOriginalLocationSuccess(lpDetail), errorResponse -> {
            view.onGetOriginalLocationSuccess(null);
            ToastUtil.showToast(errorResponse.getErrorMessage());
        });
    }

    @Override
    public void returnPickedToInventory(List<ItemReturnToInventoryEntry> returnData) {
        operateWorkItemDal.returnItemToInventory(stepBaseEntry.taskId, returnData, aVoid ->
                        rebuildPickStrategy(returnData, (pickStrategies -> view.onReturnInventorySuccess())),
                errorResponse -> ToastUtil.showErrorToast(errorResponse.getErrorMessage()));
    }

    public void rebuildPickStrategy(List<ItemReturnToInventoryEntry> returnData, SuccessHandler<List<PickStrategy>> successHandler) {
        //rebuild strategy after return picked items
        PickStrategyRebuildForReturnEntity pickStrategyRebuildEntry = new PickStrategyRebuildForReturnEntity();
        ItemReturnToInventoryEntry inventoryEntry = returnData.get(0);
        pickStrategyRebuildEntry.orderPlanningId = pickTaskViewEntry.orderPlanId;
        pickStrategyRebuildEntry.taskId = pickTaskViewEntry.id;
        pickStrategyRebuildEntry.itemSpecId = inventoryEntry.itemSpecId;
        pickStrategyRebuildEntry.unitId = inventoryEntry.unitId;
        pickStrategyRebuildEntry.customerId = pickTaskViewEntry.customerId;
        pickStrategyRebuildEntry.titleId = inventoryEntry.titleId;
        if (inventoryEntry.order != null) {
            pickStrategyRebuildEntry.orderId = inventoryEntry.order.id;
        }
        pickStrategyRebuildEntry.lotNo = inventoryEntry.lotNo;
        pickStrategyRebuildEntry.qty = inventoryEntry.returnedQty;

        asyncExecute(orderApi.rebuildTaskPickStrategiesAfterReturn(pickStrategyRebuildEntry), view, successHandler);
    }

    @Override
    public void getPickTask() {
        if (pickTaskViewEntry != null) return;

        PickTaskSearchEntry searchEntry = new PickTaskSearchEntry();
        searchEntry.taskIds = Collections.singletonList(stepBaseEntry.taskId);
        pickTaskCenterDal.search(searchEntry, pickTaskViewEntries -> {
            if (CollectionUtil.isNotNullOrEmpty(pickTaskViewEntries)) {
                setPickTask(pickTaskViewEntries.get(0));
            }
        }, errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()));
    }

    private void setPickTask(PickTaskViewEntry pickTaskViewEntry) {
        this.pickTaskViewEntry = pickTaskViewEntry;
    }

    @Override
    public void getAndOpenHelpPage(Context context, String helpPageKey, String facilityId) {
        mFunctionHelpPresenterImpl.getAndOpenHelpPage(context, helpPageKey, pickTaskViewEntry != null ? pickTaskViewEntry.customerId : "", facilityId);
    }

    @Override
    public boolean isPickToTracking() {
        if (pickTaskViewEntry == null) return false;
        if (CollectionUtil.isNullOrEmpty(pickTaskViewEntry.orders)) return false;
        OrderTypeEntry orderType = pickTaskViewEntry.orders.get(0).orderType;
        return CustomerConfigUtil.isPickToTracking(orderType, pickTaskViewEntry.customerEntry, pickTaskViewEntry.pickWay);
    }


    @Override
    public void loadItemValidateRegex(String itemSpecId) {
        itemValidateRegex=null;
        ItemSpecSearchEntry searchEntry = new ItemSpecSearchEntry();
        searchEntry.itemSpecId = itemSpecId;
        execute(itemSpecAPI.getItemRule(searchEntry), rules -> {
            ItemSNValidateRuleEntry itemSNValidateRuleEntry = Stream.of(rules).filter(rule ->
                    TextUtils.equals(rule.itemSpecId, itemSpecId)).findSingle().orElse(null);
            if (null != itemSNValidateRuleEntry) {
                itemValidateRegex = itemSNValidateRuleEntry.validateRegex;
            }
        }, errorResponse -> ToastUtil.showErrorToast(errorResponse.getErrorMessage()));
    }
}
