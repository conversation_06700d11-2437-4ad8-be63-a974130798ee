package com.linc.platform.utils;


import android.annotation.SuppressLint;
import android.os.Build;
import android.text.TextUtils;

import com.linc.platform.R;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * Author: wujf
 * Time: 2020/11/18
 * Description:
 */
@SuppressLint("SimpleDateFormat")
public class TimeUtil {

    public static final String FORMAT_FULL_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_FULL_T_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String FORMAT_H_MM_A = "H:mm a";
    public static final String FORMAT_H_MM = "H:mm";
    public static final String FORMAT_MM_DD_YYYY = "MM/dd/yyyy";
    public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String FORMAT_PERIOD_A = "a";
    public static final String FORMAT_FULL_DEFAULT = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    public static final String FORMAT_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String FORMAT_MM_DD_YYYY_HH_MM = "MM/dd/yyyy HH:mm";
    public static final String FORMAT_MM_DD_YYYY_HH_MM_A = "MM/dd/yyyy hh:mm a";
    public static final String FORMAT_HH_MM_A_MM_DD_YYYY = "hh:mm a MM/dd/yyyy";

    public static String getLocalDateTime() {
        return getLocalDateTime("");
    }

    public static String getLocalDateTime(String timeZone) {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_FULL_DEFAULT);
        if (StringUtil.isNotEmpty(timeZone)) {
            dateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));
        }
        return dateFormat.format(date);
    }

    public static String getLocalDateTime(Date date) {
        if (date == null) return null;
        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_FULL_DEFAULT);
        return dateFormat.format(date);
    }

    /**
     * 时间 ---> UTC时间
     *
     * @param time
     * @return
     */
    public static String local2UTC(long time, String zone) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
        sdf.setTimeZone(TimeZone.getTimeZone(zone));
        String gmtTime = sdf.format(new Date(time));
        return gmtTime;
    }

    public static String uTC2Local(String dateStr, String format) {
        if (TextUtils.isEmpty(dateStr)) {
            return dateStr;
        }
        try {
            Date inputDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(dateStr);
            return new SimpleDateFormat(format).format(inputDate);
        } catch (Exception e) {
            Logger.e(e.getMessage());
            return dateStr;
        }
    }

    public static String local2Default(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        sdf.setTimeZone(sdf.getTimeZone());
        String gmtTime = sdf.format(date);
        return gmtTime;
    }

    public static String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").format(date);
    }

    public static Date toDate(String date) {
        if (TextUtils.isEmpty(date)) {
            return null;
        }
        try {
            return new SimpleDateFormat(FORMAT_FULL_T_FORMAT).parse(date);
        } catch (ParseException e) {
            Logger.e(e.getMessage());
            return null;
        }
    }

    public static boolean isEffectiveDate(Date date) {
        String dateTime = getLocalDateTime(date);
        if (TextUtils.isEmpty(dateTime)) {
            return false;
        }
        String currentDateTime = getLocalDateTime();
        return currentDateTime.compareTo(dateTime) >= 0;
    }

    public static int getCurrentYear() {
        Calendar c = Calendar.getInstance();
        int year = c.get(Calendar.YEAR);
        return year;
    }

    public static int getDayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.set(year, month, 0);
        int day = c.get(Calendar.DAY_OF_MONTH);
        return day;
    }

    public static String formatDataString(String time, String defaultFormat, String targetFormat) {
        if (TextUtils.isEmpty(time)) return "";
        SimpleDateFormat defaultSdf = new SimpleDateFormat(defaultFormat);
        SimpleDateFormat targetSdf = new SimpleDateFormat(targetFormat);
        try {
            return targetSdf.format(defaultSdf.parse(time));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return time;
    }

    public static String getDateToString(long time, String format) {
        return getDateToString(time, format, null);
    }

    public static String getDateToString(long time, String format, String timeZone) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        if (!TextUtils.isEmpty(timeZone))
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        return sdf.format(new Date(time));
    }

    public static long getStringToDate(String time) {
        return getStringToDate(time, FORMAT_FULL_DEFAULT);
    }

    public static long getStringToDate(String time, String format) {
        return getStringToDate(time, format, null);
    }

    public static long getStringToDate(String time, String format, String timeZone) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        if (!TextUtils.isEmpty(timeZone))
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        try {
            return sdf.parse(time).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static double getTimeInMillisToHour(long timeInMillis) {
        int hour, minute, second;
        second = (int) (timeInMillis / 1000);
        hour = second / 60 / 60;
        minute = (second - hour * 3600) / 60;
        return ArithmeticUtil.add(hour, ArithmeticUtil.div(minute, 60));
    }

    public static String formatTimeScale(double d) {
        return new DecimalFormat("0.00#").format(d);
    }

    public static int getTimeZoneOffsetHrs() {
        Calendar cal = Calendar.getInstance();
        int zoneOffset = cal.get(Calendar.ZONE_OFFSET);
        int dstOffset = cal.get(Calendar.DST_OFFSET);
        return (dstOffset + zoneOffset) / (1000 * 60 * 60);
    }

    public static long getLastDayTime(long time) {
        return time - 24 * 60 * 60 * 1000;
    }

    public static long getNextDayTime(long time) {
        return time + 24 * 60 * 60 * 1000;
    }

    public static String getDayStartTime(long time) {
        return getDateToString(time, "yyyy-MM-dd 00:00:00", null);
    }

    public static String getDayEndTime(long time) {
        return getDateToString(time, "yyyy-MM-dd 23:59:59", null);
    }

    public static Date getDayEndTime(Date time) {
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    public static List<Long> getWeekDateList(long time) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(time);
        int week = c.get(Calendar.DAY_OF_WEEK);
        c.add(Calendar.DAY_OF_MONTH, 1 - week);
        List<Long> list = new ArrayList<>();
        list.add(c.getTimeInMillis());
        for (int i = 1; i < 7; i++) {
            c.add(Calendar.DAY_OF_MONTH, 1);
            list.add(c.getTimeInMillis());
        }
        return list;
    }

    public static String getDateToWeekString(long time) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(time);
        return TimeUtil.getWeekStr(c.get(Calendar.DAY_OF_WEEK)) + ", " +
                TimeUtil.getMonthStr(c.get(Calendar.MONTH)) + " " +
                c.get(Calendar.DAY_OF_MONTH);
    }

    public static String getWeekStr(int week) {
        switch (week) {
            case 1:
                return ResUtil.getString(R.string.week_sunday);
            case 2:
                return ResUtil.getString(R.string.week_monday);
            case 3:
                return ResUtil.getString(R.string.week_tuesday);
            case 4:
                return ResUtil.getString(R.string.week_wednesday);
            case 5:
                return ResUtil.getString(R.string.week_thursday);
            case 6:
                return ResUtil.getString(R.string.week_friday);
            case 7:
                return ResUtil.getString(R.string.week_saturday);
            default:
                return "";
        }
    }

    public static String getMonthStr(int month) {
        switch (month) {
            case 0:
                return ResUtil.getString(R.string.month_january);
            case 1:
                return ResUtil.getString(R.string.month_february);
            case 2:
                return ResUtil.getString(R.string.month_march);
            case 3:
                return ResUtil.getString(R.string.month_april);
            case 4:
                return ResUtil.getString(R.string.month_may);
            case 5:
                return ResUtil.getString(R.string.month_june);
            case 6:
                return ResUtil.getString(R.string.month_july);
            case 7:
                return ResUtil.getString(R.string.month_august);
            case 8:
                return ResUtil.getString(R.string.month_september);
            case 9:
                return ResUtil.getString(R.string.month_october);
            case 10:
                return ResUtil.getString(R.string.month_november);
            case 11:
                return ResUtil.getString(R.string.month_december);
            default:
                return "";
        }
    }

    public static Date getDateFromStr(String dateStr, String format) {
        Date date = null;
        try {
            date = new SimpleDateFormat(format).parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;

    }

    public static String getVideoDuration(int duration) {
        String formattedDuration = String.format("%02d:%02d",
                TimeUnit.MILLISECONDS.toMinutes(duration),
                TimeUnit.MILLISECONDS.toSeconds(duration) -
                        TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(duration))
        );
        return formattedDuration;
    }

    public static String getSecondTime(int second) {
        return String.format("%02d:%02d", TimeUnit.SECONDS.toMinutes(second) % 60,
                second % 60);
    }

    public static boolean isSameYear(Date date, Date otherDate) {
        Calendar calendar = Calendar.getInstance();
        Calendar otherCalendar = Calendar.getInstance();
        calendar.setTime(date);
        otherCalendar.setTime(otherDate);
        return calendar.get(Calendar.YEAR) == otherCalendar.get(Calendar.YEAR);
    }

    public static boolean isSameMonth(Date date, Date otherDate) {
        if (!isSameYear(date, otherDate)) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        Calendar otherCalendar = Calendar.getInstance();
        calendar.setTime(date);
        otherCalendar.setTime(otherDate);
        return calendar.get(Calendar.MONTH) == otherCalendar.get(Calendar.MONTH);
    }

    public static boolean isSameWeek(Date date, Date otherDate) {
        Calendar calendar = Calendar.getInstance();
        Calendar otherCalendar = Calendar.getInstance();
        calendar.setTime(date);
        otherCalendar.setTime(otherDate);
        return calendar.get(Calendar.WEEK_OF_YEAR) == otherCalendar.get(Calendar.WEEK_OF_YEAR);
    }

    public static boolean isSameDay(Date date, Date otherDate) {
        if (!isSameMonth(date, otherDate)) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        Calendar otherCalendar = Calendar.getInstance();
        calendar.setTime(date);
        otherCalendar.setTime(otherDate);
        return calendar.get(Calendar.DAY_OF_MONTH) == otherCalendar.get(Calendar.DAY_OF_MONTH);
    }

    public static String transformToLocalDate(Date date, String inFormat, String outFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(inFormat);
        String inDate = sdf.format(date);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date utcDate = sdf.parse(inDate);
            SimpleDateFormat localFormat = new SimpleDateFormat(outFormat);
            localFormat.setTimeZone(TimeZone.getDefault());
            return localFormat.format(utcDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return inDate;
    }

    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        Calendar todayCalendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR) == todayCalendar.get(Calendar.YEAR) &&
                calendar.get(Calendar.MONTH) == todayCalendar.get(Calendar.MONTH) &&
                calendar.get(Calendar.DAY_OF_MONTH) == todayCalendar.get(Calendar.DAY_OF_MONTH);
    }

    public static Date getCurrentTimeByZone(String zoneId) {
        if (TextUtils.isEmpty(zoneId)) {
            return new Date();  // 返回当前时间
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ZoneId zone = ZoneId.of(zoneId);
                ZonedDateTime zonedDateTime = ZonedDateTime.now(zone);
                return new Date(zonedDateTime.toLocalDateTime()
                        .atZone(ZoneId.systemDefault())
                        .toInstant()
                        .toEpochMilli());
            } else {
                TimeZone timeZone = TimeZone.getTimeZone(zoneId);
                long currentTime = System.currentTimeMillis();
                int offset = timeZone.getOffset(currentTime) - TimeZone.getDefault().getOffset(currentTime);
                return new Date(currentTime + offset);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Date();  // 发生错误时返回当前时间
        }
    }

    public static String getLastLocalDateTime(int lastDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, lastDay); // 获取lastDay天前的日期
        Date date = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_FULL_DEFAULT);
        return dateFormat.format(date);
    }

}