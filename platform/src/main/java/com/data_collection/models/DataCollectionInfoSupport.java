package com.data_collection.models;

import org.litepal.crud.DataSupport;

public class DataCollectionInfoSupport extends DataSupport {
    public String page; //当前事件产生的页面
    public String actionKey;//取自 view 的 id 或者自定义
    public String actionType;//事件类型，page open,page close,user input,user click,user scroll,api request,api response
    public String time;//事件发生的时间
    public String value;//可能是空，取自 点击view 的文本或是自定义，api 的参数，response
    public String userId;//当前登录 userId
    public String status;//report 状态，空表示失败
    public String facilityId;
    public String tenantId;
    public String backendTraceId;
    public String uniqueIgnoreActionTag;//report 状态，空表示失败
}
