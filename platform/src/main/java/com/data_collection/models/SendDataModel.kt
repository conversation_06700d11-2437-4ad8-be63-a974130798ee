package com.data_collection.models

import android.text.TextUtils
import com.data_collection.DataCollectionManager
import com.linc.platform.utils.TimeUtil

/**
 * <AUTHOR> on 2024/11/5
 */
data class SendDataModel(
    val actionKey: String? = null, //user 触发事件目的，一般取 view 上的 id，也可以自己定义
    val value: String? = null, //一般取 view 上的 text，在adapter的事件，开发请自行定义
    val actionType: ActionType, //事件类型，必传
    var page: String? = null, //当前发生事件页面，可 null，null为空的时候取DataCollectionManager维护的 list 最后一个
    val uniqueIgnoreActionTag: String? = null
) {

    companion object {
        fun createIgnoreDate(actionType: ActionType, actionKey: String, uniqueIgnoreActionTag: String) =
            SendDataModel(actionType = actionType, actionKey = actionKey, uniqueIgnoreActionTag = uniqueIgnoreActionTag)

        fun createPageData(actionType: ActionType, actionKey: String, page: String) = SendDataModel(actionType = actionType, actionKey = actionKey, page = page)
    }

    private var acTime: String = TimeUtil.getLocalDateTime()
    private var backendTraceId: String? = null

    init {
        page = if (TextUtils.isEmpty(page)) DataCollectionManager.instance().currentPagePath else page
    }

    constructor(actionType: ActionType, actionKey: String) : this(actionKey = actionKey, actionType = actionType, page = null, value = null)
    constructor(actionType: ActionType, actionKey: String, value: String) : this(
        actionType = actionType, actionKey = actionKey, value = value, page = null)


    fun getAcTime() = acTime
    
    fun getBackendTraceId() = backendTraceId
    
    fun setBackendTraceId(traceId: String?) {
        this.backendTraceId = traceId
    }
}
