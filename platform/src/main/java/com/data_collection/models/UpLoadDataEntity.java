package com.data_collection.models;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class UpLoadDataEntity implements Serializable {

    @SerializedName("userId")
    public String userId;

    @SerializedName("platform")
    public String platform;

    @SerializedName("mac")
    public String mac;

    @SerializedName("deviceInfo")
    public String deviceInfo;

    @SerializedName("systemInfo")
    public String systemInfo;

    @SerializedName("page")
    public String page;

    @SerializedName("actionKey")
    public String actionKey;

    @SerializedName("actionType")
    public String actionType;

    @SerializedName("value")
    public String value;

    @SerializedName("time")
    public String time;

    @SerializedName("otherItem")
    public String otherItem;
    
    @SerializedName("facilityId")
    public String facilityId;
    
    @SerializedName("tenantId")
    public String tenantId;
    
    @SerializedName("backendTraceId")
    public String backendTraceId;


    public UpLoadDataEntity(String userId, String platform, String mac, String deviceInfo, String systemInfo,
                            String page, String actionKey, String actionType, String value, String time, String otherItem,
                            String facilityId, String tenantId, String backendTraceId) {
        this.userId = userId;
        this.platform = platform;
        this.mac = mac;
        this.deviceInfo = deviceInfo;
        this.systemInfo = systemInfo;
        this.page = page;
        this.actionKey = actionKey;
        this.actionType = actionType;
        this.value = value;
        this.time = time;
        this.otherItem = otherItem;
        this.facilityId = facilityId;
        this.tenantId = tenantId;
        this.backendTraceId = backendTraceId;
    }
}
