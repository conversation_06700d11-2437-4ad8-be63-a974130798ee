package com.data_collection.repository;


import android.os.Build;
import android.text.TextUtils;

import com.data_collection.models.ActionType;
import com.data_collection.models.SendDataModel;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

public class ApiCollectionInterceptor implements Interceptor {

    private List<String> withoutReportApiList;


    public ApiCollectionInterceptor() {
        withoutReportApiList = new ArrayList<>();
//        withoutReportApiList.add("base-app/position/record");
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        collectRequest(chain.request());
        return collectResponse(chain);
    }

    private void collectRequest(Request request) {
        try {
            RequestBody body = request.body();
            String paramString = readRequestParamString(body);
            HttpUrl url = request.url();
            String path = url.encodedPath();
            if (!enableTrack(path)) return;
            Object[] objects = new Object[2];
            objects[0] = path;
            objects[1] = paramString;
            onTrackRequest(objects);
        } catch (Exception e) {

        }
    }

    private Response collectResponse(Chain chain) throws IOException {
        Request request = chain.request();
        String path = request.url().encodedPath();
        if (!enableTrack(path)) {
            return chain.proceed(request);
        }
        try {
            Response response = chain.proceed(request);
            String responseStr = readContent(response);
            String traceId = response.header("traceId");
            Object[] objects = new Object[3];
            objects[0] = path;
            objects[1] = responseStr;
            objects[2] = traceId;
            onTrackResponse(objects);
            return response;
        } catch (IOException exception) {
            String message = exception.getMessage();
            Object[] objects = new Object[3];
            objects[0] = path;
            objects[1] = message;
            objects[2] = null; // 异常情况下没有traceId
            onTrackResponse(objects);
            throw new IOException(message, exception.getCause());
        }
    }


    private String readRequestParamString(RequestBody requestBody) {
        String paramString;
        if (requestBody instanceof MultipartBody) {//判断是否有文件
            MultipartBody multipartBody = (MultipartBody) requestBody;
            StringBuilder stringBuilder = new StringBuilder();
            for (MultipartBody.Part part : multipartBody.parts()) {
                String readContent = readContent(part.body(), true);
                if (!TextUtils.isEmpty(readContent)) {
                    if (stringBuilder.length() > 0) {
                        stringBuilder.append(",");
                    }
                    stringBuilder.append(readContent);
                }
            }
            paramString = stringBuilder.toString();
        } else {
            paramString = readContent(requestBody, false);
            if (isValidJson(paramString)) {
                return paramString;
            } else {
                return "";
            }
        }
        return paramString;
    }

    private String readContent(RequestBody body, boolean isMultipart) {
        try {
            if (body == null) {
                return "";
            }
            if (isMultipart) {
                MediaType mediaType = body.contentType();
                if (null != mediaType) {
                    return mediaType.toString();
                }
            }
            Buffer buffer = new Buffer();
            body.writeTo(buffer);
            return buffer.readUtf8();
        } catch (Exception e) {
            return "";
        }
    }

    private String readContent(Response response) {
        try {
            if (response == null) {
                return "";
            }
            ResponseBody body = response.body();
            if (null == body) return "";
            MediaType mediaType = body.contentType();
            if (null == mediaType) return "";
            String subtype = mediaType.subtype();
            if (subtype.contains("stream")) {
                return subtype;
            } else {
                return response.peekBody(1024).string();
            }
        } catch (Exception e) {
            return "";
        }
    }


    private boolean enableTrack(String path) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return withoutReportApiList.stream().noneMatch(path::contains);
        }
        return true;
    }


    private boolean isValidJson(String str) {

        try {
            new JsonParser().parse(str);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        }
    }

    public void onTrackRequest(Object[] objects) {
        EventBus.getDefault().post(new SendDataModel(ActionType.API_REQUEST,objects[0].toString(), objects[1].toString()));
    }

    public void onTrackResponse(Object[] objects) {
        SendDataModel sendDataModel = new SendDataModel(ActionType.API_RESPONSE, objects[0].toString(), objects[1].toString());
        if (objects.length > 2 && objects[2] != null) {
            sendDataModel.setBackendTraceId(objects[2].toString());
        }
        EventBus.getDefault().post(sendDataModel);
    }

}
