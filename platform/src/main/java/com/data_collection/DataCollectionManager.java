package com.data_collection;

import android.Manifest;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.annimon.stream.Stream;
import com.data_collection.models.DataCollectionInfoSupport;
import com.data_collection.models.DeviceInfoEntry;
import com.data_collection.models.SendDataModel;
import com.data_collection.models.UpLoadDataEntity;
import com.data_collection.models.UpdatePageEvent;
import com.data_collection.repository.UploadRepository;
import com.data_collection.upload.InsertDataRunnable;
import com.data_collection.utils.DataCollectionUtils;
import com.linc.platform.BuildConfig;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.subscribers.BackgroundSubscriber;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.DeviceIdUtil;
import com.linc.platform.utils.PermissionUtil;
import com.linc.platform.utils.TimeUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.LinkedList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentLinkedQueue;

import io.reactivex.rxjava3.schedulers.Schedulers;
import retrofit2.Response;

/**
 * <AUTHOR> on 2024/11/1
 */
public class DataCollectionManager {

    private static DataCollectionManager sInstance;
    private Context mContext;

    private DeviceInfoEntry mDeviceInfoEntry;

    private UploadRepository mRepository;

    public LinkedList<String> beforePageList = new LinkedList<>();

    private ConcurrentLinkedQueue<SendDataModel> mCacheSendDataQueue = new ConcurrentLinkedQueue<>();

    private String dcDomain = "";

    private boolean isUploadComplete=true;

    private String userId;

    public static DataCollectionManager instance() {
        if (sInstance == null) {
            synchronized (DataCollectionManager.class) {
                if (sInstance == null) {
                    sInstance = new DataCollectionManager();
                }
            }
        }
        return sInstance;
    }

    public void bindContext(@NonNull Context context) {
        mContext = context;
    }

    private DataCollectionManager() {
        if (!enableCollectionData()) {
            return;
        }
        EventBus.getDefault().register(this);
        DataCollectionUtils.deleteLogsByTime(TimeUtil.getLastLocalDateTime(-3));
        Timer timer = new Timer();
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                sendAllLog();
            }
        };
        int interval = 1000 * 5;//10秒
        timer.schedule(timerTask, 0, interval);
        Thread insertDataThread = new Thread(new InsertDataRunnable(mCacheSendDataQueue));
        insertDataThread.start();
    }


    public void updateDeviceInfo() {
        if (PermissionUtil.hasPermissions(mContext, Manifest.permission.READ_PHONE_STATE)) {
            String brand = Build.BRAND;
            String model = Build.MODEL;
            String version = Build.VERSION.RELEASE;
            String deviceInfo = DeviceIdUtil.getDeviceIdInfo(mContext);
            this.mDeviceInfoEntry = new DeviceInfoEntry(brand, model, "android " + version, deviceInfo);
        }
    }

    private DeviceInfoEntry getTrackDeviceInfoModel() {
        return null == mDeviceInfoEntry ? new DeviceInfoEntry() : mDeviceInfoEntry;
    }


    private UploadRepository getRepository() {
        if (mRepository == null) {
            mRepository = new UploadRepository(dcDomain);
        }
        return mRepository;
    }

    private void insertCollectionData(SendDataModel dataModel) {
        if (!enableCollectionData()) {
            return;
        }
        mCacheSendDataQueue.add(dataModel);
    }


    private void updatePage(String page, boolean isEnter) {
        String pageTemp = Stream.of(beforePageList).filter(pageItem ->
                TextUtils.equals(pageItem, page)
        ).findFirst().orElse(null);
        if (!isEnter && !TextUtils.isEmpty(pageTemp)) {
            beforePageList.remove(pageTemp);
            return;
        }
        if (isEnter) {
            if (!TextUtils.isEmpty(pageTemp)) {
                beforePageList.remove(pageTemp);
            }
            beforePageList.add(page);
        }
    }

    public String getCurrentPagePath() {
        return CollectionUtil.isNullOrEmpty(beforePageList) ? "" : beforePageList.getLast();
    }


    public String getCurrentUserId() {
        if(TextUtils.isEmpty(userId)){
            userId=LocalPersistence.getUserId(mContext);
        }
        return userId;
    }


    private boolean enableCollectionData() {
        return !BuildConfig.DEBUG;
    }


    private void sendAllLog() {
        List<DataCollectionInfoSupport> cacheLog = DataCollectionUtils.getCacheLog();
        if (CollectionUtil.isNullOrEmpty(cacheLog)) {
            return;
        }
        if(!isUploadComplete)return;
        isUploadComplete=false;
        DeviceInfoEntry deviceInfoModel = getTrackDeviceInfoModel();
        List<UpLoadDataEntity> logEntities = Stream.of(cacheLog).map(log -> new UpLoadDataEntity(log.userId, "android",
                deviceInfoModel.getDeviceId(), deviceInfoModel.getBrand() + " " + deviceInfoModel.getModel(), deviceInfoModel.getVersion(), log.page,
                log.actionKey, log.actionType, log.value, log.time, null,
                log.facilityId, log.tenantId, log.backendTraceId
        )).toList();
        getRepository().batchItemUploadLog(logEntities).subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new BackgroundSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                        DataCollectionUtils.updateLogStatus(cacheLog);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {

                    }

                    @Override
                    public void onDone() {
                        isUploadComplete=true;
                    }
                });
    }

    public void updateDcDomain(String dcDomain) {
        this.dcDomain=dcDomain;
        mRepository=null;
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onDataCollectionEvent(SendDataModel event) {
        insertCollectionData(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUpdatePageEvent(UpdatePageEvent event) {
       updatePage(event.getPage(),event.isEnter());
    }
}
