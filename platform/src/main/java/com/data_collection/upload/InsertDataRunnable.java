package com.data_collection.upload;

import android.os.Build;
import android.text.TextUtils;

import com.data_collection.DataCollectionManager;
import com.data_collection.models.DataCollectionInfoSupport;
import com.data_collection.models.SendDataModel;
import com.data_collection.utils.DataCollectionUtils;
import com.linc.platform.utils.StringUtil;
import com.unis.platform.db.WMSDBManager;
import com.unis.platform.facility_v2.mode.FacilityEntity;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR> on 2024/11/1
 */
public class InsertDataRunnable implements Runnable {
    private ConcurrentLinkedQueue<SendDataModel> mCacheSendDataQueue = new ConcurrentLinkedQueue<>();
    private final int sdkInt = Build.VERSION.SDK_INT;

    public InsertDataRunnable(ConcurrentLinkedQueue<SendDataModel> mCacheSendDataQueue) {
        this.mCacheSendDataQueue = mCacheSendDataQueue;
    }

    @Override
    public void run() {
        while (true) {
            try {
                SendDataModel dataModel = mCacheSendDataQueue.poll();
                if (dataModel != null) {
                    // 执行相应操作
                    insert(dataModel);
                } else {
                    // 如果队列为空，稍作休眠再检查
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void insert(SendDataModel sendDataModel) {
        String tag = sendDataModel.getUniqueIgnoreActionTag();
        if (!TextUtils.isEmpty(tag)) {
            String latestRecordWithSameTag = DataCollectionUtils.findLatestRecordWithSameTag(tag);
            if (latestRecordWithSameTag != null) {
                long timeDifference = calculateTimeDifference(sendDataModel.getAcTime(), latestRecordWithSameTag);
                if (timeDifference < 200) {
                    return;
                }
            }
        }
        DataCollectionInfoSupport trackInfoModel = new DataCollectionInfoSupport();
        trackInfoModel.page = sendDataModel.getPage();
        trackInfoModel.actionType = sendDataModel.getActionType().getActionTypeStr();
        trackInfoModel.time = sendDataModel.getAcTime();
        trackInfoModel.userId = DataCollectionManager.instance().getCurrentUserId();
        trackInfoModel.actionKey = sendDataModel.getActionKey();
        trackInfoModel.value = sendDataModel.getValue();
        trackInfoModel.uniqueIgnoreActionTag = sendDataModel.getUniqueIgnoreActionTag();
        trackInfoModel.backendTraceId = sendDataModel.getBackendTraceId();
        if (StringUtil.isNotEmpty(trackInfoModel.userId)){
            FacilityEntity facility = WMSDBManager.INSTANCE.getFacilityManager().getOneByUserId(trackInfoModel.userId);
            if (facility != null){
                trackInfoModel.facilityId = facility.getId();
            }
        }
        trackInfoModel.tenantId = StringUtil.getTenantId();

        trackInfoModel.save();//保存至数据库
    }


    private long calculateTimeDifference(String time1, String time2) {
        if (sdkInt >= Build.VERSION_CODES.O) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
            try {
                LocalDateTime dateTime1 = LocalDateTime.parse(time1, formatter);
                LocalDateTime dateTime2 = LocalDateTime.parse(time2, formatter);
                return Math.abs(java.time.Duration.between(dateTime1.toInstant(ZoneOffset.UTC), dateTime2.toInstant(ZoneOffset.UTC)).toMillis());
            } catch (Exception e) {
                return 0;
            }
        } else {
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS", Locale.getDefault());
                Date date1 = format.parse(time1);
                Date date2 = format.parse(time2);
                return Math.abs(date1.getTime() - date2.getTime());
            } catch (Exception e) {
                return 0;
            }
        }
    }
}
