package com.unis.platform.iam.model

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.UserPermissionEntity
import com.unis.platform.facility_v2.mode.FacilityEntity
import com.unis.platform.ot_control.models.UserPayTypeEntity
import java.io.Serializable

data class UserInfoEntity(

    @SerializedName("id")
    val id: String,

    @SerializedName("userId")
    val userId: String,

    @SerializedName("userName")
    val userName: String,

    @SerializedName("firstName")
    val firstName: String,

    @SerializedName("lastName")
    val lastName: String,

    @SerializedName("accountId")
    val accountId: String,

    @SerializedName("companyId")
    val companyId: String,

    @SerializedName("companyCode")
    val companyCode: String,

    @SerializedName("tenantId")
    val tenantId: String,

    @SerializedName("contactNumber")
    val contactNumber: String,

    @SerializedName("email")
    val email: String,

    @SerializedName("userStatus")
    val userStatus: UserStatusEntity,

    @SerializedName("roleIds")
    val roleIds: List<String>,

    @SerializedName("profile")
    var profile: ProfileEntity?,

    @SerializedName("userRoles")
    val userRoles: List<UserRoleEntity>?,

    @SerializedName("tenantIds")
    val tenantIds: List<String>?,

    @SerializedName("permissions")
    val permissions: List<UserPermissionEntity>?,

    @SerializedName("userTags")
    val userTags: List<String>?,

    @SerializedName("externalInfo") val externalInfo: Map<String, Any>?,

    var selectedTenantId: String?,
    var isSupervisor: Boolean = false,
    var isOTApprover: Boolean = false,
    var isUpperApprover: Boolean = false,
    /**
     * @see com.unis.platform.ot_control.models.UserPayTypeEntity
     */
    var payType: String?,
    var teamId: Long?,
    var laborTypeId: Long?

    ): Serializable {

    companion object {
        val TAG: String = UserInfoEntity::class.java.simpleName
        private const val IT_AND_SUPPORT_USER_TAG: String = "10011" //IT & Support
        private const val IT_USER_ROLE: String = "IT" //IT
    }

    fun getUserFacilities(): List<FacilityEntity> {
        return profile?.facilities?.sortedBy { it.name } ?: listOf()
    }

    fun enableSkipWorkerAssignment(): Boolean {
        return isSupervisor
        || isOTApprover
        || isUpperApprover
        || userTags?.contains(IT_AND_SUPPORT_USER_TAG) == true
        || userRoles?.any { it.name == IT_USER_ROLE } == true
    }

    fun isOtSupervisorOrApprover(): Boolean {
        return isSupervisor
                || isOTApprover
                || isUpperApprover
    }

    fun isItRoleOrTag(): Boolean {
        return userTags?.contains(IT_AND_SUPPORT_USER_TAG) == true
        || userRoles?.any { it.name == IT_USER_ROLE } == true
    }

    fun isNeedOtControl(): Boolean {
        return payType == UserPayTypeEntity.PAY_TYPE_HOURLY
    }

    fun isOnlySupervisor(): Boolean {
        return isSupervisor && !isOTApprover && !isUpperApprover
    }

    fun isApprover()=isOTApprover||isUpperApprover
}
