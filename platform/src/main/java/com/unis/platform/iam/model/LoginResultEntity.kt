package com.unis.platform.iam.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class LoginResultEntity(

    @SerializedName("accessToken")
    val accessToken: String,

    @SerializedName("refreshToken")
    val refreshToken: String,

    @SerializedName("clientId")
    val clientId: String,

    @SerializedName("expiresAt")
    val expiresAt: String,

    @SerializedName("userInfo")
    val userInfo: UserInfoEntity,

): Serializable {
    companion object {
        val TAG: String = LoginResultEntity::class.java.simpleName
    }
}
