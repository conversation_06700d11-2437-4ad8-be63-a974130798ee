package com.unis.platform.common.model.configurationmap

import java.io.Serializable
/**
 * Entity for querying configuration maps
 */
data class ConfigurationMapQueryEntity(
    var id: String? = null,
    var ids: List<String>? = null,
    var customerId: String? = null,
    var customerIds: List<String>? = null,
    var tableName: String? = null,
    var tableNames: List<String>? = null
) : Serializable