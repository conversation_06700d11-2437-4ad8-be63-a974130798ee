package com.unis.platform.common.model.task

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.PageQueryEntity

class TaskQueryEntity(

    @SerializedName("id")
    var id: String? = null,

    @SerializedName("assigneeUserId")
    var assigneeUserId: String? = null,

    @SerializedName("status")
    var status: TaskStatus? = null,

    @SerializedName("statuses")
    var statuses: List<TaskStatus>? = null,

    @SerializedName("withTaskSteps")
    var withTaskSteps: Boolean = true,

    @SerializedName("includeNullAssigneeUserId")
    var includeNullAssigneeUserId: Boolean? = null,

    @SerializedName("keyword")
    var keyword: String? = null,

    @SerializedName("taskType")
    var taskType: TaskType? = null,

    @SerializedName("customerId")
    var customerId: String? = null,

    @SerializedName("dockId")
    var dockId: String? = null,

    @SerializedName("entryId")
    var entryId: String? = null,

    @SerializedName("containerNo")
    var containerNo: String? = null,

    ) : PageQueryEntity()