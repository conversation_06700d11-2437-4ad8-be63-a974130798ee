package com.unis.platform.common.model.step

import com.unis.platform.common.model.BaseEntity
import com.unis.platform.common.model.task.TaskPriority
import com.unis.platform.common.model.task.TaskType
import java.util.*

open class BaseTaskStepEntity : BaseEntity() {
    var id: String? = null
    var customerId: String? = null
    var status: TaskStepStatus? = null
    var exceptionCode: String? = null
    var sysNote: String? = null
    var note: String? = null
    var stepType: TaskStepType? = null
    var taskId: String? = null
    var taskType: TaskType? = null
    var stepSequence = 0
    var assigneeUserIds: List<String>? = null
    var assigneeUserNames: List<String>? = null
    var lastAssignedWhen: Date? = null
    var priority: TaskPriority? = null
    var startTime: Date? = null
    var endTime: Date? = null
    var isShawStepLocal: Boolean = true

    fun isAssigned(userId: String): Boolean {
        return assigneeUserIds?.find { it == userId } != null
    }

    fun isInProgress(): <PERSON><PERSON><PERSON> {
        return status == TaskStepStatus.IN_PROGRESS
    }

    fun isDone(): Boolean {
        return status == TaskStepStatus.CLOSED || status == TaskStepStatus.FORCE_CLOSED
    }

    fun isNew() : Boolean {
        return status == TaskStepStatus.NEW
    }
}