package com.unis.platform.common.api

import com.linc.platform.http.BaseResponse
import com.unis.platform.common.model.configurationmap.ConfigurationMapEntity
import com.unis.platform.common.model.configurationmap.ConfigurationMapQueryEntity
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * API interface for configuration map operations
 */
interface ConfigurationMapApi {
   
    @POST("mdm/configuration-map/search")
    suspend fun search(@Body query: ConfigurationMapQueryEntity): Response<BaseResponse<List<ConfigurationMapEntity>>>
}