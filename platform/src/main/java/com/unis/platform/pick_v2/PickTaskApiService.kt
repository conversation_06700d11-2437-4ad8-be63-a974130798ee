package com.unis.platform.pick_v2

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.IdResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.step.BaseTaskStepEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.equipment_v2.model.EquipmentEntity
import com.unis.platform.pick_v2.model.pick.LocationItemSuggestEntity
import com.unis.platform.pick_v2.model.progress.PickProgressEntity
import com.unis.platform.pick_v2.model.pick.LocationSuggestRequestEntity
import com.unis.platform.pick_v2.model.pick.PickItemSubmitEntity
import com.unis.platform.pick_v2.model.PickTaskEntity
import com.unis.platform.pick_v2.model.PickTaskSearchEntity
import com.unis.platform.pick_v2.model.PickTaskUpdateEntity
import com.unis.platform.pick_v2.model.order_pick.CheckCanPickOrders
import com.unis.platform.pick_v2.model.order_pick.ItemSuggestRequest
import com.unis.platform.pick_v2.model.order_pick.OrderPickItemSuggestEntity
import com.unis.platform.pick_v2.model.order_pick.OrderPickSubmitEntity
import com.unis.platform.pick_v2.model.order_pick.OrderPickSuggestEntity
import com.unis.platform.pick_v2.model.pick.BindToteCartRequestEntity
import com.unis.platform.pick_v2.model.pick.ContainerLocationEntity
import com.unis.platform.pick_v2.model.pick.LocationLpItemSuggestEntity
import com.unis.platform.pick_v2.model.pick.LpItemSuggestRequest
import com.unis.platform.pick_v2.model.pick.PickSubmitEntity
import com.unis.platform.pick_v2.model.pick.PickSubmitResultEntity
import com.unis.platform.pick_v2.model.pick.ToteLpSuggestRequest
import com.unis.platform.pick_v2.model.progress.ProgressItemEntity
import com.unis.platform.pick_v2.model.progress.ProgressLPEntity
import com.unis.platform.pick_v2.model.progress.ProgressLocationEntity
import com.unis.platform.pick_v2.model.progress.ProgressOrderEntity
import com.unis.platform.pick_v2.model.return_inventory.InventoryForReturnEntity
import com.unis.platform.pick_v2.model.return_inventory.PickReturnRequestEntity
import com.unis.platform.pick_v2.model.sorting_to_wall.SortingDropItemEntity
import com.unis.platform.pick_v2.model.sorting_to_wall.SortingSlotSuggestEntity
import com.unis.platform.pick_v2.model.sorting_to_wall.SortingToWallProgressEntity
import com.unis.platform.pick_v2.model.stage.StageLocationSuggestEntity
import com.unis.platform.pick_v2.model.stage.StageLocationSuggestRequestEntity
import com.unis.platform.pick_v2.model.stage.StageProgressEntity
import com.unis.platform.pick_v2.model.stage.StageSubmitEntity
import com.unis.platform.pick_v2.model.stage.StageValidateRequestEntity
import com.unis.platform.pick_v2.model.stage.StageValidateResponseEntity
import com.unis.platform.pick_v2.model.stage_to_wall.SlotSuggestRequest
import com.unis.platform.pick_v2.model.stage_to_wall.SlotSuggestionEntity
import com.unis.platform.pick_v2.model.stage_to_wall.StageToWallProgressEntity
import com.unis.platform.pick_v2.model.stage_to_wall.StageToWallSubmitEntity
import com.unis.platform.vlg.VirtualLocationGroupEntity
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface PickTaskApiService {
    @GET("wms-bam/outbound/pick-task/{pickTaskId}/view")
    suspend fun getPickTask(@Path("pickTaskId") request: String): Response<BaseResponse<PickTaskEntity>>

    @POST("wms-bam/outbound/pick-task/search")
    suspend fun searchPickTask(@Body request: PickTaskSearchEntity): Response<BaseResponse<List<PickTaskEntity>>>

    @POST("wms-bam/outbound/pick-task/search-by-paging")
    suspend fun searchPickTaskByPaging(@Body request: TaskQueryEntity): Response<BaseResponse<PageResponseEntity<PickTaskEntity>>>

    @POST("wms/outbound/pick-task/pick-step/{stepId}/force-close")
    suspend fun forceClosePickStep(@Path("stepId") stepId: String): Response<BaseResponse<IdResponse>>

    @PUT("wms/outbound/pick-task/pick-step/{stepId}/close")
    suspend fun closePickStep(@Path("stepId") stepId: String): Response<BaseResponse<IdResponse>>

    //pick step
    @POST("wms-bam/outbound/pick-task/{taskId}/pick-step/location/item/suggest")
    suspend fun suggestLocation(
        @Path("taskId") taskId: String,
        @Body request: LocationSuggestRequestEntity
    ): Response<BaseResponse<LocationItemSuggestEntity>>

    @POST("wms-bam/outbound/pick-task/{taskId}/pick-step/pick-location/item/suggest/{locationId}")
    suspend fun suggestSpecificPickLocation(
        @Path("taskId") taskId: String,
        @Path("locationId") locationId: String,
        @Body request: LocationSuggestRequestEntity
    ): Response<BaseResponse<LocationItemSuggestEntity>>

    @POST("wms-bam/outbound/pick-task/{pickTaskId}/pick-step/location/skip-for-now/{locationId}")
    suspend fun skipPickLocation(
        @Path("pickTaskId") taskId: String,
        @Path("locationId") skipLocationId: String,
        @Body request: LocationSuggestRequestEntity
    ): Response<BaseResponse<LocationItemSuggestEntity>>

    @POST("wms-bam/outbound/pick-task/{taskId}/pick-step/rebuild-pick-strategy")
    suspend fun rebuildPickStrategy(
        @Path("taskId") taskId: String,
        @Body request: LocationSuggestRequestEntity
    ): Response<BaseResponse<LocationItemSuggestEntity>>

    @POST("wms-bam/outbound/pick-task/{taskId}/pick-step/lp/item/suggest/{lpId}")
    suspend fun suggestLpItem(
        @Path("taskId") taskId: String,
        @Path("lpId") lpId: String,
        @Body request: LpItemSuggestRequest
    ): Response<BaseResponse<LocationLpItemSuggestEntity>>

    @POST("wms/outbound/pick-task/{taskId}/pick-step/{stepId}/pick-submit")
    suspend fun submitPickItem(
        @Path("taskId") taskId: String,
        @Path("stepId") stepId: String,
        @Body request: PickItemSubmitEntity
    ): Response<BaseResponse<IdResponse>>

    @GET("wms/outbound/pick-task/pick-step/{pickTaskId}")
    suspend fun getPickStep(@Path("pickTaskId") taskId: String): Response<BaseResponse<BaseTaskStepEntity>>

    //progress
    @GET("wms-bam/outbound/pick-task/pick-step/{stepId}/pick-progress")
    suspend fun getPickStepProgress(@Path("stepId") stepId: String): Response<BaseResponse<List<PickProgressEntity>>>

    @GET("wms-bam/outbound/pick-task/pick-step/{stepId}/pick-progress-by-item")
    suspend fun getPickProgressByItem(@Path("stepId") stepId: String): Response<BaseResponse<List<ProgressItemEntity>>>

    @GET("wms-bam/outbound/pick-task/pick-step/{stepId}/pick-progress-by-lp")
    suspend fun getPickProgressByLp(@Path("stepId") stepId: String): Response<BaseResponse<List<ProgressLPEntity>>>

    @GET("wms-bam/outbound/pick-task/{taskId}/pick-step/pick-progress-by-location")
    suspend fun getPickProgressByLocation(@Path("taskId") taskId: String): Response<BaseResponse<List<ProgressLocationEntity>>>

    @GET("wms-bam/outbound/pick-task/pick-step/{stepId}/pick-progress-by-order")
    suspend fun getPickProgressByOrder(@Path("stepId") stepId: String): Response<BaseResponse<List<ProgressOrderEntity>>>

    @POST("wms-bam/outbound/pick-task/pick-step/bind/tote-cart")
    suspend fun bindToteCart(@Body request: BindToteCartRequestEntity): Response<BaseResponse<EquipmentEntity>>

    @POST("wms-bam/outbound/pick-task/pick-step/unbind/tote-cart")
    suspend fun unbindToteCart(@Body request: BindToteCartRequestEntity): Response<BaseResponse<EquipmentEntity>>

    @PUT("wms/equipment/{id}/release")
    suspend fun releaseToteCart(@Path("id") id: String): Response<BaseResponse<Any>>

    @POST("wms-bam/outbound/pick-task/pick-step/tote/lp/suggest")
    suspend fun suggestTote(@Body request: ToteLpSuggestRequest): Response<BaseResponse<List<String>>>

    //stage step
    @GET("wms-bam/outbound/pick-task/stage-step/{stepId}/stage-progress")
    suspend fun getStageStepProgress(@Path("stepId") stepId: String): Response<BaseResponse<StageProgressEntity>>

    @POST("wms/outbound/pick-task/pick-stage/{stepId}/close")
    suspend fun closeStageStep(@Path("stepId") stepId: String): Response<BaseResponse<IdResponse>>

    @POST("wms/outbound/pick-task/pick-stage/{stepId}/force-close")
    suspend fun forceCloseStageStep(@Path("stepId") stepId: String): Response<BaseResponse<IdResponse>>

    @POST("wms-bam/outbound/pick-task/stage-step/stage/location/suggest")
    suspend fun getStageLocationSuggest(@Body request: StageLocationSuggestRequestEntity): Response<BaseResponse<StageLocationSuggestEntity>>

    @POST("wms-bam/outbound/pick-task/stage-step/stage/submit/validate")
    suspend fun stageValidate(@Body request: StageValidateRequestEntity): Response<BaseResponse<StageValidateResponseEntity>>

    @POST("wms/outbound/pick-task/pick-stage/{stepId}/lp-stage")
    suspend fun stage(@Path("stepId") stepId: String, @Body request: StageSubmitEntity): Response<BaseResponse<IdResponse>>

    @PUT("wms/outbound/pick-task/close/{pickTaskId}")
    suspend fun closeTask(@Path("pickTaskId") pickTaskId: String): Response<BaseResponse<IdResponse>>

    @PUT("wms/outbound/pick-task/force-close/{pickTaskId}")
    suspend fun forceCloseTask(@Path("pickTaskId") pickTaskId: String): Response<BaseResponse<IdResponse>>

    //Order Pick
    @POST("wms-bam/pick/task/{taskId}/suggest-orderPickFromWallStepService-to-pick")
    suspend fun suggestOrderToPick(@Path("taskId") taskId: String): Response<BaseResponse<OrderPickSuggestEntity>>

    @POST("wms-bam/pick/task/{taskId}/step/{stepId}/orderPickFromWallStepService/{orderId}/suggest-item-to-pick")
    suspend fun suggestItemToPick(
        @Path("taskId") taskId: String, @Path("stepId") stepId: String, @Path("orderId") orderId: String,
        @Body request: ItemSuggestRequest
    ): Response<BaseResponse<OrderPickItemSuggestEntity>>

    @POST("wms-bam/pick/task/{taskId}/orderPickFromWallStepService-pick-step/check-can-pick-orders")
    suspend fun checkCanPickOrders(
        @Path("taskId") taskId: String,
        @Body request: CheckCanPickOrders
    ): Response<BaseResponse<List<String>>>

    @PUT("wms/outbound/pick-task/{taskId}/order-pick-from-wall-step/{stepId}/close")
    suspend fun closeOrderPickStep(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @PUT("wms/outbound/pick-task/{taskId}/order-pick-from-wall-step/{stepId}/force-close")
    suspend fun forceCloseOrderPickStep(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @POST("wms/outbound/pick-task/{taskId}/order-pick-from-wall-step/{stepId}/inventory-move")
    suspend fun submitOrderPick(
        @Path("taskId") taskId: String, @Path("stepId") stepId: String,
        @Body request: OrderPickSubmitEntity
    ): Response<BaseResponse<IdResponse>>

    //Stage To Wall
    @GET("wms/outbound/pick-task/{taskId}/stage-to-wall-step/stage-progress")
    suspend fun getStageToWallStepProgress(@Path("taskId") taskId: String): Response<BaseResponse<StageToWallProgressEntity>>

    @PUT("wms/outbound/pick-task/{taskId}/stage-to-wall-step/{stepId}/stage")
    suspend fun stageToWall(@Path("taskId") taskId: String, @Path("stepId") stepId: String, @Body request: StageToWallSubmitEntity): Response<BaseResponse<Any>>

    @PUT("wms/outbound/pick-task/{taskId}/stage-to-wall-step/{stepId}/close")
    suspend fun closeStageToWallStep(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @PUT("wms/outbound/pick-task/{taskId}/stage-to-wall-step/{stepId}/force-close")
    suspend fun forceCloseStageToWallStep(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @POST("wms-bam/opportunity-pick/task/{taskId}/item/{itemId}/get-slot-suggestion")
    suspend fun getSlotSuggestion(@Path("taskId") taskId: String, @Path("itemId") itemId: String, @Body request: SlotSuggestRequest):
            Response<BaseResponse<List<SlotSuggestionEntity>>>

    @GET("wms-bam/opportunity-pick/all-vlg/customer/{customerId}")
    suspend fun getAllVlgByCustomer(@Path("customerId") customerId: String): Response<BaseResponse<List<VirtualLocationGroupEntity>>>

    @PUT("wms/location/virtual-group/occupied/{vlgId}/{planId}")
    suspend fun occupiedVlg(@Path("vlgId") vlgId: String, @Path("planId") planId: String): Response<BaseResponse<Void>>

    //Sorting to Wall
    @PUT("wms/outbound/pick-task/sorting-step/{stepId}/close")
    suspend fun closeSortingStep(@Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @GET("wms-bam/outbound/sorting-to-wall/{taskId}/progress")
    suspend fun getSortingToWallProgress(@Path("taskId") taskId: String): Response<BaseResponse<SortingToWallProgressEntity>>

    @GET("wms-bam/outbound/pick-sorting/{taskId}/{itemId}/slot-suggestion")
    suspend fun getSortingSlotSuggestion(@Path("taskId") taskId: String, @Path("itemId") itemId: String): Response<BaseResponse<SortingSlotSuggestEntity>>

    @POST("wms-bam/outbound/pick-sorting/{taskId}/{itemId}/drop")
    suspend fun dropSortingItem(@Path("taskId") taskId: String, @Path("itemId") itemId: String, @Body request: SortingDropItemEntity): Response<BaseResponse<Any>>

    //return inventory
    @POST("wms/outbound/pick-task/{taskId}/return-to-inventory")
    suspend fun pickReturnInventory(
        @Path("taskId") taskId: String,
        @Body itemReturnList: List<PickReturnRequestEntity>
    ): Response<BaseResponse<Any>>

    @POST("wms/outbound/wms-pick-suggest/rebuild/by-task/{taskId}")
    suspend fun rebuildPickStrategyByTask(@Path("taskId") taskId: String): Response<BaseResponse<Any>>

    @GET("wms-bam/outbound/pick-task/{taskId}/pick-step/return/{keyword}/inventories")
    suspend fun getInventoriesForReturn(@Path("taskId") taskId: String, @Path("keyword") keyword: String): Response<BaseResponse<InventoryForReturnEntity>>

    @POST("wms-bam/outbound/pick-task/{taskId}/pick-step/{stepId}/pick-submit-and-next")
    suspend fun submitPickItemAndNext(
        @Path("taskId") taskId: String,
        @Path("stepId") stepId: String,
        @Body request: PickSubmitEntity
    ): Response<BaseResponse<PickSubmitResultEntity>>

    @PUT("wms/outbound/pick-task/batch-update")
    suspend fun batchUpdateTask(@Body tasks: List<PickTaskUpdateEntity>): Response<BaseResponse<Void>>
}