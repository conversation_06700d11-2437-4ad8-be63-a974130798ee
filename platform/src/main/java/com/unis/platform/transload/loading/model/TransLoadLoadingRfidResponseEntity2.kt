package com.unis.platform.transload.loading.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class TransLoadLoadingRfidResponseEntity2<T>(
    @SerializedName("companyId")
    val companyId: String,
    @SerializedName("facilityId")
    val facilityId: String,
    @SerializedName("method")
    val method: String,
    @SerializedName("data")
    val data: T,
    @SerializedName("clientId")
    val clientId: String,
    @SerializedName("type")
    val type: String
): Serializable
