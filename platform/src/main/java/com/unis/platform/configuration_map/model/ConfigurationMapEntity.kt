package com.unis.platform.configuration_map.model

import java.util.Date

/**
 * 配置映射数据传输对象
 */
data class ConfigurationMapEntity(
    val id: String? = null,
    val customerId: String? = null,
    val tableName: String? = null,
    val valueMapping: Map<String, String>? = null,
    val createdTime: Date? = null,
    val updatedTime: Date? = null,
    val createdBy: String? = null,
    val updatedBy: String? = null
)