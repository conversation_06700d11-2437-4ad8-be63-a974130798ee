package com.unis.platform.configuration_map

import com.linc.platform.http.BaseResponse
import com.unis.platform.configuration_map.model.ConfigurationMapEntity
import com.unis.platform.configuration_map.model.ConfigurationMapSearch
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

interface ConfigurationMapApi {
    /**
     * 搜索配置映射
     *
     * @param query 搜索条件
     * @return 配置映射列表
     */
    @POST("mdm/configuration-map/search")
    suspend fun search(@Body query: ConfigurationMapSearch): Response<BaseResponse<List<ConfigurationMapEntity>>>
}



