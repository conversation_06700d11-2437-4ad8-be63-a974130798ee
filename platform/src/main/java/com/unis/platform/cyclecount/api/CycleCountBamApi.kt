package com.unis.platform.cyclecount.api

import com.linc.platform.http.BaseResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.organization.OrganizationEntity
import com.unis.platform.common.model.organization.OrganizationQueryEntity
import com.unis.platform.common.model.organization.CustomerRelationshipEntity
import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.cyclecount.model.*
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.location_v2.model.LocationRequestEntity
import io.reactivex.rxjava3.core.Observable
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface CycleCountBamApi {

    companion object {
        const val TOP_LEVEL_DIRECTORY = "cyclecount-bam"
    }

    @POST("$TOP_LEVEL_DIRECTORY/count-task/search-by-paging")
    fun searchCountTaskByPaging(@Body countTaskQueryEntity: CountTaskQueryEntity): Observable<Response<BaseResponse<PageResponseEntity<CountTaskEntity>>>>

    @POST("${TOP_LEVEL_DIRECTORY}/cycle-count/count-result/detail/search")
    fun searchCountResult(@Body countResultQueryEntity: CountResultQueryEntity): Observable<Response<BaseResponse<List<CountResultEntity>>>>

    @POST("$TOP_LEVEL_DIRECTORY/cycle-count/count-task/{taskId}/next-location")
    fun getCountTaskNextLocation(@Path("taskId") taskId: String, @Body currentCountLocation: CurrentCountLocation): Observable<Response<BaseResponse<LocationEntity>>>

    /**
     * Validate if a location has been counted
     * @param currentCountLocation Contains ticketId and locationId to validate
     * @return Boolean indicating if the location has been counted
     */
    @POST("$TOP_LEVEL_DIRECTORY/cycle-count/count-task/validate-location-counted")
    fun validateLocationCounted(@Body currentCountLocation: CurrentCountLocation): Observable<Response<BaseResponse<Any>>>
    /*inventory service api start*/

    @POST("${TOP_LEVEL_DIRECTORY}/inventory-app/location/search")
    fun searchLocation(@Body locationQueryEntity: LocationRequestEntity): Observable<Response<BaseResponse<List<LocationEntity>>>>

    @GET("${TOP_LEVEL_DIRECTORY}/inventory-app/location/{id}")
    fun getLocation(@Path("id") locationId: String): Observable<Response<BaseResponse<LocationEntity>>>

    @POST("${TOP_LEVEL_DIRECTORY}/inventory-bam/inventory/search")
    fun searchInventory(@Body countInventoryQueryEntity: CountInventoryQueryEntity): Observable<Response<BaseResponse<List<CountInventoryEntity>>>>

    /*inventory service api end*/

    /*item master api start*/

    @GET("${TOP_LEVEL_DIRECTORY}/item-master-app/item/{id}")
    fun getItem(@Path("id") itemId: String): Observable<Response<BaseResponse<ItemEntity>>>

    @POST("${TOP_LEVEL_DIRECTORY}/item-master-app/item/search")
    fun searchItem(@Body itemQueryEntity: ItemQueryEntity): Observable<Response<BaseResponse<List<ItemEntity>>>>

    @POST("${TOP_LEVEL_DIRECTORY}/item-master-app/item-uom/search")
    fun searchItemUom(@Body itemUomQueryEntity: ItemUomQueryEntity): Observable<Response<BaseResponse<List<ItemUomEntity>>>>

    /*item master  api end*/

    @GET("${TOP_LEVEL_DIRECTORY}/mdm-app/customer/orgId/{orgId}")
    fun getCustomer(@Path("orgId") customerId: String): Observable<Response<BaseResponse<CustomerEntity>>>

    @POST("${TOP_LEVEL_DIRECTORY}/mdm-app/organization/search")
    fun searchOrganization(@Body organizationQueryEntity: OrganizationQueryEntity): Observable<Response<BaseResponse<List<OrganizationEntity>>>>

    @POST("${TOP_LEVEL_DIRECTORY}/mdm-app/customer/orgId/{orgId}/relationship/title/search")
    fun searchCustomerRelationshipTitles(@Path("orgId") orgId: String): Observable<Response<BaseResponse<List<CustomerRelationshipEntity>>>>

}