package com.unis.platform.pick_to_light.mode.group

import com.google.gson.annotations.SerializedName
import java.util.Date

class CreatePickToLightGroupEntity {
    @SerializedName("name")
    var name: String? = null
    
    @SerializedName("ip")
    var ip: String? = null
    
    @SerializedName("port")
    var port: String? = null
    
    @SerializedName("type")
    var type: PickToLightGroupType? = null

    @SerializedName("deviceType")
    var deviceType: PickToLightDeviceType? = null
    
    @SerializedName("createdBy")
    var createdBy: String? = null
    
    @SerializedName("createdTime")
    var createdTime: Date? = null
    
    @SerializedName("updatedBy")
    var updatedBy: String? = null
    
    @SerializedName("updatedTime")
    var updatedTime: Date? = null
} 