package com.unis.platform.pick_to_light.mode.group

import com.google.gson.annotations.SerializedName
import java.util.Date

data class PickToLightGroupEntity(
    @SerializedName("id")
    val id: Long? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("ip")
    val ip: String? = null,

    @SerializedName("port")
    val port: String? = null,

    @SerializedName("type")
    val type: PickToLightGroupType? = null,

    @SerializedName("deviceType")
    val deviceType: PickToLightDeviceType? = null,

    @SerializedName("createdBy")
    val createdBy: String? = null,

    @SerializedName("createdTime")
    val createdTime: Date? = null,

    @SerializedName("updatedBy")
    val updatedBy: String? = null,

    @SerializedName("updatedTime")
    val updatedTime: Date? = null,

    @SerializedName("lightOnCommands")
    val lightOnCommands: String? = null,

    @SerializedName("lightOffCommands")
    val lightOffCommands: String? = null,

    @SerializedName("lightFlashCommands")
    val lightFlashCommands: String? = null,

    @SerializedName("lightSetCommands")
    val lightSetCommands: String? = null,

    @SerializedName("lightUnlockCommands")
    val lightUnlockCommands: String? = null,
)

enum class PickToLightGroupType {
    @SerializedName("EQUIPMENT")
    EQUIPMENT,

    @SerializedName("LOCATION")
    LOCATION
}

enum class PickToLightDeviceType {
    
    @SerializedName("ITEM_PICK_TO_LIGHT_1B")
    ITEM_PICK_TO_LIGHT_1B,   // 不带屏幕

    @SerializedName("ITEM_PICK_TO_LIGHT_1B4D")
    ITEM_PICK_TO_LIGHT_1B4D; // 带屏幕

    fun getDisplayName(): String {
        return when (this) {
            ITEM_PICK_TO_LIGHT_1B4D -> "ITEM PICK TO LIGHT 1B4D"
            ITEM_PICK_TO_LIGHT_1B -> "ITEM PICK TO LIGHT 1B"
        }
    }
} 