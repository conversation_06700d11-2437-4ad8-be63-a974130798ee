package com.unis.platform.receive.api

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.IdResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.receive.model.DockCheckInReqEntity
import com.unis.platform.receive.model.ReceiveTaskEntity
import com.unis.platform.receive.model.ReceiveTaskQueryEntity
import com.unis.platform.receive.model.ReceiveTaskUpdateEntity
import io.reactivex.rxjava3.core.Observable
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface ReceiveTaskApi {

    @POST("wms/inbound/receive-task/dock-check-in")
    fun dockCheckIn(@Body dockCheckInReqEntity: DockCheckInReqEntity): Observable<Response<BaseResponse<Void>>>

    @POST("wms/inbound/receive-task/{taskId}/close")
    suspend fun close(@Path("taskId") taskId: String): Response<BaseResponse<Void>>

    @POST("wms/inbound/receive-task/{taskId}/force-close")
    suspend fun forceClose(@Path("taskId") taskId: String): Response<BaseResponse<Void>>

    @POST("wms-bam/inbound/receive-task/search-by-paging")
    fun searchReceiveTaskByPaging(@Body receiveTaskQueryEntity: TaskQueryEntity): Observable<Response<BaseResponse<PageResponseEntity<ReceiveTaskEntity>>>>

    @POST("wms/inbound/receive-task/search")
    suspend fun searchReceiveTask(@Body receiveTaskQueryEntity: TaskQueryEntity): Response<BaseResponse<List<ReceiveTaskEntity>>>

    @GET("wms-bam/inbound/receive-task/{receiveTaskId}")
    suspend fun getReceiveTask(@Path("receiveTaskId") receiveTaskId: String): Response<BaseResponse<ReceiveTaskEntity>>

    @GET("wms/inbound/receive-task/{receiveTaskId}")
    suspend fun getTask(@Path("receiveTaskId") receiveTaskId: String): Response<BaseResponse<ReceiveTaskEntity>>

    @PUT("wms/inbound/receive-task")
    suspend fun updateReceiveTask(@Body receiveTaskUpdateEntity: ReceiveTaskUpdateEntity): Response<BaseResponse<IdResponse>>

    @PUT("wms/inbound/receive-task/batch-update")
    suspend fun batchUpdateReceiveTask(@Body receiveTaskUpdateEntities: List<ReceiveTaskUpdateEntity>): Response<BaseResponse<List<IdResponse>>>
}