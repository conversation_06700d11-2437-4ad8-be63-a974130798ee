package com.unis.platform.put_away

import com.linc.platform.http.BaseResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.put_away.model.LocationSuggestRequest
import com.unis.platform.put_away.model.PutAwayByItemSubmitEntity
import com.unis.platform.put_away.model.PutAwayByItemSuggestionEntity
import com.unis.platform.put_away.model.PutAwayHistoryEntity
import com.unis.platform.put_away.model.PutAwayLpDetailEntity
import com.unis.platform.put_away.model.PutAwayLpInfoEntity
import com.unis.platform.put_away.model.PutAwaySearchEntity
import com.unis.platform.put_away.model.PutAwaySubmitEntity
import com.unis.platform.put_away.model.PutAwayTaskCreate
import com.unis.platform.put_away.model.PutAwayTaskEntity
import com.unis.platform.put_away.model.PutAwayTaskUpdate
import com.unis.platform.put_away.model.PutAwayTaskUpdateEntity
import com.unis.platform.put_away.model.UpdateReceivedLpQtyEntity
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface PutAwayApiService {

    @GET("wms/inbound/put-away-task/get/{id}")
    suspend fun getPutAwayTask(@Path("id") id: String): Response<BaseResponse<PutAwayTaskEntity>>

    @POST("wms/inbound/put-away-task/create")
    suspend fun createPutAwayTask(@Body putAwayTaskCreate: PutAwayTaskCreate): Response<BaseResponse<String>>

    @PUT("wms/inbound/put-away-task/update")
    suspend fun updatePutAwayTask(@Body update: PutAwayTaskUpdate): Response<BaseResponse<Any>>

    @POST("wms/inbound/put-away-task/search")
    suspend fun searchPutAwayTask(@Body putAwayTaskCreate: PutAwaySearchEntity): Response<BaseResponse<List<PutAwayTaskEntity>>>

    @POST("wms/inbound/put-away-task/search-by-paging")
    suspend fun searchPutAwayTaskByPaging(@Body putAwayTaskCreate: TaskQueryEntity): Response<BaseResponse<PageResponseEntity<PutAwayTaskEntity>>>

    @POST("wms/inbound/put-away-task/{taskId}/step/{stepId}/by-lp/put-away")
    suspend fun putAwayByLp(
        @Path("taskId") taskId: String,
        @Path("stepId") stepId: String,
        @Body request: PutAwaySubmitEntity
    ): Response<BaseResponse<Any>>

    @POST("wms/inbound/put-away-task/close/{id}")
    suspend fun closePutAwayTask(@Path("id") id: String): Response<BaseResponse<Any>>

    @POST("wms-bam/inbound/put-away-task/{taskId}/step/by-lp/scan")
    suspend fun scanPutAwayLP(@Path("taskId") id: String, @Body request: PutAwayLpDetailEntity): Response<BaseResponse<List<PutAwayLpInfoEntity>>>

    @POST("wms-bam/inbound/put-away/location-suggest")
    suspend fun getSuggestLocation(@Body request: LocationSuggestRequest): Response<BaseResponse<PutAwayByItemSuggestionEntity>>

    @GET("wms-bam/inbound/put-away-task/{taskId}/put-away-history")
    suspend fun getPutAwayHistory(@Path("taskId") taskId: String): Response<BaseResponse<List<PutAwayHistoryEntity>>>

    @POST("wms/inbound/put-away-task/{taskId}/step/{stepId}/by-item/put-away")
    suspend fun putAwayByItem(
        @Path("taskId") taskId: String, @Path("stepId") stepId: String, @Body request: PutAwayByItemSubmitEntity
    ): Response<BaseResponse<Any>>

    @POST("wms-bam/inbound/put-away/location-suggest-by-lp/{lpId}")
    suspend fun getSingleLpMultipleItemSuggestLocation(
        @Path("lpId") lpId: String,
        @Body request: LocationSuggestRequest
    ): Response<BaseResponse<PutAwayByItemSuggestionEntity>>

    @GET("wms-bam/inbound/validate/lp-create-new-putaway-task/{lpId}")
    suspend fun validateLpCreateNewPutawayTask(@Path("lpId") lpId: String): Response<BaseResponse<Any>>

    @POST("wms/inbound/put-away-task/update-lp-qty")
    suspend fun updateLpQty(@Body request: UpdateReceivedLpQtyEntity): Response<BaseResponse<Any>>
    
    @GET("wms-bam/inbound/put-away/sub-allow-days/{itemId}")
    suspend fun getSubAllowDay(@Path("itemId") itemId: String): Response<BaseResponse<Int>>

    @PUT("wms/inbound/put-away-task/batch-update")
    suspend fun batchUpdateTask(@Body tasks: List<PutAwayTaskUpdateEntity>): Response<BaseResponse<List<String>>>
}