package com.unis.platform.movement.api

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.IdResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.inventory.model.InventoryDetailEntity
import com.unis.platform.movement.model.*
import io.reactivex.rxjava3.core.Observable
import retrofit2.Response
import retrofit2.http.*

interface MovementTaskApi {

    @POST("wms/inventory/movement-task")
    fun createTask(@Body movementTaskCreateEntity: MovementTaskCreateEntity): Observable<Response<BaseResponse<IdResponse>>>

    @POST("wms/inventory/movement-task/search-by-paging")
    fun searchMovementTaskByPaging(@Body taskQueryEntity: TaskQueryEntity): Observable<Response<BaseResponse<PageResponseEntity<MovementTaskEntity>>>>

    @GET("wms/inventory/movement-task/{id}")
    fun getTask(@Path("id") taskId: String): Observable<Response<BaseResponse<MovementTaskEntity>>>

    @PUT("wms/inventory/movement-task/{id}")
    fun bindEquipment(@Path("id") taskId: String, @Body bindEquipmentEntity: BindEquipmentEntity): Observable<Response<BaseResponse<Void>>>

    @POST("wms/inventory/movement-task/{taskId}/step/{stepId}/collect")
    fun collect(@Path("taskId") taskId: String, @Path("stepId") stepId: String, @Body collectCreateEntity: CollectCreateEntity): Observable<Response<BaseResponse<Void>>>

    @POST("wms/inventory/movement-task/{taskId}/step/{stepId}/drop")
    fun drop(@Path("taskId") taskId: String, @Path("stepId") stepId: String, @Body dropCreateEntity: DropCreateEntity): Observable<Response<BaseResponse<Void>>>

    @PUT("wms/inventory/movement-task/{taskId}/close")
    fun closeTask(@Path("taskId") taskId: String): Observable<Response<BaseResponse<Void>>>

    @PUT("wms/inventory/movement-task/{taskId}/step/{stepId}/close")
    fun closeStep(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Observable<Response<BaseResponse<Void>>>

    @POST("wms/inventory/movement-task/{taskId}/step/{stepId}/batch-movement")
    fun batchMovement(@Path("taskId") taskId: String, @Path("stepId") stepId: String, @Body batchMovementRequest: List<DropCreateEntity>): Observable<Response<BaseResponse<Void>>>

    @POST("wms-bam/inventory/movement-task/search-inventory-lp")
    fun searchInventoryLP(@Body searchInventoryLPRequest: SearchInventoryLpRequest): Observable<Response<BaseResponse<InventoryDetailEntity>>>

    @PUT("wms/inventory/movement-task/batch-update")
    suspend fun batchUpdateTask(@Body tasks: List<MovementTaskUpdateEntity>): Response<BaseResponse<Void>>
}