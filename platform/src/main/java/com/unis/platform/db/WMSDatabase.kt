package com.unis.platform.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.linc.platform.print.model.PrintServerEntry
import com.linc.platform.print.model.PrinterEntry
import com.linc.platform.utils.ResUtil
import com.unis.platform.common.model.company.CompanyEntity
import com.unis.platform.db.company.CompanyDao
import com.unis.platform.db.facility.FacilityDao
import com.unis.platform.db.printer.PrintServerDao
import com.unis.platform.db.printer.PrinterDao
import com.unis.platform.facility_v2.mode.FacilityEntity

@Database(
    entities = [
        FacilityEntity::class,
        CompanyEntity::class,
        PrinterEntry::class,
        PrintServerEntry::class
    ], version = 4, exportSchema = false
)
abstract class WMSDatabase : RoomDatabase() {

    companion object {
        private const val DATABASE_NAME = "WMS_DB"
        val instance: WMSDatabase by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            Room.databaseBuilder(ResUtil.getContext(), WMSDatabase::class.java, DATABASE_NAME)
                .addMigrations(RoomUpgradeUtil.MIGRATION_1_2)
                .addMigrations(RoomUpgradeUtil.MIGRATION_2_3)
                .addMigrations(RoomUpgradeUtil.MIGRATION_3_4)
                .allowMainThreadQueries().build()
        }
    }

    abstract fun facilityDao(): FacilityDao

    abstract fun companyDao(): CompanyDao

    abstract fun printerDao(): PrinterDao

    abstract fun printServerDao(): PrintServerDao
}