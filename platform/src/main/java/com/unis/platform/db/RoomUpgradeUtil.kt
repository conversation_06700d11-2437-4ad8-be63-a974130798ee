package com.unis.platform.db

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * @see RoomUpgrade.md
 */
object RoomUpgradeUtil {

    val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE facility ADD COLUMN autoReturnToLocation INTEGER DEFAULT 0")
        }
    }

    val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE facility ADD COLUMN enableScanditToScanBarcode INTEGER DEFAULT 0")
        }
    }

    val MIGRATION_3_4 = object : Migration(3, 4) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE facility ADD COLUMN allowOTControl INTEGER DEFAULT 0")
        }
    }
}