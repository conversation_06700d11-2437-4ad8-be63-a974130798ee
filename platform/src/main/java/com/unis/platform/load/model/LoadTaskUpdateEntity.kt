package com.unis.platform.load.model

import com.unis.platform.common.model.task.TaskPriority
import java.io.Serializable
    
data class LoadTaskUpdateEntity(
    var id: String? = null,
    var dockId: String? = null,
    var loadIds: List<String>? = null,
    var priority: TaskPriority? = null,
    var preAssigneeUserId: String? = null,
    var assigneeUserId: String? = null,
    var truckPhotoIds: List<String>? = null,
    var carrierId: String? = null,
    var entryId: String? = null,
    var forkliftId: String? = null
) : Serializable 