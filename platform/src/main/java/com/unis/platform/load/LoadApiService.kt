package com.unis.platform.load

import com.linc.platform.http.BaseResponse
import com.unis.platform.common.model.LoadTaskDescribeEntity
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.common.model.tasknode.TaskResultEntity
import com.unis.platform.load.model.CollectSealEntity
import com.unis.platform.load.model.LoadTaskUpdateEntity
import com.unis.platform.load.model.dockcheck.DockCheckEntity
import com.unis.platform.load.model.dockcheck.DockReleaseEntity
import com.unis.platform.load.model.inspect.EquipmentInspectEntity
import com.unis.platform.load.model.load.BolTypeEntity
import com.unis.platform.load.model.load.LoadEntity
import com.unis.platform.load.model.load.LoadOrderEntity
import com.unis.platform.load.model.load.LoadQueryEntity
import com.unis.platform.load.model.load.LoadUpdateEntity
import com.unis.platform.load.model.load.ProNoAddEntity
import com.unis.platform.load.model.load.UploadSignatureEntity
import com.unis.platform.load.model.task.LoadTaskEntity
import com.unis.platform.load.model.task.LoadingRequest
import com.unis.platform.load.model.task.UnloadAllRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface LoadApiService {

    @POST("wms-bam/outbound/load-task/search-by-paging")
    suspend fun searchByPaging(@Body request: TaskQueryEntity): Response<BaseResponse<PageResponseEntity<LoadTaskEntity>>>

    @GET("wms-bam/outbound/load-task/{id}")
    suspend fun getLoadTask(@Path("id") id: String): Response<BaseResponse<LoadTaskEntity>>

    @POST("wms/outbound/load-task/{taskId}/dock-check")
    suspend fun dockCheckIn(@Path("taskId") taskId: String, @Body request: DockCheckEntity): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/equipment-inspect")
    suspend fun equipmentInspect(
        @Path("taskId") taskId: String,
        @Body request: List<EquipmentInspectEntity>
    ): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/uccPrinted-check")
    suspend fun checkUccPrintedLabel(@Path("taskId") taskId: String): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/check-pallet-config")
    suspend fun checkPalletConfig(@Path("taskId") taskId: String): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/current-node-name")
    suspend fun getProcessNode(@Path("taskId") taskId: String): Response<BaseResponse<TaskResultEntity>>

    ////////step////////
    @POST("wms/outbound/step/loading/{stepId}/batch-load-lp")
    suspend fun batchLoadSlp(@Path("stepId") stepId: String, @Body request: List<LoadingRequest>): Response<BaseResponse<Any>>

    @POST("wms/outbound/step/loading/{stepId}/load-lp")
    suspend fun loadSlp(@Path("stepId") stepId: String, @Body request: LoadingRequest): Response<BaseResponse<Any>>

    @POST("wms/outbound/step/loading/{stepId}/unload-lp")
    suspend fun unLoadSlp(@Path("stepId") stepId: String, @Body request: LoadingRequest): Response<BaseResponse<Any>>

    @POST("wms/outbound/loading/step/{stepId}/unload-all-lps")
    suspend fun unloadAllLps(@Path("stepId") stepId: String, @Body request: UnloadAllRequest): Response<BaseResponse<Any>>

    @POST("wms-bam/outbound/load/search-by-paging")
    suspend fun searchLoadByPaging(@Body query: LoadQueryEntity): Response<BaseResponse<PageResponseEntity<LoadEntity>>>

    @POST("wms-bam/outbound/load/search")
    suspend fun searchLoad(@Body query: LoadQueryEntity): Response<BaseResponse<List<LoadEntity>>>

    @PUT("wms/outbound/load/{loadId}")
    suspend fun updateLoad(@Path("loadId") loadId: String, @Body request: LoadUpdateEntity): Response<BaseResponse<Any>>

    @GET("wms/outbound/load/{loadId}")
    suspend fun getLoad(@Path("loadId") loadId: String): Response<BaseResponse<LoadEntity>>

    @POST("wms/outbound/step/loading/{stepId}/complete")
    suspend fun closeStep(@Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @POST("wms/outbound/step/loading/{stepId}/close-with-task")
    suspend fun closeWithTask(@Path("stepId") stepId: String): Response<BaseResponse<Any>>

    @GET("wms-bam/outbound/load-task/load/{loadId}/load-order-view")
    suspend fun getLoadOrderView(@Path("loadId") loadId: String): Response<BaseResponse<LoadOrderEntity>>

    //Signature
    @POST("wms/outbound/load-task/{taskId}/load/{loadId}/signature")
    suspend fun uploadLoadSignature(
        @Path("taskId") taskId: String,
        @Path("loadId") loadId: String,
        @Body cmd: UploadSignatureEntity
    ): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/order/{orderId}/signature")
    suspend fun uploadOrderSignature(
        @Path("taskId") taskId: String,
        @Path("orderId") loadId: String,
        @Body cmd: UploadSignatureEntity
    ): Response<BaseResponse<TaskResultEntity>>

    @POST("wms/outbound/load-task/{taskId}/load/{loadId}/complete")
    suspend fun completeLoad(
        @Path("taskId") taskId: String,
        @Path("loadId") loadId: String,
        @Body cmd: ProNoAddEntity
    ): Response<BaseResponse<TaskResultEntity>>

    @POST("wms-bam/outbound/bol/{loadId}/master-bol")
    suspend fun buildMasterBol(@Path("loadId") loadId: String, @Body request: BolTypeEntity): Response<BaseResponse<List<String>>>

    @POST("wms-bam/outbound/bol/order/{orderId}")
    suspend fun buildBolByOrder(@Path("orderId") orderId: String, @Body request: BolTypeEntity): Response<BaseResponse<List<String>>>

    @POST("wms/outbound/load-task/{taskId}/signature/complete")
    suspend fun completeSignature(@Path("taskId") taskId: String): Response<BaseResponse<TaskResultEntity>>

    // Collect Seal
    @POST("wms/outbound/load-task/{taskId}/collect-seal")
    suspend fun collectSeal(@Path("taskId") taskId: String, @Body sealCmd: CollectSealEntity): Response<BaseResponse<Any>>

    @POST("wms/outbound/load-task/{taskId}/collect-task-info/complete")
    suspend fun completeCollectTaskInfo(@Path("taskId") taskId: String): Response<BaseResponse<TaskResultEntity>>


    @POST("wms/outbound/load-task/{taskId}/close")
    suspend fun closeTask(@Path("taskId") taskId: String): Response<BaseResponse<Any>>

    //add force close task
    @POST("wms/outbound/load-task/{taskId}/forceClose")
    suspend fun forceCloseTask(@Path("taskId") taskId: String): Response<BaseResponse<Any>>

    @PUT("wms/wms-location/dock-release")
    suspend fun dockRelease(@Body request: DockReleaseEntity): Response<BaseResponse<Any>>

    @PUT("wms/outbound/load-task/batch-update")
    suspend fun batchUpdateTask(@Body tasks: List<LoadTaskUpdateEntity>): Response<BaseResponse<Void>>

    @GET("wms-bam/outbound/load-task/{taskId}/describe")
    suspend fun getLoadTaskDescribe(@Path("taskId") taskId: String): Response<BaseResponse<LoadTaskDescribeEntity>>
}