package com.unis.platform.replenishment.api

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.IdResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.replenishment.model.*
import io.reactivex.rxjava3.core.Observable
import retrofit2.Response
import retrofit2.http.*

interface ReplenishmentTaskApi {

    @POST("wms/outbound/replenishment-task/create")
    fun createTask(@Body replenishmentTaskCreateEntity: ReplenishmentTaskCreateEntity): Observable<Response<BaseResponse<IdResponse>>>

    @POST("wms-bam/outbound/replenishment-task/search-by-paging")
    fun searchReplenishmentTaskByPaging(@Body replenishmentTaskQueryEntity: TaskQueryEntity): Observable<Response<BaseResponse<PageResponseEntity<ReplenishmentTaskEntity>>>>

    @GET("wms/outbound/replenishment-task/{id}")
    fun getTask(@Path("id") taskId: String): Observable<Response<BaseResponse<ReplenishmentTaskEntity>>>

    @PUT("wms/outbound/replenishment-task/{taskId}/{hlpId}/bind-equipment")
    fun bindEquipment(@Path("taskId") taskId: String, @Path("hlpId") hlpId: String): Observable<Response<BaseResponse<Void>>>

    @POST("wms/outbound/replenishment-task/replenish-step/collect")
    fun collect(@Body collectCreateEntity: CollectCreateEntity): Observable<Response<BaseResponse<Void>>>

    @POST("wms/outbound/replenishment-task/replenish-step/drop")
    fun drop(@Body dropCreateEntity: DropCreateEntity): Observable<Response<BaseResponse<Void>>>

    @PUT("wms/outbound/replenishment-task/{taskId}/replenish-step/close/{stepId}")
    fun closeStepAndTask(@Path("taskId") taskId: String, @Path("stepId") stepId: String): Observable<Response<BaseResponse<Void>>>

    @GET("wms-bam/outbound/replenishment-task/{taskId}/replenish-step/collect-suggest")
    fun getCollectSuggestion(@Path("taskId") taskId: String): Observable<Response<BaseResponse<ReplenishmentStrategySuggestionEntity>>>

    @GET("wms-bam/outbound/replenishment-task/{taskId}/replenish-step/drop-suggest")
    fun getDropSuggestion(@Path("taskId") taskId: String): Observable<Response<BaseResponse<ReplenishmentStrategySuggestionEntity>>>

    @GET("wms-bam/outbound/replenishment-task/{taskId}/replenish-step/collected-list")
    fun getCollectedList(@Path("taskId") taskId: String): Observable<Response<BaseResponse<List<ReplenishmentCollectedEntity>>>>

    @PUT("wms/outbound/replenishment-task/batch-update")
    suspend fun batchUpdateTask(@Body tasks: List<ReplenishmentTaskUpdateEntity>): Response<BaseResponse<Void>>
}