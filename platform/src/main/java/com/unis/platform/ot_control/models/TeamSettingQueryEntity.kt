package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.PageQueryEntity

class TeamSettingQueryEntity(

    @SerializedName("id")
    var id: Long? = null,

    @SerializedName("name")
    var name: String? = null,

    @SerializedName("customerGroupId")
    var customerGroupId: Long? = null,

    @SerializedName("customerGroupIds")
    var customerGroupIds: List<Long>? = null,

    @SerializedName("shiftId")
    var shiftId: Long? = null,

    @SerializedName("serviceTypes")
    var serviceTypes: List<String>? = null,

    @SerializedName("enableAutoAssignment")
    var enableAutoAssignment: Boolean? = null,

    @SerializedName("supervisorIds")
    var supervisorIds: List<String>? = null,

    @SerializedName("otApproverIds")
    var otApproverIds: List<String>? = null,

    @SerializedName("upperApproverIds")
    var upperApproverIds: List<String>? = null
) : PageQueryEntity()