package com.unis.platform.ot_control.api

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.IdResponse
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.ot_control.models.LaborEntity
import com.unis.platform.ot_control.models.LaborTypeQueryEntity
import com.unis.platform.ot_control.models.OTControlSettingEntity
import com.unis.platform.ot_control.models.OTControlSettingQueryEntity
import com.unis.platform.ot_control.models.OtCountDownTimerQueryEntity
import com.unis.platform.ot_control.models.OtCountDownTimerRemainTimeEntity
import com.unis.platform.ot_control.models.OtRequestDetailEntity
import com.unis.platform.ot_control.models.OtRequestEntity
import com.unis.platform.ot_control.models.OtRequestQueryEntity
import com.unis.platform.ot_control.models.OtRequestUpdateEntity
import com.unis.platform.ot_control.models.ShiftEntity
import com.unis.platform.ot_control.models.ShiftSettingQueryEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingCreateEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingQueryEntity
import com.unis.platform.ot_control.models.TeamLaborsSettingUpdateEntity
import com.unis.platform.ot_control.models.TeamSettingEntity
import com.unis.platform.ot_control.models.TeamSettingQueryEntity
import com.unis.platform.ot_control.models.UserPayTypeEntity
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

/**
 * <AUTHOR> on 2024/12/19
 */
interface OtControlApi {

    @POST("wms/labor/setting/team/search-by-paging")
    suspend fun searchTeamsByPaging(@Body query: TeamSettingQueryEntity): Response<BaseResponse<PageResponseEntity<TeamSettingEntity>>>

    @POST("wms-bam/labor/setting/team/search")
    suspend fun searchTeams(@Body query: TeamSettingQueryEntity): Response<BaseResponse<List<TeamSettingEntity>>>

    @POST("wms/labor/setting/labor-type/search-by-paging")
    suspend fun searchLabors(@Body query: LaborTypeQueryEntity): Response<BaseResponse<PageResponseEntity<LaborEntity>>>

    @POST("wms/labor/setting/shift-setting/search-by-paging")
    suspend fun searchShifts(@Body query: ShiftSettingQueryEntity): Response<BaseResponse<PageResponseEntity<ShiftEntity>>>

    @POST("wms/labor/setting/ot-control/search-by-paging")
    suspend fun searchOtControlSettings(@Body query: OTControlSettingQueryEntity): Response<BaseResponse<PageResponseEntity<OTControlSettingEntity>>>

    @POST("wms/labor/setting/team-labors/search")
    suspend fun searchTeamLabors(@Body query: TeamLaborsSettingQueryEntity): Response<BaseResponse<List<TeamLaborsSettingEntity>>>

    @POST("wms/labor/setting/team-labors")
    suspend fun createTeamLabors(@Body request: TeamLaborsSettingCreateEntity): Response<BaseResponse<IdResponse>>

    @PUT("wms/labor/setting/team-labors/{id}")
    suspend fun updateTeamLabors(@Path("id") id: Long, @Body request: TeamLaborsSettingUpdateEntity): Response<BaseResponse<Void>>

    @POST("wms/labor/ot-request")
    suspend fun createOtRequest(@Body request: OtRequestEntity): Response<BaseResponse<IdResponse>>

    @POST("wms/labor/ot-request/search")
    suspend fun queryOtRequests(@Body query: OtRequestQueryEntity): Response<BaseResponse<List<OtRequestDetailEntity>>>

    @POST("wms-bam/labor/setting/ot-request/search-by-paging")
    suspend fun queryOtRequestsByPaging(@Body query: OtRequestQueryEntity): Response<BaseResponse<PageResponseEntity<OtRequestDetailEntity>>>

    @PUT("wms/labor/ot-request/{id}")
    suspend fun updateOtRequest(@Path("id") id: Long, @Body request: OtRequestUpdateEntity): Response<BaseResponse<Void>>

    @GET("wms-bam/labor/setting/ot-request/{id}")
    suspend fun getOtRequest(@Path("id") id: Long): Response<BaseResponse<OtRequestDetailEntity>>

    @POST("wms-bam/labor/setting/get-work-hours")
    suspend fun getOtCountDownTimerTotalTime(@Body query: OtCountDownTimerQueryEntity): Response<BaseResponse<List<OtCountDownTimerRemainTimeEntity>>>

    @GET("wms-bam/labor/{userId}/pay-type")
    suspend fun getUserPayType(@Path("userId") userId: String): Response<BaseResponse<UserPayTypeEntity>>
}