package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.PageQueryEntity

data class TeamLaborsSettingQueryEntity(
    @SerializedName("id")
    var id: Long? = null,

    @SerializedName("teamId")
    var teamId: Long? = null,

    @SerializedName("laborTypeId")
    var laborTypeId: Long? = null,

    @SerializedName("laborUserId")
    var laborUserId: String? = null,

    @SerializedName("laborUserIds")
    var laborUserIds: List<String>? = null,

) : PageQueryEntity()