package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class OTControlSettingEntity(
    @SerializedName("id")
    val id: Long? = null,

    @SerializedName("type")
    val type: OtControlSettingType? = null,

    @SerializedName("condition")
    val condition: String? = null,

    @SerializedName("hours")
    val hours: Int? = null
) : Serializable