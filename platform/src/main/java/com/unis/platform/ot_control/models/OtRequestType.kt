package com.unis.platform.ot_control.models

import java.io.Serializable

enum class OtRequestType : Serializable{
    EXTENDED_HOURS,
    DAILY_OT,
    DAILY_DT,
    WEEKLY_OT
}

fun OtRequestType?.getDisplayName(): String {
    this?: return ""
    return when (this) {
        OtRequestType.EXTENDED_HOURS -> "Extended Hours"
        OtRequestType.DAILY_OT -> "Daily OT"
        OtRequestType.DAILY_DT -> "Daily DT"
        OtRequestType.WEEKLY_OT -> "Weekly OT"
    }
}
