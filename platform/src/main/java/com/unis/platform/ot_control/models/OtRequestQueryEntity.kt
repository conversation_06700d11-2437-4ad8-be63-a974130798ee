package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.PageQueryEntity
import java.util.Date

class OtRequestQueryEntity(
    @SerializedName("id")
    var id: Long? = null,

    @SerializedName("laborUserId")
    var laborUserId: String? = null,

    @SerializedName("processedBy")
    var processedBy: String? = null,

    @SerializedName("submitTimeFrom")
    var submitTimeFrom: Date? = null,

    @SerializedName("submitTimeTo")
    var submitTimeTo: Date? = null,

    @SerializedName("processedTimeFrom")
    var processedTimeFrom: Date? = null,

    @SerializedName("processedTimeTo")
    var processedTimeTo: Date? = null,

    @SerializedName("status")
    var status: OtRequestStatus? = null,

    @SerializedName("statuses")
    var statuses: List<OtRequestStatus>? = null
): PageQueryEntity()