package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * <AUTHOR> on 2024/9/3
 */
data class ShiftEntity(
    @SerializedName("id") val id: Long? = null,
    @SerializedName("shiftName") val shiftName: String? = null,
    @SerializedName("workHourFrom") val workHourFrom: String? = null,
    @SerializedName("workHourTo") val workHourTo: String? = null,
) : Serializable