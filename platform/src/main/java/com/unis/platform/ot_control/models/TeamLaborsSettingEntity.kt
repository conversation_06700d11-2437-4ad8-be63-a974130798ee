package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class TeamLaborsSettingEntity(
    @SerializedName("id") val id: Long? = null,
    @SerializedName("teamId") val teamId: Long? = null,
    @SerializedName("laborTypeId") val laborTypeId: Long? = null,
    @SerializedName("laborUserId") val laborUserId: String? = null,
    @SerializedName("operationTypes") val operationTypes: List<String>? = null
) : Serializable 