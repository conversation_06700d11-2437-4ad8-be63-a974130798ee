package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class OtCountDownTimerRemainTimeEntity(

    @SerializedName("userId")
    val userId: String?,

    @SerializedName("employeeId")
    val employeeId: String?,

    @SerializedName("dailyWorkHours")
    val dailyWorkHours: Double?,

    @SerializedName("weeklyWorkHours")
    val weeklyWorkHours: Double?,

    @SerializedName("remainTimeOfDailyWork")
    val remainTimeOfDailyWork: Long?,
    
    @SerializedName("remainTimeOfWeeklyWork")
    val remainTimeOfWeeklyWork: Long?,

    @SerializedName("remainTimeOfOTWork")
    val remainTimeOfOTWork: Long?,

    @SerializedName("otRequestId")
    val otRequestId: Long?,

) : Serializable {

    fun getRemainTimeOfDailyWork(): Long {
        return remainTimeOfDailyWork ?: -1L
    }

    fun getRemainTimeOfWeeklyWork(): Long {
        return remainTimeOfWeeklyWork ?: -1L
    }

    fun getRemainTimeOfOTWork(): Long {
        return remainTimeOfOTWork ?: -10L
    }

    fun getOtRequestId(): Long {
        return otRequestId ?: -1L
    }
}