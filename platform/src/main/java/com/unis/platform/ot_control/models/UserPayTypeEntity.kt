package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class UserPayTypeEntity(
    @SerializedName("id") var id: String? = null,
    @SerializedName("employeeCode") var employeeCode: String? = null,
    @SerializedName("payType") var payType: String? = null,
    @SerializedName("payTypeName") var payTypeName: String? = null
) : Serializable {
    companion object {
        const val PAY_TYPE_HOURLY = "1"//Hourly
        const val PAY_TYPE_Salary = "2"//Salary
        const val PAY_TYPE_Other = "3"//Other
    }
}
