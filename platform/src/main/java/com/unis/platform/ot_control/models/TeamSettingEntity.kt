package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * <AUTHOR> on 2024/9/3
 */
data class TeamSettingEntity(
    @SerializedName("id") val id: Long? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("serviceTypes") val serviceTypes: List<String>? = null,
    @SerializedName("serviceTypeMap") val serviceTypeMap: Map<String, OperationTypeSettingEntity>? = null,
    @SerializedName("shiftId") val shiftId: Long? = null,
    @SerializedName("supervisorIds") val supervisorIds: List<String>? = null,
    @SerializedName("otApproverIds") val otApproverIds: List<String>? = null,
    @SerializedName("upperApproverIds") val upperApproverIds: List<String>? = null,
    var shift: ShiftEntity? = null
) : Serializable