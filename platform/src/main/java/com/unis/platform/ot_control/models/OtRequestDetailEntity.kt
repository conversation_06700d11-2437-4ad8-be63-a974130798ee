package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.util.Date

data class OtRequestDetailEntity(
    @SerializedName("id")
    val id: Long? = null,

    @SerializedName("requestType")
    val requestType: OtRequestType? = null,

    @SerializedName("duration")
    val duration: Int? = null, //duration (minutes)

    @SerializedName("status")
    val status: OtRequestStatus? = null,

    @SerializedName("laborUserId")
    val laborUserId: String? = null,

    @SerializedName("laborUserName")
    val laborUserName: String? = null,

    @SerializedName("employeeId")
    val employeeId: String? = null,

    @SerializedName("teamId")
    val teamId: Long? = null,

    @SerializedName("teamName")
    val teamName: String? = null,

    @SerializedName("laborTypeId")
    val laborTypeId: Long? = null,

    @SerializedName("laborTypeName")
    val laborTypeName: String? = null,

    @SerializedName("reason")
    val reason: String? = null,

    @SerializedName("submitTime")
    val submitTime: Date? = null,

    @SerializedName("assignedApproverIds")
    val assignedApproverIds: List<String>? = null,

    @SerializedName("processedBy")
    val processedBy: String? = null,

    @SerializedName("processedTime")
    val processedTime: Date? = null,

    @SerializedName("startTime")
    val startTime: Date? = null,

    @SerializedName("endTime")
    val endTime: Date? = null,

    @SerializedName("createdBy")
    val createdBy: String? = null,

    @SerializedName("createdTime")
    val createdTime: Date? = null,

    @SerializedName("updatedBy")
    val updatedBy: String? = null,

    @SerializedName("updatedTime")
    val updatedTime: Date? = null,

    @SerializedName("submitType")
    val submitType: OtSubmitTypeEntity? = null,

    @SerializedName("state")
    val state: String? = null,

    @SerializedName("dailyWorkHours")
    val dailyWorkHours: Double? = null,

    @SerializedName("weeklyWorkHours")
    val weeklyWorkHours: Double? = null,
) : Serializable 