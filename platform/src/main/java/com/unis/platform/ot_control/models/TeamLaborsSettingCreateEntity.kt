package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class TeamLaborsSettingCreateEntity(
    @SerializedName("teamId") var teamId: Long? = null,
    @SerializedName("laborTypeId") var laborTypeId: Long? = null,
    @SerializedName("laborUserId") var laborUserId: String? = null,
    @SerializedName("operationTypes") var operationTypes: List<String>? = null
) : Serializable 