package com.unis.platform.ot_control.models

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class OtRequestEntity(
    @SerializedName("requestType")
    var requestType: OtRequestType? = null,
    
    @SerializedName("duration")
    var duration: Int? = null,
    
    @SerializedName("laborUserId")
    var laborUserId: String? = null,
    
    @SerializedName("teamId")
    var teamId: Long? = null,
    
    @SerializedName("laborTypeId")
    var laborTypeId: Long? = null,
    
    @SerializedName("reason")
    var reason: String? = null,

    @SerializedName("state")
    var state: String? = null,

    @SerializedName("dailyWorkHours")
    var dailyWorkHours: Double? = null,

    @SerializedName("weeklyWorkHours")
    var weeklyWorkHours: Double? = null,

    @SerializedName("submitType")
    var submitType: OtSubmitTypeEntity? = null,

) : Serializable