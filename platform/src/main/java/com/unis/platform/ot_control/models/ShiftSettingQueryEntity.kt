package com.unis.platform.ot_control.models

import com.unis.platform.common.model.PageQueryEntity
import com.google.gson.annotations.SerializedName

class ShiftSettingQueryEntity(
    @SerializedName("id")
    var id: Long? = null,

    @SerializedName("shiftName")
    var shiftName: String? = null,

    @SerializedName("shiftNames")
    var shiftNames: List<String>? = null,

    @SerializedName("workDays")
    var workDays: List<String>? = null,

    @SerializedName("workHourFrom")
    var workHourFrom: String? = null,

    @SerializedName("workHourTo")
    var workHourTo: String? = null
) : PageQueryEntity() 