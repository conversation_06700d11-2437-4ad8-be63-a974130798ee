<resources>
    <string name="app_name">WISE</string>
    <string name="app_name_logo">WISE.APP</string>
    <string name="voice_app_id">5a39cddf</string>
    <string name="title_activity_login">Sign in</string>
    <string name="task_done">Task done</string>
    <string name="success">Success!</string>
    <string name="take_over_success_wait_confirm">Take over success and wait confirm</string>
    <string name="take_over_success">Take over success</string>
    <string name="common_lang_setting">Language</string>
    <string name="common_lang_en" translatable="false">English</string>
    <string name="common_lang_zh" translatable="false">中文</string>
    <string name="in_service">In Service</string>
    <string name="notify_channel_general">General Notification</string>
    <string name="notify_channel_general_description">Notification Message</string>
    <string name="text_tip">Tip</string>

    <!-- QC Inspection Strings -->
    <string name="text_pass">Pass</string>
    <string name="text_fail">Fail</string>
    <string name="text_comments">Comments</string>
    <string name="hint_input_comment">Please input Comment</string>
    <string name="text_add_comment">Add Comment</string>
    <string name="text_pallet_qty">Pallet Qty</string>

    <!-- Toggle header strings -->
    <string name="tap_to_expand">Tap to expand</string>
    <string name="tap_to_collapse">Tap to collapse</string>

    <!-- Location strings -->
    <string name="expand">Expand</string>

    <!-- Location Occupancy Status Collect -->
    <string name="title_location_occupancy_status_collect">Collect Location Status </string>
    <string name="location">Location</string>
    <string name="type">Type</string>
    <string name="pick_type">Pick Type</string>
    <string name="pallet_deep">Pallet Deep</string>
    <string name="pallet_deep_format">%d</string>
    <string name="full">FULL</string>
    <string name="occupied">OCCUPIED</string>
    <string name="empty">EMPTY</string>
    <string name="enter_start_location">Enter starting location to begin collection:</string>
    <string name="location_format_hint">e.g. 01.012.032</string>
    <string name="start">START</string>
    <string name="enter_location_to_start">Enter a location to start collection</string>
    <string name="sort_order_ascending">Sort: A-Z</string>
    <string name="sort_order_descending">Sort: Z-A</string>
    <string name="collection_complete">Collection complete</string>
    <string name="location_occupancy_summary">Collect location occupancy status</string>
    <string name="backward">Backward</string>
    <string name="select_direction">Select Direction</string>
    <string name="cancel">Cancel</string>

    <!-- Collect Stack Height -->
    <string name="title_collect_stack_height">Collect Stack Height</string>
    <string name="stack_height_summary">View and update item stack height</string>
    <string name="stack_height_label">Stack Height:</string>
    <string name="item_id">Item ID</string>
    <string name="item_name">Item Name</string>
    <string name="stack_height">Stack Height</string>
    <string name="stack_height_format">%d</string>
    <string name="enter_stack_height">Enter stack height</string>
    <string name="update_stack_height">Update</string>
    <string name="stack_height_updated">Stack height updated</string>
    <string name="stack_height_update_failed">Failed to update stack height</string>
    <string name="no_items_found">No items found</string>
    <string name="select_customer_first">Please select a customer first</string>
    <string name="start_collection">Start Collection</string>

    <string name="msg_replenishment_notice">There are still Replenishment Tasks: %s not completed, please complete them first</string>
    <string name="take_photo_of_trailer">Take photo of trailer</string>
    <string name="take_video_of_trailer">Take video of trailer</string>
    <string name="take_photo_of_seal">Take photo of seal</string>
    <string name="take_video_of_seal">Take video of seal</string>
    <string name="take_photo_of_container">Take photo of container</string>
    <string name="take_video_of_container">Take video of container</string>
    <string name="take_photo_of_dock">Take photo of dock</string>
    <string name="take_video_of_dock">Take video of dock</string>
    <string name="take_photo_of_count_sheet">Take photo of count sheet</string>
    <string name="take_video_of_count_sheet">Take video of count sheet</string>
    <string name="lp_sub_type">LP Sub-type:</string>
    <string name="text_picked_sku_ea">%1$s Skus, %2$s EA Picked</string>
    <string name="text_pick_ticket">Pick Ticket</string>
    <string name="text_packing_list_by_order">Packing List By Order</string>
    <!-- Inventory Status -->
    <string name="inventory_status_available">AVAILABLE</string>
    <string name="inventory_status_damage">DAMAGE</string>
    <string name="inventory_status_onhold">ONHOLD</string>
    <string name="inventory_status_adjustout">ADJUSTOUT</string>
    <string name="inventory_status_receiving">RECEIVING</string>
    <string name="inventory_status_picked">PICKED</string>
    <string name="inventory_status_packed">PACKED</string>
    <string name="inventory_status_loaded">LOADED</string>
    <string name="inventory_status_shipped">SHIPPED</string>

    <!-- Inventory Issue -->
    <string name="inventory_issue_location_issue">Location Issue</string>
    <string name="inventory_issue_item_not_found">Item Not Found</string>
    <string name="inventory_issue_item_qty_shortage">Item Qty Shortage</string>
    <string name="inventory_issue_item_damage">Item Damage</string>
    <string name="inventory_issue_location_full">Location Full</string>
    <string name="inventory_issue_location_unreachable">Location Unreachable</string>
    <string name="inventory_issue_location_not_found">Location Not Found</string>
    <string name="inventory_issue_manual_override">Manual Override</string>

    <string name="text_standard">Standard</string>
    <string name="text_count_sheet">Count Sheet</string>
    <string name="text_no_customer_selected">No customer selected</string>
    <string name="text_start_location">Start Location</string>
    <string name="text_no_locations_found">No locations found</string>
    <string name="text_search_customer">Search Customer</string>
    <string name="text_search_customer_by_name">Search customer by name</string>
    <string name="text_location_occupancy_status">Location Occupancy Status</string>
    <string name="msg_rn_items_not_same">RN contains different items, cannot put away together</string>
    <string name="label_selected_rn_count">Selected %1$d RNs</string>

    <!-- rewrite BaseRecyclerViewAdapterHelper start-->
    <string name="load_end">No more data</string>
    <string name="load_failed">load more failed</string>
    <string name="loading">Loading...</string>
    <!-- rewrite BaseRecyclerViewAdapterHelper end-->

    <string name="your_email_address">Your email address</string>
    <string name="enter_your_password">Enter your password</string>
    <string name="remember_password">Remember Password</string>
    <string name="login">login</string>
    <string name="title_select_tenant">Select Tenant</string>
    <string-array name="language">
        <item>@string/language_english</item>
        <item>@string/language_chinese</item>
        <item>@string/language_spanish</item>
        <item>@string/language_japanese</item>
    </string-array>
    <string name="language_english">English</string>
    <string name="language_chinese">Chinese</string>
    <string name="language_spanish">Spanish</string>
    <string name="language_japanese">Japanese</string>

    <!-- main activity start -->
    <string name="nav_title_me">Me</string>
    <string name="nav_title_tasks">Tasks</string>
    <string name="nav_title_more">More</string>
    <string name="nav_title_message">Messages</string>
    <string name="nav_title_timesheet">Timesheet</string>
    <string name="menu_printer_summary">Set default printer</string>
    <string name="menu_task_assign_summary">Assign task</string>
    <string name="general_task">General Task</string>
    <!-- main activity end -->

    <!-- Strings related to login -->
    <string name="toast_login_invalid_employee_id">invalid employee ID</string>
    <string name="toast_login_invalid_qrcode">invalid QRCode</string>
    <string name="toast_login_scan_failed">scan failed,please try again</string>
    <string name="action_sign_in">Sign in or register</string>
    <string name="action_sign_in_short">Sign in</string>
    <string name="error_invalid_account">This account is invalid</string>
    <string name="error_invalid_password">This password is too short</string>
    <string name="error_incorrect_password">This password is incorrect</string>
    <string name="error_incorrect_password_or_account">The password or account you entered is incorrect. Please check and try again</string>
    <string name="error_field_required">This field is required</string>
    <string name="error_load_progress">Load progress error</string>
    <string name="prompt_account">Account</string>
    <string name="prompt_password">Password</string>
    <string name="title_dialog_serveraddr">Server Address</string>
    <string name="title_btn_continue">Continue</string>
    <string name="hint_please_assign_facility_for_user">Please assign facility for user</string>
    <string name="login_fail">Login fail</string>
    <string name="text_sign_in_by_scan_code">Scan Badge to SIGN IN</string>
    <string name="text_login_by_scan_code">Scan Badge to Login</string>
    <string name="text_first_name">First Name</string>
    <string name="text_last_name">Last Name</string>
    <string name="hint_please_input_first_name">Please input first name</string>
    <string name="hint_please_input_last_name">Please input last name</string>

    <string name="title_home_nav_inventory">Inventory</string>
    <string name="title_home_nav_items">Test</string>
    <string name="title_home_nav_signature">Signature</string>
    <string name="title_home_nav_sub_other">Others</string>
    <string name="title_home_nav_send">Send</string>
    <string name="title_home_nav_settings">Settings</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="title_home_nav_categories">Categories</string>
    <string name="title_home_nav_print">Print</string>
    <string name="title_home_nav_dock">Dock</string>
    <string name="title_home_nav_task_assign">Task Assign</string>
    <string name="title_home_nav_damage_good_handling">Damage goods handling</string>
    <string name="title_home_nav_yms">YMS</string>
    <string name="title_home_nav_home">Home</string>
    <string name="title_home_nav_task_center">Task center</string>
    <string name="title_home_nav_message">Message</string>
    <string name="title_home_nav_tools">Tools</string>
    <string name="title_home_nav_material_receiving">Material Receiving</string>
    <string name="title_home_nav_material_checkout">Material Check Out</string>
    <string name="title_home_nav_material_search">Material Search</string>
    <string name="title_home_nav_material_inventory_count">Material Inventory Count</string>
    <string name="title_home_nav_material_item_print">Print item label</string>
    <string name="title_home_nav_zebra_printer_test_page">Zebra Printer Test Page</string>
    <string name="title_home_nav_printer_test">Printer Test</string>
    <string name="msg_please_input_zpl_code">Please input zplCode</string>
    <string name="title_home_nav_pallet_label">Pallet Label</string>
    <string name="title_home_nav_pallet_label_reprint">Pallet Label Reprint</string>

    <string name="action_sort_by_time">Sort by time</string>
    <string name="action_sort_by_priority">Sort by priority</string>

    <string name="action_report_exception">Report exception</string>
    <string name="action_adjustment">Adjustment</string>

    <string name="prompt_task_desc_no_desc">Nothing Description</string>

    <string name="title_progress_table_status">Status</string>
    <string name="title_progress_table_title">Title</string>
    <string name="title_progress_table_location">Location</string>
    <string name="title_progress_table_warehouse">Wareshouse</string>
    <string name="title_progress_table_sku">SKU</string>
    <string name="title_progress_table_supplier">Supplier</string>
    <string name="title_progress_table_customer">Customer</string>
    <string name="title_progress_table_weight">Weight</string>
    <string name="title_progress_table_category">Category</string>
    <string name="title_progress_table_description">Description</string>
    <string name="title_activity_location_list">Task Location</string>
    <string name="title_activity_test">testActivity</string>
    <string name="title_activity_inventory_task">Inventory Task</string>
    <string name="title_activity_inventory_task_assign">Inventory Task Assign</string>
    <string name="title_activity_inventory_progress">Inventory Progress Location</string>
    <string name="title_activity_inventory_list">Progress List</string>
    <string name="title_print_detail">Print detail</string>
    <string name="title_task_center">Task Center</string>

    <string name="text_task_id">TaskId</string>
    <string name="text_task_name">TaskName</string>
    <string name="text_complete_progress">complete progress</string>
    <string name="text_location">Location</string>
    <string name="text_staging">Staging</string>
    <string name="text_dock">Dock</string>
    <string name="text_progress">Progress</string>
    <string name="text_item_id">ItemId</string>
    <string name="text_item">Item</string>
    <string name="text_start">Start</string>
    <string name="text_edit">Edit</string>
    <string name="text_inventory_task">Inventory Task</string>
    <string name="text_before">Before</string>

    <!-- yms  begin-->
    <string name="title_gate_checkout_driver">Check Driver</string>
    <string name="text_mc_dot">MC/DOT</string>
    <string name="title_lp">Check LP</string>
    <string name="text_lp_must">LP*</string>
    <string name="title_gate_check_out_id">Gate Check Out ID</string>
    <string name="text_entry_id">EntryID</string>
    <string name="text_driver">Driver</string>
    <string name="text_container">Container</string>
    <string name="title_gate_checkout_container">Check Container</string>
    <string name="text_check_out_container">"Container NO: "</string>
    <string name="text_verify">Verify</string>
    <string name="title_gate_check_out">Gate Check Out</string>
    <string name="text_add_filter">Add Filter</string>
    <string name="text_sub_task_list">Sub Task List</string>
    <string name="text_show_all">Show All</string>
    <string name="text_select_all">Select Show</string>
    <string name="text_filter_criteria">Filter Criteria</string>
    <string name="text_criteria">Criteria</string>
    <string name="text_title">Title</string>
    <string name="load_title">Title :</string>
    <string name="text_status">Status:</string>
    <string name="text_status1">Status</string>
    <string name="text_desc">Desc:</string>
    <string name="text_carton_status">Carton Status:</string>
    <string name="text_picked_by">Picked By:</string>
    <string name="text_type">Type</string>
    <string name="text_assign">Assign</string>
    <string name="text_task_count">Task Count</string>
    <string name="text_check_in">Check In</string>
    <string name="text_check_out">Check Out</string>
    <string name="text_yard_check">Yard Check</string>
    <string name="text_action_list">Select action</string>
    <string name="text_load">Load</string>
    <string name="text_delivery">Delivery</string>
    <string name="text_parking">Parking</string>
    <string name="text_visitor">Visitor</string>
    <string name="text_unknown">Unknown</string>
    <string name="text_submit">Submit</string>
    <string name="btn_submit_and_print">Submit And Print</string>
    <string name="text_lp">LP</string>
    <string name="text_item_name">Item name</string>
    <string name="text_carrier">Carrier</string>
    <string name="load_task_carrier">Carrier :</string>
    <string name="text_driver_name">Driver Name</string>
    <string name="text_mcdot">MC/DOT</string>
    <string name="text_driver_license">Driver License</string>
    <string name="text_in_container">In Container :</string>
    <string name="text_yes">Yes</string>
    <string name="text_no">No</string>
    <string name="text_container_no">Container No :</string>
    <string name="text_container_type">Container Type :</string>
    <string name="text_out_container">Out Container :</string>
    <string name="text_another">Another</string>
    <string name="text_load_no">Load No.</string>
    <string name="text_reference_no">Reference NO.</string>
    <string name="text_spot_area">Spot Area</string>
    <string name="text_available">Available</string>
    <string name="text_previous">Previous</string>
    <!-- yms  end-->
    <!-- receiving  begin-->
    <string name="error">Error</string>
    <string name="msg_bluetooth_printer_error">Please you bluetooth printer connection and try again</string>
    <string name="do_you_want_to_submit">Do you want to submit?</string>
    <string name="data_not_match">Data not match</string>
    <string name="item_template_qty_not_match">Qty not match \n item qty: %1$s \n template qty: %2$.2f</string>
    <string name="item_template_uom_not_match">Uom not match \n item uom: %1$s \n template uom: %2$s</string>
    <string name="exceeds_lp_template_qty">Item qty: %1$s exceeds the max qty of selected LP template: %2$s</string>
    <string name="select_item_properties">Select item properties</string>
    <string name="msg_lot_no_require">LOT NO. require!</string>
    <string name="msg_expiration_date_require">Expiration date require!</string>
    <string name="msg_mfg_date_require">MFG date require</string>
    <string name="msg_self_life_day_require">Shelf life days require!</string>
    <string name="lot_no">LOT#</string>
    <string name="self_life_days">Shelf life days</string>
    <string name="expiration_date">Expiration date</string>
    <string name="expiration_date_colon">Expiration Date</string>
    <string name="text_expiration_date">Exp.</string>
    <string name="mfg_date">MFG date</string>
    <string name="msg_please_check_all_pallet">Please check all pallet</string>
    <string name="pallet_qty">Pallet QTY</string>
    <string name="actual_pallet_qty">Actual Pallet QTY</string>
    <string name="original_pallet_qty">Original Pallet QTY</string>
    <string name="original_pallet_qty_xx_plt">Original Pallet QTY: %1$s PLT</string>
    <string name="error_description_null">Description is empty</string>
    <string name="msg_layer_error">Layer is empty</string>
    <string name="msg_customer_error">Customer is empty</string>
    <string name="msg_company_error">Company is empty</string>
    <string name="msg_please_one_company">Please select one company</string>
    <string name="msg_total_qty_error">Total qty is empty</string>
    <string name="msg_name_error">Name is empty</string>
    <string name="total_qty">Total QTY</string>
    <string name="total_lock_qty">Total Lock QTY</string>
    <string name="new_lp_template">New Lp Template</string>
    <string name="update_lp_template">Update Lp Template</string>
    <string name="msg_please_select_lp_template">Please select lp template</string>
    <string name="msg_select_item_condition">Please select item condition</string>
    <string name="msg_rework_location_xx_support_item_condition_rework_needed">Rework Location:%1$s, support item conditions:Rework Needed</string>
    <string name="msg_rework_needed_condition_only_allow_select_rework_location">Rework Needed condition,only allow select Rework Location</string>
    <string name="layer_total_qty">(total qty/layer)</string>
    <string name="text_total">Total</string>
    <string name="text_ti_hi">Ti * Hi</string>
    <string name="title_receiving_task_assignment">Receiving Task Assignment</string>
    <string name="text_task_ID">"Task ID: "</string>
    <string name="text_door">"Dock:"</string>
    <string name="text_CTNR">"CTNR:"</string>
    <string name="text_door_capital">"DOCK:"</string>
    <string name="text_offload">"Offload"</string>
    <string name="text_count">"Count:"</string>
    <string name="text_inspect">"Inspect:"</string>
    <string name="text_palletize">"Palletize:"</string>
    <string name="text_put_away">"Put Away:"</string>
    <string name="title_Assign_to">"Assign To"</string>
    <string name="button_assign">"ASSIGN"</string>
    <string name="btn_cancel">CANCEL</string>
    <string name="button_ok">"OK"</string>
    <string name="menu_offload">Offload</string>
    <string name="menu_count">Count</string>
    <string name="menu_exception">"Exception"</string>
    <string name="menu_inspect">Inspect</string>
    <string name="menu_palletize">Palletize</string>
    <string name="menu_put_away">"Put Away"</string>
    <string name="label_assignee">"Assignee"</string>
    <string name="label_assigned_to">Assigned to:</string>
    <string name="label_last_updated">"Last Updated:"</string>
    <string name="hint_count_item_id">"Item Id:"</string>
    <string name="text_seal">Seal:</string>
    <string name="label_seal">Seal</string>
    <string name="lp_view">"LP View"</string>
    <string name="item_view">"Item View"</string>
    <string name="location_view">"Location View"</string>
    <string name="label_pick_scan_location_pick">"Input or Scan Location to Pick"</string>
    <string name="label_pick_scan_location_tracking_pick">"Input or Scan Location/Tracking# to Pick"</string>
    <string name="label_pick_scan_location_stage">"Input or Scan Location to Stage"</string>
    <string name="label_input_or_lp_to_pick">"Input or Scan LP to Start"</string>
    <string name="label_input_item_to_verify">"Scan Item/UPC/EAN/AKA/ABBR"</string>
    <string name="label_input_pick_to_lp">"To LP"</string>
    <string name="label_input_stage_location">"Stage Location"</string>
    <string name="label_scan_item_label">"Scan Item Label"</string>
    <string name="label_input_or_sn_to_pick">"Input or Scan SN to Pick"</string>
    <string name="label_input_or_sn_to_order">"Input or Scan SN to order"</string>
    <string name="label_input_order">"Input or Scan order"</string>
    <string name="hint_total_pick_qty">"Suggest Pick Qty:"</string>
    <string name="hint_lp_total_qty">"LP Total Qty:"</string>
    <string name="hint_attach_new_lp">"Attach New LP"</string>
    <string name="title_start_period">"Start Period:"</string>
    <string name="title_planned_start_time">Planned Start Time</string>
    <string name="title_end_period">"End Period:"</string>
    <string name="title_planned_end_time">Planned End Time</string>
    <string name="text_exception_note">Exception Note:</string>
    <string name="text_photo">Photos:</string>
    <string name="button_no_match">"No Match"</string>
    <string name="button_match">Match</string>
    <string name="button_reopen">Reopen</string>
    <string name="button_done">Done</string>
    <string name="btn_save">SAVE</string>
    <string name="text_report_exception">"Report Exception"</string>
    <string name="text_resolve_exception">"Resolve Exception"</string>
    <string name="label_resolve_exception_note">"Resolve Exception Note:"</string>
    <string name="text_pallet_type">Pallet Type:</string>
    <string name="text_pallet_configuration">Pallet Configuration:</string>
    <string name="label_item_title">"Item:"</string>
    <string name="label_qty">QTY</string>
    <string name="label_setup_qty">Need To Setup Qty</string>
    <string name="label_qty_title">Qty:</string>
    <string name="label_time">Time:</string>
    <string name="text_item_id_sn">"Item ID / SN:"</string>
    <string name="text_scaned_sn">"Scaned SN:"</string>
    <string name="text_add_new_lp">"Add New LP"</string>
    <string name="hint_lp_barcode">"LP Barcode"</string>
    <string name="label_lp_barcode">"LP Barcode:"</string>
    <string name="text_lp_title">"LP:"</string>
    <string name="text_type_title">"Type:"</string>
    <string name="hint_zone">"Zone"</string>
    <string name="hint_location">"Location"</string>
    <string name="text_print">"Print"</string>
    <string name="text_sn">"SN:"</string>
    <string name="text_configuration">"Configuration:"</string>
    <string name="text_expected_title">"Expected:"</string>
    <string name="text_G">"G:"</string>
    <string name="label_palletize_number">"LP Number"</string>
    <string name="label_damage">Damage</string>
    <string name="label_received_qty">Received Qty</string>
    <string name="label_received_qty_mark">Received Qty:</string>
    <string name="action_receive_setting">Receive Setting</string>
    <string name="txt_one_qty_submit">One QTY Submit</string>
    <string name="text_receive_qty">Receive Qty</string>
    <string name="text_receive_progress">Receive Progress</string>
    <string name="label_received_counted_received_qty">"Counted/Received Qty:"</string>
    <string name="label_count_qty">"Count Qty:"</string>
    <string name="label_put_away_total">"Finished/Total:"</string>
    <string name="label_delete">DELETE</string>
    <string name="label_text_delete">Delete</string>
    <string name="label_sn_view">SN View: </string>
    <string name="chk_right">Right</string>
    <string name="chk_error">Error</string>
    <string name="title_palletize_add">"Palletize add"</string>
    <string name="title_put_away_add">"Put away add"</string>
    <string name="title_exception">"Exception"</string>
    <string name="action_print">Print</string>
    <string name="action_new_item">"New Item"</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="label_container_short">CNTR:</string>
    <string name="label_trailer_colon">Trailer:</string>
    <string name="label_appointment_time">Appointment time</string>
    <string name="label_priority_high">High</string>
    <string name="label_priority_middle">Middle</string>
    <string name="label_priority_low">Low</string>
    <string name="label_priority_top">Top</string>
    <string name="label_priority_normal">Normal</string>
    <string name="title_receive_task_list">Receive task list</string>
    <string name="label_diverse">Diverse</string>
    <string name="label_diverse_mark">Diverse : </string>
    <string name="label_replenishment_qty">Replenishment Qty</string>
    <string name="label_expected_qty">Expected Qty</string>
    <string name="label_expected_qty_mark">Expected Qty:</string>
    <string name="label_material_qty">Qty</string>
    <string name="label_verify_qty">Verify Qty</string>
    <string name="label_verified_good_qty">Verified Good Qty</string>
    <string name="label_verified_damage_qty">Verified Damage Qty</string>
    <string name="label_damage_qty">Damage Qty</string>
    <string name="label_rework_need_qty">Rework Need Qty</string>
    <string name="label_on_hold_qty">On Hold Qty</string>
    <string name="label_you_task">You task</string>
    <string name="capacity">Capacity</string>
    <string name="selected_items">Selected items</string>
    <string name="label_add_item">Add item</string>
    <string name="lp_item_list">LP item list</string>
    <string name="text_verified">Verified</string>
    <string name="text_need_verify">Need verify</string>
    <string name="text_received_qty_lower_than_expected">RECEIVED QTY LOWER THAN EXPECTED.</string>
    <string name="text_received_qty_greater_than_expected">RECEIVED QTY GREATER THAN EXPECTED.</string>
    <string name="text_received_expected_qty_not_match">RECEIVED QTY/EXPECTED QTY NOT MATCH.</string>
    <string name="text_confirm_received_qty">CONFIRM RECEIVED QTY?</string>
    <string name="btn_adjust">Adjust</string>
    <string name="text_template_name_mark">Template Name:</string>
    <string name="msg_remind_input_customer_pallet_no">Customer Pallet No can not be empty!</string>
    <string name="text_equipment_size">Equipment Size</string>
    <string name="title_confirmation_needed">Confirmation Needed</string>
    <string name="msg_click_cancel_to_choose_a_different_location_or_lick_continue_to_ignore_this_alert">Click Cancel to choose a different location,\nor click Continue to ignore this alert</string>
    <string name="text_error_sn_photo_capture">Error SN Photo Capture</string>
    <string name="msg_exception_note_can_not_be_empty">Exception note can not be empty</string>
    <string name="msg_rescan_sn">The SN is not belonged to preProvided list, please reScan</string>
    <string name="msg_not_allow_receive_invalid_sn">Not allow receive invalid SN</string>
    <string name="msg_rescan_sn_container_no_match">Container not match. SN: %1$s expected container#: %2$s, current container#: %3$s</string>
    <string name="msg_missing_container_in_receipt">Missing container No. in receipt: %1$s, please fix it</string>
    <string name="double_scan_lot_no">Please double scan lot number</string>
    <string name="msg_lot_no_not_match_and_wait">The collected Lot No. does not match the RN. Please confirm with CSR.</string>
    <string name="label_by_hand_shotgun">Hand Shotgun</string>
    <string name="text_by_hand_shotgun">Hand Shotgun</string>
    <string name="collect_sn_dimens_not_be_empty">dimension can not be empty</string>
    <string name="toast_receiving_lot_violation">LOT# format violation {%s}</string>
    <string name="toast_add_item_same_lot">not allow add LotNo of the same item </string>

    <!-- receiving  end-->

    <string name="text_edit_criteria">Edit Criteria</string>
    <string name="text_ctnr">CTNR :</string>
    <string name="text_priority">Priority :</string>
    <string name="text_task_type">Task Type :</string>
    <string name="text_receiving">Receiving</string>
    <string name="text_receiving_mark">Receiving:</string>
    <string name="text_loading">Loading</string>
    <string name="text_picking">Picking</string>
    <string name="text_staged">Staged</string>
    <string name="text_task_status">Task Status :</string>
    <string name="text_nostarted">NoStarted</string>
    <string name="label_inprogress">In Progress</string>
    <string name="text_closed">Closed</string>
    <string name="text_close">Close</string>
    <string name="text_assign_with_mark">Assign :</string>
    <string name="text_high">HIGH</string>
    <string name="text_middle">MIDDLE</string>
    <string name="text_low">LOW</string>
    <string name="text_unassigned">Unassigned</string>
    <string name="text_filter">Filter</string>
    <string name="text_ok">OK</string>
    <string name="text_cancel">Cancel</string>
    <string name="text_off">Off</string>
    <string name="text_on">On</string>
    <string name="title_yard_control">Yard control</string>
    <string name="action_settings">Settings</string>

    <string-array name="check_in_action_array">
        <item>Unknown</item>
        <item>Load</item>
        <item>Delivery</item>
        <item>Parking</item>
        <item>Visitor</item>
    </string-array>


    <string name="text_new_inform">New Inform</string>
    <string name="text_inform_list">Inform List</string>
    <string name="text_movement">Movement</string>
    <string name="text_item_mark">Item :</string>
    <string name="text_lp_mark">LP :</string>
    <string name="text_to_lp_mark">To LP :</string>
    <string name="text_pack_to_slp_mark">Pack To SLP :</string>
    <string name="text_zone_mark">Zone :</string>
    <string name="text_location_mark">Location :</string>
    <string name="text_qty_mark">Qty :</string>
    <string name="text_record">Record</string>
    <string name="title_damage_good_inform">Damage Good Inform</string>
    <string name="title_damage_good_inform_list">Damage Good Inform List</string>
    <string name="text_approve">Approve</string>
    <string name="text_reject">Reject</string>
    <string name="title_damage_good_handling">Damage Good Handling</string>

    <string name="label_lp">LP</string>
    <string name="label_lp_receipt_id">LP/RN</string>
    <string name="label_container">Container</string>
    <string name="label_dock_door">Dock Door</string>
    <string name="label_container_no">Container No</string>
    <string name="label_driver_license">Driver License</string>
    <string name="label_driver_license_mark">Driver License :</string>
    <string name="label_carrier">Carrier :</string>
    <string name="label_carrier_name">Carrier Name</string>
    <string name="label_driver_name">Driver Name</string>
    <string name="label_entryid">Entry ID</string>
    <string name="label_mc_dot">MC/DOT</string>
    <string name="label_driver_info">Driver Info</string>
    <string name="label_yes">Yes</string>
    <string name="label_no">No</string>
    <string name="label_submit">SUBMIT</string>
    <string name="label_stage">Stage</string>
    <string name="msg_stage_not_complete">Staging not complete, exit?</string>
    <string name="label_door">Dock</string>
    <string name="label_ctnr">CTRN :</string>
    <string name="label_priority">Priority :</string>
    <string name="title_priority">Priority</string>
    <string name="label_edit">Edit</string>
    <string name="label_trailer">Trailer</string>
    <string name="label_tractor">Tractor</string>
    <string name="label_sn">SN</string>
    <string name="sn_amount">SN amount</string>
    <string name="item_not_include_sn">Item not include sn</string>
    <string name="text_yard_entry_id">Entry ID:</string>
    <string name="label_type">Type :</string>
    <string name="label_yard_container_no">Container No :</string>
    <string name="label_yard_entry">Entry Id :</string>
    <string name="label_spot_area">Spot Area :</string>
    <string name="label_yard_check_in_photo">Check In Photos :</string>
    <string name="label_move_to">Move To</string>
    <string name="label_change_type">Change Type</string>
    <string name="label_change_container">Change Container No</string>
    <string name="hint_please_input">Please input</string>
    <string name="hint_yard_check">Input Container no/Entry id</string>
    <string name="title_pick_detail">Pick detail</string>
    <string name="title_order_allocate_detail">Order Allocate Detail</string>
    <string name="title_pick_task">Pick Task</string>
    <string name="title_pick_task_list">Pick Task List</string>
    <string name="title_put_away_task">Put Away Task</string>
    <string name="title_put_away_task_list">Put Away Task List</string>
    <string name="title_replenishment_task">Replenishment Task</string>
    <string name="text_get_item">Get Item</string>
    <string name="text_scan_item_label">Please scan or input item label</string>
    <!-- yms start -->
    <string name="text_shuttle">Shuttle</string>
    <string name="hint_yard_shuttle">Container/LP/Emtry Id</string>
    <string name="label_spot">Spot :</string>
    <string name="title_activity_door_check_in">Dock Check In</string>
    <string name="input_entry_id_and_dock">Input entry id and dock</string>
    <string name="text_door_checkin_success">Dock check in success!</string>
    <string name="dock_error">Dock name error</string>
    <string name="text_start_task">Start Task</string>
    <string name="text_door_checkin_failed">Dock Check In Is Failed!</string>
    <string name="text_back">Back</string>
    <string name="text_regular_movement">Regular movement by LP/Item</string>
    <string name="text_batch_movement">Batch Movement by LP/DN/PICKED TICKET/LOAD</string>
    <!-- yms end -->

    <!-- load start -->
    <string name="msg_auto_cc_failed_try_again">Auto CC failed please try again</string>
    <string name="label_ship_to_account">Ship to account</string>
    <string name="label_note">Note</string>
    <string name="title_start_btn">Start</string>
    <string name="label_ship_to_colon">Ship To :</string>
    <string name="body_not_note">Nothing here</string>
    <string name="title_task_detail">Task detail</string>
    <string name="title_load_type_ftl">FTL</string>
    <string name="title_load_type_ltl">LTL</string>
    <string name="title_load_type_spl">SPL</string>
    <string name="title_load_status_progress">In progress</string>
    <string name="title_load_status_exception">Exception</string>
    <string name="title_load_status_available">Available</string>
    <string name="title_load_status_new">New</string>
    <string name="title_load_status_unavailable">Unavailable</string>
    <string name="title_load_status_done">Done</string>
    <string name="label_item">Item</string>
    <string name="title_task_detail_tab_view">view</string>
    <string name="title_task_detail_tab_work">work</string>
    <string name="label_task_detail_order_view">Order View</string>
    <string name="label_task_detail_location_view">Location View</string>
    <string name="title_load_work_load">Load</string>
    <string name="title_load_work_seal">Seal</string>
    <string name="title_load_work_counting_sheet">Count sheet</string>
    <string name="hint_load_work">Scan or Input SLP</string>
    <string name="label_unload">Unload</string>
    <string name="label_progress">Progress</string>
    <string name="txt_progress">Progress:</string>
    <string name="msg_order_not_found">Order not found by: %1$s</string>
    <string name="msg_order_not_match_dst">%1$s doesn\'t match with load DST:%2$s</string>
    <string name="msg_order_trans_load_lp_have_been_load">%1$s carton have been loaded</string>
    <string name="msg_order_trans_load_lp_not_load">%1$s carton not loaded</string>
    <string name="txt_order_qty">Order Qty:</string>
    <string name="action_bol">BOL</string>
    <string name="title_exception_list">Exception list</string>
    <string name="title_report_exception">Report exception</string>
    <string name="title_mbol_list">MBOL-Bol list</string>
    <string name="label_instruction">Instruction</string>
    <string name="label_detail">Detail </string>
    <string name="toast_general_server_error">Connect failed</string>
    <string name="toast_general_argument_error">Argument error</string>
    <string name="message_report_exception_alert">Are you want to cancel submit data?</string>
    <string name="label_approve_note">Approve note</string>
    <string name="title_load_task_list">Load task list</string>
    <string name="label_load_type">Load Type</string>
    <string name="text_scan_sn">Scan SN</string>
    <string name="text_scan_tracking_num">Scan TRACKING#</string>
    <string name="title_load_sn_collection">Load - Sn Collection</string>
    <string name="text_sn_count_mark">SN Count:</string>
    <string name="text_so_id_count_mark">SOID Count:</string>
    <string name="text_sn_count">SN Count</string>
    <string name="text_sn_qty_no_match">SN qty no match</string>
    <string name="text_sn_amount_mark">SN Amount:</string>
    <string name="title_load_scan_sn">Load - Scan SN</string>
    <string name="title_show_sn">Show SN</string>
    <string name="msg_not_loading_yet">Not loading yet, Task: %1$s</string>
    <string name="msg_lp_not_loading_yet_or_not_belong_to_task">%1$s not loaded or not belong to Task: %2$s</string>
    <string name="msg_contain_invalid_sn_please_check">SN List contains invalid SN, please check!</string>
    <string name="msg_contain_invalid_rfid_please_check">RFID List contains invalid RFID, please check!</string>
    <string name="title_scanned">Scanned</string>
    <string name="title_lp_sn_collection_list">LP SN/RFID Collection List</string>
    <string name="msg_sn_qty_no_match">SN qty no match</string>
    <string name="msg_rfid_qty_no_match">RFID qty no match</string>
    <string name="title_lp_sn_scan">LP SN/RFID Scan</string>
    <string name="title_order_sn_scan">Order SN/RFID Scan</string>
    <string name="hint_please_scan_or_input_order">Please scan or input Order</string>
    <string name="text_progress_txt">%1$s / %2$s</string>
    <string name="text_total_progress_txt">Total Progress: %1$s / %2$s</string>
    <string name="hint_please_scan_or_input_lp_item">Please scan or input LP/Item</string>
    <string name="msg_order_not_need_collect_sn">Order no need Collect SN, please check!</string>
    <string name="msg_lp_not_need_collect_sn">LP no need Collect SN, please check!</string>
    <string-array name="collect_sn_type_array">
        <item>Collect By Item</item>
        <item>Collect By LP</item>
    </string-array>
    <string name="msg_no_lp_item_match">No Item/LP match, please check!</string>
    <string name="title_confirm">Confirm</string>
    <string name="msg_save_your_change_for_current_lp">Save your change for current LP?</string>
    <string name="text_sn_is_empty">SN is empty</string>
    <string name="msg_order_is_locked_please_load_by_load_sequence">%1$s is locked,please load order by load sequence</string>
    <string name="msg_data_load_failed">Page data load failed, please restart this page to refresh.</string>
    <string name="msg_item_line_load_unfinished">Item line load unfinished, please wait.</string>
    <string name="text_destination">Destination:</string>
    <string name="text_item_po">Item(PO):</string>
    <string name="text_appointment_date">Appt Date:</string>
    <string name="msg_order_pallet_count_validate">%1$s Pallet Count does not match LP Count</string>
    <string name="text_sign_all_mbol">Sign all MBOLs</string>
    <string name="text_sign_all_bol">Sign all BOLs</string>
    <!-- load end -->

    <string name="label_ok">ok</string>
    <string name="label_cancel">cancel</string>
    <string name="label_approve">Approve</string>
    <string name="label_zone">Zone</string>
    <string name="label_location">Location</string>
    <string name="label_quantity">Quantity</string>
    <string name="label_search">Search</string>
    <string name="label_sort_by_checkin_time">Sort by check in time</string>
    <string name="label_assign">Assign</string>
    <string name="rb_case_upc">Case Upc</string>
    <string name="rb_upc">Upc</string>
    <string name="rb_aka">Aka</string>
    <string name="rb_description">Description</string>
    <string name="rb_abbreviation">Abbreviation</string>
    <string name="title_activity_damage_movement">Damage Movement</string>
    <string name="label_case">Case :</string>
    <string name="btn_remove">Remove</string>
    <string name="btn_palletize">Palletize</string>
    <string name="btn_put_away">Put Away</string>
    <string name="btn_batch_movement">Movement</string>
    <string name="btn_add_new_lp">Add New LP</string>
    <string name="title_view">View</string>
    <string name="title_work">Work</string>
    <string name="title_activity_new_lp">New LP</string>
    <string name="text_good">Good</string>
    <string name="text_damage">Damage</string>
    <string name="label_configuration">Configuration :</string>
    <string name="label_lp_no">LP No</string>
    <string name="hint_scan_or_input">Scan Or Input</string>
    <string name="label_add_qty">Add Qty :</string>
    <string name="hint_scan_or_input_add">Scan or Input to Add</string>
    <string name="btn_confirm">Confirm</string>
    <string name="text_add_item_successfully">Add Item Successfully !</string>
    <string name="btn_complete">Complete</string>
    <string name="btn_Resume">Resume</string>
    <string name="btn_skip">skip</string>
    <string name="txt_qty_format_error">Qty format error</string>
    <string name="btn_add_more">Add More</string>
    <string name="title_activity_put_away">"Put Away"</string>
    <string name="label_remove_qty">Remove Qty :</string>
    <string name="text_item_with_sn">Item With SN</string>
    <string name="text_item_without_sn">Item Without SN</string>
    <string name="hint_damage_remove_sn">Scan or Input SN to Remove</string>
    <string name="text_is_not_damage"> is not damage Item!</string>
    <string name="title_worker_selector">Worker selector</string>

    <!-- cc task start-->
    <string name="label_task_period">Task period</string>
    <string name="label_task_cc">CC Task</string>
    <string name="label_start">Start</string>
    <string name="label_enter">Enter</string>
    <string name="label_done">Done</string>
    <string name="label_new">New</string>
    <string name="label_re_open">Reopen</string>
    <string name="label_exception">Exception</string>
    <string name="label_in_progress">In progress</string>
    <string name="title_cc_task_list">CC Task List</string>
    <string name="item_unit_content">%1$s | %2$s</string>
    <string name="label_quantity_unit_content">"%1$s  (%2$s)"</string>
    <string name="label_all_item_remove">All Item Removed!</string>
    <string name="label_put_away">Put Away</string>
    <string name="label_internal_transfer_put_away">Internal Transfer Put Away</string>
    <string name="label_internal_transfer">Internal Transfer</string>
    <string name="label_item_movement">Item movement</string>
    <string name="label_lp_configuration">LP configuration</string>
    <string name="label_lp_type">"LP Type"</string>
    <string name="label_item_condition">GoodsType</string>
    <string name="label_item_partial_pallet">"Partial Pallet"</string>
    <string name="label_from">From</string>
    <string name="label_to">To</string>
    <string name="label_do_move">Do movement</string>
    <string name="label_lp_type_verify">LP type verify</string>
    <string name="toast_select_action">Please select one action</string>
    <string name="label_input_qty">Please input quantity</string>
    <string name="label_scan_input_sn">Please scan or input SN</string>
    <string name="label_entire_lp">Entire Lp</string>
    <string name="label_entire_pallet">Entire Pallet</string>
    <string name="label_cc_collect">CC - Collect</string>
    <string name="label_cc_build">CC - Build</string>
    <string name="msg_current_lp_no_contain_item">Current LP no contain item: %1$s</string>
    <string name="label_add_to_target">Add To Target</string>
    <string name="label_traget_lp_template">Target LP Template</string>
    <string name="msg_sn_not_collected">SN Not Collected</string>
    <string name="msg_input_qty_and_scan_sn">Please input item qty and scan Sn</string>

    <!-- cc task end-->
    <string name="label_scan_or_input">Please scan or input</string>
    <string name="hint_scan_or_input_to_search">Scan or Input to Search</string>
    <string name="label_task_time">Task Time :</string>
    <string name="label_create_by">Create By</string>
    <string name="label_start_period">Start Period</string>
    <string name="label_end_period">End Period</string>
    <string name="hint_scan_or_input_location_to_start">Scan or Input Location to Start</string>
    <string name="text_following_items_in_the_same_lp">Following Items In The Same LP</string>
    <string name="btn_verify_select">Verify Selected</string>
    <string name="btn_verify_all">Verify All</string>
    <string name="btn_modify">Modify</string>
    <string name="btn_not_found">Not Found</string>
    <string name="btn_verify">Verify</string>
    <string name="hint_input_any_text_to_search">Input any text to search</string>
    <string name="hint_load_work_input">Scan or input pallet label</string>
    <string name="btn_verify_done">Verify Done</string>
    <string name="text_lp_not_found">Lp Not Found</string>
    <string name="text_scan_lp">Scan LP</string>
    <string name="text_scan_order">Scan Order</string>
    <string name="msg_confirm_close_sn_scan_step">LP scan finished.Are you sure to close step?</string>
    <string name="msg_all_step_confirm_close_task">All steps finished.Are you sure to close task?</string>
    <string name="hint_scan_or_input_sn">Scan or input SN</string>
    <string name="btn_done">Done</string>
    <string name="text_qty">Qty</string>
    <string name="hint_input_qty">Input Qty</string>
    <string name="text_blind">Blind</string>
    <string name="btn_add">Add</string>
    <string name="btn_new_item">New Item</string>
    <string name="btn_history">History</string>
    <string name="btn_select">Select</string>
    <string name="btn_unit">Unit</string>

    <!-- receipt begin -->
    <string name="msg_lp_not_found">LP not found</string>
    <string name="label_match">Match</string>
    <string name="label_no_match">No Match</string>
    <string name="title_item_setup">Item setup</string>
    <string name="label_select_item">Select item</string>
    <string name="label_select">Select</string>
    <string name="label_unit">Unit</string>
    <string name="label_add">Add</string>
    <string name="label_remove">Remove</string>
    <string name="title_select_unit">Select unit</string>
    <string name="title_input_qty">Input qty</string>
    <string name="title_select_location">Select location</string>
    <string name="hint_please_scan_or_input_lp">Please scan or input LP</string>
    <string name="hint_please_scan_or_input_lp_tote">Please scan or input LP/tote</string>
    <string name="hint_please_scan_or_input_item">Please scan or input item</string>
    <string name="hint_please_scan_or_input_item_upc_aka">Please scan or input item/upc/aka</string>
    <string name="label_aka">AKA</string>
    <string name="hint_please_scan_or_input_item_upc_aka_abbr_lp">Please scan or input item/upc/aka/abbr/LP</string>
    <string name="msg_please_select_one_item">Please select one item</string>
    <string name="msg_please_select_one_unit">Please select one unit</string>
    <string name="msg_please_select_title_to_submit">Please select title to submit</string>
    <string name="msg_please_select_one_template">Please select one template</string>
    <string name="label_width">Width</string>
    <string name="label_height">Height</string>
    <string name="label_length">Length</string>
    <string name="label_weight">Weight</string>
    <string name="label_item_width">Item Width</string>
    <string name="label_item_height">Item Height</string>
    <string name="label_item_length">Item Length</string>
    <string name="label_item_weight">Item Weight</string>
    <string name="toast_not_found_uom_name">not found uom</string>
    <string name="toast_input_item_width">please input Item Width</string>
    <string name="toast_input_item_height">please input Item Height</string>
    <string name="toast_input_item_length">please input Item Length</string>
    <string name="toast_input_item_weight">please input Item Weight</string>
    <string name="toast_input_item_length_greater_zero">Item length must be greater than 0</string>
    <string name="toast_input_item_width_greater_zero">Item width must be greater than 0</string>
    <string name="toast_input_item_height_greater_zero">Item height must be greater than 0</string>
    <string name="toast_input_item_weight_greater_zero">Item weight must be greater than 0</string>
    <string name="toast_input_item_qty_greater_zero">Item qty of inside uom must be greater than 0</string>
    <string name="toast_input_uom_already_exists">input uom already exists</string>
    <string name="toast_input_item_base_uom">please input uom</string>
    <string name="toast_input_item_base_qty">please input qty</string>
    <string name="label_get_dimension">Get Dimension</string>
    <string name="label_dimension_collect">Collect Item Dimension</string>
    <string name="label_item_dimension_unit_in">INCH</string>
    <string name="label_item_dimension_weight_unit_pound">POUND</string>
    <string name="label_item_weight_unit">LB</string>
    <string name="label_put_item_on_the_cube_scan">* Please get dimension after measured!</string>
    <string name="label_select_server">Select Server</string>
    <string name="label_select_cube_scan">Select Cube Scan</string>
    <string name="label_select_cube_scan_server">Cubic Scan Servers</string>
    <string name="label_layer">Layer:</string>
    <string name="text_layer">Layer</string>
    <string name="label_good">Good</string>
    <string name="label_default_location">Default location</string>
    <string name="hint_input_text_to_search">Input text to search</string>
    <string name="label_unit_container">Unit container</string>
    <string name="hint_name">Name</string>
    <string name="label_bundle">Bundle</string>
    <string name="hint_customer">Customer</string>
    <string name="hint_supplier">Supplier</string>
    <string name="hint_title">Title</string>
    <string name="hint_brand">Brand</string>
    <string name="note_test">"Please capture photo when offloading"</string>
    <string name="label_seal_content">Seal: %s</string>
    <string name="msg_submit_success">Submit success</string>
    <string name="label_add_extra_item">Extra item</string>
    <string-array name="unit_test_array">
        <item>Case</item>
        <item>Piece</item>
        <item>Package</item>
    </string-array>
    <string name="label_key">Key</string>
    <string name="label_item_setup">Item setup</string>
    <string name="label_offload_done">Offload done</string>
    <string name="label_offload_start">Start offload</string>
    <string name="txt_seal_not_match">Seal is not match</string>
    <string name="txt_container_not_match">Container is not match</string>
    <string name="txt_seal_match">Seal is match</string>
    <string name="notice_contact_scr">Please contact CSR</string>
    <string name="notice_step_done">This step is done</string>
    <string-array name="lp_type_array">
        <item>Good</item>
        <item>Damage</item>
        <item>Contain Damage</item>
        <item>On Hold</item>
        <item>Rework Needed</item>
    </string-array>
    <string name="msg_please_add_lp">Please scan and add LP</string>
    <string name="label_lp_detail">LP detail</string>
    <string name="label_verify">Verify</string>
    <string name="hint_please_scan_or_input_sn">Please scan or input SN</string>
    <string name="hint_please_scan_or_input_rfid">Please scan or input RFID</string>
    <string name="please_input_qty">Please input qty</string>
    <string name="no_lp_seleted_for_suggest">No LP selected for suggest</string>
    <string name="no_lp_selected">No LP selected</string>
    <string name="non_compliant_lP">Non-compliant LP：\n"</string>
    <string name="no_location_selected">No Location selected</string>
    <string name="hint_please_scan_or_input_sn_one_by_one">LP contains invalid SN, Please scan or input SN one by one to confirm</string>
    <string name="label_title">Title</string>
    <string name="label_pallet">Plts</string>
    <string name="label_customer">Customer</string>
    <string name="label_lpId_orderId">LP/Order</string>
    <string name="load_task_customer">Customer :</string>
    <string name="msg_please_setup_lp">Please setup LP first</string>
    <string name="msg_please_capture_photo">Please capture photo</string>
    <string name="msg_please_select_seal_status">Please select seal status</string>
    <string name="msg_please_add_damage_photo">Please add damage photo</string>
    <string name="toast_update_success">Add LP success</string>
    <string name="label_is_verified">Verified</string>
    <string name="label_not_verified">Not verified</string>
    <string name="label_done_step">Done this step</string>
    <string name="action_release_dock">Release Dock</string>
    <string name="query_release_dock">Release dock ?</string>
    <string name="label_LP_is_error">LP is error</string>
    <string name="label_LP_is_setup">LP has been setup already</string>
    <string name="label_lp_setup_submit_respond">"%1$s fail. Fail message:%2$s\n"</string>
    <string name="carton_configuration">Carton configuration</string>
    <string name="item_configuration">Item configuration</string>
    <string name="msg_delete_lp_prompt">Delete %1$s ?</string>
    <string name="sn_edit">Edit SN</string>
    <string name="scan_new_lp">Scan new LP</string>
    <string name="text_next_layer">next layer</string>
    <string name="text_next_LP">next LP</string>
    <string name="text_scan_item_number">Scan Item Number</string>
    <string name="text_scan_sn_finished">Scan SN finished</string>
    <string name="text_scan_sn_duplicate">Duplicate SN, Scanned in SN list</string>
    <string name="text_scan_sn_Existed">Duplicate SN, Scanned in %1$s</string>
    <string name="msg_do_you_want_to_add_materials">Do you want to add materials?</string>
    <string name="btn_select_from_list">Select From List</string>
    <string name="title_enter_receiving_location">Enter Receiving Location</string>
    <string name="title_use_preset_lp_template">Use Pre-Set LP Template</string>
    <string name="title_enter_total_lp_pallet">Enter Total LP/Pallet</string>
    <string name="btn_use_different_template">Use Different Template</string>
    <string name="msg_do_you_want_to_receive_additional_items">Do you want to receive additional items?</string>
    <string name="text_system_suggested_pallet_ti_hi">System Suggested Pallet Ti Hi</string>
    <string name="text_total_qty_per_pallet">Total QTY per Pallet</string>
    <string name="text_enter_new_pallet_name">Enter New Pallet Name</string>
    <string name="text_enter_new_pallet_ti_hi">Enter New Pallet Ti Hi</string>
    <string name="text_enter_pallet_name">Enter Pallet Name</string>
    <string name="text_enter_pallet_ti">Enter Pallet Ti</string>
    <string name="text_enter_pallet_hi">Enter Pallet Hi</string>
    <string name="text_select_template_from_list">Select Template From List</string>
    <string name="msg_exceeded_lp_count_qty">Exceeded LP count QTY.</string>
    <string name="title_confirm_qty">Confirm QTY</string>
    <string name="msg_total_qty_mark_qty">Total QTY: %1$s x %2$s = %3$s (%4$s)</string>
    <string name="msg_ti_hi_cannot_be_zero">"total qty per pallet cannot be '0'"</string>
    <string name="msg_input_total_qty_per_pallet">please input total qty per pallet</string>
    <string name="msg_checkout_dock">Checkout Dock?</string>
    <string name="msg_checkout_success">Checkout success</string>
    <string name="msg_item_dimension_has_not_been_updated">Item dimension has not been updated yet.Please collect item dimension and update in system</string>
    <string name="msg_item_need_an_case_pick_location">Item need an Case Pick Location.Please assign item-location relationship.</string>
    <string name="msg_item_need_an_each_pick_location">Item need an Each Pick Location.Please assign item-location relationship.</string>
    <string name="msg_lp_contains_multiple_items">LP contains Multiple items: %1$s</string>
    <string name="text_batch_reprint">Batch Reprint</string>
    <string name="text_delete_all">Delete All</string>
    <string name="msg_delete_all_received_lp">Are you sure delete all LP?</string>
    <string name="msg_please_select_rework_needed_or_damage_condition">Please select Rework Needed or Damage condition</string>
    <string name="msg_not_allow_select_rework_needed_or_damage_condition">Not allow select Rework Needed or Damage condition</string>
    <string name="msg_not_allow_change_condition">Not allowed to change the Goods Type</string>
    <string name="msg_select_item">Please select item</string>
    <!-- receipt end -->

    <string name="text_picked_colon">"Picked:"</string>
    <string name="text_picked">"Picked"</string>
    <string name="text_orderId">OrderId</string>
    <string name="text_pick_total_colon">"Pick Total:"</string>
    <string name="text_pick_required_qty">"Required Qty:"</string>
    <string name="text_pick_picked_qty">"Picked Qty:"</string>
    <string name="cntr_content">CNTR: %1$s</string>
    <string name="trailer_content">Tailer: %1$s</string>
    <string name="door_content">Dock: %1$s</string>
    <string name="text_expiredData_colon">"ExpiredData:"</string>
    <string name="location_content">Location: %1$s</string>
    <string name="lp_content">LP: %1$s</string>
    <string name="text_damage_type">Damage Type :</string>
    <string name="text_note">Note</string>
    <string name="text_staging_area">Staging Area</string>
    <string name="text_shipping_account_address">Shipping Account/Address</string>
    <string name="text_items">Items</string>
    <string name="title_lp_configure">"LP Configure"</string>
    <string name="title_lp_configure_edit">"LP Configure Edit"</string>
    <string name="text_input_order_no">"Input Order No"</string>
    <string name="title_wave_pick">"Link lp and order"</string>
    <string name="text_alert_dialog_message">"Found the same LP, whether to replace?"</string>
    <string name="text_update">"Updates available, proceed to download?"</string>
    <string name="text_update_not_supported_api_under_21">"Upgrading to the new App version requires a minimum system version 'Android 5.0'. Please upgrade your phone system."</string>
    <string name="title_alarm">Alarm</string>
    <string name="title_message">message</string>
    <string name="text_input_scan_location">"Input or scan location to put away"</string>
    <string name="text_input_scan_lp">"Input or scan LP to put away"</string>
    <string name="text_input_scan_lp_get_item">"Input or scan LP/OrderId/Tracking#"</string>
    <string name="text_put_away_alarm_message">"LocationId or Lp cannot be null!"</string>
    <string name="gate_check_in">Gate CheckIn :</string>
    <string name="window_check_in">Window CheckIn :</string>
    <string name="text_load_list">Load List</string>
    <string name="hint_photo_upload_incompleted">Photo Upload Incomplete, please wait</string>
    <string name="msg_photo_upload_incompleted">Photos upload incomplete,some photos may be lost,submit?</string>
    <string name="msg_please_input_description">Please input some description</string>
    <string name="msg_please_input_description_for">Please input some description for</string>
    <string name="status_new">New</string>
    <string name="submit_success">Submit Success</string>
    <string name="text_pro_no_submit_success">Pro No. Submit Success</string>
    <string name="text_please_input_seal">Please input seal number</string>
    <string name="text_pro_no">Pro No</string>
    <string name="text_add_pro_no">Add pro No</string>
    <string name="text_is_complete">is Complete</string>
    <string name="text_please_input_pro_no">Please input Pro No</string>
    <string name="title_load_status_loaded">Loaded</string>
    <string name="title_load_status_no_load">No Load</string>
    <string name="title_load_status_unlock">Unlock</string>
    <string name="title_load_status_loading">Loading</string>
    <string name="title_load_status_locked">Locked</string>
    <string name="title_load_status_wait_for_load">Wait For Load</string>

    <string name="title_item_spec_selector">Please select item</string>
    <string name="title_location_selector">Please select location</string>
    <string name="title_please_select_order_id">Please select Order ID</string>
    <string name="label_please_input_item_name">Please input item name</string>
    <string name="label_item_name_carton_id">Item Name/Carton ID</string>
    <string name="text_add_material">Add Material</string>
    <string name="text_tractor">Tractor :</string>
    <string name="text_trailer">Trailer :</string>
    <string name="text_containers">Containers :</string>
    <string name="checking_no_error">Checking No. error</string>
    <string name="text_material">Material :</string>
    <string name="label_group">Group</string>
    <string name="label_add_field">Add field</string>
    <string name="label_value">Value</string>
    <string name="label_name">Name</string>
    <string name="hint_dock_occupy">Dock is occupied, please wait</string>
    <string name="hint_dock_reserve">Dock is reserved, please wait</string>
    <string name="text_original_pallet_type">Original Pallet Type</string>
    <string name="text_select_new_type">Select New Type</string>
    <string name="text_lp_confirm_fail">LP Type Confirm Fail</string>
    <string name="text_get_lp_detail_fail">get LP Detail Fail</string>
    <string name="label_material">Material</string>
    <string name="label_material_type">Material Type</string>
    <string name="msg_please_select_material_type">Please select material type</string>
    <string name="label_material_mark">Material:</string>
    <string name="label_lp_setup">LP setup</string>
    <string name="label_input_qty_to_get">"Input Qty To Get"</string>
    <string name="label_input_staging_to_filter">"Input Staging To Filter"</string>
    <string name="label_input_dock_to_filter">"Input Dock To Filter"</string>
    <string name="siganture_upload_fail">Signature Upload Fail</string>
    <string name="siganture_upload_success">Signature Upload Success</string>
    <string name="text_mbol">MBOL</string>
    <string name="text_bol">BOL</string>
    <string name="btn_preview">Preview</string>
    <string name="title_release_dock">Release dock</string>
    <string name="label_release">Release</string>
    <string name="label_dock_detail">Dock detail</string>
    <string name="label_entry_id">Entry ID</string>
    <string name="label_entry_id_mark">Entry ID:</string>
    <string name="label_exit">SIGN OUT</string>
    <string name="label_update">Check New Version</string>
    <string name="label_version">Version %1$s</string>
    <string name="title_load_status_not_resolved">Not Resolved</string>
    <string name="title_load_status_resolved">Resolved</string>
    <string name="label_description">Description</string>
    <string name="label_description_colon">Description :</string>
    <string name="btn_add_instruction">Add Instruction</string>

    <string name="title_help">Help</string>
    <string name="msg_help_text">This app lack of permission, you need enable item from system setting </string>
    <string name="label_quit">Quit</string>
    <string name="label_settings">Settings</string>
    <string name="msg_network_timeout">Network timeout</string>

    <string name="title_check_in_action">Check In Action</string>
    <string name="title_check_in">Check In</string>
    <string name="title_select_truck_type">Select Truck Type</string>
    <string name="label_car">Car</string>
    <string name="label_license_plate">License Plate</string>
    <string name="label_trailer_no">trailer</string>
    <string name="label_carrier_info">Carrier Info</string>
    <string name="label_reference_no">Reference No</string>
    <string name="btn_add_container">Add Container</string>
    <string name="label_container_info">Container Info</string>
    <string name="label_trailer_info">Trailer Info</string>

    <string name="title_system_version">Version: %1$s</string>
    <string name="title_task_cc">CC task $1%d</string>
    <string name="title_task_receive">Receive Task</string>
    <string name="title_task_cycle_count">Cycle Count Task</string>
    <string name="title_task_recount">Recount Task</string>
    <string name="title_task_cycle_count_list">Cycle Count Task List</string>
    <string name="title_task_load">Load Task</string>
    <string name="title_task_putaway">Put Away Task</string>
    <string name="title_task_put_away_auditing">Put Away Auditing Task</string>
    <string name="title_task_replenishment">Replenishment Task</string>
    <string name="title_task_replenishment_list">Replenishment Task List</string>
    <string name="title_exception_instruction">Exception Instruction</string>
    <string name="hint_please_input_instruction">Please Input Instruction</string>
    <string name="submit_fail">Submit Fail</string>
    <string name="text_instruction">Instruction</string>
    <string name="hint_please_input_license_plate">Please input license plate</string>
    <string name="hint_please_select_carrier">Please Select Carrier</string>
    <string name="hint_please_input_driver_name">Please Input Driver Name</string>
    <string name="hint_please_input_driver_license">Please Input Driver License</string>
    <string name="hint_please_input_container_no">Please Input Container No</string>
    <string name="hint_please_input_trailer">Please input trailer</string>
    <string name="text_confirm">Confirm</string>
    <string name="title_download">Download…</string>
    <string name="msg_in_latest_version">You are in the latest version</string>
    <string name="batch_movement_input_tips">Please enter starting with Specific prefix</string>
    <string name="close_success">Close Success!</string>

    <string name="title_task_assign_center">Task assign center</string>
    <string name="label_select_printer">Select Printer :</string>
    <string name="label_select_zpl_printer">Select ZPL Printer :</string>
    <string name="label_select_pdf_printer">Select PDF Printer :</string>
    <string name="title_label_batch_print">Label Batch Print</string>
    <string name="text_combination_text">%1$s (%2$s)</string>
    <string name="action_printing">Printing…</string>
    <string name="label_select_print_type">Select Print Type :</string>
    <string name="label_print_copies">Print Copies :</string>
    <string name="label_print">Print</string>
    <string name="label_preview">Preview</string>
    <string name="label_print_config">Print Config</string>
    <string name="label_print_preview">Print Preview</string>
    <string name="error_print_server_not_found">Print Server Not Found, Please contact IT Admin</string>
    <string name="text_print_job_success">Print Job Success</string>
    <string name="text_print_pdf_success">Print PDF Success</string>
    <string name="error_printer_is_not_match_pdf">This Printer can\'t print pdf, please select another.</string>
    <string name="label_lp_amount">LP Amount</string>
    <string name="label_lp_count">LP Count</string>
    <string name="label_lp_copy_count">Copy NO</string>
    <string name="label_copies">Copies</string>
    <string name="label_lp_new">NEW</string>
    <string name="label_lp_copy">Copy</string>
    <string name="label_lp_copies">LP Copies</string>
    <string name="print_label_copies">Label Copies</string>
    <string name="label_reprint">Reprint</string>
    <string name="label_reprint_so_id">Reprint SOID ?</string>
    <string name="msg_invalid_soid">Invalid SOID:%1$s, rescan.</string>
    <string name="label_soid">SOID</string>
    <string name="title_lp_print">LP printing</string>
    <string name="error_create_job_fail">Create Job Fail</string>
    <string name="btn_update">Update</string>
    <string name="msg_nobody">Nobody</string>
    <string name="msg_seal_not_given">Seal not given</string>
    <string name="label_item_name">Item Name</string>
    <string name="label_item_name_mark">Item name : </string>
    <string name="label_item_photo">Item photo</string>
    <string name="label_resolve">Resolve</string>
    <string name="label_please_comfirm_exception_resolved">Please confirm exception resolved</string>
    <string name="text_update_fail">Update Fail</string>
    <string name="btn_add_trailer">Add Trailer</string>

    <string name="text_door_no">Dock No:</string>
    <string name="text_ship_to_id">Ship To ID</string>
    <string name="label_order_amount">Order Amount :</string>
    <string name="title_home_nav_logout">Logout</string>
    <string name="text_occupied">Occupied</string>
    <string name="text_reserved">Reserved</string>
    <string name="text_close_task">Close Task</string>
    <string name="text_task_closed">Task closed</string>
    <string name="text_task_close_fail">Task close fail</string>
    <string name="text_load_success">Load Success</string>
    <string name="text_unload_success">Unload Success</string>
    <string name="text_enter">Enter</string>
    <string name="text_item_done">Item Done</string>
    <string name="text_count_unshipped">Count Unshipped</string>

    <string name="label_sn_not_submit">You have SN not submitted, cancel it?</string>
    <string name="label_finish">Finished</string>
    <string name="btn_finish_qc">Finish QC</string>
    <string name="label_unfinish">Unfinished</string>
    <string name="label_total">"Total:"</string>
    <string name="label_suggest">"Suggest:"</string>
    <string name="label_actual">"Actual:"</string>
    <string name="label_uom_colon">"UOM:"</string>
    <string name="lable_progress_colon">"progress:"</string>
    <string name="label_too_much_quantity">"Too much quantity"</string>
    <string name="txt_pick_qty_error">pick qty error"</string>
    <string name="label_print_lp">Print LP</string>
    <string name="text_pallet">Pallet Label</string>
    <string name="text_shipping_label">Reprint Shipping Label</string>
    <string name="text_label_type">Label Type:</string>
    <string name="text_label_type_zpl">ZPL</string>
    <string name="text_label_type_pdf">PDF</string>
    <string name="text_print_success">Print Success</string>
    <string name="text_print_failed">Print Failed</string>
    <string name="msg_search_tracking_no_failed">Search failed, please check trackingNo again!</string>
    <string name="title_print_pallet_label">Print Pallet Label</string>
    <string name="hint_print_pallet_label">Please input Order ID</string>
    <string name="title_print_ucc_label">Print Ucc Label</string>
    <string name="hint_print_ucc_label">Please input Load ID or Order ID</string>
    <string name="error_unsupport_organization">Unsupported organization :</string>
    <string name="text_ucc_label">Ucc Label</string>
    <string name="text_remove_success">Remove success</string>
    <string name="label_sn_list">SN List</string>
    <string name="label_sn_list_mark">SN List:</string>
    <string name="text_require_print_label_content">Required Label(s):</string>

    <string name="msg_release_dock_failed">Release dock failed</string>
    <string name="msg_release_dock_success">Release dock success</string>
    <string name="label_dock_name">Dock name</string>
    <string name="label_dock">Dock: %1$s</string>
    <string name="label_dock_name_mark">Dock name</string>
    <string name="checking_no">Checking No.</string>
    <string name="label_dock_status">Dock status</string>
    <string name="msg_do_your_want_to_release_dock">Do you want to release dock?</string>
    <string name="alert_release_dock">Task complete, release dock ?</string>
    <string name="title_dock_operate">Dock Operate</string>
    <string name="title_dock_check_out">Dock Check Out</string>
    <string name="title_wifi_locating_info_collect">Wifi Locating Info Collect</string>
    <string name="title_wifi_info_collect">Wifi Info Collect</string>
    <string name="menu_configuration">Configuration</string>
    <string name="error_dock_not_found">Dock not found, please input again</string>
    <string name="error_dock_xxx_not_found">Dock: %1$s not found, please input again</string>
    <string name="title_default_printer_settings">Default printer settings</string>
    <string name="label_change">Change</string>
    <string name="msg_small_label_printer">Print item LP and entry label </string>
    <string name="msg_middle_label_printer">Print shipping label, 4-6 LP Label</string>
    <string name="msg_large_bale_printer">Print bol and pdf file</string>
    <string name="msg_please_select_printer">Please select printer</string>
    <string name="msg_please_select_pdf_printer">Please select PDF printer</string>
    <string name="msg_no_printer_for_this_label">No printer for this type</string>
    <string name="title_select_printer">Select printer</string>
    <string name="hint_comfirm_load_complete">Please comfirm load completed</string>
    <string name="hint_comfirm_load_complete_for_close">Load is not completed, do you want to close it?</string>
    <string name="hint_comfirm_submit_load_pro_number">Submit will overwrite all orders(except DS order) pro number in current load, do you confirm ?</string>
    <string name="text_load_complete">Load completed</string>
    <string name="title_print_item_label">Print item label</string>
    <string name="label_print_item">Print item</string>
    <string name="msg_please_input_amount">Please input amount</string>
    <string name="hint_scan_or_input_location_to_search">Scan or input location to search</string>
    <string name="hint_scan_or_input_location">Scan or input location</string>
    <string name="hint_scan_or_input_item">Scan or input item</string>
    <string name="msg_item_put_away_not_complete">Item put away unfinished, exit?</string>
    <string name="hint_scan_or_input_location_of_order">Scan or input location of order</string>
    <string name="msg_do_you_want_to_pack_all_lp_in_this_order">Do you want to pack all LP in this order?</string>
    <string name="hint_please_confirm_start_step">Do you confirm start step</string>
    <string name="error_load_seal">Seal Is Not Exist</string>
    <string name="title_add_ctnr_trailer_no">ADD CTNR / Trailer NO.</string>
    <string name="hint_please_input_ctnr_trailer_no">Please input CTNR / Trailer NO.</string>
    <string name="msg_please_collect_ctnr_trailer_no_first">Please collect CTNR / Trailer NO. first</string>
    <string name="msg_add_ctnr_trailer_no_success">Add CTNR / Trailer NO. success</string>
    <string name="title_add_seal">Add Seal</string>
    <string name="pallet_view">Pallet</string>
    <string name="msg_not_allow_to_done_load_step">Load is not completed!</string>
    <!-- pick begin -->
    <string name="label_order_group">ORDER GROUP</string>
    <string name="label_order_colon">Order:</string>
    <string name="label_item_colon">Item:</string>
    <string name="label_allocated_progress">Allocated Progress:%1$s/%2$s(%3$s)</string>
    <string name="label_suggest_lp_colon">Suggest LP:</string>
    <string name="label_lp_colon">LP:</string>
    <string name="label_require_qty_colon">Require Qty:</string>
    <string name="label_on_hand_quantity_colon">On Hand Quantity:</string>
    <string name="label_sum_colon">Sum:</string>
    <string name="label_allocate_to_order_colon">Allocate To Order :</string>
    <string name="label_sure_reopen_task">Are you sure reopen task?</string>
    <string name="label_force_close_task">Item pick is not finished yet. Do you want to force close task?</string>
    <string name="label_need_pick">Need Pick:</string>
    <string name="label_diverse_msg">Diverse Message</string>
    <string name="label_task_no_contains_this_LP">Task no contains this LP: %1$s</string>
    <string name="label_filter_lp_by_order">Filter LP by order</string>
    <string name="title_filter_lp_by_order">Filter LP by order?</string>
    <string name="label_no_diverse">No Diverse</string>
    <string name="label_sn_to_allocate">Please scan sn to allocate</string>
    <string name="label_lp_to_allocate">Please scan lp to allocate</string>
    <string name="label_no_suggest">No Suggest!</string>
    <string name="label_order_no">Order Number</string>
    <string name="title_tote">Tote</string>
    <string name="title_carton">Carton</string>
    <string name="title_pallet">Pallet</string>
    <string name="title_brackets_single_item">(Single Item)</string>
    <string name="title_brackets_consolidation">(Consolidation)</string>
    <string name="label_carrier_pick_up_tags">Carrier Pick Up | Tags</string>
    <string name="label_priority_creator_created_time">Priority | Creator | Created Time</string>
    <string name="label_item_pallet_qty_type_stock_high">Item | Pallet Qty | Type | Stock High</string>
    <string name="label_task_create_msg">"Creator | Created Time "</string>
    <string name="label_customer_and_retailer">Customer</string>
    <string name="menu_item_bind_order_with_LP">Bind Order With LP</string>
    <string name="menu_close_pallet">Close Pallet</string>
    <string name="menu_scan_lp_to_stage">Scan LP To Stage</string>
    <string name="menu_material_view">Material View</string>
    <string name="menu_item_order_allocate">Order Allocate</string>
    <string name="menu_item_order_group">Order Group</string>
    <string name="menu_item_print_pallet">Print Pallet Label</string>
    <string name="menu_item_movement">Movement</string>
    <string name="menu_item_return_inventory">Return Picked To Inventory</string>
    <string name="menu_item_print_copy_lp">Print Or Copy LP</string>
    <string name="menu_item_preset_stage_location_and_to_lp">Preset Stage Location And To LP</string>
    <string name="label_preset">Preset Value:</string>
    <string name="label_order">Order</string>
    <string name="label_Input_LP">Please Input LP</string>
    <string name="label_please_scan_sn_to_return">Please scan sn to return</string>
    <string name="label_please_scan_lp_to_return">Please scan lp to return</string>
    <string name="label_lp_no_contains_this_sn">LP No Contains this SN</string>
    <string name="label_return_inventory">Return Inventory</string>
    <string name="label_this_task_no_contain_this_Lp">this task no contain this Lp</string>
    <string name="label_entire_allocate_contain_lp">Entire Allocate(contain LP):</string>
    <string name="label_entire_pick_contain_lp">Entire Pick(contain LP):</string>
    <string name="label_entire_return_contain_lp">Entire Return(contain LP):</string>
    <string name="label_to_lp_is_empty">to LP is empty</string>
    <string name="msg_in_the_submission">In the submission</string>
    <string name="label_pick_qty_is_empty">pick Qty is empty</string>
    <string name="label_pick_qty_is_0">pick Qty is 0</string>
    <string name="label_LP_total_qty_is_0">LP Total Qty is 0</string>
    <string name="title_preset_arrangement_rule">Preset Arrangement Rule</string>
    <string name="title_allocate_qty">Allocate Qty</string>
    <string name="txt_bind_lp">%1$s unbound LP,Please bind order with LP.</string>
    <string name="txt_is_jump_bind">%1$s unbound LP,Please bind order with LP. Are you jump to bind page.</string>
    <string name="txt_is_jump_to_order_allocate">Are you jump to order allocate page.</string>
    <string name="txt_system_prompt">system prompt</string>
    <string name="label_you_can_change_qty_and_attach_lp">You can change qty and attach LP</string>
    <string name="label_qty_more_than_suggest_qty">Qty more than suggest qty</string>
    <string name="label_pick_enough">You have picked enough current item from this LP.</string>
    <string name="txt_is_jump_stage">You have picked enough current task, Are you jump to stage page.</string>
    <string name="txt_is_jump_order_allocate">You have picked enough current task, Are you jump to order allocate page.</string>
    <string name="txt_select_the_way_to_allocate_order">Please select the way to allocate order.</string>
    <string name="select_lp_type">Select the LP type.</string>
    <string name="label_pick_scan_sn_enough">You have scan enough SN.</string>
    <string name="label_remove_sn">Please remove %1$d SN</string>
    <string name="title_remove_sn">Remove SN</string>
    <string name="msg_remove_sn">Remove SN: %1$s ?</string>
    <string name="label_from_lp">From LP</string>
    <string name="label_from_lp_mark">From LP:</string>
    <string name="label_suggest_picked_qty">Suggest Picked Qty:</string>
    <string name="label_suggest_qty_mark">Suggest Qty:</string>
    <string name="text_bulk_pick">Bulk Pick</string>
    <string name="text_pallet_pick">Pallet Pick</string>
    <string name="text_piece_pick">Piece Pick</string>
    <string name="text_case_pick">Case Pick</string>
    <string name="text_none">None</string>
    <string name="text_order_pick">Order Pick</string>
    <string name="text_wave_pick">Wave Pick</string>
    <string name="text_wave_pick_by_order">Wave Pick(By Order)</string>
    <string name="text_wave_pick_by_item">Wave Pick(By Item)</string>
    <string name="text_batch_order_pick">Batch Order Pick</string>
    <string name="msg_please_input_order_no">Please input order NO</string>
    <string name="label_pick_to_lp">Pick To LP:</string>
    <string name="label_pick_to_assigned_lp">Pick to Assigned LP:</string>
    <string name="msg_lp_is_empty">Lp is empty</string>
    <string name="select_item_mark">Select item:</string>
    <string name="label_pick_qty_mark">Pick Qty:</string>
    <string name="label_pick_item_mark">Pick Item:</string>
    <string name="msg_task_not_complete_force_close">Task not complete force close?</string>
    <string name="title_picked_message">Picked Message</string>
    <string name="msg_force_close">Do you want to force close this task?</string>
    <string name="hint_scan_or_input_lp_to_bind_order">Scan or input lp to bind order</string>
    <string name="meg_bind_success_select_lp_to_pick">Bind success,select lp to pick.</string>
    <string name="label_picking_item">Picking Item:</string>
    <string name="label_qty_in_lp">Qty in LP:</string>
    <string name="text_input_pick_qty">Input Pick Qty:</string>
    <string name="text_scan_lp_to_bind">Scan LP to Bind:</string>
    <string name="text_new_lp">New Lp</string>
    <string name="label_picked_mark">Picked:</string>
    <string name="label_location_mark">Location:</string>
    <string name="label_to_location_mark">To Location:</string>
    <string name="label_pack_to_location_mark">Pack To Location:</string>
    <string name="text_next_location">Next Location</string>
    <string name="text_scan_lp_to_unbind">Scan lp to unbind</string>
    <string name="title_unbind">Unbind</string>
    <string name="msg_lp_not_bind_any_order">Lp not bind any order</string>
    <string name="msg_unbind_lp_with_order">Unbind %1$s | %2$s ?</string>
    <string name="title_pallet_status_mark">Pallet Status:</string>
    <string name="title_full_pallet">Full pallet</string>
    <string name="title_partial_pallet">Partial pallet</string>
    <string name="title_lp_list">LP List</string>
    <string name="title_need_to_pack_lps_mark">Need to Pack LPs:</string>
    <string name="msg_please_select_pallet_status_first">Please select pallet status first</string>
    <string name="msg_invalidated_input">Invalidated Input!</string>
    <string name="msg_no_support_pick_from_slp_for_this_customer">No support pick from slp for this customer</string>
    <string name="msg_pick_confirm">Suggest picked Qty:%1$s, submit Qty:%2$s, continue?</string>
    <string name="msg_current_location_is_not_an_pick_location_please_scan_lp_to_pick">Current location is not a Pick Location,please scan LP to pick</string>
    <string name="hint_scan_tracking_no">Scan trackingNO</string>
    <string name="label_switch_item">Switch Item</string>
    <string name="msg_only_one_item">Not need switch, single item LP: %1$s</string>
    <string name="hint_scan_so_id">Scan SOID</string>
    <string name="hint_please_scan_so_id">Please Scan SOID</string>
    <string name="msg_order_load_failed_reload">Order load failed, reload ?</string>
    <string name="msg_stage_picked_items">Please stage all the picked items</string>
    <string name="title_go_to">Go to</string>
    <string name="title_assign_to_other">Assign to other user</string>
    <string name="msg_no_other_suggest_location">No other suggesting location</string>
    <string name="msg_report_partial_pallet_issue_input_lp">input lp</string>
    <string name="msg_report_partial_pallet_issue_input_function_pwd">please input function password</string>
    <string name="btn_report_partial_pallet_issue">report pallet issue</string>
    <string name="btn_report_partial_pallet_issue_cancel">cancel</string>
    <string name="suggest_stage_location">Suggest Stage Location:</string>
    <string name="no_new_suggestion">No new Suggestions</string>
    <string name="no_tracking_numbers_found_for_task">No Tracking Numbers found for this task</string>
    <string name="error_scanned_tracking_excessive">Numbers of scanned tracking# exceeded</string>
    <string name="error_scanned_tracking_numbers_not_match">Numbers of scanned tracking# does not match with pick quantity</string>
    <string name="error_scanned_tracking_not_match">Tracking number: %1$s does not match</string>
    <string name="error_not_found_location_by_tracking">can not found suggestion location by tracking number: %1$s, please scan location</string>
    <string name="error_no_location_by_bluetooth">no locate suggest location data with blueTooth,please assign facility </string>
    <string name="text_pick_v1_start">Let’s Start</string>
    <string name="text_pick_v1_scan_tote_cart">Scan Tote Cart or Input</string>
    <string name="hint_pick_v1_scan_tote_cart">Input Tote Cart or Scan</string>
    <string name="text_pick_v1_tote_cart">Your Tote Cart</string>
    <string name="text_pick_v1_pick_to_tote">Pick to Tote</string>
    <string name="text_pick_v1_pick_to_pre_lp">Pick to Pre Print LP</string>
    <string name="text_pick_v1_pick_to_live_lp">Pick to Live Print LP</string>
    <string name="text_pick_v1_pick_to_robot">Pick to Robot</string>
    <string name="text_pick_v1_pick_to_sorting_wall">Pick to Sorting Wall</string>
    <string name="button_pick_v1_start">START</string>
    <string name="button_pick_v1_print_lp">PRINT LP</string>
    <string name="title_pre_print_lp_num">Number of pre-print LP</string>
    <string name="text_case">CASE</string>
    <string name="hint_pick_v1_scan_location">Enter or Scan Location</string>
    <string name="hint_pick_v1_scan_lp">Input ILP or Scan</string>
    <string name="hint_pick_v1_scan_upc">Input Or Scan Item Code</string>
    <string name="hint_pick_v1_scan_sn">Input Or Scan SN</string>
    <string name="button_next_location">NEXT LOCATION</string>
    <string name="button_new_container">PRINT NEW LP</string>
    <string name="text_my_progress">My Progress</string>
    <string name="hint_pick_v1_scan_container">Scan Container</string>
    <string name="text_pick_v1_remove_item_qty_title">How many would you like to remove?</string>
    <string name="msg_please_scan_suggest_location">Please scan suggest Location</string>
    <string name="msg_ilp_not_allow_input">ILP not allow</string>
    <string name="msg_scan_to_previous_LP">Please scan to previous Lp</string>
    <string name="msg_not_match_printed_LP">Scan Lp not match printed Lp</string>
    <string name="msg_tote_cart_remove">%1$s was removed</string>
    <string name="msg_not_allow_override_picking_suggested_location">not allow override picking suggested location</string>
    <string name="no_lot_no_found_for_task">LOT number not found in the inventory</string>
    <string name="pick_qty_more_than_lot_no_qty">Pick qty more than lot number qty</string>
    <string name="text_pick_mode">PICK MODE</string>
    <string name="text_pick_mode_general_process">General Process</string>
    <string name="text_pick_mode_pick_by_tote_cart">Pick by ToteCart</string>
    <string name="text_pick_mode_pick_by_pre_lp">Pick by Pre-Print LP</string>
    <string name="text_pick_scan_tote_cart">Scan or Enter Tote Cart</string>
    <!-- pick end -->

    <!--Pick v1 start-->
    <string name="hint_input_or_scan_location_below">Please Input Location or Scan Below</string>
    <string name="process_to_next_task">Process to next task?</string>
    <string name="location_added">Location Added</string>
    <string name="lps_staged_at_location">%1$s LPs staged at %2$s</string>
    <string name="auto_close_step_failed_ask_force_close">Failed to automatically close this step, force close?</string>
    <string name="lp_added">ILP Added</string>
    <string name="container_added">Container Added</string>
    <string name="menu_view_progress">View Progress</string>
    <string name="search_location">Search Location</string>
    <string name="message_no_more_suggest">No more suggestion location</string>
    <string name="txt_lot_no">LotNo(Qty): %1$s</string>
    <string name="msg_tote_cart_to_stage">Tote cart is full，please to stage tote</string>
    <string name="msg_tote_cart_check_out">Tote cart is full，please check out</string>
    <string name="msg_tote_cart_used_other_task">Tote cart is occupied by other tasks</string>
    <string name="msg_please_scan_tote">Scanned/entered is not Tote, please scan/enter again</string>
    <string name="msg_tote_is_not_in_tote_cart">The tote:%1$s is not in Tote Cart:%2$s  </string>
    <string name="msg_tote_used"> The tote:%1$s already used by order %2$s</string>
    <string name="msg_lot_no_scanned">The Lot NO %1$s scanned</string>
    <string name="text_expired">Expire:%1$s</string>
    <string name="text_mfg">Mfg:%1$s</string>
    <string name="text_confirm_removal">Confirm removal:</string>
    <string name="text_error">Error:</string>
    <string name="text_report">REPORT</string>
    <string name="message_pick_by_lot_no_next_lp">No more LP found at current location, REPORT exception or SUBMIT and proceed to next location.</string>
    <string name="total_lp_to_stage">Total LP to Stage</string>
    <string name="sure_to_remove_this_lp">Are you sure you want to remove this LP?</string>
    <string name="msg_error_stage_to_pick_location">Not allowed to stage to a Pick Location</string>
    <string name="dialog_pick_force_close">Yes, Force Close</string>
    <!--Pick v1 end-->

    <!-- Robot stage start-->
    <string name="msg_please_select_lp">Please select LP</string>
    <string name="receive_new_staging_assignment">You have received a new staging assignment, do it now?</string>
    <string name="new_staging_assignment">New Staging Assignment</string>
    <!-- Robot stage end-->

    <!-- Shuttle Task start-->
    <string name="title_shuttle_tasks">Shuttle Tasks</string>
    <string name="title_shuttle_tasks_view_create_edit_delete">Shuttle Tasks view/create/edit</string>
    <string name="title_create_shuttle_task">Create Shuttle Task</string>
    <string name="title_edit_shuttle_task">Edit Shuttle Task</string>
    <string name="title_view_shuttle_task">View Shuttle Task</string>
    <string name="label_planned_start">Planned Start</string>
    <string name="label_need_finish_by">Need Finish By</string>
    <string name="msg_planned_start_date_required">Planned start date is required</string>
    <string name="msg_planned_start_date_after_current">Planned start date must be after current date</string>
    <string name="msg_planned_start_date_after_finish_date">Planned start date must be before need finish by date</string>
    <string name="msg_need_finish_by_date_required">Need finish by date is required</string>
    <string name="msg_need_finish_by_date_after_current">Need finish by date must be after current date</string>
    <string name="msg_need_finish_by_date_after_planned_start">Need finish by date must be after planned start date</string>
    <string name="shuttle_task_created_successfully">Shuttle task created successfully</string>
    <string name="shuttle_task_updated_successfully">Shuttle task updated successfully</string>
    <string name="search_task_id_eq_no_location">TASK ID/EQ No/Location</string>
    <string name="to_location_cannot_be_same_as_from_location">Source and destination locations cannot be me same</string>
    <string name="location_is_not_available">Location is not available</string>
    <string name="label_seal_no_well_number">Seal#</string>
    <string name="label_is_reefer">Is Reefer?</string>
    <string name="label_vehicle_no_well_number">Vehicle #</string>
    <string name="label_trailer_no_well_number">Trailer #</string>
    <string name="label_chassis_no_well_number">Chassis #</string>
    <string name="label_container_no_well_number">Container #</string>
    <string name="label_flatbed_no_well_number">Flatbed #</string>
    <string name="label_dn_load_container_trailer_seal">DN#/LOAD#/EQ#/SEAL#</string>
    <string name="search_equipment_to_shuttle">Search Equipment to Shuttle</string>
    <string name="label_exception_with_colon">Exception:</string>
    <string name="to_location_group">To Location Group</string>
    <string name="release_and_create_task">Release and Create Task</string>
    <string name="continue_to_create_task">Continue to Create Task</string>
    <string name="msg_release_dock_occupied_xxx">The current location is occupied by: \n\n%1$s \n\nDo you want to release it?</string>
    <string name="msg_release_dock_assigned_xxx">The current location is assigned by: \n\n%1$s \n\nDo you want to release it?</string>
    <string name="msg_release_dock_occupied">The current location is occupied \nDo you want to release it?</string>
    <string name="msg_release_dock_assigned">The current location is assigned \nDo you want to release it?</string>
    <string name="msg_no_location_in_group">No location in group, please select other group</string>
    <!-- Shuttle Task end-->

    <!-- put away start-->
    <string name="label_start_task">Click the button to create the task and start the task.</string>
    <string name="label_confirm_closing_the_task">Confirm closing the task</string>
    <string name="label_new_task">New Task</string>
    <string name="text_forklift">Forklift</string>
    <string name="text_pallet_jack">Pallet Jack</string>
    <string name="lp_not_found_by_mark">LP not found by: </string>
    <string name="location_not_found_by_mark">Location not found by: </string>
    <string name="scan_location_mark">Scan Location:</string>
    <string name="put_away_lp_list">Put Away LP List:</string>
    <string name="batch_movement_lp_list">Batch Movement LP List:</string>
    <string name="location_select_success">Location select success!</string>
    <string name="label_go_to_the_location_and_scan">Go to the Location and Scan</string>
    <string name="label_suggested_location_mark">Suggested Location:</string>
    <string name="text_loc_full">Location Full</string>
    <string name="label_select_or_scan_the_lp_to_put_away">Select or Scan the LP to putaway</string>
    <string name="hint_enter_the_qty">Enter the Qty</string>
    <string name="text_put_away_qty_greater_lp_qty">Putaway QTY greater LP QTY</string>
    <string name="label_putaway_qty_mark">Putaway QTY:</string>
    <string name="label_lp_qty_mark">LP QTY:</string>
    <string name="msg_put_away_qty_is_different_from_lp_qty_continue">Putaway QTY is different from LP QTY,Continue?</string>
    <string name="label_generate_scan_lp_for_the_qty_left_on_fork">Generate/SCAN LP for the QTY left on fork</string>
    <string name="msg_lp_xx_item_xx_lp_qty_xx_put_away_qty_xx">LP: %1$s \nItem: %2$s \nLP QTY: %3$s\nPutaway QTY: %4$s</string>
    <string name="msg_location_full">Location Full</string>
    <string name="msg_could_not_find_the_location">Couldn\'t find the Location</string>
    <string name="msg_do_not_like_it">Don\'t like it</string>
    <string name="msg_inventory_not_found_for_selected_lps">Inventory not found for selected LPs</string>
    <string name="text_sn_count_mark_xx">SN Count: %1$s</string>
    <string name="label_suggest_plan_preview">Suggest Plan Preview</string>
    <string name="label_putaway_location">Putaway - Location</string>
    <string name="label_suggest_location_mark">Suggest Location:</string>
    <string name="label_scanned_location_mark">Scanned Location:</string>
    <string name="label_suggest_stage_area_mark">Suggest Stage Area: </string>
    <string name="label_suggest_item_sku_mark">Suggest Item/SKU:</string>
    <string name="hint_scan_item_barcode">Scan Item Barcode</string>
    <string name="text_to_storage_location">To Storage Location</string>
    <string name="text_to_pick_front">To Pick Front</string>
    <string name="text_put_away_by_lp">Putaway By LP</string>
    <string name="text_put_away_by_item">Putaway By Item</string>
    <string name="hint_check_digit">Check Digit</string>
    <string name="text_scan_tote_id_lp">Scan ToteID / LP</string>
    <string name="hint_scan_other_location">Scan Other Location</string>
    <string name="btn_start_putaway">Start Putaway</string>
    <string name="title_location_space_availability">Location space availability</string>
    <string name="label_empty">Empty</string>
    <string name="label_full">Full</string>
    <string name="confirm_remove_item">Remove Item ?</string>
    <string name="confirm_remove_item_xx">Remove Item %1$s?</string>
    <string name="text_scan_location_digit">Scan Location / Digit</string>
    <string name="label_tote_lp">Tote / LP</string>
    <string name="msg_no_permission_to_put_away_by_lp">No permission to put away by LP.</string>
    <string name="msg_the_putaway_location_is_full">The scanned location was marked as Full, confirm to PutAway?</string>
    <string name="text_allow_multi_scan">Allow Multi Scan</string>
    <string name="msg_for_rlp_please_use_by_item_method_to_put_away">For RLP, please use by item method to put away</string>
    <!-- put away end -->

    <!-- put away v1 start -->
    <string name="label_multi_scan">Multi Scan</string>
    <string name="label_remove_lp_mode">Remove LP Mode</string>
    <string name="text_lp_to_put_away">LP to Put Away</string>
    <string name="hint_scan_rn_or_container">Enter or scan LP/RN</string>
    <string name="title_scanned_lp">Scanned LP</string>
    <string name="text_suggest">Suggest</string>
    <string name="hint_scan_lp_or_location">Enter or scan LP | Location</string>
    <string name="current_put_away_location">Current Put Away Location: %1$s</string>
    <string name="error_first_scan_rn_or_container">Please first scan LP, RN</string>
    <string name="message_add_putaway_location">Location %1$s is added</string>
    <string name="text_qty_large">QTY: %1$s</string>
    <string name="error_no_lp_for_suggest">No scanned lp for suggest</string>
    <string name="title_change_putaway_location">Change Putaway Location?</string>
    <string name="message_change_putaway_location">Are you sure you want to change put away location?</string>
    <string name="text_submitted_lps_location">Submitted LPs @ %1$s</string>
    <string name="msg_lp_not_exist_scanned_lp">LP not exist on scanned lp</string>
    <string name="msg_complete_put_away">Are you sure you want to complete？</string>

    <!-- put away v1 end -->

    <!-- receive start -->
    <string name="msg_item_received_using_template_cannot_use_different_template">Item has been received using template: %1$s, cannot use a different template for receiving</string>
    <string name="msg_alert_high_value_item">High Value Item! Please see supervisor for special handing.</string>
    <string name="msg_iqc_item_receive_alert">Can not set up require IQC item with other item, please separate.</string>
    <string name="msg_iqc_item_should_receive_as_on_hold">%1$s is required for IQC, please receive as On-Hold item.</string>
    <string name="error_select_item_first">Please select item first</string>
    <string name="error_msg_input_qty">Please input QTY</string>
    <string name="lp_type_description">Goods is damage or good</string>
    <string name="lp_template_description">LP capacity</string>
    <string name="select_lp_template">Select LP Template</string>
    <string name="msg_please_select_suggested_pallet_ti_hi">Customer forbid create LP config at receive task\nPlease select suggested pallet Ti Hi</string>
    <string name="select_qty_uom">Select QTY/UOM</string>
    <string name="summary_item_uom_qty_and_so_on">Item UOM/QTY and so on</string>
    <string name="item_detail">Item Detail</string>
    <string name="item_detail_indicate">Item Detail (Name/QTY/UOM)</string>
    <string name="lp_template">LP Template</string>
    <string name="select_item">Select item</string>
    <string name="select_po">Select po</string>
    <string name="single_item">Single item</string>
    <string name="single_item_description">LP only contain single item type</string>
    <string name="mix_item">Mix item</string>
    <string name="text_by_carton">By Carton</string>
    <string name="receive_to_put_away">Receive to Putaway</string>
    <string name="btn_submit_and_put_away">Submit And Putaway</string>
    <string name="receive_to_put_away_description">Receive item and putaway direct</string>
    <string name="msg_please_scan_or_input_forklift">Please scan or input Forklift</string>
    <string name="mix_item_description">LP should contain multiple item type</string>
    <string name="receive_to_pick_location">Receive to Pick Location</string>
    <string name="receive_to_pick_location_description">Item can only received to Pick Location</string>
    <string name="receive_by_carton">Receive By Carton ID</string>
    <string name="receive_by_carton_description">User is required to collect carton ID</string>
    <string name="item_qty_uom">%1$s / %2$s / %3$s</string>
    <string name="msg_complete_lp_verify_step_first">Please complete LP Verify step first!</string>
    <string name="not_allow_select_pick_location_for_single_item">Not allow select pick location for Single item</string>
    <string name="not_allow_select_pick_location">Not allow select pick location</string>
    <string name="msg_please_select_pick_location">Please select pick location</string>
    <string name="msg_please_set_item_qty">Please set item Qty</string>
    <string name="title_remind">Remind</string>
    <string name="msg_delete_lp">Delete it?</string>
    <string name="text_force_close_verification">Force Close Verification</string>
    <string name="text_packing_list_photo">Packing List Photo</string>
    <string name="text_packing_list_photo_capture">Packing List Photo Capture</string>
    <string name="hint_select_reason">Select Force Close Reason</string>
    <string name="hint_fill_reason">Fill Force Close Information</string>
    <string name="text_partial_received_note">Physical Count is less than receipt item line qty</string>
    <string name="text_short_received_note">Physical Count is less than packing list qty</string>
    <string name="text_other_note">Physical Count does not match to packing list</string>
    <string name="text_mixed_item_in_carton">Mixed Item In Carton</string>
    <string name="text_mixed_item_to_sort">Mixed Item on Tote/CLP</string>
    <string name="btn_complete_current_rn">Complete Current RN</string>
    <string name="text_sorting_suggestion">Sorting Suggestion</string>
    <string name="text_carton_id">Carton ID</string>
    <string name="text_bol_or_hawb">BOL/HAWB: %1$s</string>
    <string name="label_carton_id">Carton ID: %1$s</string>
    <string name="hint_scan_bol_tracking_no_rn_no">Please scan BOL/Tracking#/RN Number</string>
    <string name="hint_scan_carton_id_or_tracking_no">Please scan Carton ID or tracking#</string>
    <string name="titel_scan_carton_id">Scan Carton ID</string>
    <string name="msg_rn_not_found">RN not found by %1$s, please check!</string>
    <string name="msg_item_line_not_found_format">Not itemLine found by: %1$s</string>
    <string name="title_select_item_line">Select Item Line</string>
    <string name="label_bol_tracking_no">BOL/Tracking.</string>
    <string name="text_add_qty">Added QTY:</string>
    <string name="label_items">%1$s Items</string>
    <string name="hint_scan_or_input_lp_tote_id">Please scan or input LP/Tote ID</string>
    <string name="title_select_equipment">Select Equipment</string>
    <string name="msg_more_item_from_the_carton">More item from the Carton: %1$s?</string>
    <string name="text_received_qty_expect_qty">%1$s: %2$s | Received Qty:%3$s%4$s / Expected Qty: %5$s%6$s</string>
    <string name="text_carton_view">Carton View</string>
    <string name="hint_item_rn_bol_reference_lp">Item/RN/BOL/Reference/LP</string>
    <string name="hint_item_rn_bol_reference_carton_id">Item/RN/BOL/Reference/Carton ID</string>
    <string name="hint_item_rn_bol_reference_carton_id_lp_lpn">Item/RN/BOL/Reference/Carton ID/LP/LPN</string>
    <string name="txt_receipt_no">Receipt NO:</string>
    <string name="txt_bol">BOL:</string>
    <string name="txt_carton_id">Carton ID:</string>
    <string name="txt_cart_id">Cart ID:</string>
    <string name="txt_turn_off_request_put_away_switch_confirm">Turn off Request Putaway switch?</string>
    <string name="msg_not_allow_turn_off_request_put_away_switch">Not allow turn off Request Putaway switch</string>
    <string name="msg_cart_not_found">Cart not found</string>
    <string name="hint_please_scan_cart">Please Scan Cart</string>
    <string name="label_request_putaway">Request Putaway</string>
    <string name="label_ready_for_putaway">Ready for Putaway</string>
    <string name="txt_assignee">Assignee:</string>
    <string name="msg_item_line_already_added">Item line:%1$s already added to list, please check.</string>
    <string name="msg_receipt_need_collect_carton_id">Receipt:%1$s required collect carton ID, please receive by carton ID.</string>
    <string name="msg_some_verification_data_not_submit">Some verification data not submit, please check.</string>
    <string name="msg_verification_data_submit_success">Verification data submit success</string>
    <string name="msg_complete_rn_success">Complete RN:%1$s success</string>
    <string name="msg_at_least_select_one_item">Please select one item at least.</string>
    <string name="msg_not_allow_edit_you_can_delete_it_and_setup_again">Not allow edit,you can delete it and Setup again</string>
    <string name="msg_please_remove_iqc_item">Please remove IQC item</string>
    <string name="label_tracking_no_mark">Tracking No:</string>
    <string name="label_pallet_id_mark">Pallet ID:</string>
    <string name="msg_not_allow_select_xx_location">Not allow select %1$s location</string>
    <string name="msg_not_allow_select_xx_xx_location">Not allow select %1$s/%2$s location</string>
    <string name="label_please_scan_coo">Please scan COO</string>
    <string name="label_sn_scanning">SN Scanning</string>
    <string name="label_collect_record">Collect Record</string>
    <string name="msg_sn_qty_no_match_skip_continue_scan_sn">SN qty not match,skip or continue scan SN</string>
    <string name="label_required_sn_mark">Required SN:</string>
    <string name="label_required_sn">Required SN</string>
    <string name="text_collect_lower_case">collect</string>
    <string name="collect_sn_dimension_title">Collect SN Dimension</string>
    <string name="qty_not_match_provide_proof_files">Received Item Qty not matched with Receipt, Please verify and provide proof files.</string>
    <string name="text_ready_for_putaway">Ready for Putaway</string>
    <string name="text_already_putaway">Already Putaway</string>
    <string name="msg_no_lp_meeting_the_conditions_was_found">No LpView meeting the conditions was found</string>
    <string name="msg_serial_number_does_not_match_the_validation_rule">Serial number does not match the validation rule</string>
    <string name="msg_serial_number_length_not_match">Serial number length not match</string>
    <string name="msg_invalid_validation_rule_xx">Invalid validation rule: %1$s</string>
    <string name="get_lp_view_info_failure">Get lp view info failure</string>
    <string name="get_lp_view_info_empty_please_contact_the_administrator">Lp view info empty, please contact the administrator</string>
    <string name="msg_do_you_want_to_add_upc_code_optional">Do you want to add UPC code (optional)?</string>
    <string name="msg_item_id_empty_please_contact_administrator">Item ID is empty, please contact administrator</string>
    <string name="label_worker_count">Estimate Worker Count</string>
    <string name="hint_please_input_worker_count">Please input worker count</string>
    <string name="text_take_photo_for_case">Take photo for case</string>
    <string name="text_uom_pack">UOM Pack:</string>
    <string name="msg_please_select_lp_subtype">Please select LP subtype</string>
    <string name="text_work_count">Work Count(s)</string>
    <string name="text_total_hours">Total Hours</string>
    <string name="text_forklift_driver">Forklift Driver</string>
    <string name="text_lumper">Lumper</string>
    <string name="text_confirm_total_hours">Confirm Total Hours</string>
    <string name="msg_please_input_valid_forklift_work_count">Please input valid forklift work count</string>
    <string name="msg_please_input_valid_forklift_total_hours">Please input valid forklift total hours</string>
    <string name="msg_please_input_valid_lumper_work_count">Please input valid lumper work count</string>
    <string name="msg_please_input_valid_lumper_total_hours">Please input valid lumper total hours</string>
    <string name="collect_all_pallet_to_stage">Collect All Pallets to Stage</string>
    <string name="msg_lp_not_received_cannot_stage">LP not received, cannot stage</string>
    <string name="msg_pallet_already_staged">Pallet already staged</string>
    <string name="msg_confirm_close_stage_step">LP stage finished. Are you sure to close step?</string>
    <!-- receive end -->

    <string name="label_print_amount">Print amount</string>
    <string name="msg_cannot_find">Find nothing</string>
    <string name="action_filter">Set filter</string>
    <string name="action_printer_settings">Printer Settings</string>
    <string name="action_set_lp_label_size">Set LP Label Size</string>
    <string name="label_filter">Filter</string>
    <string name="label_print_qty">Print qty</string>
    <string name="label_item_style">Item style</string>
    <string name="label_item_qty">Item qty</string>
    <string name="hint_1">1</string>
    <string name="label_uom">UOM</string>
    <string name="label_quantity_setting">Quantity setting</string>
    <string name="title_receipt_item_line">Item line</string>
    <string name="label_receive_no">Receipt No.</string>
    <string name="label_extra_item_line">Extra item line</string>
    <string name="msg_please_select_all_attribute">Please select all attribute</string>
    <string name="label_select_unit">Select unit :</string>
    <string name="hint_please_select_item">Please select item</string>
    <string name="error_invalid_pallet_label">Invalid pallet label</string>
    <string name="text_signature_as_before">Signature as before</string>
    <string name="hint_please_input_entry">Please input entry id</string>
    <string name="hint_please_input_dock">Please input dock</string>
    <string name="please_input_checking_no">Please input checking No.</string>
    <string name="hint_please_take_photo">Please take photo</string>
    <string name="hint_please_take_photo_for">Please take photo for</string>
    <string name="text_take_photo">take photo</string>
    <string name="hint_please_take_seal_photos">Please take seal photo</string>
    <string name="hint_please_take_container_photos">Please take container photo</string>
    <string name="hint_please_take_photos">Please take photo</string>
    <string name="text_input">Input</string>
    <string name="submit_seal">Submit seal</string>
    <string name="submit_count_sheet">Add Count Sheet</string>
    <string name="submit_material">Submit Material</string>
    <string name="hint_load_please_input_qty">Please input qty for :</string>
    <string name="hint_load_please_take_photo">Please take photo for :</string>
    <string name="msg_please_contact_csr_to_create_new_item">Please contact CSR to create new item</string>
    <string name="msg_please_input_qty">Please input qty</string>
    <string name="msg_qty_not_match_force_close">Qty not match force close?</string>
    <string name="qty_not_match_force_close">Qty not match, Force close?</string>
    <string name="hint_please_input_reason">Please Input Reason</string>
    <string name="label_force_close">Force close</string>f
    <string name="query_force_close">Force close ?</string>
    <string name="msg_please_select_oum">Please select uom</string>
    <string name="text_signed">Signed</string>
    <string name="entry_info">Entry info</string>
    <string name="hint_dock_check_way">Please dock check in form receipt or load task</string>
    <string name="label_offload_type">Offload type</string>
    <string name="label_offload_by_forklift_pallet">By forklift with pallet</string>
    <string name="label_offload_by_forklift_no_pallet">By forklift without pallet</string>
    <string name="label_offload_by_hand">By hand</string>
    <string name="title_select_offload_type">Select offload type</string>
    <string name="label_unknown">Unknown</string>
    <string name="text_by_hand">By hand</string>
    <string name="text_by_forklift_with_pallet">By forklift with pallet</string>
    <string name="text_by_forklift_without_pallet">By forklift without pallet</string>
    <array name="array_offload_type">
        <item>By forklift with pallet</item>
        <item>By hand</item>
        <item>By forklift without pallet</item>
    </array>
    <array name="array_trail_size">
        <item>20fter</item>
        <item>28fter</item>
        <item>32fter</item>
        <item>36fter</item>
        <item>40fter</item>
        <item>42fter</item>
        <item>42ft/43ft</item>
        <item>43fter</item>
        <item>45fter</item>
        <item>48fter</item>
        <item>53fter</item>
    </array>
    <array name="array_ship_method">
        <item>TL</item>
        <item>LTL</item>
        <item>Parcel</item>
    </array>
    <string name="text_twenty_fter">20fter</string>
    <string name="text_twenty_eight_fter">28fter</string>
    <string name="text_thirty_two_fter">32fter</string>
    <string name="text_thirty_six_fter">36fter</string>
    <string name="text_forty_fter">40fter</string>
    <string name="text_forty_two_fter">42fter</string>
    <string name="text_forty_two_three_fter">42ft/43ft</string>
    <string name="text_forty_three_fter">43fter</string>
    <string name="text_forty_five_fter">45fter</string>
    <string name="text_forty_eight_fter">48fter</string>
    <string name="text_fifty_three_fter">53fter</string>
    <string name="text_shipping_method_tl">TL</string>
    <string name="text_shipping_method_ltl">LTL</string>
    <string name="text_shipping_method_small_parcel">Small Parcel</string>
    <string name="work_done">Work done.</string>
    <string name="label_need_to_set">Need to set</string>
    <string name="label_check">Check</string>
    <string name="label_checkin">Check in</string>
    <string name="msg_please_capture_container_photo">Please capture container photos</string>
    <string name="msg_please_capture_trailer_photo">Please capture trailer photos</string>
    <string name="msg_please_capture_equipment_photo">Please capture equipment photos</string>
    <string name="msg_please_capture_dock_door_photo">Please capture dock door photos</string>
    <string name="msg_please_capture_seal_photo">Please capture seal photos</string>
    <string name="msg_seal_or_equipment_not_match_do_force_close">Seal or %1$s no match, force close?</string>
    <string name="status_force_close">Force close</string>
    <string name="need_verify">Need verify</string>
    <string name="lp_verified">LP verified</string>
    <string name="title_alert">Alert</string>
    <string name="text_seal_or_container_no_match">Seal/Container/Trailer no match, do you confirm check in?</string>
    <string name="msg_container_no_is_incorrect_please_contact_csr_to_update_receipt">Container No. is incorrect, please contact CSR to update Receipt.</string>
    <string name="title_receive_dock_check_in">Receive Dock Check In</string>
    <string name="label_warehouse_colon">Warehouse </string>
    <string name="hint_please_scan_item_label">Please scan item label</string>
    <string name="hint_please_scan_item_number">Please scan item number</string>
    <string name="label_please_select_item">Please select ITEM</string>
    <string name="title_add_new_item_line">Add new item line?</string>
    <string name="title_is_check">Is check?</string>
    <string name="toast_please_scan_lp">Please select a LP before selecting a location</string>
    <string name="text_bind_lp">Bind LP:</string>
    <string name="toast_please_select_one_item">Please select one item</string>
    <string name="title_packing_task_list">Packing Task List</string>
    <string name="label_item_amount">Item Amount</string>
    <string name="label_unpacked">Unpacked</string>
    <string name="label_packed">Packed</string>
    <string name="label_component_list">Component list </string>
    <string name="label_pack_task_order_no">Order No. </string>
    <string name="label_create_time">Create Time </string>
    <string name="label_earliest_mabd_date">Earliest MABD Date </string>
    <string name="label_mabd_date">MABD Date </string>
    <string name="text_kitting">Kitting</string>
    <string name="text_kitting_progress">Kitting progress</string>
    <string name="text_pick">Pick</string>
    <string name="label_item_detail">Item Detail</string>
    <string name="title_pick_component">Pick Component</string>
    <string name="text_switch_to_select">Switch to select</string>
    <string name="text_random_qc_scan_item">Scan Item</string>
    <string name="text_select_item">Select Item</string>
    <string name="text_picked_qty_mark">Picked Qty :</string>
    <string name="hint_error_dock">Error Dock</string>
    <string name="hint_invalid_lp">Invalid LP</string>
    <string name="hint_pick_qty_more_than_total">Picked qyt more than total qty</string>
    <string name="hint_pick_qty_more_than_require">Picked qyt more than require qty</string>
    <string name="hint_allocated_qty_more_than_require">Allocated qty more than require qty</string>
    <string name="hint_invalid_item">Invalid item</string>
    <string name="hint_pick_qty_mot_match_total">Pick qty is not match require qty</string>
    <string name="hint_is_not_complete">is not complete</string>
    <string name="hint_not_kitting_item">No kitting item</string>
    <string name="text_pack_task_complete">Task complete</string>
    <string name="hint_no_enough_qty_in_lp">No enough qty in lp</string>
    <string name="query_start_task">Start task now?</string>
    <string name="query_start_step">Start step now?</string>
    <string name="action_lp_detail">Lp detail</string>
    <string name="title_lp_detail">LP Detail</string>
    <string name="title_item_detail">Item detail</string>
    <string name="label_qty_uom">Qty/UOM</string>
    <string name="label_receipt_no">Receipt NO.</string>
    <string name="label_lot_no">Lot NO.</string>
    <string name="msg_please_select_server">Please select printer server</string>
    <string name="msg_please_setup_local_printer_server_for_wifi_printer">Please setup local printer server</string>
    <string name="msg_printer_server">Printer server</string>
    <string name="msg_please_contact_system_admin_to_setup_printer_server">Please contact you
        system admin to setup printer server.</string>
    <string name="label_task_type_load">Load</string>
    <string name="label_task_type_receive">Receive</string>
    <string name="label_task_type_pack">Pack</string>
    <string name="label_task_type_pick">Pick</string>
    <string name="label_task_type_put_away">Put away</string>
    <string name="label_task_type_unknown">Unknown</string>
    <string name="label_join">Join</string>
    <string name="title_join_task">Join this task?</string>
    <string name="title_task_pack">Pack task</string>
    <string name="text_pack_to_slp">Pack To SLP</string>
    <string name="msg_pack_success">Pack Success</string>
    <string name="label_task_id">Task ID</string>
    <string name="msg_user_disable_please_contact_manager">Use is disable, please contact to manager</string>
    <string name="msg_account_already_login">This user already login in the other device.
        Do you want to login force in this device?</string>
    <string name="msg_load_success">Load success</string>
    <string name="title_truck_info">Truck Info</string>
    <string name="title_select_container_type">Select Container type</string>
    <string name="label_chassis_no">Chassis No</string>
    <string name="title_container_driver"><![CDATA[Container & Driver]]></string>
    <string name="title_print_and_select_spot">Print And Select Spot</string>
    <string name="label_spot_type">Spot Type</string>
    <string name="label_select_spot">Select Spot :</string>
    <string name="warring">Waring</string>
    <string name="error_truck_in_black_list">Truck in black, please reject it</string>
    <string name="label_size">Size</string>
    <string name="label_color">Color</string>
    <string name="label_condition">Condition</string>
    <string name="label_damage_description">Damage description</string>
    <string name="label_chassis_info">Chassis Info</string>
    <string name="text_add_trailer">Add Trailer</string>
    <string name="text_add_chassis">Add Chassis</string>
    <string name="btn_add_chassis">Add Chassis</string>
    <string name="hint_please_input_carrier_name">Please input carrier name</string>
    <string name="error_please_input_chassis">Please input Chassis No</string>
    <string name="hint_please_add_container">Please add container</string>
    <string name="hint_invalid_container_no">Invalid container No.</string>
    <string name="text_truck_photo_capture">Truck Photo Capture</string>
    <string name="text_chassis_photo_capture">Chassis Photo Capture</string>
    <string name="text_container_photo_capture">Container Photo Capture</string>
    <string name="text_trailer_photo_capture">Trailer Photo Capture</string>
    <string name="text_driver_photo_capture">Driver Photo Capture</string>
    <string name="msg_please_input_itemid">Please input item id</string>
    <string name="msg_item_not_found">Item not found</string>
    <string name="msg_wrong_item">Wrong Item</string>

    <string name="label_ilp">ILP</string>
    <string name="label_clp">CLP</string>
    <string name="label_slp">SLP</string>
    <string name="label_slp_or_tote">SLP/TOTE</string>

    <!-- task assign start-->
    <string name="step_can_not_assign_toast">Step can not be assign</string>
    <string name="task_can_not_assign_toast">Task can not be assign</string>
    <string name="msg_assign_success">Assign success!</string>
    <string name="btn_change_dock">Change dock</string>
    <string name="msg_exception_found_while_connect_the_printer">Exception found while connect the printer</string>
    <string name="msg_no_worker_selected">At least one worker should be selected!</string>
    <!-- task assign end-->

    <!-- push message -->
    <string name="show_update_message">Show update</string>
    <string name="hide_update_message">Hide update message</string>
    <string name="select_to_delete">Select to delete</string>
    <string name="default_icon_txt">Temporarily no new messages!</string>
    <string name="label_new_version">New Version</string>
    <string name="text_delete">"Delete the selected task message?"</string>
    <string name="message_group_topic">Message</string>
    <string name="label_task_message_list">Task message list</string>
    <string name="title_update_message">New Version</string>
    <string name="msg_you_got_a_new_assignment">You got a new assignment</string>
    <string name="text_delete_all_messages">Delete All messages?</string>

    <!-- home fragment -->
    <string name="label_user_name">User name: </string>
    <string name="label_account">Account</string>
    <string name="label_facility">Facility</string>
    <string name="label_company">Company</string>
    <string name="label_role">Role</string>
    <string name="label_task_done_init_amout">...</string>

    <!-- more fragment -->
    <string name="task_over">Task over</string>
    <string name="text_pending">Pending</string>
    <string name="take_over_manage">Take over manage</string>
    <string name="send_message_to_owner">Send message to task owner</string>
    <string name="text_optional">Optional</string>
    <string name="Only_pending_status_can_be_take_over">Only pending status can be take over</string>
    <string name="label_task_over">Take over</string>
    <string name="task_over_cancel">Cancel</string>
    <string name="label_task_over_message">Task over message</string>
    <string name="more_take_over_task_title">Take over</string>
    <string name="more_take_over_task_summary">Take over task</string>
    <string name="title_pending_task_list">Pending task list</string>
    <string name="label_receive">Receive</string>
    <string name="label_load">Load</string>
    <string name="label_pick">Pick</string>
    <string name="label_pack">Pack</string>
    <string name="label_unpack">Unpack</string>
    <string name="label_take_over_manage">Take Over Manage</string>
    <string name="more_take_over_manage_summary">Manage take over apply</string>
    <string name="label_cycle_count">Cycle count</string>
    <string name="label_applicant">Applicant</string>
    <string name="label_task">Task</string>
    <string name="label_parcel_receiving">Parcel Receiving</string>
    <string name="label_parcel_receiving_summary">Create Parcel Receiving Task</string>
    <string name="label_epi_kitting">EPI Kitting</string>
    <string name="label_carton_checker">Carton Checker</string>
    <string name="title_carton_info">Carton Info</string>
    <string name="title_carton_info_pallet">Pallet: %1$s</string>
    <string name="title_carton_info_status">Status: %1$s</string>
    <string name="title_carton_info_customer">Customer: %1$s</string>
    <string name="title_carton_info_destination">Destination: %1$s</string>
    <string name="title_carton_info_dimension">Dimension: %1$s</string>
    <string name="title_carton_info_weight">Weight: %1$s %2$s</string>
    <string name="title_carton_info_item">Item: %1$s</string>
    <string name="title_carton_inbound">Inbound</string>
    <string name="title_carton_inbound_rn_no">RN#: %1$s</string>
    <string name="title_carton_inbound_container">Container#: %1$s</string>
    <string name="title_carton_inbound_po_no">PO#: %1$s</string>
    <string name="title_carton_inbound_received_date">Received Date: %1$s</string>
    <string name="title_carton_outbound">Outbound</string>
    <string name="title_carton_outbound_load">Load#: %1$s</string>
    <string name="title_carton_outbound_dn">DN#: %1$s</string>
    <string name="title_carton_outbound_trailer">Trailer#: %1$s</string>
    <string name="title_carton_outbound_shipped_date">Shipped Date: %1$s</string>
    <string name="label_bluetooth_location">Bluetooth Location</string>

    <!-- more fragment: LP SN collection start -->
    <string name="title_lp_sn_collection">LP SN Collection</string>
    <string name="summary_collect_lp_sn">Collect LP SN/RFID</string>

    <!-- more fragment: LP SN collection end -->

    <!-- more fragment: lp template manage start -->
    <string name="title_lp_template_manage">Lp Template Manage</string>
    <string name="title_lp_template_search">Lp template search</string>
    <string name="title_select_company">Select company</string>
    <string name="text_company">Company</string>
    <string name="msg_create_success">Create success</string>
    <string name="msg_update_success">Update success</string>
    <string name="summary_new_or_edit_lp_template">New or edit lp template</string>
    <string name="title_new_item_lp_template">New item lp template</string>

    <!-- more fragment: lp template manage end -->

    <!-- more fragment: adjustment start -->
    <string name="title_adjustment_add_inventory">Adjustment Add Inventory</string>
    <string name="title_new_inventory">New Inventory</string>
    <string name="title_new_inventory_confirm">New Inventory ?</string>
    <string name="text_location_not_found">Location not found</string>
    <string name="text_approve_now">Approve Now</string>
    <string name="title_lp_adjustment">Lp Adjustment</string>
    <string name="title_adjustment">Adjustment</string>
    <string name="title_adjust_to_item">Adjust To Item</string>
    <string name="title_adjust_to_title">Adjust To Title</string>
    <string name="title_adjust_to_goods_type">Adjust To Goods Type</string>
    <string name="title_adjust_to_diverse">Adjust To Diverse</string>
    <string name="title_adjust_to_qty">Adjust To Qty</string>
    <string name="title_adjust_to_status">Adjust To Status</string>
    <string name="title_adjust_to_location">Adjust To Location:</string>
    <string name="title_adjust_to_unit">Adjust To Unit</string>
    <string name="title_lp_inside">LP inside</string>
    <string name="title_adjustment_type">Adjustment Type</string>
    <string name="msg_please_select_adjustment_type">Please Select Adjustment Type</string>
    <string name="title_select_adjustment_type">Select Adjustment Type</string>
    <string name="msg_input_note_to_submit">Input Note To Submit</string>
    <string name="msg_select_new_location_to_submit">Select New Location To Submit</string>
    <string name="msg_select_new_item_to_submit">Select New Item To Submit</string>
    <string name="msg_select_new_unit_to_submit">Select New Uom To Submit</string>
    <string name="msg_input_qty_to_submit">Input New Qty To Submit</string>
    <string name="msg_select_new_status_to_submit">Input Qty And Select New Status To Submit</string>
    <string name="msg_scan_or_input_new_lot_no_to_submit">Scan Or Input New Lot No To Submit</string>
    <string name="msg_select_new_title_to_submit">Select New Title To Submit</string>
    <string name="msg_select_new_goods_type_to_submit">Select New Goods Type To Submit</string>
    <string name="label_adjust_mark">Adjust:</string>
    <string name="label_to_mark">To:</string>
    <string name="hit_input_or_scan_to_filter">Input or scan to filter</string>
    <string name="label_select_title">Select Title</string>

    <array name="adjustment_type_array">
        <item>@string/adjust_location</item>
        <item>@string/adjust_status</item>
        <item>@string/adjust_qty</item>
        <item>@string/adjust_title</item>
        <item>@string/adjust_goods_type</item>
        <item>@string/adjust_lot_no</item>
        <!--<item>Adjust UOM</item>-->
        <!--<item>Adjust Item</item>-->
    </array>

    <string name="adjust_location">Adjust Location</string>
    <string name="adjust_status">Adjust Status</string>
    <string name="adjust_qty">Adjust QTY</string>
    <string name="adjust_title">Adjust Title</string>
    <string name="adjust_goods_type">Adjust Goods Type</string>
    <string name="adjust_lot_no">Adjust LotNo</string>
    <string name="adjust_uom">Adjust UOM</string>
    <string name="adjust_item">Adjust Item</string>
    <!-- more fragment: adjustment end -->

    <!-- more fragment: task search start -->
    <string name="title_task_search">Task Search</string>
    <string name="please_input_or_select_search_condition_to_search">Please input or select search condition to search task</string>
    <string name="please_input_or_select_other_search_condition_with_type_to_search">please input or select other search condition with Task Type to search task</string>
    <string name="title_barcode_print">Barcode Print</string>
    <string name="title_gis">GIS</string>
    <string name="text_geographic_information_system">Geographic Information System</string>
    <string name="summary_task_search_and_take_over">Search and take over</string>
    <string name="label_step">Steps</string>
    <string name="label_task_type">Task type</string>
    <string name="msg_title_take_over">Take over:</string>
    <string name="msg_step_is_done">Step is done</string>
    <string name="msg_task_is_done">Task is done</string>
    <string name="error_task_not_found">Task not found</string>
    <string name="title_step_detail">Step detail</string>
    <string name="title_step_name">Step name</string>
    <string name="msg_send_take_over_request">Send take over request</string>
    <string name="msg_task_take_over_success">Take over success,you can start this task now</string>
    <string name="msg_step_take_over_success">Take over success,you can start this step now</string>
    <string name="msg_the_application_has_been_submitted">The application has been submitted</string>
    <string name="msg_this_task_is_already_yours">This task is already yours</string>
    <string name="msg_send_take_over_message_request_success">Send take over message request success</string>
    <string name="text_advanced">Advanced</string>
    <string name="label_task_status">Task status</string>
    <string name="text_advanced_search">Advanced search</string>
    <string name="hint_please_input_task_id_or_entry_ticket">Please input task Id or entry ticket</string>
    <string name="title_assignee_in">Assignee in:</string>
    <string name="title_include_step_assignee">Include step assignee:</string>
    <string name="title_task_step_not_owner">"Task/Step(not owner):"</string>
    <string name="title_select_task_type">Select task type</string>
    <string name="title_select_task_status">Select task status</string>
    <string name="title_task_qty">Records: %1$s</string>
    <string name="title_pending_time">Pending time</string>
    <string name="title_order">Order:</string>
    <string name="title_day">Day</string>
    <string name="title_hour">Hours</string>
    <string name="hint_search_by_dock">TASK ID#/DN#/RN#</string>

    <string-array name="task_type_array">
        <item>Cycle Count</item>
        <item>Inventory Recount</item>
        <item>Receive</item>
        <item>Pick</item>
        <item>Stage To Load</item>
        <item>Pack</item>
        <item>Load</item>
        <item>Put Away</item>
        <item>Put Away Auditing</item>
        <item>Put Back</item>
        <item>Configuration Change</item>
        <item>Generic</item>
        <item>Replenishment</item>
        <item>Transload</item>
        <item>Transfer Out</item>
        <item>Transload Receive</item>
        <item>Transload Load</item>
        <item>Inventory Consolidation</item>
        <item>Hospital</item>
        <item>Consolidate To Pallet</item>
    </string-array>
    <string-array name="task_type_enum_array">
        <item>CYCLE_COUNT</item>
        <item>INVENTORY_RECOUNT</item>
        <item>RECEIVE</item>
        <item>PICK</item>
        <item>STAGE_TO_LOAD</item>
        <item>PACK</item>
        <item>LOAD</item>
        <item>PUT_AWAY</item>
        <item>PUT_AWAY_AUDITING</item>
        <item>PUT_BACK</item>
        <item>CONFIGURATION_CHANGE</item>
        <item>GENERIC</item>
        <item>REPLENISHMENT</item>
        <item>TRANSLOAD</item>
        <item>INTERNAL_TRANSFER_OUT</item>
        <!--        <item>TRANSFER_IN</item>-->
        <item>TRANSLOAD_RECEIVING</item>
        <item>TRANSLOAD_LOADING</item>
        <item>INVENTORY_CONSOLIDATION</item>
        <item>HOSPITAL_TASK</item>
        <item>Consolidate_Pallet_TASK</item>
    </string-array>

    <string-array name="task_status_array">
        <item>New</item>
        <item>In Progress</item>
        <item>Closed</item>
        <item>Force Closed</item>
    </string-array>
    <string-array name="task_status_enum_array">
        <item>NEW</item>
        <item>IN_PROGRESS</item>
        <item>CLOSED</item>
        <item>FORCE_CLOSED</item>
    </string-array>

    <!-- more fragment: task search end -->

    <!-- more fragment: quality control start -->
    <string name="text_quality_control">Quality Control</string>
    <string name="text_tracking_no_qc">Tracking No. QC</string>
    <string name="text_carrier_pickup_print">Print Carrier PickUp Label</string>
    <string name="search_quality_control_task">Search Quality Control Task</string>
    <!-- more fragment: quality control end -->

    <!-- tasks fragment -->
    <string name="title_pending_task">Pending task</string>
    <string name="title_task_status_pending">Pending</string>
    <string name="title_take_over">Take over</string>
    <string name="msg_take_over_task">Take over the selected task?</string>
    <string name="assignment_turned_in">Assignment turned in!</string>

    <!-- general task start-->
    <string name="title_general_task_list">General task list</string>
    <string name="label_general_task">General Task</string>
    <string name="label_general_task_create_tip">Select a customer and a job to check in</string>
    <string name="label_general_task_create_new_tip">Or start a new job check in from below</string>
    <string name="label_general_task_choose_customer">Choose a customer</string>
    <string name="label_general_task_choose_customer_tip">Please choose a customer</string>
    <string name="label_general_task_choose_material">Choose materials</string>
    <string name="label_general_task_choose_job">Choose a job code</string>
    <string name="label_general_task_choose_job_tip">Please choose a job code</string>
    <string name="label_general_task_work_name">Name(%1$s)</string>
    <string name="label_general_task_work_customer">Customer:</string>
    <string name="label_general_task_work_job">Job Code:</string>
    <string name="label_general_task_work_duration">Job Duration:</string>
    <string name="btn_end_job">End Job</string>
    <string name="alert_general_task_checkout">Confirm to check out?</string>
    <string name="alert_general_task_quit">Confirm to quit?</string>
    <string name="btn_pause">Pause</string>
    <string name="btn_resume">Resume</string>
    <string name="label_view_all"><u>View All</u></string>
    <string name="btn_start_work">Start Work</string>
    <string name="btn_start_new_job">Start New Job</string>
    <string name="btn_unknow_qty">I do not have an exact number</string>
    <string name="label_material_list">Add the materials you take</string>
    <string name="label_set_material_qty_title">How many %s do you currently use?</string>
    <string name="label_temp_user_manage">Temp User Manage</string>
    <string name="title_general_task_user_manager">Temp User Manager</string>
    <string name="btn_batch_commit">BATCH COMMIT</string>
    <string name="customer_not_found">Customer not found</string>
    <string name="jobcode_not_found">Job Code not found</string>
    <string name="general_task_estimated_time">Job Code Estimated Time:</string>
    <string name="estimated_time">Estimated Time:</string>
    <string name="general_task_remain_time">Job Code Remain Time:</string>
    <string name="error_input_task_estimated_time">Please Input Job Code Estimated Time</string>
    <string name="minutes">mins</string>
    <string name="create">create</string>
    <string name="start_another_general_step">This task has another unfinished step, start it now?</string>
    <string name="create_another_general_task">Create another task now?</string>
    <string name="start_another_general_task">You have another unfinished task, start it now?</string>
    <string name="label_cost_title">Cost:</string>
    <string name="error_msg_input_cost">Please input Cost</string>
    <string name="label_general_assembly_pick_task">Assembly Pick Task</string>
    <string name="label_general_assembly_put_away_task">Assembly Put Away Task</string>
    <string name="text_component_list_title">Component List</string>
    <string name="label_component_itemspec_name">Component Name</string>
    <string name="label_pick_qty_total_qty">PICK QTY/Total QTY</string>
    <string name="error_msg_component_empty">NO Components </string>
    <string name="error_msg_pick_qty_more_than_total_qty">Pick qty more than total qty</string>
    <string name="error_msg_assembly_qty">There are no assembles item</string>
    <string name="error_msg_putaway_more_than_assembly_qty">Putaway Qty more than assembly qty</string>
    <string name="text_pick_component">Pick component</string>
    <string name="btn_putaway">Putaway</string>
    <string name="btn_putaway_all">Putaway All</string>
    <string name="error_msg_start_work">Please Start Work </string>
    <string name="error_msg_scan_component_item_error">component item is error! </string>
    <string name="msg_please_select_job_code">Please select job code</string>
    <!-- general task end-->

    <!--tasks fragment: CCTask-->
    <string name="label_packaging_item">Packaging item</string>
    <string name="label_packaging_diverse">Packaging diverse</string>
    <string name="label_package_amount">Package amount:</string>
    <string name="text_put_into_pool">Put into pool</string>
    <string name="label_sn_item">SN item</string>
    <string name="label_no_sn">No SN</string>
    <string name="msg_please_scan_or_input_lp">Please scan or input LP</string>
    <string name="error_input_item_qty">Input item qty</string>
    <string name="title_packing_target_lp">Packing target lp</string>
    <string name="btn_text_to_view">To view</string>
    <string name="text_scan_resource_lp">Scan resource lp</string>
    <string name="text_packing_target_lp">Packing target lp</string>
    <string name="label_packing_item">Packing item</string>
    <string name="label_packing_start">Packing start</string>
    <string name="label_packing_product">Packing product</string>
    <string name="error_qty_not_match">Qty not match</string>
    <string name="btn_next">Next</string>
    <string name="label_item_and_qty">Item/Qty</string>
    <string name="text_pick_item">Pick Item</string>
    <string name="text_pick_lp">Pick Lp</string>
    <string name="label_item_count">Item count</string>
    <string name="text_batch_cc">Batch CC</string>
    <string name="text_traditional">Traditional</string>
    <string name="label_lp_total_qty">Total qty:</string>
    <string name="label_configuration_change">Configuration Change</string>


    <!--tasks fragment: CCTask-->

    <!-- messages fragment -->
    <string name="title_message_group">Message group</string>
    <string name="title_take_over_message">Take over message</string>
    <string name="title_take_assign_message">Task assign message</string>
    <string name="text_accept_the_take_over">Accept the take over</string>

    <!-- task step menu -->
    <string name="msg_nothing_to_do_for_cancelled_task">Nothing to do for cancelled task!</string>
    <string name="step_status_new">New</string>
    <string name="step_status_not_start">Not start</string>
    <string name="step_status_in_progress">In progress</string>
    <string name="step_status_skipped">Skipped</string>
    <string name="step_status_exception">Exception</string>
    <string name="step_status_done">Done</string>
    <string name="step_status_force_closed">Force closed</string>
    <string name="step_status_cancelled">Cancelled</string>
    <string name="dialog_title_generic_step">Finish</string>
    <string name="dialog_message_generic_step">Do you confirm done this step?</string>
    <string name="generic_step_done_toast">Done this step</string>
    <string name="label_status">Status</string>
    <string name="text_cycle_count">Cycle Count</string>
    <string name="text_inventory_recount">Inventory Recount</string>
    <string name="text_receive">Receive</string>
    <string name="text_pack">Pack</string>
    <string name="text_cc">CC</string>
    <string name="text_lp_scan">LP scan</string>
    <string name="text_lp_scanned">LP Scanned</string>
    <string name="item_not_found_by_lp">Lp/Order not found </string>
    <string name="text_scan_res_lps">Scan resource lps</string>
    <string name="efficiency_description">Collect item from all LP for packaging</string>
    <string name="traditional_description">Packing item by single lp</string>
    <string name="label_scan_done">Scan done</string>
    <string name="label_scan">Scan:</string>
    <string name="please_scan_or_input_resource_lp">Please scan or input resource LP</string>
    <string name="label_target_lp">Target LP</string>
    <string name="label_resource_lps">Resource LPS: </string>
    <string name="please_scan_or_input_target_lp">Please scan or input target LP</string>
    <string name="btn_text_add">Add</string>
    <string name="scan_or_input_new_resource_lp">Please scan or input new resource LP</string>
    <string name="move_lp_to_packing_area">Move lp to packing area.</string>
    <string name="label_lp_suggestion">Lp suggestion:</string>
    <string name="label_in_pool">In pool:</string>
    <string name="label_pool_qty_total_qty">Pool Qty/Total Qty: </string>
    <string name="label_traditional_start">Start traditional</string>
    <string name="btn_text_put_into_pool">Put Into Pool</string>
    <string name="label_step_progress">Step progress:</string>
    <string name="label_collected_demand">Collected/Demand:</string>
    <string name="label_number_of_assignees">Number Of Assignees</string>
    <string name="label_turn_on_off_direct_load">Turn on/off Direct Load</string>
    <string name="title_input_assignee_count">Input Assignee Count</string>

    <!-- material line manage start -->
    <string name="title_add_material">Add material</string>
    <string name="btn_add_material">Add material</string>
    <string name="btn_reset">Reset</string>
    <string name="title_material_item_line">Material item line</string>
    <string name="label_tags">Tags</string>
    <string name="title_select_diverse">Select diverse</string>
    <string name="error_please_select_diverse">Please select diverse</string>
    <string name="text_no_diverse">No diverse</string>
    <string name="summary_add_material">Add material</string>
    <string name="title_material_line">Material line</string>
    <string name="title_edit">Edit</string>
    <string name="msg_material_line_on_found_please_contact_administrator">Material line no found, please contact administrator</string>
    <!-- material line manage end -->

    <!-- lp print start -->
    <string name="error_bt_device_not_support">Device not support bluetooth</string>
    <string name="error_bt_device_not_found">Device not found</string>
    <string name="error_bt_device_offline">Device offline</string>
    <string name="error_bt_device_unknown">Unknown error</string>
    <string name="print_success">Print success</string>
    <string name="error_no_route_to_host">No route to host (Host unreachable)</string>
    <string name="error_bt_disable">Bluetooth is disable</string>
    <string name="text_in_printing">In printing...</string>
    <!-- lp print end -->

    <string name="goods_type">Goods type</string>
    <string name="title_dock_selector">Dock selector</string>

    <!-- pick task split/merge start -->
    <string name="label_take_split_merge">Task Split/Merge</string>
    <string name="more_task_split_merge_summary">Split or merge task</string>
    <string name="text_split">Split</string>
    <string name="text_merge">Merge</string>
    <string name="title_task_split">Task Split</string>
    <string name="text_create_task">Create Task</string>
    <string name="hint_create_task_first">Please create new task first</string>
    <string name="text_cancel_merge">Cancel Merge</string>
    <string name="hint_please_select_merge_task">Please select merge task</string>
    <string name="hint_please_select_merge_to_task">Please select merge to task</string>
    <string name="text_please_select_a_task_merge_to">Please select a merge to task for those tasks you select before</string>
    <string name="text_task">Task</string>
    <string name="hint_please_select_task">Please select a task</string>
    <string name="hint_please_input_qty">Please input qty</string>
    <string name="error_qty_overflow">Invalid qty, qty overflow</string>
    <string name="label_item_amount_total_qty">Item Amount/Total Qty</string>
    <string name="hint_different_pick_type_pick_way_can_not_merge">Different pick type/pick way can not merge</string>
    <string name="hint_task_can_not_split">Task can\'t split, because pick way is wave pick by order and order amount is 1</string>
    <!-- pick task split/merge end -->

    <!-- dock check out start -->
    <string name="scan_or_input_entry_id">Scan/input entry ID</string>
    <string name="scan_or_input_dock_name">Scan/input dock name</string>
    <string name="scan_or_input_equipment_no">Scan/input equipment no</string>
    <string name="text_door_checkin_scan_entry">Scan or Input Entry Id</string>
    <string name="text_door_checkin_scan_dock">Scan or Input Dock</string>
    <string name="collect_load_material">Collect Load Material</string>
    <string name="text_slp_view">SLP View</string>
    <string name="text_content">Content</string>
    <!-- dock check out end -->

    <!-- material center start -->
    <string name="label_bol_no">Bol NO.</string>
    <string name="label_seal_no">Seal NO.</string>
    <string name="label_po_no">Po NO.</string>
    <string name="label_select_receipt">Receipt select</string>
    <string name="error_msg_select_receipt">Please select receipt</string>
    <string name="text_complete_load">Complete Load</string>
    <string name="label_ship_to">Ship To</string>
    <string name="label_slp_amount">SLP Amount:</string>
    <!-- material center end -->

    <!-- task merge/split start -->
    <string name="msg_nothing_to_do_for_this_task">Nothing to do for this task</string>
    <string name="title_task_marge_split">Task Merge/Split</string>
    <string name="text_task_started">Task Started</string>
    <string name="close_step">Close Step</string>
    <string name="text_step_started">Step Started</string>
    <string name="text_take_over_step">Take Over Step</string>
    <string name="text_step_closed">Step Closed</string>
    <string name="hint_do_you_want_to_start">do you want to start</string>
    <string name="hint_scan_or_input_to_load_lp">Scan or input to load</string>
    <string name="hint_scan_or_input_to_unload_lp">Scan or input to unload</string>
    <!-- task merge/split end -->

    <!-- new replenish start -->
    <string name="text_replenishment_collect_task">Replenishment Collect Task</string>
    <string name="text_replenishment_drop">Replenishment Drop</string>
    <string name="title_replenish">Replenish</string>
    <string name="text_start_step">Start Step</string>
    <string name="text_batch_step">Start Batch Movement Step</string>
    <string name="text_collected">Collected</string>
    <string name="text_scan_ilp">SCAN ILP</string>
    <string name="text_scan_or_input_to_start">SCAN ITEM NAME/UPC TO START</string>
    <string name="text_scan_or_input_to_next">SCAN ITEM NAME/UPC TO NEXT</string>
    <string name="text_scan_location_to_start">SCAN LOCATION TO START TASK</string>
    <string name="text_available_inventory_at_this_location">Available inventory at this location:</string>
    <string name="text_replenish_drop_location_description">Collect Completed.\nPlease leave collected pallet\nat above suggested location.</string>
    <string name="text_scan_lp_or_location_to_collect_description">Task Started.\nPlease scan LP or location to collect</string>
    <string name="text_scan_or_input_drop_location">Scan Or Input Drop Location</string>
    <string name="text_scan_or_enter_zone_code">Scan Or Enter Code</string>
    <string name="text_location_confirmed_please_make_of_the_following_selection">Location confirmed.\nPlease make of the following selection.</string>
    <string name="text_start_replenish">Start Replenish</string>
    <string name="text_continue_collect">Continue Collect</string>
    <string name="text_leave_pallet">Leave Pallet</string>
    <string name="text_replenish_to_lp">Replenish To LP :</string>
    <string name="text_print_lp">Print LP</string>
    <string name="text_scan_for_next">Scan For Next Item</string>
    <string name="text_finish_collect">Finish Collect</string>
    <string name="msg_location_is_empty">Location is empty</string>
    <string name="text_scan_or_enter_lp">Scan Or Enter LP</string>
    <string name="text_scan_or_enter_location">Scan Or Enter Location</string>
    <string name="title_sn_list_mark">SN List:</string>
    <string name="text_inventory_not_found">Inventory not found</string>
    <string name="text_inventory_location_not_found">Inventory location not found</string>
    <string name="msg_invalid_qty">Invalid Qty!</string>
    <string name="msg_sn_does_not_match_qty">Sn Does Not Match Qty!</string>
    <string name="query_force_close_step">Force close step now?</string>
    <string name="msg_this_item_has_not_been_collected">This item has not been collected!</string>
    <string name="msg_this_item_has_been_replenished">This item has been replenished already!</string>
    <string name="text_replenish_as">Replenish As:</string>
    <string name="msg_pick_location_no_need_print_lp">Pick Location no need print LP</string>
    <string name="msg_collect_qty_exceeds_inventory_qty">Collect QTY exceeds current Location/LP QTY</string>
    <string name="msg_replenish_to_location_is_empty">Replenish to location can not be empty</string>
    <string name="title_suggestion_for_collect">Suggestion for Collect</string>
    <string name="title_suggestion_for_replenish">Suggestion for Replenish</string>
    <string name="msg_replenish_to_lp_is_empty">Location %1$s is not a pick location,Please print LP</string>

    <!-- new replenish end-->

    <!-- replenishment start -->
    <string name="replenishment_create">Replenishment task create</string>
    <string name="process_history">Process history</string>
    <string name="to_location">To location</string>
    <string name="from_location">From location</string>
    <string name="to_lp">To LP</string>
    <string name="title_replenishment_process">Replenishment process</string>
    <string name="need_setup">Need to setup</string>
    <string name="pick_uom">Pick UOM</string>
    <string name="msg_to_uom">Please select to UOM</string>
    <string name="scan_lp_for_replenishment">Scan lp label for replenishment</string>
    <string name="scan_lp_of_location_for_replenishment">Scan lp label or location for replenishment</string>
    <string name="item_on_lp">Item on LP</string>
    <string name="item_on_order">LP on Order</string>
    <string name="btn_replenish">Replenish</string>
    <string name="btn_replenish_step_search">Replenish step search</string>
    <string name="txt_scan_location">SCAN LOCATION</string>
    <string name="txt_key_in_location">KEY IN</string>
    <string name="msg_please_input_location">Please input location</string>
    <!-- replenishment end -->

    <!--  put back task start -->
    <string name="title_put_back_task_list">Put Back Task List</string>
    <string name="title_put_back_task">Put Back Task</string>
    <string name="label_reference">Reference</string>
    <string name="label_filter_item">Filter Item</string>
    <string name="msg_item_not_found_by_upc">Item not found by UPC: %1$s</string>
    <string name="label_put_back_history">Put Back History:</string>
    <string name="label_inventory_status">Inventory status</string>
    <string name="label_inventory_status_damage">Damage</string>
    <string name="label_inventory_status_available">Available</string>
    <string name="label_inventory_status_on_hold">OnHold</string>
    <string name="label_inventory_status_picked">Picked</string>
    <string name="label_inventory_status_packed">Packed</string>
    <string name="label_inventory_status_loaded">Loaded</string>
    <string name="label_inventory_status_occupied">Occupied</string>
    <string name="label_inventory_status_shipped">Shipped</string>
    <string name="label_inventory_status_configuring">Configuring</string>
    <string name="msg_item_switch_is_off_confirm_whether_the_item_has_sn">The item switch(Has Serial Number) is off,Confirm whether the item has SN</string>
    <string name="label_so_id_detail">SOID Detail</string>
    <string name="label_so_id_duplicate">SOID Duplicate</string>
    <string name="hint_please_scan_or_input_so_id">Please scan or input SOID</string>
    <string name="text_so_id_quantity_exceeded">SOID quantity exceeded</string>
    <string name="text_number_of_so_id_scanned_shortage">Number of SOID scanned Shortage</string>
    <string name="msg_so_id_not_scan">please scan so ids first</string>
    <string name="msg_scan_so_id_error">so id can not be null or repeat</string>
    <string name="msg_lp_selected_does_not_associate_with_any_dn">"LP selected doesn't associate with any DN"</string>
    <string name="text_tracking_scanned_shortage">Not enough tracking number has been scanned</string>
    <string name="msg_tracking_qty_not_match">Tracking qty not match</string>
    <!-- put back task end -->

    <!-- pack task start -->
    <string name="title_pack_task">Pack Task</string>
    <string name="title_pack_task_list">Pack Task List</string>
    <string name="title_assign_slp">Assign SLP</string>
    <string name="title_collect_packing_material">Collect Pack Material</string>
    <string name="text_bind_item_lp">Bind Item LP</string>
    <string name="text_scan">Scan</string>
    <string name="label_pack_from_lp">From LP</string>
    <string name="text_slp_assign_location">Assign SLP Location</string>
    <string name="text_pack_msg">Pack Msg</string>
    <string name="text_packed_colon">Packed :</string>
    <string name="label_need_pack">Need Pack :</string>
    <string name="btn_add_picked_lp">Add Picked LP</string>
    <string name="hint_scan_or_input_lp">Scan or input LP</string>
    <string name="hint_please_scan_or_input_slp">Please print or input slp</string>
    <string name="text_return_to_picked_lp">Return To Picked LP</string>
    <string name="text_return_to_lp">Return To LP</string>
    <string name="text_return_item">Return Item</string>
    <string name="text_return_qyt">Return Qty</string>
    <string name="text_return">Return</string>
    <string name="text_return_all">Return All</string>
    <string name="query_close_task">Close task now?</string>
    <string name="query_close_step">Close step now?</string>
    <string name="text_print_slp">Print SLP</string>
    <string name="label_add_item_or_lp">Add Item Or LP</string>
    <string name="btn_add_whole_lp">Add Whole LP</string>
    <string name="label_start_time">Start Time</string>
    <string name="hint_select_template_type">Please Select Template Type</string>
    <string name="text_single_item_template">Single Item Template</string>
    <string name="text_multiple_template">Multiple Template</string>
    <string name="text_reopen_task">Reopen Task</string>
    <string name="test_suggest_lp">Suggest LP</string>
    <string name="label_uom_map_qty">UOM/QTY</string>
    <string name="text_reopen_success">Reopen Success</string>
    <string name="label_candidate_lp">Candidate LP</string>
    <string name="label_select_item_colon">Select Item:</string>
    <string name="text_take_over">Take Over</string>
    <string name="text_close_step">Close Step</string>
    <string name="text_take_counting_sheet_photo">Take Counting Sheet Photo</string>
    <string name="text_dock_released">Dock released</string>
    <string name="msg_auto_cc_success">Auto CC success</string>
    <string name="msg_lp_configuration_not_match">LP Configuration not match with order itemline, CC required.</string>
    <string name="print_consol_label">Print Consol Label</string>
    <!-- pack task end -->

    <!-- put away start -->
    <string name="put_away_success">Put away success</string>
    <string name="text_please_take_over_step">Please take over step</string>
    <string name="btn_add_all_item">Add All Item</string>
    <string name="hint_scan_lp_rn_dn_pick_ticket">Scan LP/RN/DN/Pick Ticket</string>
    <string name="hint_scan_lp_rn_carton_id">Scan LP/RN/CartonId</string>
    <string name="hint_scan_lp_dn_pick_ticket_load">by LP/DN/PICK TICKET/LOAD</string>
    <string name="label_select_all">Select All</string>
    <string name="label_lp_to_putaway">LP to Putaway</string>
    <string name="msg_xx_put_away_success">%1$s Putaway Success</string>
    <string name="msg_please_scan_or_new_lp">Please scan or new LP</string>
    <string name="msg_please_split_item_to_other_lp">Please split item to other LP</string>
    <string name="hint_scan_lp_tote_id">Scan LP/Tote ID</string>
    <string name="msg_put_away_to_location_xx">Putaway to Location:%1$s?</string>
    <string name="txt_put_away_later">Putaway Later</string>
    <string name="txt_item_does_not_fix">Item does not fit</string>
    <string name="hint_enter_qty">Enter QTY</string>
    <string name="msg_xx_do_you_want_to_continue_this_item_xx">%1$s\nDo you want to continue this Item:%2$s?</string>
    <string name="msg_location_not_match">Location:%1$s not match previous scanned location:%2$s, please check.</string>
    <string name="msg_confirm_create_lp_xx_put_away_task">Confirm create LP:%1$s put away task?</string>
    <string name="msg_partial_pallets_not_allowed">Pallet %1$s is partial. Suggest different locations for full and partial pallets.</string>
    <!-- put away end -->

    <!-- parcel load start -->
    <string name="parcel_load">Parcel Load</string>
    <string name="load_parcel_to_carriers">Load Parcel to Carriers</string>
    <string name="text_scan_entry_ticket">Scan Entry Ticket</string>
    <string name="text_input_container_no">Input Container Number</string>
    <string name="text_input_location">Input Location</string>
    <string name="text_select_customer">Select Customer</string>
    <string name="text_select_location">Select Location</string>
    <string name="text_scan_1_tracking_no">Scan 1 Tracking#</string>
    <string name="btn_start_loading">Start Loading</string>
    <string name="msg_input_customer">Input customer to search</string>
    <string name="msg_please_select_one_customer">Please select one customer</string>
    <string name="msg_please_select_input_customer">Please select input customer</string>
    <string name="msg_please_select_input_title">Please select input title</string>
    <string name="msg_invalid_entry_ticket">Invalid Entry Ticket</string>
    <string name="msg_invalid_container_no">Invalid Container Number</string>
    <string name="msg_invalid_location">Invalid Location</string>
    <string name="msg_invalid_customer">Invalid Customer</string>
    <string name="msg_invalid_carrier">Invalid Carrier</string>
    <string name="text_tracking_no">Tracking Number</string>
    <string name="msg_warning">WARNING:</string>
    <string name="msg_unload_success">Unload Success</string>
    <string name="msg_load_success_next">Load Success, Next</string>
    <string name="title_slp_count_mark">SLP Count:</string>
    <string name="title_tracking_no_count_mark">Tracking NO Count:</string>
    <string name="title_tracking_no">Tracking NO</string>
    <string name="msg_force_close_parcel_load">Fail to close properly, force close?</string>
    <!-- parcel load end -->

    <!-- label print start -->
    <string name="hint_please_input_print_qty">Please input print Qty</string>
    <string name="title_label_printing">Label printing</string>
    <string name="label_printer">Printer</string>
    <string name="title_label_type">Label type</string>
    <string name="title_print_level">Print level</string>
    <string name="title_label_amount">Label Amount</string>
    <string name="title_select_label_type">Select label type.</string>
    <string-array name="label_type_array">
        <item>Shipping Label</item>
        <item>Carton Label</item>
        <item>Packing List</item>
        <item>UCC For AAFE</item>
        <item>UCC For Amazon</item>
        <item>UCC For Target</item>
        <item>UCC For BestBuy</item>
        <item>Entry Label</item>
        <item>Item Spec Label</item>
        <item>LP Label</item>
        <item>Equipment Label</item>
    </string-array>
    <string-array name="label_type_enum_array">
        <item>SHIPPING_LABEL</item>
        <item>CARTON_LABEL</item>
        <item>PACKING_LIST</item>
        <item>UCC_AAFE</item>
        <item>UCC_AMAZON</item>
        <item>UCC_TARGET</item>
        <item>UCC_BESTBUY</item>
        <item>ENTRY_LABEL</item>
        <item>ITEM_SPEC_LABEL</item>
        <item>LP_LABEL</item>
        <item>EQUIPMENT_LABEL</item>
    </string-array>
    <string name="title_select_print_level">Select print level.</string>
    <string-array name="print_level_array">
        <item>Order</item>
        <item>LP</item>
        <item>Receipt</item>
        <item>Load</item>
        <item>Entry</item>
        <item>Item</item>
    </string-array>
    <string-array name="print_level_enum_array">
        <item>ORDER</item>
        <item>LP</item>
        <item>RECEIPT</item>
        <item>LOAD</item>
        <item>ENTRY</item>
        <item>ITEM</item>
    </string-array>
    <string name="text_print_detail">Print detail</string>
    <string name="label_order_id">Order Id:</string>
    <string name="label_customer_name">Customer:</string>
    <string name="label_title_name">Title:</string>
    <string name="label_retailer_name">Retailer:</string>
    <string name="label_carton_no">Carton NO:</string>
    <string name="msg_can_not_find_order_with_this_lp">"Can't find order with this LP"</string>
    <string name="msg_invalid_lp">Invalid LP</string>

    <!-- label print end -->

    <string name="msg_do_your_want_to_release_current_dock">Do you want to release current dock?</string>
    <string name="label_packing_type">Packing Type</string>

    <!-- put back start -->
    <string name="text_put_back_view">Put Back View</string>
    <string name="text_put_back_item">Put Back Item</string>
    <string name="title_put_back">Put Back</string>
    <string name="item_progress">Item Progress</string>
    <string name="label_put_back_from_lp">Put Back From LP</string>
    <string name="text_required_qty_colon">"Required Qty:"</string>
    <string name="please_input_return_to_lp">Please input return to lp</string>
    <string name="text_put_back">Put Back</string>
    <string name="text_put_backed">Put Backed:</string>
    <string name="text_need_put_back">need put back:</string>
    <string name="text_put_back_lp_to_location">Put Back LP To Location</string>
    <string name="text_put_back_item_to_lp">Put Back Item To LP</string>
    <string name="text_please_start_step">Please start step</string>
    <string name="label_put_back_to">Put Back To</string>
    <string name="text_process">Process %1$s:</string>
    <string name="msg_not_allow_put_back_to_slp">Not allow put back to SLP: %1$s</string>
    <string name="msg_invalid_sn_order_not_match">Invalid SN, Order not match: %1$s</string>
    <string name="msg_invalid_sn_item_not_match">Invalid SN, Item not match: %1$s</string>
    <string name="msg_confirm_put_back_inventory">Select inventory firstly</string>
    <string name="msg_put_back_remaining_inventory">Item Remaining Qty:%s, Please scan or input upc code to continue</string>
    <!-- put back end -->

    <!-- transload start -->
    <string name="title_transload">Transload</string>
    <string name="title_shipping">Shipping</string>
    <string name="title_shipping_mark">Shipping:</string>
    <string name="title_lpn_setup">LPN Setup</string>
    <string name="title_transload_task">Transload Task</string>
    <string name="title_transload_task_list">Transload task list</string>
    <string name="title_dock_num">"Dock#:"</string>
    <string name="text_nobody">Nobody</string>
    <string name="text_pause">Pause</string>
    <string name="text_receiving_scan">Receiving Scan</string>
    <string name="text_resume">Resume</string>
    <string name="text_complete_receiving_step">Complete Receiving Step</string>
    <string name="text_complete_shipping_step">Complete Shipping Step</string>
    <string name="text_unload">Unload</string>
    <string name="text_unload_all">Unload All</string>
    <string name="text_unload_all_success">Unload All Success</string>
    <string name="text_shipping_scan">Shipping Scan</string>
    <string name="text_current_dock">Current Dock#</string>
    <string name="text_received">Received</string>
    <string name="text_loaded">Loaded</string>
    <string name="text_grand_total">Grand Total</string>
    <string name="text_destination_dock">Destination Dock#</string>
    <string name="label_expected_item_qty_mark">Expected Item Qty:</string>
    <string name="label_received_item_qty_mark">Received Item Qty:</string>
    <string name="label_complete_lpn_setup">Complete LPN Setup</string>
    <string name="msg_the_received_qty_exceeds_expected_qty_are_you_sure_to_continue">The received Qty exceeds expected Qty, are you sure to continue ?</string>
    <string name="label_qty_remaining">QTY Remaining</string>
    <string name="label_qty_per_lpn">QTY per LPN</string>
    <string name="hint_search_existing_trailer_here">Search existing trailer# here</string>
    <string name="msg_please_select_entry">Please select entry</string>
    <string name="msg_please_select_assignee">Please select assignee</string>
    <string name="msg_please_select_dock">Please select dock</string>
    <string name="msg_new_shipping_step_ont_found">New shipping step not found</string>

    <string name="text_item_qty_mark">Item Qty</string>
    <string name="btn_put_away_all_mark">Put Away All</string>
    <string name="msg_pallet_no_require">Pallet NO. require!</string>

    <string name="text_unreceiving">Unreceiving</string>
    <string name="text_unreceiving_all">Unreceive All</string>
    <string name="text_unreceiving_all_success">Unreceive All Success</string>
    <string name="text_unreceiving_success">Unreceiving Success</string>
    <string name="text_transload_receiving_note">NOTE: Transload is not fully supported on App yet,please follow the instruction below to work with Desktop application.</string>
    <string name="label_rn_no">RN#:</string>
    <string name="label_load_no">Load#:</string>
    <string name="title_trailer_no">Trailer#:</string>
    <string name="title_dock_name">Dock Name</string>
    <string name="title_equipment_info">Equipment Info</string>
    <string name="title_equipment_type">Equipment Type</string>
    <string name="label_tractor_no">Tractor#:</string>
    <string name="label_mc_dot_mark">Mc/DOT:</string>
    <string name="label_driver_mark">Driver:</string>
    <string name="label_carrier_mark">Carrier:</string>
    <string name="text_lot_no">Lot No</string>
    <string name="text_lot_no_colon">Lot No. :</string>
    <string name="label_available_lot_no_mark">Available Lot No.</string>
    <string name="label_required_lot_no_mark">Required Lot No.</string>
    <string name="label_required_lot_no">Required Lot No.</string>
    <string name="text_pallet_no">Pallet No.</string>
    <string name="text_customer_pallet_no">Customer Pallet No.</string>
    <string name="label_inbound_entry_id">Inbound Entry ID:</string>
    <string name="label_dock_no">Inbound Dock#:</string>
    <string name="out_bound_dock">Outbound Dock#:</string>
    <string name="out_bound_entry_id">Outbound Entry ID:</string>
    <string name="label_lpn_qty">LPN Qty:</string>
    <string name="label_item_qty_mark">Item Qty:</string>
    <string name="text_lpn">LPN</string>
    <string name="dn_no">DN#:</string>
    <string name="title_instruction">Instruction:</string>
    <string name="text_logon_wise_web_application">1.Logon WISE Web application</string>
    <string name="title_search_task_on_transload_task_list_page_by_rn">2.Search task on transload task list page by RN</string>
    <string name="title_open_task">3.Open task</string>
    <string name="title_open_task_to_start">3.Open task</string>
    <string name="title_open_receiving_step">4.Open Receiving Step</string>
    <string name="title_once_loading_is_finished_complete_the_step">4.Once Loading is finished,complete the step</string>
    <string name="msg_cartons_are_not_fully_processed_in">Cartons are not fully processed in this step,are you sure you will force to complete the task</string>
    <string name="label_loaded_carton">Loaded Carton:</string>
    <string name="label_received_carton">Received Carton:</string>
    <string name="hint_scan_or_input_carton_to_load">Scan or input carton to load</string>
    <string name="hint_scan_or_input_carton_to_receive">Scan or input carton to receive</string>
    <string name="hint_scan_or_input_to_unload">Scan or input carton to unload</string>
    <string name="hint_scan_or_input_to_unreceiving">Scan or input to unreceiving</string>
    <string name="hint_scan_or_input_lpn_to_put_away">Scan or Input Lpn to Start</string>
    <string name="title_task_transload">Transload Task</string>
    <string name="title_alert_release_dock">Step complete, release dock ?</string>
    <string name="step_status_pause">Pause</string>
    <string name="msg_do_you_want_to_close_this_step">All done, do you want to close this step?</string>
    <string name="msg_whether_to_lazy_close_the_step">Whether to lazy close the step ?</string>
    <string name="msg_whether_to_close_the_task">Whether to close the task ?</string>
    <string name="text_receive_all">Receive All</string>
    <string name="text_ship_all">Ship All</string>
    <string name="text_print_tally_sheet">Print Tally Sheet</string>
    <string name="text_tally_sheet_preview">Tally Sheet Preview</string>
    <string name="msg_are_you_sure_you_want_to_receive_all_cartons_without_scanning">Are you sure you want to receive all cartons without scanning ?</string>
    <string name="msg_are_you_sure_you_want_to_unreceive_all_cartons">Are you sure you want to unreceive all cartons ?</string>
    <string name="msg_are_you_sure_you_want_to_load_all_cartons_without_scanning">Are you sure you want to load all cartons without scanning ?</string>
    <string name="msg_are_you_sure_you_want_to_unload_all_cartons">Are you sure you want to unload all cartons ?</string>
    <string name="text_print_label">Print Label</string>
    <string name="text_label_preview">Print Preview</string>
    <string name="text_lazy_close">Lazy Close</string>
    <string name="title_lazy_close_new_load">Lazy Close/New Load</string>
    <string name="status_done_lazy_close">Done (Lazy Close)</string>
    <string name="text_yes_new_load">Yes, New Load</string>
    <string name="text_select_item_to_setup_lpn">Select item to setup LPN</string>
    <string name="text_container_no_mark">Container #:</string>
    <string name="msg_receive_success">Receive success</string>
    <string name="receive_carton_to_suggest_dock">[ R ] %1$s >> %2$s</string>
    <string name="text_unknown_dock">Unknown Dock</string>
    <string name="unreceive_carton">[ Unreceive ] %1$s </string>
    <string name="receiving_scan_error">[ Error ]</string>
    <string name="transload_offload_step">Transload - Offload</string>
    <string name="transload_photo_step">Transload - Photo</string>
    <string name="transload_lpn_setup_step">Transload - LPN Setup</string>
    <string name="transload_receiving_step">Transload - Receiving</string>
    <string name="transload_shipping_step">Transload - Shipping</string>
    <string name="text_transload_shipping_scan">Transload - Shipping Scan</string>
    <string name="text_transload_receiving_scan">Transload - Receiving Scan</string>
    <string name="text_transload_qty_mark">Transload Qty:</string>
    <string name="text_transload_create_lpn">Transload - Create LPN</string>
    <string name="text_transload_delete_lpn">Transload - Delete LPN</string>
    <string name="text_transload_trailer_check_in">Transload - Trailer Checkin</string>
    <string name="text_transload_select_entry">Transload - Select Entry</string>
    <string name="btn_select_entry">Select Entry</string>
    <string name="text_transload_new_entry">Transload - New Entry</string>
    <string name="text_transload_put_away">Transload - Put Away</string>
    <string name="text_scan_lpn_to_delete">Scan LPN to delete</string>
    <string name="btn_delete_lpn">Delete LPN</string>
    <string name="btn_create_and_print_lpn">Create And Print LPN</string>
    <string name="msg_lpn_is_deleted">LPN %1$s is deleted!</string>
    <string name="text_item_number">Item Number</string>
    <string name="text_label_qty">Label Qty</string>
    <string name="msg_lpn_is_empty">LPN is empty!</string>
    <string name="msg_error_lpn">Error LPN: %1$s</string>
    <string name="msg_please_input_item_qty">Please input item Qty</string>
    <string name="title_create_new_load">Create New Load</string>
    <string name="msg_order_not_fulfill_create_new_load">Order is not fully loaded by current truck, need a new load?</string>
    <string name="yes_create">YES, CREATE</string>
    <string name="no">NO</string>
    <string name="msg_print_pallet_label">Print pallet label?</string>
    <string name="msg_print_tally_sheet">Print tally sheet?</string>
    <string name="msg_please_select_the_printer_first">Please select the printer first</string>
    <string name="msg_dock_is_empty">"Dock is empty"</string>
    <string name="label_lpn_view">LPN View</string>
    <string name="msg_delete_success">Delete Success</string>
    <string name="msg_create_new_load_successful">Create new load successful. Load#: %1$s</string>
    <string name="msg_load_created_checkin_load_right_away">%1$s just created, checkin for this load right away ?</string>
    <string name="msg_order_completed_not_allow_create_new_load">Order completed, Not allow create new load</string>
    <string name="title_check_in_load">Checkin Load</string>
    <string name="title_input_load">Input Load</string>
    <string name="msg_close_transload_task_confirm">Are you sure you want to close task, please confirm the carton Qty?</string>
    <string name="msg_close_transload_task_destination_confirm">We found cartons with same destination received but not scanned for outbound, do you still want to close task ?</string>

    <string name="equipment_type_bobtail_truck">Bobtail Truck</string>
    <string name="equipment_type_box_truck">Box Truck</string>
    <string name="equipment_type_car">Car</string>
    <string name="equipment_type_tractor_container">Tractor + Container</string>
    <string name="equipment_type_tractor_trailer">Tractor + Trailer</string>
    <string name="equipment_type_tractor_flatbed">Tractor + Flatbed</string>
    <string name="equipment_type_other">Other</string>
    <string name="equipment_type_tractor_only">Tractor Only</string>
    <string name="error_invalid_driver_name">Invalid Driver Name</string>
    <string name="error_invalid_driver_license">Invalid Driver License</string>
    <string name="error_invalid_tractor">Invalid Tractor</string>
    <string name="error_invalid_trailer">Invalid Trailer</string>
    <string name="error_invalid_container">Invalid Container</string>
    <string name="msg_carrier_not_found">Carrier not found</string>
    <string name="title_select_carrier">Select Carrier</string>
    <string name="title_last_check_in_mark">Last Checkin:</string>
    <string name="btn_new_entry">New Entry</string>
    <string name="msg_checkin_success">Checkin Success</string>

    <string-array name="equipment_type_array">
        <item>Bobtail Truck</item>
        <item>Box Truck</item>
        <item>Car</item>
        <item>Tractor + Container</item>
        <item>Tractor + Trailer</item>
        <item>Tractor + Flatbed</item>
        <item>Other</item>
        <item>Tractor Only</item>
    </string-array>
    <string-array name="equipment_type_enum_array">
        <item>BOBTAIL_TRUCK</item>
        <item>BOX_TRUCK</item>
        <item>CAR</item>
        <item>TRACTOR_PLUS_CONTAINER</item>
        <item>TRACTOR_PLUS_TRAILER</item>
        <item>TRACTOR_PLUS_FLATBED</item>
        <item>OTHER</item>
        <item>TRACTOR_ONLY</item>
    </string-array>

    <string name="msg_lpn_does_not_belong_to_this_task">LPN does not belong to this task!</string>
    <string name="msg_please_scan_or_input_lpn_to_putAway">Please scan or input LPN to putaway!</string>
    <string name="msg_please_scan_or_input_location_to_putAway">Please scan or input location!</string>
    <string name="msg_put_away_all_carton_confirm">Would you want to put away all cartons to one location？</string>
    <string name="title_put_away_delete">Put Away - Delete</string>
    <string name="msg_are_you_sure_delete_lpn">Are you sure delete LPN: %1$s?</string>
    <string name="msg_are_you_sure_delete_sn">Are you sure delete SN: %1$s?</string>
    <string name="msg_do_you_want_to_collect_sn">Do you want to collect SN?</string>
    <string name="title_lpn_delete">LPN - Delete</string>
    <string name="title_sn_delete">SN - Delete</string>
    <string name="title_sn_collection">SN - Collection</string>
    <string name="btn_delete_all">Delete All</string>
    <string name="msg_all_lpn_deleted">All LPN deleted</string>
    <string name="msg_delete_all_lpn">Are you sure delete all LPN?</string>
    <string name="text_received_loaded_mark">Received/Loaded:</string>
    <string name="text_receipt_no">Receipt No</string>
    <string name="msg_find_duplicate_receipt_item_line">Find Duplicate receipt-item-line With itemSpec = %1$s,Unit = %2$s</string>
    <string name="msg_entry_ticket_checkined_by_load">Entry: %1$s checkin by load: %2$s, Choose another.</string>
    <string name="msg_entry_ticket_checkined_by_receipt">Entry: %1$s checkin by receipt: %2$s, Choose another.</string>
    <string name="text_serial_no">Serial No</string>
    <string name="msg_serial_no_require">Serial No require!</string>
    <string name="msg_use_shipping_scan">Please use shipping scan!</string>
    <string name="btn_no_ship">No, ship</string>
    <string name="title_is_sn_match">IS SN MATCH</string>
    <string name="btn_yes_ship">Yes, ship</string>
    <string name="btn_no_scan">No, scan</string>
    <string name="btn_yes_scan">Yes, scan</string>
    <string name="label_update_sn">Update Sn</string>
    <string name="label_add_sn">Add Sn</string>
    <string name="hint_please_scan_sn">Please scan sn</string>
    <string name="msg_carton_is_already_loaded">Carton:%1$s is already loaded.</string>
    <string name="msg_not_allow_update_sn">Not allow to update carton because lpnSetup step has done.</string>
    <string name="title_pending_load_mark">Pending Load:</string>
    <string name="btn_delete_selected">Delete Selected</string>
    <string name="text_force_close_dock">Force Release Dock</string>
    <string name="hint_scan_trip_pro_more">Scan Trip#/Pro#/Invoice Pro#/DO#/Pallet ID#/LH#</string>
    <string name="msg_please_scan_trip_pro">Please Scan Trip#/Pro#</string>
    <string name="title_create_or_search_task">Create Or Search Task</string>
    <string name="text_later">Later</string>
    <string name="text_pro_progress">%1$s/%2$s Pro</string>
    <string name="label_pro_no_with_colon">Pro#: %1$s</string>
    <string name="label_do_no_with_colon">DO#: %1$s</string>
    <string name="label_pro_no">Pro#</string>
    <string name="hint_scan_pro">Scan Pro#</string>
    <string name="hint_scan_pro_pallet_no">Scan Pro#/Pallet#</string>
    <string name="msg_please_scan_pro">Please Scan Pro#</string>
    <string name="msg_pro_has_completed">Pro#: %1$s has completed</string>
    <string name="msg_pallet_qty_need_greater_than_zero">Pallet qty need greater than 0</string>
    <string name="msg_pallet_qty_need_less_than_one_hundred">Pallet qty need less than 100</string>
    <string name="collect_dimension">Collect Info</string>
    <string name="label_uom_length_width_height">UOM(Length/Width/Height)</string>
    <string name="label_uom_weight">UOM(Weight)</string>
    <string name="text_auto_collect">Auto Collect</string>
    <string name="title_paired_bluetooth_devices">Paired Bluetooth Devices</string>
    <string name="title_bluetooth_device">Bluetooth Device</string>
    <string name="msg_please_select_uom">Please select uom</string>
    <string name="label_print_pallet_label_stage">Print Pallet Label &amp; Stage</string>
    <string name="text_pallets_printed">%1$s/%2$s Pallets Printed</string>
    <string name="text_pallets_scanned">%1$s/%2$s Pallets Scanned</string>
    <string name="hint_scan_pallet_no">Scan Pallet#</string>
    <string name="hint_scan_inventory_location">Scan Inventory Location</string>
    <string name="text_suggest_inventory_location">Suggest Inventory Location: </string>
    <string name="label_schedule_outbound_date">Schedule Outbound Date</string>
    <string name="label_dimension">Dimension</string>
    <string name="hint_select_type">Select Type</string>
    <string name="text_un_assignee">UnAssignee</string>
    <string name="msg_build_pallet_failure">Build pallet failure</string>
    <string name="msg_the_pro_un_received_or_has_been_putaway">The Pro# un received or has been putaway</string>
    <string name="msg_pallet_no_match_continue">Pallet qty no match, Continue?</string>
    <string name="msg_scanned_pallet_expected_pallet_comfirm_pallet_complete">Scanned Pallet: %1$s PLT\n\nExpected Pallet: %2$s PLT,\n\nPlease confirm whether to complete it?</string>
    <string name="msg_submit_success_and_printing_label">Submit success and printing label</string>
    <string name="msg_qr_code_is_not_recognized_to_pallet">QR code is not recognized to pallet</string>
    <string name="msg_please_select_need_to_putaway_pallet">Please select need to putaway pallet</string>
    <string name="msg_please_scan_the_pallet_to_putaway">Please scan the pallet to putaway</string>
    <string name="msg_pallet_putaway_complete">All pallets in the list have been put away, please scan other pallets for putaway</string>
    <string name="msg_pro_does_not_belong_to_this_task">Pro does not belong to this task</string>
    <string name="label_load_colon">Load:</string>
    <string name="label_please_scan_pro_to_load">Please Scan Pro#: %1$s to Load</string>
    <string name="text_pallet_progress">%1$s/%2$s Pallets</string>
    <string name="label_pro_colon">Pro#:</string>
    <string name="text_load_next_pro">Load Next Pro</string>
    <string name="msg_pro_has_been_loaded">Pro: %1$s has been loaded</string>
    <string name="msg_pallet_has_been_loaded">Pallet: %1$s has been loaded</string>
    <string name="msg_pallet_un_printed">Pallet(s) %1$s unprinted</string>
    <string name="msg_pallet_un_putaway">Pallet(s) %1$s un-putaway</string>
    <string name="msg_pallet_missing_dimension">Pallet(s) %1$s missing dimension</string>
    <string name="please_dock_check_in_first">Please dock check in first</string>
    <string name="msg_pallet_has_been_scanned">Pallet: %1$s has been scanned</string>
    <string name="msg_pallet_has_not_yet_been_loaded">Pallet: %1$s has not yet been loaded</string>
    <string name="msg_void_pallet_success">Void pallet success</string>
    <string name="action_void">Void</string>
    <string name="action_pending_to_load">Pending to Load</string>
    <string name="action_filter_by_received">By Received</string>
    <string name="action_filter_by_putaway">By PutAway</string>
    <string name="action_filter_by_un_putaway">By UnPutAway</string>
    <string name="msg_pallet_has_not_yet_been_received">Pallet: %1$s has not yet been received</string>
    <string name="msg_pro_has_not_yet_been_received">Pro: %1$s has not yet been received</string>
    <string name="msg_pro_has_been_putaway">Pro: %1$s has been putaway</string>
    <string name="msg_pallet_has_been_putaway">Pallet: %1$s has been putaway</string>
    <string name="msg_pallet_no_found">Pallet: %1$s no found</string>
    <string name="text_un_putaway">Un-PutAway</string>
    <string name="title_dimension_template">Dimension Template</string>
    <string name="text_pallets_standard">Pallets: Standard(%1$s X %2$s)</string>
    <string name="length_must_greater_than_zero">Length must greater than 0</string>
    <string name="width_must_greater_than_zero">Width must greater than 0</string>
    <string name="height_must_greater_than_five">Height must greater than 5</string>
    <string name="msg_all_pro_have_been_loaded_close_or_not">All pros have been loaded, please close the task or close it later</string>
    <string name="msg_all_pallets_have_been_completed_please_click_complete">All pallets have been completed, please click Complete</string>
    <string name="msg_please_take_photo_for_pallet">Please take photo(s) for pallet(s) %1$s</string>
    <string name="msg_please_cofirm_to_unload_pallet">Please confirm to unload pallet %1$s?</string>
    <string name="msg_receive_now">Receive Now?</string>
    <string name="msg_load_now">Load Now?</string>
    <string name="title_dock_check">Dock Check</string>
    <string name="title_spot_check">Spot Check</string>
    <string name="transfer_dock_location">Transfer dock location</string>
    <string name="msg_are_sure_remove_all_pallets">Are you sure to remove all scanned pallets?</string>
    <string name="msg_pro_has_no_pallet_please_contact_admin">This pro: %1$s has no pallet, please contact administrator</string>
    <string name="pallet_xxx_not_belong_to_pro_xxx">Pallet: %1$s does not belong to Pro#: %2$s</string>
    <string name="msg_scanned_pallet_expected_pallet_scan_continually">Scanned Pallet: %1$s PLT\n\nExpected Pallet: %2$s PLT,\n\nPlease scan pallet continually</string>
    <string name="text_shipment_detail">Shipment Detail</string>
    <string name="text_shipper_address">Shipper Address</string>
    <string name="text_pickup_date">Pickup Date</string>
    <string name="text_consignee">Consignee</string>
    <string name="text_consignee_address">Consignee Address</string>
    <string name="title_dock_check_list">Dock Check Task List</string>
    <string name="label_pallet_counts">Pallet Counts: %1$s</string>
    <string name="label_invoice_pro_no">Invoice Pro#</string>
    <string name="label_linehaul_no">LH#</string>
    <string name="label_do_no">DO#</string>
    <string name="text_report_damage">Report Damage</string>
    <string name="text_edit_damage">Edit Damage</string>
    <string name="text_report_osd">Report OSD</string>
    <string name="label_damage_type">Damage Type</string>
    <string name="title_select_a_damage_type">Select a Damage Type</string>
    <string name="label_comment_optional">Comment (Optional)</string>
    <string name="label_photo_of_pallet">Photo of Pallet</string>
    <string name="label_photo_of_damage_pallet_label">Photo of Damage Pallet Label</string>
    <string name="label_photo_of_damage">Photo of Damage</string>
    <string name="label_is_osd">Is OSD?</string>
    <string name="msg_please_input_comment">Please input comment</string>
    <string name="msg_please_take_photo_of_damage_pallet_label">Please take photo of damage pallet label</string>
    <string name="msg_please_take_photo_of_damage">Please take photo of damage</string>
    <string name="text_is_osd">Is OSD</string>
    <string name="label_comment">Comment: </string>
    <string name="label_assignee_to_me">Assign to me</string>
    <string name="title_create_dock_check">Create Dock Check</string>
    <string name="label_dock_check_method">Dock Check Method</string>
    <string name="label_scanned_pallet">Scanned Pallet: %1$s</string>
    <string name="msg_the_pallet_is_present_cant_report_empty">The pallet is present, can\'t report empty</string>
    <string name="msg_no_pro_associate_with_pallet">No Pro associated with this pallet:%1$s was found, Please scan Pro to receive</string>
    <string name="msg_no_pro_associate_with_pallet_for_load">No Pro associated with this pallet:%1$s was found, Please scan Pro to load</string>
    <string name="msg_there_has_some_pallet_un_dock_check">There has some pallet un-dock check, Confirm to dock check continually?</string>
    <string name="msg_make_sure_this_location_is_empty">Make sure this location: %1$s is empty?</string>
    <string name="msg_the_location_has_some_pallet_confirm_to_report_empty">The location: %1$s has some pallet, Confirm to report empty continually?</string>
    <string name="label_check_in_time">Check in Time</string>
    <string name="label_complete_time">Complete Time</string>
    <string name="label_trip">TRIP#</string>
    <string name="label_tms_trip">TMS TRIP#</string>
    <string name="label_route_name">Route Name</string>
    <string name="label_original_freight_info">Original Freight Info</string>
    <string name="label_actual_freight_info">Actual Freight Info</string>
    <string name="label_upload_photo">Upload Photo</string>
    <string name="msg_please_upload_photo">Please Upload Photo</string>
    <string name="text_report_variance">Report Variance</string>
    <string name="msg_there_is_no_next_pro_please_complete_the_current_pro">There is no next Pro, please complete the current Pro %1$s</string>
    <string name="msg_loaded_pallet_expected_load_pallet_continue_complete_load">Loaded Pallet: %1$s PLT\n\nExpected Load Pallet: %2$s PLT\n\nUnload PalletNo: %3$s\n\nPlease continue to complete load</string>
    <string name="label_upcoming_suggestion">Upcoming Suggestion</string>
    <string name="msg_invalid_pallet_x_pallet_belongs_to_other_pro_x">Invalid Pallet: %1$s. Pallet belongs to other PRO: %2$s</string>
    <string name="msg_unable_to_locate_the_shipment_please_report_to_your_supervisor">Unable to locate the shipment. Please report to your supervisor</string>
    <string name="text_completed">Completed</string>
    <string name="text_direct_putaway">Direct Putaway</string>
    <string name="text_putaway_completed">Putaway Completed</string>
    <string name="msg_all_pro_has_been_offloaded_proceed_to_put_away">All pro has been offloaded. Proceed to put away?</string>
    <string name="msg_the_pallet_no_related_to_xxx_was_not_found">The palletNo related to %1$s was not found</string>
    <string name="msg_inventory_for_the_pallet_xx_not_found_please_contact_your_supervisor">Inventory for the pallet:%1$s not found, please contact your supervisor</string>
    <string name="msg_pallet_label_not_found">Pallet: %1$s label not found</string>
    <string name="msg_input_or_scan_pallet_no">Input or Scan Pallet#</string>
    <string name="msg_please_input_or_scan_pallet_no">Please Input or Scan Pallet#</string>
    <string name="msg_pallet_xx_zplcode_missing">Pallet: %1$s zplCode missing</string>
    <string name="label_apply_to_rest_pallets">Apply to rest pallets</string>
    <string name="msg_no_pallet_need_to_putaway">No Pallet need to putaway</string>
    <string name="text_offload_completed">Offload Completed</string>
    <string name="text_putAway_completed">PutAway Completed</string>
    <string name="msg_do_you_want_to_reopen_the_pro">Do you want to reopen the Pro#: %1$s?</string>
    <string name="msg_warning_removing_this_will_delete_the_entire_dn">Warning: Removing this will delete the entire DN. Are you sure you want to proceed?</string>
    <string name="msg_must_unload_before_remove_pro">Need to unload loaded Pro#(s) before removing Pro#</string>
    <string name="text_other_terminal_xx">Other Terminal: %1$s</string>
    <string name="text_reprint_all">Reprint All</string>
    <string name="text_void_all_label">Void All Label</string>
    <string name="text_count_pallet_on_the_location">Count pallet amount on the location</string>
    <string name="msg_pallet_match_inventory_move_to_next_location">Pallet Match Inventory, Move to Next Location?</string>
    <string name="msg_please_start_the_dock_check_for_the_next_location">Please start the dock check for the next location</string>
    <string name="msg_currently_xx_labels_how_many_pallet_label_you_want_to_void">Currently %1$s labels. how many pallet label you want to void?</string>
    <string name="title_void_pallet_label">Void Pallet Label</string>
    <string name="msg_the_quantity_of_void_pallets_label_must_not_exceed_the_total_pallet_count">The quantity of void pallets label must not exceed the total pallet count</string>
    <string name="msg_you_have_already_scanned_some_pro_no_xx_pallets">You have already scanned some ProNo: %1$s pallets. Do you want to switch to ProNo: %2$s? If yes, the list will be cleared.</string>
    <string name="msg_record_not_found">Record not found</string>
    <string name="msg_pallets_under_pro_match">%1$s pallets under Pro %2$s, does it match?</string>
    <string name="msg_pallets_under_pro_match_but_in_other_location">%1$s pallets under Pro %2$s, but they are in other location: %3$s, does it match?</string>
    <string name="msg_pallets_is_not_located_at_current_terminal">%1$s(%2$s pallets) is not located at current terminal %3$s per record, please scan the pallet(s) to verify</string>
    <string name="msg_pallets_are_under_pro_scan_but_in_other_location">%1$s pallets under %2$s, %3$s pallets are at below location(s), Please move all pallet to current location and then start scanning.</string>
    <string name="msg_pallets_at_location">%1$s pallets at %2$s</string>
    <string name="msg_pallet_at_location">%1$s pallet at %2$s</string>
    <string name="msg_pallet_not_belong_to_pro">%1$s does not belong to Pro# %2$s</string>
    <string name="msg_pro_xx_already_scanned">Pro#: %1$s already scanned</string>
    <string name="title_gps_bound_list">GPS bound list</string>
    <string name="title_gps_bind">Bind GPS</string>
    <string name="text_gps">GPS</string>
    <string name="msg_please_scan_or_input_gps_code">Please scan or input GPS code</string>
    <string name="msg_bind_success">Bind success</string>
    <string name="msg_bind_failed">Bind failed</string>
    <string name="the_following_pallets_do_not_belong_to_task_xxx">The following pallet(s) do not belong to task: %1$s</string>
    <!-- transload end -->

    <!-- transload V2 start -->
    <string name="transload_receiving_task">Transload Receiving Task</string>
    <string name="title_receipt_id">Receipt ID:</string>
    <string name="title_container_no">Container No:</string>
    <string name="title_container_size">Container Size</string>
    <string name="title_reference_no">Reference No:</string>
    <string name="title_received_qty_expected_qty">Received / Expected qty:</string>
    <string name="title_dock_no">Dock#:</string>
    <string name="title_asn_qty">ASN Qty:</string>
    <string name="title_overage_qty">Overage Qty:</string>
    <string name="title_shortage_qty">Shortage Qty:</string>
    <string name="receiving_with_new_pallet">Receiving with New Pallet</string>
    <string name="receiving_with_new_pallet_by_carton">Receiving with New Pallet By Carton</string>
    <string name="receiving_with_existing_pallet">Receiving with Existing Pallet</string>
    <string name="receiving_with_dynamic_pallet">Receiving with Dynamic Pallet</string>
    <string name="receiving_with_last_mile">Receiving with Last Mile</string>
    <string name="receiving_with_exception_pallet">Receiving Exception</string>
    <string name="title_expected_item_qty_and_build_qty">Expected Item Qty / Build Qty:</string>
    <string name="title_expected_pallet_qty_and_build_qty">Expected Pallet Qty / Build Qty:</string>
    <string name="title_pallet_no">Pallet No:</string>
    <string name="title_carton_no">Carton No:</string>
    <string name="title_destination_code">Destination Code:</string>
    <string name="title_item_summary">Item Summary</string>
    <string name="title_pro_summary">Pro Summary</string>
    <string name="title_pallet_summary">Pallet Summary</string>
    <string name="review">Review</string>
    <string name="reprint">Reprint</string>
    <string name="title_pallet_list">Pallet List</string>
    <string name="title_carton_list">Carton List</string>
    <string name="title_scan_pallet">Scan pallet</string>
    <string name="title_keep_pallet">Keep pallet</string>
    <string name="title_scan_carton">Scan carton</string>
    <string name="title_receive_all">Receive All</string>
    <string name="title_select_destination">Select shipping destination</string>
    <string name="title_item_list">Item list on single pallet</string>
    <string name="title_select_item">Select item</string>
    <string name="title_pallet_qty">Pallet Qty</string>
    <string name="title_copy_of_each_pallet">Copy of each pallet</string>
    <string name="title_print_label_and_submit">Print label and submit</string>
    <string name="title_transload_receiving">Transload Receive</string>
    <string name="title_transload_loading">Transload Loading</string>
    <string name="error_invalid_pallet">Invalid Pallet!</string>
    <string name="error_invalid_carton">Invalid Carton!</string>
    <string name="message_confirm_delete_pallet">Confirm to delete this pallet?</string>
    <string name="message_confirm_delete_all_pallet">Confirm to delete all pallets?</string>
    <string name="message_confirm_delete_select_pallet">are you sure to delete the selected pallet/cartons?</string>
    <string name="message_confirm_receive_all_pallet">Confirm to receive all pallets?</string>
    <string name="message_confirm_reprint">Confirm to reprint this pallets?</string>
    <string name="message_all_pallets_received">No unreceived pallets</string>
    <string name="title_scan_exceptional_carton">Scan Exceptional Carton barcode</string>
    <string name="title_scanned_carton_list">Scanned carton list</string>
    <string name="error_pallet_received">Pallet has been received!</string>
    <string name="error_input_pallet_qty">Please input the pallet qty!</string>
    <string name="error_input_copy_of_each_pallet">Please input the copy of each pallet!</string>
    <string name="error_select_destination">Please select shipping destination!</string>
    <string name="error_add_pallet_item">Please add item!</string>
    <string name="title_add_item_qty">/ %1$s AVL</string>
    <string name="error_add_item_input_empty">Please input qty!</string>
    <string name="error_add_item_input_more">qty can not greater than %s</string>
    <string name="message_add_item_success">add item success</string>
    <string name="error_invalid_carton_format">carton %s invalid carton</string>
    <string name="title_scan_carton_to_pallet">Scan Carton to Pallet</string>
    <string name="title_desc">DEST</string>
    <string name="error_destination_not_match">Destination not Match</string>
    <string name="message_confirm_delete_carton">Confirm to delete this carton?</string>
    <string name="title_carton_qty">Carton Qty:</string>
    <string name="menu_print_pallet_label">Print Pallet Label</string>
    <string name="error_destination_empty">Destination empty!</string>
    <string name="error_order_id_empty">OrderId empty!</string>
    <string name="error_appointment_date_empty">Appointment date empty!</string>
    <string name="label_item_list">Item list:</string>
    <string name="text_select_destination">Select Destination</string>
    <string name="message_confirm_transload_unload">Confirm to unload this pallet</string>
    <string name="msg_pallet_not_in_recepit">Pallet not in receipt!</string>
    <string name="menu_scan_or_select_pallet">Scan or select pallet</string>
    <string name="total_pallet">Total Pallet :</string>
    <string name="title_transload_operation">Transload Operation</string>
    <string name="sub_title_transload_operation">Split / Merge / Change Transload Pallet destination</string>
    <string name="title_transload_more_split">Split pallet</string>
    <string name="title_transload_more_merge">Merge pallet</string>
    <string name="title_transload_change_dc"> Change Transload Pallet destination</string>
    <string name="title_transload_search"> Transload Task Search</string>
    <string name="title_transload_scan_pallet">Source pallet</string>
    <string name="title_split_by_item">Split by item</string>
    <string name="title_split_by_carton">Split by carton</string>
    <string name="title_item_from_pallet">Item form pallet:</string>
    <string name="title_item_qty_max">(maximum:%1$s)</string>
    <string name="error_input_item_qty_empty">Please input the item qty!</string>
    <string name="error_input_item_qty_max">The item qty cannot be greater than %1$s !</string>
    <string name="title_merge_tip">Pallet with the same destination can be merged </string>
    <string name="title_merge_pallet_from">Merge Pallet From</string>
    <string name="title_merge_pallet_to">Merge Pallet To</string>
    <string name="title_split_pallet_to">Split to pallet</string>
    <string name="title_new_destination">New Destination:</string>
    <string name="edit_qty">Edit Qty</string>
    <string name="label_dc">DC :</string>
    <string name="label_edit_pallet">Edit Pallet</string>
    <string name="label_add_pallet">Add Pallet</string>
    <string name="pallet_merge_destination_no_same">Pallet Merge should be with same destination</string>
    <string name="error_add_item_qty_max">received qty is more than expected qty</string>
    <string name="label_existing_pallet">Existing Pallet</string>
    <string name="error_select_container_size">please select container size</string>
    <string name="force_release_dock">Force release dock now</string>
    <!-- transload V2 end -->

    <string name="label_ship_method">Ship Method</string>
    <string name="title_ship_method">Ship Method</string>
    <string name="label_trailer_size">Trailer Size</string>
    <string name="title_package_type_list">Package Type List</string>
    <string name="text_package_type">Package Type</string>
    <string name="hint_please_select_package_type">Please select package type</string>
    <string name="text_input_scan_location_check_no">Please input or scan location check NO.</string>
    <string name="text_invalid_check_no">Invalid check NO.</string>
    <string name="label_expected_pallet_qty">Expected Pallet Qty</string>
    <string name="text_check_no">Check No.</string>
    <string name="text_print_fail">Print Fail</string>
    <string name="text_please_reprint">Print Fail, please reprint</string>
    <string name="msg_please_reprint">Print Fail, Reprint?</string>
    <string name="lp_label">LP Label</string>
    <string name="equipment_label">Equipment Label</string>
    <string name="label_location_label">Location Label</string>
    <string name="hint_type_any_text_to_search">Type any text to search</string>
    <string name="msg_please_scan_or_input_equipment">Please scan or input Equipment</string>
    <string name="msg_please_select_equipment">Please select Equipment</string>
    <string name="msg_equipment_not_found">Equipment not found.</string>
    <string name="item_label">Item Label</string>
    <string name="msg_entry_not_match_by_task">Entry not match by task</string>
    <string name="text_print_by_load">print by load</string>
    <string name="text_print_by_ship_to">print by ship to</string>
    <string name="label_order_count">Order Count</string>
    <string name="label_slp_count">SLP Count</string>
    <string name="text_switch_to_entire">Switch To Entire</string>
    <string name="text_switch_to_partial">Switch To Partial</string>
    <string name="get_suggestion">Get suggestion</string>
    <string name="get_location">Get location</string>
    <string name="replenishment_suggestion">Replenishment suggestion</string>
    <string name="short_description">Short description</string>
    <string name="upc_code">UPC</string>
    <string name="upc_code_case">Case UPC</string>
    <string name="ean_code">EAN Code</string>
    <string name="item_selector">Item selector</string>
    <string name="hint_item_search">ItemName/UPC/desc...</string>
    <string name="scan_or_input_checking_no">Scan or input checking No</string>
    <string name="text_load_sequence">Load Sequence</string>
    <string name="label_picked_lp">Picked Lp</string>
    <string name="title_print_packing_list">Print Packing List</string>
    <string name="packing_list_print_success">packing list print success</string>
    <string name="put_away_time">Put away time</string>
    <string name="hint_please_set_packaging_type_for">Please set packaging type for</string>
    <string name="msg_inventory_not_found_item">Inventory not found for this item</string>
    <string name="inventory_search">Inventory Search</string>
    <string name="item_inventory_detail">Item inventory detail</string>
    <string name="btn_sn_detail">SN Detail</string>
    <string name="lp_path">LP Path</string>
    <string name="label_received_time">Received Time</string>
    <string name="show_image">Show Image</string>
    <string name="sort_by_location">Sort By Location</string>
    <string name="sort_by_ea_qty">Sort By EA QTY</string>
    <string name="sort_by_lp">Sort By LP</string>
    <string name="sort_by_status">Sort By Status</string>
    <string name="sort_by_received_time">Sort By Received Time</string>
    <string name="location_count">Location Count</string>
    <string name="label_last_count">Last Count</string>
    <string name="text_please_go_to">Please Go To</string>
    <string name="title_purpose_of_next_location">Purpose of next location</string>
    <string name="pick_location_issue_will_complete_later">Location issue, will complete later</string>
    <string name="pick_inventory_issue_please_suggest_a_different_location">Inventory issue, please suggest a different location</string>
    <string name="pick_other_issue_please_suggest_a_different_location">Other issue, please suggest a different location</string>
    <string name="text_please_input_order">Please Input Order</string>
    <string name="movement_task_list">Movement Task List</string>
    <string name="text_refresh">Refresh</string>
    <string name="text_nextwork_weak_please_refresh">Network weak please refresh</string>
    <string name="text_nextwork_weak_location_may_not_complete_do_you_want_to_back">Netwok weak, location may not complete, do you want to back ?</string>
    <string name="text_calculator">Calculator</string>
    <string name="query_do_you_want_to_find_nearest_pick_task">Do you want to find nearest pick task ?</string>
    <string name="query_do_you_want_to_start_next_pick_task">Do you want to start next pick task?</string>
    <string name="error_item_not_belong_this_pick_task">Item not belong to pick task,please check item.</string>
    <string name="message_batch_order_pick_new_lp_validate">Current order have pick to %1$s, please input password to print new LP</string>
    <string name="message_batch_order_pick_rebuld_pick_strategy">rebuild pick strategy</string>
    <string name="message_batch_order_pick_rebuld_pick_strategy_succeed">rebuild pick strategy succeed</string>
    <string name="message_batch_order_pick_rebuld_pick_strategy_failed">rebuild pick strategy failed</string>
    <string name="text_pack_all_lp">Pack All LP</string>
    <string name="hint_scan_tote_lp_item_bp_reference">Scan Tote/LP/Item/BP Reference</string>
    <string name="label_bp_reference">BP Reference</string>
    <string name="text_pack_all_lp_of_order">Pack All LP Of Order</string>
    <string name="text_pack_all">Pack All</string>
    <string name="text_pallet_count">Pallet Count</string>
    <string name="hint_please_input_pallet_count">Please Input Pallet Count</string>
    <string name="go_to">(Go to)</string>
    <string name="new_pick_flow">New Pick Flow</string>
    <string name="location_inventory">Inventory On This Location</string>
    <string name="forward">Forward</string>
    <string name="please_scan_location_or_lp">Please Scan Location Or LP</string>
    <string name="please_scan_location_to_log_out">Please scan location barcode to logout</string>
    <string name="pick_qty">Pick QTY</string>
    <string name="batch_print_lp">Batch Print LPs</string>
    <string name="force_close_step">Force Close Step</string>
    <string name="return_picked">Return Picked</string>
    <string name="print_lps_for_this_task">Do you want to print LPs for this task?</string>
    <string name="text_search_task_by_lp">Search Task By LP</string>
    <string name="text_contains_multiple_item">contains multiple item</string>
    <string name="item_info_collect">Item Info Collect</string>
    <string name="btn_reprint_lp">Reprint LP</string>
    <string name="title_item_info_collector">Item Information Collector</string>
    <string name="text_upc_collection">UPC Collection</string>
    <string name="please_scan_or_input_upc_code">Please scan or input upc code</string>
    <string name="item_line">Item Line</string>
    <string name="history">History</string>
    <string name="text_case_upc_collection">Case UPC Collection</string>
    <string name="picked_qty_bigger_than_order_needed">Picked QTY bigger than order needed!</string>
    <string name="please_create_new_clp_to_handle_picked_item">Please create new CLP to handle picked item</string>
    <string name="return_to_inventory">Return To Inventory</string>
    <string name="inventory_count">Inventory Count</string>
    <string name="inventory_recount">Inventory Recount</string>
    <string name="inventory_recount_task">Inventory Recount Task</string>
    <string name="text_submit_data_mark">Submit Data:</string>
    <string name="text_update_data_mark">Update Data:</string>
    <string name="text_confirmed_scanned_sn_qty">Confirmed scanned sn qty</string>
    <string name="title_new_lp_confirm">New LP ?</string>
    <string name="title_new_item_confirm">New Item ?</string>
    <string name="label_location_lp_mark">Location/LP:</string>
    <string name="label_new_pallet">New Pallet</string>
    <string name="label_new_pallet_confirm">New Pallet ?</string>
    <string name="msg_item_selected">Item Selected !</string>
    <string name="text_uploading">Uploading..</string>
    <string name="msg_take_inventories_pictures">Take inventories pictures?</string>
    <string name="msg_take_more_inventories_pictures">Take more inventories pictures?</string>


    <string name="ilp">ILP</string>
    <string name="ea_upc">EA UPC</string>
    <string name="ea_upc_mark">EA UPC:</string>
    <string name="case_upc">CASE UPC</string>
    <string name="label_inventory_count_lot_no">Lot No.</string>
    <string name="label_inventory_count_exp_date">Exp Date</string>
    <string name="label_inventory_count_select_exp_date">Select Exp Date</string>
    <string name="case_upc_mark">CASE UPC:</string>
    <string name="verify_by_upc">verify by UPC</string>
    <string name="verify_by_item_number">verify by Item number</string>
    <string name="empty_location">Empty Location</string>
    <string name="empty_location_only">Empty Location Only</string>
    <string name="bulk_location">Bulk Location</string>
    <string name="enable">Enable</string>
    <string-array name="inventory_count_uom">
        <item>CS</item>
        <item>EA</item>
        <item>IN</item>
        <item>UOM</item>
    </string-array>
    <string name="location_error_please_scan_again">Location error, please scan again</string>
    <string name="lp_error_please_scan_again">LP error, please scan again</string>
    <string name="case_upc_error_please_scan_again">Case UPC error, please scan again</string>
    <string name="ea_upc_error_please_scan_again">EA UPC error, please scan again</string>
    <string name="qty_error_please_input_again">QTY error, please input again</string>
    <string name="uom_error_please_select_one">UOM error, please select one</string>
    <string name="bind_order_with_lp">Bind Order With LP</string>
    <string name="nothing_suggestion_in_this_lp_please_scan_another">Nothing suggestion in this LP, please scan another</string>
    <string name="nothing_suggestion_in_this_location_please_scan_another">Nothing suggestion in this location, please scan another</string>
    <string name="new_pick_flow_summary">New Feature Under Testing</string>
    <string name="system_feature_management">System Feature Management</string>
    <string name="replenishment_uom_downgrade">UOM Downgrade</string>
    <string name="replenishment_same_lp_unit_downgrade_summary">Enable UOM Downgrade Under Same LP</string>
    <string name="label_title_selector">Title Selector</string>
    <string name="inventory_adjustment">Inventory Adjustment</string>
    <string name="inventory_adjustment_summary">Inventory Adjustment Feature Under More Tab</string>
    <string name="back_end_trace_log">Back End Trace Log</string>
    <string name="enable_back_end_trace_log">Enable back end trace log</string>
    <string name="create_empty_put_back_task">Create Empty Put Back Task</string>
    <string name="text_pick_to_order">Pick To Order</string>
    <string name="text_can_not_put_away_to_pick_location">Can\'t put away to pick location</string>
    <string name="text_can_not_stage_consolidation_task_location">LP: %1$s Can\'t stage to location %2$s</string>
    <string name="msg_item_not_include_this_location">Item not include this location</string>
    <string name="do_you_want_to_close_pallet">Do you want to close pallet ?</string>
    <string name="do_you_want_to_change_item">Do you want to change item ?</string>
    <string name="text_pick_location">Pick Location</string>
    <string name="test_no_suggestion_please_scan_lp_or_item_to_pick">No suggestion. Please scan lp or item to pick</string>
    <string name="query_receive_collect_upc">Number input is not UPC/Case UPC. Do you want to collect item info?</string>
    <string name="text_search_by_item">Search By: Item</string>
    <string name="error_found_multiple_location">Found multiple location</string>
    <string name="text_case_upc_checked">Case UPC Checked</string>
    <string name="label_voice_param">Voice Param</string>
    <string name="label_speed">Speed</string>
    <string name="label_speed_scope">1~100</string>
    <string name="label_speed_default">35</string>
    <string name="label_pitch">Pitch</string>
    <string name="label_pitch_default">50</string>
    <string name="label_volume">Volume</string>
    <string name="label_volume_default">80</string>
    <string name="label_voice_stream">Voice Stream</string>
    <string name="label_voice_stream_default">Call</string>
    <string name="label_voicer">Voicer</string>
    <string name="label_voicer_default">catherine</string>
    <string name="label_speak_param">Speak Param</string>
    <string name="label_speak_language">Language</string>
    <string name="label_speak_language_default">English</string>
    <string name="label_speak_first_point">Speak First Point</string>
    <string name="label_speak_first_point_default">3000</string>
    <string name="label_speak_end_point">Speak End Point</string>
    <string name="label_speak_end_point_default">800</string>
    <string name="label_speak_point_scope">100~5000</string>
    <string name="voice_setting">Voice Setting</string>
    <string name="voice_params_setting">Voice Params Setting</string>

    <string name="qc_work">QC Work</string>
    <!--    <string name="qc_work">QC Work</string>-->
    <string name="text_QC">QC</string>
    <string name="text_upc">UPC</string>
    <string name="text_multiple_upc">Multiple UPC</string>
    <string name="text_enter_the_qty_or_scan_upc">Enter The Qty Or Scan UPC</string>
    <string name="text_scan_upc">SCAN UPC</string>
    <string name="text_scan_item_num">SCAN ITEM#</string>
    <string name="text_scan_sn_num">SCAN SN</string>
    <string name="text_scan_clp">SCAN CLP</string>
    <string name="text_print_clp">PRINT CLP</string>
    <string name="text_key_in">KEY IN</string>
    <string name="title_qc_task_item_view">QC TASK ITEM VIEW</string>
    <string name="text_last_scanned_dn_colon">Last Scanned Order :</string>
    <string name="text_committed">Committed</string>
    <string name="text_qc_passed">QC Passed</string>
    <string name="text_last_scanned_clp_colon">Last Scanned CLP :</string>
    <string name="please_select_order_or_scan_clp">Please select order or scan CLP</string>
    <string name="text_qc_upc_uom_not_match">UPC uom is not match, please input qty and select uom</string>
    <string name="error_invalid_qty">Invalid qty</string>
    <string name="please_select_order">Please select order</string>
    <string name="title_qc_task">QC Task</string>
    <string name="text_add_order">Add Order</string>
    <string name="query_remove">Do you want to remove</string>
    <string name="text_sun">Sun</string>
    <string name="text_mon">Mon</string>
    <string name="text_tue">Tue</string>
    <string name="text_wed">Wed</string>
    <string name="text_thu">Thu</string>
    <string name="text_fri">Fri</string>
    <string name="text_sat">Sat</string>
    <string name="text_am">AM</string>
    <string name="text_pm">PM</string>
    <string name="text_am_lower_case">am</string>
    <string name="text_pm_lower_case">pm</string>
    <string name="text_adjustment_reason">Adjustment Reason</string>
    <string name="error_invalid_upc">Invalid UPC</string>
    <string name="text_random_check">Random Check</string>
    <string name="text_regular_qc">By LongHaul</string>
    <string name="text_trackingno_qc">Tracking No QC</string>
    <string name="text_enter_or_scan_lp">Enter or Scan LP</string>
    <string name="text_scan_item">Scan Item</string>
    <string name="text_random_qc">Random QC</string>
    <string name="label_start_qc">Start QC</string>
    <string name="label_qc_task_status">QC Task Status</string>
    <string name="hint_enter_qc_qty">Enter QC QTY</string>
    <string name="hint_re_enter_qc_qty">Re-Enter QC QTY</string>
    <string name="msg_please_check_lp">Not Found, Please Check LP</string>
    <string name="msg_please_check_item">Not Found, Please Check Item</string>
    <string name="error_qc_qty_not_match_enter_again">QC QTY does not matched with picked record, Enter again to confirm</string>
    <string name="error_qc_qty_not_match_see_supervisor">QC QTY does not match, please see supervisor for special handing</string>
    <string name="error_item_qty_all_qc_passed">Item QC passed qty match picked qty, Enter again to confirm</string>
    <string name="hint_carrier_pickup_label">Carrier Pick Up Label</string>
    <string name="menu_print_carrier_pickup">Print Carrier PickUp</string>
    <string name="label_scan_carrier_pickup">Please Scan Carrier PickUp %s</string>
    <!-- more fragment: quality control end-->
    <!-- more fragment: re-palletize start-->
    <string name="label_repalletize">Re-Palletize</string>
    <string name="label_split_combine_mass_repalletize">Split, Combine, Mass Re-Palletize, Consolidate By Load</string>
    <string name="text_combine">Combine</string>
    <string name="text_consolidate_by_order">Consolidate By Order</string>
    <string name="text_consolidate_by_Load">Consolidate By Load</string>
    <string name="text_mass_repalletize">Mass Re-Palletize</string>
    <string name="text_transfer_lp">Transfer LP</string>
    <string name="text_scanned_lp">Scanned LP: </string>
    <string name="please_scan_or_input_to_lp">Please scan or input toLP</string>
    <string name="label_add_to_lp">Add To LP</string>
    <string name="label_combine_to_new_lp">Combine To New LP</string>
    <string name="label_lp_inventories">LP Inventories</string>
    <string name="label_split_complete">Split Complete</string>
    <string name="title_transfer_lp_from">Transfer LP from</string>
    <string name="title_transfer_lp_to">Transfer LP To</string>
    <string name="hint_please_select_lot_no">Please select lotNo</string>
    <string name="select_lot_no">Select LotNo</string>
    <!-- more fragment: re-palletize end-->

    <!-- more fragment: Parcel Load start -->
    <string name="text_parcel_load">Parcel Load</string>
    <string name="text_compare">Compare</string>
    <string name="label_service_type_mark">Service Type:</string>
    <string name="label_last_scanned">Last Scanned (%1$s):</string>
    <string name="label_contain_package_mark">Contain Package:</string>
    <string name="label_customer_info_mark">Customer Info:</string>
    <string name="label_loading_progress_mark">Loading Progress:</string>
    <string name="text_scan_slp_shipping_label_to_load">Scan SLP/Shipping Label to load</string>
    <string name="text_scan_slp_shipping_label_to_unload">Scan SLP/Shipping Label to unload</string>
    <string name="text_scan_slp_tote_form_to_load">Scan SLP/Tote/End Of Day Form to load</string>
    <string name="text_scan_tracking_no_to_load">Scan Tracking Number to load</string>
    <string name="text_scan_slp_tote_form_to_unload">Scan SLP/Tote/End Of Day Form to unload</string>
    <string name="text_scan_tracking_no_to_unload">Scan Tracking Number to unload</string>
    <string name="text_loading_compare_report">Loading Compare Report</string>
    <string name="text_tracking">Tracking</string>
    <string name="label_dimension_mark">Dimension:</string>
    <string name="label_weight_mark">Weight:</string>
    <string name="label_item_description_mark">Item Description: </string>
    <string name="label_description_mark">Description:</string>
    <string name="label_destination_zip_mark">Destination ZIP:</string>
    <string name="label_dn_mark">DN:</string>
    <string name="label_ref_mark">Ref#:</string>
    <string name="label_user_mark">User:</string>
    <string name="title_label_time_mark">Label Time:</string>
    <!-- more fragment: Parcel Load End -->

    <!-- parcel pack start-->
    <string name="title_pack_to_pallet">Pack To Pallet</string>
    <string name="text_consolidate_packages_to_pallet">Consolidate packages to pallet</string>
    <string name="text_generate_slp_to_pack">Generate SLP To Pack</string>
    <string name="text_generate_slp_scan_tote_to_pack">Generate SLP Or Scan Tote To Pack</string>
    <string name="text_generate_slp_for_pallet">Generate SLP For Pallet</string>
    <string name="text_scan_tote_for_pallet">Scan Tote For Pallet</string>
    <string name="text_start_load">Start Load</string>
    <string name="text_scan_slp_tote_to_edit_pack">Scan SLP/Tote To Edit Pack</string>
    <string name="text_edit_pallet">Edit Pallet</string>
    <string name="text_pack_parcel_package">Pack Parcel Package</string>
    <string name="text_printed">Printed</string>
    <string name="text_please_generate_slp_or_scan_tote">Please generate SLP or scan TOTE</string>
    <string name="text_please_generate_new_slp">Please generate new SLP</string>
    <string name="text_tracking_number_not_packed">Tracking Number Not Packed</string>
    <string name="text_current_pallet_slp">Current Pallet SLP :</string>
    <string name="text_scan_tracking_number">Scan Tracking Number</string>
    <string name="title_parcel_pack">Parcel Pack</string>
    <string name="text_pack_to_pallet">Pack To Pallet</string>
    <string name="msg_pack_success_next">Pack Success, Next.</string>
    <string name="text_unpack_to_pallet">Unpack To Pallet</string>
    <string name="msg_unpack_success">Unpack Success</string>
    <string name="text_tacking_number_is_not_under_this_slp">Tracking number is not under this SLP</string>
    <string name="text_tacking_number_not_found">Tracking number not found</string>
    <string name="title_parcel_load_task_list">Parcel Load Task List</string>
    <string name="title_fedex">FedEx</string>
    <string name="title_digit">Digit</string>
    <string name="msg_please_input_digit">Invalid digit, please input again</string>
    <string name="msg_tacking_number_submit_error">Tracking Number Submit Error</string>
    <string name="pack_tracking_number_to_lp">[ Pack ] %1$s >> %2$s</string>
    <string name="unpack_tracking_number_to_lp">[ Unpack ] %1$s >> %2$s</string>
    <string name="msg_pack_to_lp">Pack to %1$s</string>
    <string name="msg_pallet_changed">Pallet changed</string>
    <string name="text_generate_slp">Generate SLP</string>
    <string name="scan_lp_or_location">Scan LP/Location </string>
    <string name="total_qty_mark">Total QTY:</string>
    <!-- parcel pack end-->

    <string name="please_scan_case_upc">Please scan case UPC</string>
    <string name="text_stage_all">Stage All</string>
    <string name="title_print_equipment">Print Equipment</string>
    <string name="please_scan_or_input_tracking_no">Please scan or input tracking NO.</string>
    <string name="please_scan_or_input_equipment">Please scan or input Equipment</string>
    <string name="error_item_status_is_inactive">Item %1$s is inactive, please contact your supervisor.</string>
    <string name="msg_item_info_missing_or_unit_disabled_contact_supervisor">Item\'s(%1$s) Description, UOM, Dimensions or Weight is missing, or the UOM is disabled. Please contact supervisor.</string>
    <string name="msg_item_info_missing_or_unit_disabled">Item\'s(%1$s) Description, UOM, Dimensions or Weight is missing, or the UOM is disabled.</string>
    <string name="msg_item_inactive_check_info">The Item(%1$s) is inactive. Please verify if the Description, UOM, Dimensions &amp; Weight information is correct. The item will be activated after confirmation.</string>
    <string name="msg_item_template_not_set_up">Item %1$s template not set up, please contact your supervisor.</string>
    <string name="text_scan_serial_number">Scan Serial Number</string>
    <string name="text_number_of_sn_scanned_overage">Number of SN scanned Overage</string>
    <string name="text_number_of_rfid_scanned_overage">Number of RFID scanned Overage</string>
    <string name="text_number_of_master_sn_scanned_overage">Number of SN scanned Overage, current sn count: %1$s, new sn count:%2$s</string>
    <string name="text_number_of_sn_scanned_shortage">Number of SN scanned Shortage</string>
    <string name="text_number_of_rfid_scanned_shortage">Number of RFID scanned Shortage</string>
    <string name="text_invalid_sn_length">SN length not match</string>
    <string name="text_sn_quantity_exceeded">SN quantity exceeded</string>
    <string name="text_invalid_sn_format">Invalid SN format</string>
    <string name="msg_please_double_scan">Please double scan</string>
    <string name="title_set_sn_weight">Set SN Weight</string>
    <string name="title_input_sn_weight">Input SN Weight</string>
    <string name="msg_weight_can_not_be_empty">Weight can not be empty</string>
    <string name="title_customer_not_allow_short_receive">Not Allow Short Receive</string>
    <string name="msg_should_turn_on_allow_short_receive_flag">if want to force close task, please turn on customer flag:Allow Short Receive on the web page and refresh task</string>
    <string name="title_customer_not_allow_force_close_step">Not Allow Force Close Step</string>
    <string name="msg_should_turn_off_allow_force_close_step_flag">if want to force close step, please turn off customer flag:Forbid Force Close Steps From Pick Task</string>
    <string name="msg_no_permission_to_force_close">User does not have Force Close permission</string>
    <string name="text_verify_pick_item">please scan item</string>
    <string name="text_verify_pick_item_fail">Invalid item, please try again</string>
    <string name="text_verify_pick_item_succ">verify pick item successful</string>
    <string name="msg_verify_pick_item_first">please verify pick item first</string>

    <!-- inventory movement start-->
    <string name="title_inventory_movement">Inventory Movement</string>
    <string name="title_inventory_initial">Initial Inventory</string>
    <string name="title_replenishment">Replenishment</string>
    <string name="scan_sn_or_lp_or_location_for_movement">Scan SN/LP/Location for movement</string>
    <string name="text_movement_collect">Movement Collect</string>
    <string name="text_collect">Collect</string>
    <string name="text_scan_sn_lp_location">Scan SN/LP/Location</string>
    <string name="text_scan_sn_item">Scan SN/Item</string>
    <string name="text_please_scan_sn_to_change_qty">Please scan SN to change quantity</string>
    <string name="error_item_quantity_is_not_match_sn_quantity">Item quantity is not match SN quantity</string>
    <string name="error_item_quantity_exceed_max">Item quantity exceed max quantity</string>
    <string name="text_collect_success">Collect Success</string>
    <string name="text_drop">Drop</string>
    <string name="text_inventory_move">Inventory Move</string>
    <string name="sort_by_item">Sort By Item</string>
    <string name="text_by_time">By Time</string>
    <string name="text_inventory_collect_detail">Inventory Collect Detail</string>
    <string name="text_inventory_move_detail">Inventory Move Detail</string>
    <string name="text_please_scan_input_sn_item_for_inventory_move">Please scan or input SN/Item for inventory move</string>
    <string name="msg_rescan_item_or_exit_and_redo_the_operation">Please rescan SN/Item or exit and redo the operation</string>
    <string name="text_please_scan_sn_lp_location_for_inventory_collect">Please scan SN/LP/Location for inventory collect</string>
    <string name="text_print_tolp_first">Please print to lp first</string>
    <string name="please_scan_input_move_to_location">Please scan or input move to Location</string>
    <string name="please_scan_to_location">Please scan to Location</string>
    <string name="msg_move_to_lp_not_found">Move to LP not found</string>
    <string name="title_inventory_movement_task">Inventory Movement Task</string>
    <string name="label_create_when">Create When</string>
    <string name="text_please_scan_move_to_lp_location">Please Scan Move To LP/Location</string>
    <string name="msg_move_to_lp_location_not_found">Move to LP/Location not found</string>
    <string name="text_select_barcode_type">Select Barcode Type</string>
    <string name="text_move_to">Move To</string>
    <string name="text_scan_lp_location">Scan LP/Location</string>
    <string name="text_collect_entire_pallet_lp">Collect Entire LP(Pallet)</string>
    <string name="text_move_entire_pallet_lp">Move Entire LP(Pallet)</string>
    <string name="title_inventory_movement_task_list">Inventory Movement Task List</string>
    <string name="text_drop_success">Drop Success</string>
    <string name="msg_cellected_item_not_found">Collected item not found</string>
    <string name="text_create_move_to_lp">Create move to LP</string>
    <string name="sn_not_found">SN not found</string>
    <string name="sn_not_found_for_current_item">SN not found for current item</string>
    <string name="text_not_a_sn">Not a serial number</string>
    <string name="title_suggestion_for_drop">Suggestion for Drop</string>
    <string name="msg_force_close_collect_step">Collect qty less than task require qty,force close?</string>
    <string name="msg_force_close_drop_step">Drop qty less than task require qty,force close?</string>
    <string name="txt_lot_num">LotNo: %1$s</string>
    <string name="msg_movement_diff_from_lp">SN:%1$s is from different LP, please check!</string>
    <string name="msg_movement_diff_sn_lot_no">SN:%1$s: LotNo: %2$s not match, please submit first!</string>
    <string name="label_inventory_movement_collect_list">collect list:</string>
    <string name="label_inv_movement_force_close">Force close</string>
    <string name="msg_inv_movement_force_close_collect_step">\nDo you want to force close this collect step?</string>
    <string name="msg_inv_movement_force_close_drop_step">\nDo you want to force close this drop step?</string>
    <string name="msg_inv_movement_force_close_task">\nDo you want to force close this task?</string>
    <string name="title_inventory_batch_movement">Batch Movement</string>
    <!-- inventory movement end-->

    <!-- new pick task-->
    <string name="wrong_item_scanned">Wrong Item Scanned</string>
    <string name="please_re_scan_correct_item">Please re-scan correct item</string>
    <string name="report_issue">Report Issue</string>
    <string name="report_issue_success">Report Issue Success</string>
    <string name="issue_empty_location">Empty location</string>
    <string name="issue_inventory_shortage">Inventory shortage</string>
    <string name="issue_item_not_reach">Item cannot be reached</string>
    <string name="issue_wrong_item_location">Wrong item at location</string>
    <string name="amount_exceed">Amount Exceeded</string>
    <string name="pick_task_incomplete">Pick Task %1$s incomplete</string>
    <string name="pick_task_complete">Pick %1$s complete</string>
    <string name="search_item">SEARCH ITEM</string>
    <string name="start_next_task">START NEXT TASK</string>
    <string name="text_scan_no_suggest">SCAN ILP</string>
    <string name="text_next_item">Next Item</string>
    <string name="text_current_round">Current Round: </string>
    <string name="hint_scan_upc">Scan UPC/Item Name</string>
    <string name="hint_scan_item">Item Num</string>
    <string name="hint_scan_sn">Item SN</string>
    <string name="hint_scan_tracking">Item Tracking</string>
    <string name="hint_scan_lp">ILP</string>
    <string name="hint_scan_by_no_suggest">ILP</string>
    <string name="msg_unfinish_lp">No suggestion.Do you want to search inventory for picking?</string>
    <string name="title_unfinish_lp">No Suggestion</string>
    <string name="text_please_scan_or_input_clp">Please scan or input CLP</string>
    <string name="text_please_scan_clp">Please scan CLP</string>
    <string name="text_please_scan_or_input_upc">Please scan or input UPC</string>
    <string name="text_please_select_order">Please select order</string>
    <string name="text_re_scan">Re-Scan</string>
    <string name="sort_by_dn">Sort By DN</string>
    <string name="text_back_to_task">Back To Task</string>
    <string name="text_not_yet_pick">Not Yet Pick</string>
    <string name="text_by_location">By Location</string>
    <string name="text_by_status">By Status</string>
    <string name="text_by_lp">By LP</string>
    <string name="text_by_history">By History</string>
    <string name="text_new_pick_task_view_detail">Pick %1$s View Detail</string>
    <string name="text_time_stamped">Time Stamped</string>
    <string name="text_scan_location">Scan Location</string>
    <string name="text_confirm_location">Confirm Location</string>
    <string name="bind_order_with_lp_start_pick">Bind Order With LP success.Start pick...</string>
    <string name="text_pick_success">Pick Success</string>
    <string name="text_inventory_issue_dialog_title">Inventory Issue</string>
    <string name="text_pick_issue_dialog_message">Pick has problem.Do you want to search inventory for picking?</string>
    <string name="text_order_is_already_pick">This order is already picked.</string>
    <string name="text_item_not_found_in_ilp">Item not found in this ILP</string>
    <string name="text_pick_by_pallet">Pick by Pallet</string>
    <string name="text_item_picked">Item Picked</string>
    <string name="text_item_unit_is_diff">Unit of Item is different</string>
    <string name="voice_go_to_location">Go to location </string>
    <string name="voice_pick">Pick </string>
    <string name="voice_scan_upc">Scan UPC </string>
    <string name="voice_scan_clp">Scan CLP </string>
    <string name="text_scan_clp_or_slp">Scan CLP or SLP</string>
    <string name="voice_scan_ilp">Scan ILP </string>
    <string name="voice_scan_sn">Scan SN</string>
    <string name="voice_sn_duplicate">Duplicate SN</string>
    <string name="voice_sn_amount_is_enough">SN Amount is enough</string>
    <string name="voice_scan_location">Scan location </string>
    <string name="voice_pick_success_next_item">Pick Success.Next Item</string>
    <string name="voice_invalid_check_num">Invalid checking number</string>
    <string name="voice_scan_sn_success">%1$s Serial number Scan Success</string>
    <string name="text_paired_printer">Paired printer %1$s</string>
    <string name="text_no_find_printer">No find printer</string>
    <string name="text_scan_uppercase">SCAN</string>
    <string name="text_upc_code">UPC Code</string>
    <string name="text_item_label">Item Label</string>
    <string name="text_serial_number">Serial Number(SN)</string>
    <string name="text_other">Other</string>
    <string name="text_print_all">Print All</string>
    <string name="text_print_selected">Print Selected</string>
    <string name="text_number_of_copy">Number of Copy</string>
    <string name="text_collect_reprint">Collect &amp; Reprint</string>
    <string name="text_barcode_label">Barcode Label</string>
    <string name="text_barcode_no_recognized">Barcode not in system</string>
    <string name="text_no_select_code">no select code</string>
    <string name="text_no_empty_code">code is empty</string>
    <string name="text_must_scan_or_input_sn">must scan or input at least one serial number</string>
    <string name="text_please_scan_sn">Scan Serial Number</string>
    <string name="text_duplicate_sn">Duplicate SN</string>
    <string name="text_invalid_upc">Invalid UPC</string>
    <string name="text_no_find_item">No find ,please scan or input ILP</string>
    <string name="hint_scan_clp_tracking">CLP/Tracking#</string>
    <string name="text_cc_task">Continue CC Task</string>
    <string name="text_create_cc_task">Create CC Task</string>
    <string name="text_lp_configuration_no_match_message">LP Configuration No Match,do you want to create CC task?</string>
    <string name="text_task_over_confirm_message">CC task created,do you want to take over %1$s?</string>
    <string name="text_create_cc_task_success">Create CC Task %1$s Success!</string>
    <string name="text_lp_configuration_no_match_continue">LP Configuration No Match,do you want to continue CC task?</string>
    <string name="text_cannot_be_recognize">can not recognize, scan again</string>
    <string name="text_can_not_pick_to_lp">can not pick to %1$s</string>
    <string name="text_barcode_is_empty">barcode is empty</string>
    <string name="text_repalletized">Repalletized</string>
    <string name="text_task_has_not_sn">This Task has no serial number</string>
    <string name="text_no_need_check_location">No need check Location</string>
    <string name="text_exit_pick_tip">Sn not save,do you want to exit picking?</string>
    <string name="text_count_unshipped_lp">Count Unshipped LP</string>
    <string name="text_already_submited">already submitted</string>
    <string name="text_reopen_step">Reopen Step</string>
    <string name="text_source_lp">Source LP</string>
    <string name="text_printer_setting">Printer Setting</string>
    <string name="text_command">Command</string>
    <string name="please_input_command">Please input command</string>
    <string name="text_processing">Processing....</string>
    <string name="text_command_processed">Command Processed</string>
    <string name="label_yes_uppercase">YES</string>
    <string name="label_no_uppercase">NO</string>
    <string name="please_select_lp_to_pick">Please select one LP to pick</string>
    <string name="please_select_lp">Please select one LP</string>
    <string name="text_order_of_lp_null">The order of this LP is null</string>
    <string name="text_invalid_sn">Invalid SN</string>
    <string name="text_invalid_sn_status">Invalid SN Status: %1$s</string>
    <string name="item_no_match">Item no match</string>
    <string name="sn_is_not_expected">This SN %1$s is not expected</string>
    <string name="text_invalid_sn_lot_no_illegal">Invalid SN,lot number illegal</string>
    <string name="text_invalid_sn_num_lot_no_illegal">Invalid SN:%1$s,lot number illegal,need lot number:%2$s</string>
    <string name="text_sn_has_already_picked">SN %1$s has already picked</string>
    <string name="text_invalid_ilp">Invalid ILP</string>
    <string name="invalid_lp_format_wrong">Invalid LP,format wrong</string>
    <string name="text_item_not_found">Item not found</string>
    <string name="text_title_not_found">Title not found</string>
    <string name="msg_submit_error">Submit Error</string>
    <string name="text_pick_complete">Pick Complete</string>
    <string name="text_pick_force_close">Pick Force Close</string>
    <string name="text_scan_is_empty">scan is empty</string>
    <string name="text_please_submit">Please Submit</string>
    <string name="text_round_weight_over">Round over weight,check again</string>
    <string name="text_order_weight_over">Order over weight,check again</string>
    <string name="text_scan_ilp_or_sn">SCAN ILP/SN</string>
    <string name="text_whether_round_finish">Whether round: %1$s finish?</string>
    <string name="text_whether_change_round">Whether change ROUND to %1$s?</string>
    <string name="text_inventory_lock_by_other_task">Inventory may lock by other task</string>
    <string name="text_close_step_done">CLOSE PICK STEP</string>
    <string name="text_round">Round</string>
    <string name="text_new_pick_item_picked">Item Picked:</string>
    <string name="text_new_pick_from_location">From Location:</string>
    <string name="text_new_pick_pick_qty">Picked QTY:</string>
    <string name="text_new_pick_pick_unit">Picked Unit:</string>
    <string name="text_new_pick_sn">SN: </string>
    <string name="text_actual_pick">actual picked </string>
    <string name="msg_get_error_or_null">Return to location can not be empty!</string>
    <string name="msg_return_to_inventory_confirm">Return this picked to inventory?</string>
    <string name="text_submit_previous_lp">Submit previous lp</string>
    <string name="text_submit_previous_item">Submit previous Item</string>
    <string name="title_sn_not_match">SN Not Match</string>
    <string name="text_have_switch_round">You have switched to ROUND No %1$s.</string>
    <string name="lp_no_found_in_system">No found LP: %1$s in system,do you want to continue this LP?</string>
    <string name="item_title_or_lot_no_not_match_in_system">Inventory Title not match in system,do you want to continue this LP?</string>
    <string name="inventory_title_not_match">Inventory Title not match</string>
    <string name="msg_do_you_want_to_continue_this_sn">%1$s,do you want to continue this SN:%2$s?</string>
    <string name="msg_do_you_want_to_continue_this_lp">%1$s,do you want to continue this LP?</string>
    <string name="sn_has_not_match_in_system">%1$s SNs not match with system record, click YES to continue</string>
    <string name="sn_title_not_match_in_system">Inventory Title not match in system,do you want to continue this SN:%1$s?</string>
    <string name="sn_title_not_match">SN Title Not Match</string>
    <string name="sn_status_is_shipped">SN Status Shipped</string>
    <string name="sn_status_is_shipped_do_make_pallet">SN Status is shipped,do you want to continue this SN:%1$s?</string>
    <string name="sn_not_match">SN Not Match</string>
    <string name="sn_invalid">SN Invalid</string>
    <string name="list_has_invalid_sn_please_handle">List has invalid SN,please remove it before submit.</string>
    <string name="pick_no_found_item">Pick No Found Item</string>
    <string name="sn_no_found_in_system">No found SN: %1$s in system,do you want to continue this SN?</string>
    <string name="msg_confirm_sn">Confirm SN</string>
    <string name="sn_no_found_in_inventory">SN no found in inventory</string>
    <string name="sn_no_found">SN No Found</string>
    <string name="check_sn">Check SN</string>
    <string name="sn_lp_is_different">lp of this sn is different ,do you want to continue this SN:%1$s?</string>
    <string name="lp_is_different_submit_previous_lp">lp is different ,do you want to submit previous LP:%1$s?</string>
    <string name="lot_num">LotNo.: %1$s</string>
    <string name="please_print_clp_before_scan_sn">Please print CLP first before scan SN</string>
    <string name="pick_entire_pallet">Pick Entire Pallet</string>
    <string name="item_on_location">Item on location:</string>
    <string name="suggest_ilp">Suggested ILP:</string>
    <string name="scan_location_checking_num">Scan Location checking NO.</string>
    <string name="success_pick">Success Pick</string>
    <string name="from_lp_different">From LP Different</string>
    <string name="whether_submit_current_lp">Whether to submit the operation for the current LP，and continue next LP?</string>
    <string name="whether_give_up_current_lp">Whether to give up the operation for the current LP，and switch to next LP?</string>
    <string name="label_count_mark">Count:</string>
    <string name="confirm_qty_and_click_submit">Confirm Qty and click Submit.</string>
    <string name="switch_from_lp">Switch From LP</string>
    <string name="switch_to_another_lp">SN Not Submit.Do you want to switch to another LP to Pick?</string>
    <string name="title_allocate">Allocate</string>
    <string name="title_allocate_progress">Allocate Progress</string>
    <string name="title_no_progress">No Progress</string>
    <string name="msg_allocate_is_done">Allocate is done.</string>
    <string name="different_lp">Different LP</string>
    <string name="please_submit_previous_lp">Please submit previous LP</string>
    <string name="multiple_sn_inventory_select">Multiple SN found, Please select one</string>
    <string name="error_over_picked">Over Picked.</string>
    <string name="complete">complete</string>
    <!-- new pick task  -->

    <!-- new pick task pull step -->
    <string name="title_pick_pull_step">Pick Pull</string>
    <string name="text_sort_area">Sort Area</string>
    <string name="text_sorting_area">Sorting Area</string>
    <string name="text_enter_two_digit_code">Enter the 2 digit code on ILP</string>
    <string name="text_pick_pull_work_progress">STEP %1$s of %2$s </string>
    <string name="text_start_pack">Start Pack</string>
    <string name="text_done_pick_pull_step">DONE PICK PULL STEP</string>
    <string name="text_pick_pull_finish">This Location is finished</string>
    <string name="text_go_to_location_enter_two_digit">GO TO LOCATION %1$s\n ENTER LAST 2 DIGITS OF LP</string>
    <string name="text_please_select_sorting_area">Please select Sorting Area</string>
    <string name="text_scan_or_input_lp">Please scan or input LP</string>
    <string name="text_sort_by_time_line">Sort by Time Line</string>
    <string name="text_sort_by_item">Sort by Item</string>
    <string name="text_pick_pull_progress">Pick Pull Progress</string>
    <string name="text_no_lp_by_this_digit">No LP by this digit</string>
    <string name="text_this_lp_already_in_list">This LP already in list</string>
    <string name="text_this_lp_is_not_this_item">This LP is not this item</string>
    <string name="text_no_find_lp_in_inventory">No find this LP in the Inventory</string>
    <string name="done_pull_step_title">Done Pick Pull Step</string>
    <string name="done_pull_step_message">Do you want to close pick pull step now?</string>
    <string name="pick_pull_task_complete">Pick Pull  %1$s complete</string>
    <string name="pick_pull_task_force_close">Pick Pull  %1$s force closed</string>
    <string name="text_not_the_location">Not this Location</string>
    <string name="text_found_multiple_task_by">Found multiple task by</string>
    <string name="text_not_your_task">This is not your task</string>
    <string name="text_sorting_area_is_empty">Sorting Area is empty</string>
    <string name="please_close_load_step_then_start_count_unshipped">Please close loading step then start count unshipped</string>
    <string name="text_scan_sn_finished_do_you_want_to_put_to_pool">Scan SN finished, do you want to put to poll?</string>
    <string name="text_scan_sn_finished_do_you_want_to_add_to_target">Scan SN finished, do you want to add to target?</string>
    <string name="error_replenishment_task_please_create_new_one">Error replenishment task, please create a new one</string>
    <string name="title_make_pallet">Make Pallet</string>
    <string name="title_make_pallet_by_sn">Make Pallet By SN</string>
    <string name="msg_found_multiple_inventory">Found multiple inventory</string>
    <string name="msg_found_multiple_item">Found multiple item</string>
    <string name="sn_not_match_item">Sn not match item</string>
    <string name="sn_lp_not_match_from_lp">SN in %1$s not match from LP, please check!</string>
    <string name="tote_is_occupied">%1$s is binded to Order: %2$s</string>
    <string name="make_pallet_success">Make pallet success</string>
    <string name="msg_not_match_inventory_title">Not match with inventory title</string>
    <string name="test_pallet_no">Pallet No</string>
    <string name="label_add_item_line">New Item Line</string>
    <string name="label_lp_size">LP Size</string>
    <string name="select_lp_size">Select LP Size</string>
    <string name="hint_no_support_2_1_copy">No support 2X1 LP copy</string>
    <string name="text_not_found_in_inventory_do_you_want_to_adjust">not found in inventory, do you want to add a inventory?</string>
    <string name="msg_unit_not_found_can_not_adjust">unit not found, can not adjust</string>
    <string name="msg_please_double_check">please double check</string>
    <string name="msg_found_multiple_barcode">Found multiple barcode</string>
    <string name="msg_pick_submit_partial_lp">is from different LP, do you want to submit previous LP?</string>
    <string name="test_item_scanned">Item Scanned</string>
    <string name="text_remind_seasonal_pack">This Item has seasonal pack, please create new unit for it</string>
    <string name="twenty_fter">20\'</string>
    <string name="text_na">N/A</string>
    <string name="forty_fter">40\'</string>
    <string name="forty_h_feter">40\'H</string>
    <string name="forty_five_fter">45\'</string>
    <string name="forty_eight_fter">48\'</string>
    <string name="fifty_three_fter">53\'</string>
    <string name="label_equipment_size">Equipment Size</string>
    <string name="hint_please_select_equipment_size">Please select equipment size</string>
    <string name="hint_please_select_ship_method">Please select ship method</string>
    <string name="hint_please_select_hand_shotgun">Please select hand_shotgun</string>
    <!-- new pick task pull step -->

    <!-- pick progress activity -->
    <string name="pick_progress_count">Count: %1$s</string>
    <string name="pick_progress_item">Item: %1$s</string>
    <string name="pick_progress_qty">QTY: %1$s</string>
    <string name="pick_progress_required_qty">Required QTY: %1$s</string>
    <string name="pick_progress_picked_qty">Picked QTY: %1$s</string>
    <string name="pick_progress_need_pick">Need Pick: %1$s</string>
    <string name="pick_progress_location">Location: %1$s</string>
    <string name="pick_progress_suggest_qty_and_picked_qty">Suggested: %1$s | Picked: %2$s</string>
    <string name="pick_progress_status">Status: %1$s</string>
    <string name="pick_progress_pick_message">Pick Message</string>
    <!-- pick progress activity -->

    <!-- return picked activity -->
    <string name="text_scan_upc_to_verify_item">Scan UPC to verify item</string>
    <string name="text_scan_from_lp_or_sn">Scan From LP/SN</string>
    <string name="text_scan_from_lp_or_tracking">Scan From LP/Tracking#</string>
    <string name="msg_tracking_qty_no_match">Tracking# qty no match</string>
    <string name="text_scan_to_lp">Scan To LP</string>
    <string name="text_scan_to_location">Scan To Location</string>
    <string name="msg_scan_sn">Scan SN</string>
    <string name="text_no_upc">No UPC</string>
    <string name="text_return_from_lp">Return from LP:</string>
    <string name="txt_return_to_lp">Return to LP:</string>
    <string name="text_return_to_location">Return to location:</string>
    <string name="text_return_qty">Return QTY:</string>
    <string name="text_return_sn_list">Return SN list:</string>
    <string name="text_return_tracking_list">Return Tracking# list:</string>
    <string name="text_return_so_id_list">Return SOID list:</string>
    <string name="text_scan_or_input_sn">scan or input SN</string>
    <string name="text_print_new_lp">Print new LP</string>
    <string name="text_select_new_location">Select new location</string>
    <string name="text_return_pick_item">Return Pick %1$s</string>
    <string name="msg_please_scan_or_input_from_lp">Please scan or input return from LP!</string>
    <string name="text_select_to_return">Select to return</string>
    <string name="msg_please_scan_or_input_upc_to_verify_item">Please scan or input upc to verify item!</string>
    <string name="msg_please_scan_or_input_to_lp">Please scan or input return to LP!</string>
    <string name="msg_please_scan_or_input_to_location">Please scan or input return to location!</string>
    <string name="msg_wrong_return_qty">Wrong return qty!</string>
    <string name="msg_wrong_sn">LP: %1$s not contain this SN, please check!</string>
    <string name="txt_pick_from_lp">Pick from LP: </string>
    <string name="msg_item_verified">Item verified</string>
    <string name="sync_gis_nofound">GIS not found</string>
    <!-- return picked activity -->

    <!--barcode print start-->
    <string name="label_sn_no">Serial No.</string>
    <string name="label_model_no">Model No.</string>
    <string name="label_part_no">Part No.</string>
    <string name="label_clear">Clear</string>
    <string name="label_clear_all">Clear all</string>
    <string name="error_sn_no_error">Sn No. error</string>
    <string name="error_part_no_error">Part No. error</string>
    <string name="error_item_name_error">Model No. is empty</string>
    <string name="label_save">Save</string>
    <string name="title_printer">Printer list</string>
    <string name="tts_scan_sn_no">scan serial number</string>
    <string name="tts_scan_model_no">scan model number</string>
    <string name="tts_scan_part_no">scan part number</string>
    <string name="text_set_pick_to_lp_type">Set Pick To LP Type</string>
    <string name="text_report_partial_pallet_issue">Report Partial Pallet Issue</string>
    <string name="text_reprint_so_id">Reprint SOID</string>
    <string name="please_select_pick_to_lp_type">Plase select pick to LP type</string>
    <string name="please_select_pack_type">Please select pack type</string>
    <string name="please_select_clp">Please select CLP</string>
    <string name="text_material_management">Material Management</string>
    <string name="text_ea">EA</string>
    <string name="hint_query_counting_sheet">Counting Sheet is required, do you want to add counting sheet?</string>
    <string name="text_load_bar">Load Bar</string>
    <string name="text_strap">Strap</string>
    <string name="text_shipper">Shipper</string>
    <string name="text_need_config_lp">need to set LP configuration</string>
    <string name="text_status_need_to_be_picked_or_packed">status need to be picked/packed</string>
    <string name="text_no_pro">No Pro</string>
    <string name="text_add_load_pro_no">Add Load Pro No</string>
    <string name="text_air_bag">Air Bag</string>
    <string name="msg_please_retry_after_a_while">Please retry after a while</string>
    <string name="text_does_not_have_available_inventory">doesn\'t hava available inventory</string>
    <string name="text_available_label_not_found">Available inventory not found, can not print 4X6 label</string>
    <string name="msg_please_remove_shipping_sn_first">have been collected for shipping, please remove it first</string>
    <string name="text_rework_label">Rework Label</string>
    <string name="text_customized_label">Customized Label</string>
    <string name="text_carton_number_label">SSCC Carton Label</string>
    <string name="title_print_rework_label">Print Rework Label</string>
    <string name="hint_please_input_label_count">Please input label count</string>
    <string name="title_print_customized_label">Customized Label Print</string>
    <string name="text_print_btn">Print</string>
    <string name="text_detail_item_label">Detail Item Label</string>
    <string name="title_print_detail_item_label">Print detail item label</string>
    <string name="label_customized_label_print_copies">Copies</string>
    <string name="label_customized_label_print_data">Prepare Data</string>
    <string name="label_customized_label_print_label_template">Label Template</string>
    <string name="label_customized_label_print_customer">Customer</string>
    <string name="hint_customized_label_print_copies">1</string>
    <string name="toast_customized_label_print_print_success">Print success</string>
    <string name="hint_customized_label_print_enter_customer">please enter customer</string>
    <string name="hint_customized_label_print_enter_customer_is_empty">template is empty,please enter customer to select template</string>
    <string name="toast_customized_label_print_not_found_template">not found template for %s</string>
    <string name="hint_please_input_capacity">Please input capacity</string>
    <!--barcode print end-->

    <!--Cycle Count start-->
    <string name="title_cycle_count">Cycle Count</string>
    <string name="title_cycle_count_new_item">Cycle Count - New Item</string>
    <string name="title_recount">Recount</string>
    <string name="msg_all_counted_close_task">All locations have been counted. Do you want to close the task?</string>
    <string name="text_please_enter_the_start_off_location">Please Enter The Start Off Location</string>
    <string name="text_please_enter_the_lp">Please Enter The LP</string>
    <string name="text_location_enter_no_within_range">Location is not within the Cycle Count Range!Please Enter the Location Again!</string>
    <string name="text_location_enter_invalid">Invalid Location! Please Enter Again!</string>
    <string name="text_lp_enter_invalid">Invalid LP! Please Enter Again!</string>
    <string name="text_item_enter_invalid">Invalid Item! Please Enter Again!</string>
    <string name="text_wrong_qty">Wrong QTY!</string>
    <string name="text_please_scan_item_no">Please Scan Item No.</string>
    <string name="text_please_enter_count_qty">Please Enter Count QTY</string>
    <string name="text_please_re_enter_count_qty">Please Re-Enter Count QTY</string>
    <string name="text_cycle_count_task">Cycle Count Task %1$s</string>
    <string name="text_new_item">New Item</string>
    <string name="text_please_enter_the_following">Please Enter the following:</string>
    <string name="text_lot">Lot</string>
    <string name="text_exp">Exp.</string>
    <string name="text_inventory_count">Inventory Count</string>
    <string name="text_recount">Recount</string>
    <string name="label_count">Count</string>
    <string name="text_please_go_to_location">Please Go To Location</string>
    <string name="error_must_complete_cycle_count">This Cycle Count Task must be completed before you can proceed</string>

    <string name="text_multiple_item_found">Multiple item found</string>
    <string name="hint_enter_lp"> Enter LP </string>
    <string name="hint_enter_lot">Enter lot #</string>
    <string name="hint_enter_exp">Enter Exp Date</string>
    <string name="hint_enter_mfg_date">Enter Mfg Date</string>
    <string name="label_lps_mark">LPs:</string>

    <string name="title_count_history">Count History</string>
    <string name="title_count_record">Count Record</string>
    <!-- gis start-->
    <string name="title_activity_gis_maps">Map</string>
    <string name="text_camera">Camera</string>
    <string name="text_download_rtsp_player">Download RTSP Player</string>
    <string name="msg_gis_not_support_for_this_facility">GIS not support for this facility</string>
    <string name="text_current_inventory">Current Inventory</string>
    <string name="text_inventory">Inventory</string>
    <string name="msg_current_inventory_location_not_fount">Current inventory locaiton not found</string>
    <string name="label_desc">Desc</string>
    <string name="text_show_item_location">Show item location on map</string>
    <string name="text_pick_way">Pick way</string>
    <string name="text_road">Road</string>
    <string name="text_road_heat_map">Road Heat Map</string>
    <string name="test_intelligence">intelligence</string>
    <string name="text_inventory_playback">Inventory Playback</string>
    <string name="text_playback">Playback</string>
    <string name="text_demand">Demand</string>
    <string name="text_load_status">Load Status</string>
    <string name="text_truck">Truck</string>
    <string name="title_intelligence_gis">Intelligence GIS</string>
    <string name="text_state">State</string>
    <string name="text_receipt_qty">Receipts</string>
    <string name="text_order_qty">Order</string>
    <string name="text_order_no">Order No.</string>
    <string name="text_inventory_qty">Inventory</string>
    <string name="error_url_error_or_no_browser">URL error or No Browser</string>
    <string name="text_please_select_browser">Please select browser</string>
    <!-- gis end-->

    <!--inventory recount start-->
    <string name="title_location_with_lp">Location/LP</string>
    <string name="title_enable_sn">Enable SN</string>

    <string name="msg_empty_location_confirm">Empty Location ? </string>
    <string name="msg_double_check_that_this_location_is_empty_before_proceeding">Double-check that this location is empty before proceeding.</string>

    <!--Cycle Count end-->

    <!--pick to slp start-->
    <string name="txt_pallet">Pallet</string>
    <string name="txt_uom">UOM:</string>
    <string name="txt_uom_ea">EA</string>
    <string name="txt_pick_from_location">Pick From Location:</string>
    <string name="text_pick_from_lp">Pick From LP:</string>
    <string name="text_picked_message">Picked Message:</string>
    <string name="text_required_qty">Required Qty:</string>
    <string name="text_picked_qty">Picked Qty:</string>
    <string name="text_unpicked_qty">Unpicked Qty:</string>
    <string name="text_picked_item">Picked Item</string>
    <string name="text_shipment_unit_material">Shipment Unit Material</string>
    <string name="msg_please_select_material">Please select material</string>
    <string name="msg_invalid_item_group_name">Invalid item group name: Pallet (Pick to SLP), please check</string>
    <string name="msg_not_need_to_print">Not need to print, task has unStage SLP:%1$s</string>
    <string name="btn_add_new_item">Add new item</string>
    <string name="btn_stage_slp">Stage SLP</string>
    <string name="label_pick_task">Pick Task:</string>
    <!--pick to slp end-->

    <!--inventory recount start-->
    <string name="title_inventory_recount_task_list">Inventory Recount Task List</string>
    <string name="text_search_truck">Search Truck</string>
    <string name="text_license_plate">License Plate</string>
    <string name="text_play">Play</string>
    <string name="text_to">To</string>
    <string name="text_query">Query</string>
    <string name="hint_please_input_from_time">Please input from time</string>
    <string name="hint_please_input_to_time">Please input to time</string>
    <string name="text_found">Found</string>
    <string name="hint_user_load_fail_please_re_login">User information load fail, please re-login</string>
    <string name="text_sn_title_no_match">SN title no match</string>
    <string name="hint_item_customer_no_match">Item customer no match</string>
    <string name="text_demand_qty">Demand QTY</string>
    <string name="text_number_zero">0</string>
    <string name="text_50k">50K</string>
    <string name="text_rma">RMA</string>
    <string name="text_model">Model</string>
    <string name="text_service_status">Service Status</string>
    <string name="text_recycle_collection_center">Recycle Collection Center</string>
    <string name="text_osr_tech">OSR Tech</string>
    <string name="title_print_count_sheet">Print Count Sheet</string>
    <string name="please_select_suggest_location">Please select suggest location</string>
    <!--inventory recount end-->

    <!--Transfer Out start-->
    <string name="title_transfer_out">Transfer Out</string>
    <string name="title_transfer_out_task">Transfer Out Task</string>
    <string name="title_transfer_out_task_list">Transfer Out Task List</string>
    <string name="label_to_facility">To Facility</string>
    <string name="label_from_facility">From Facility</string>
    <string name="msg_lp_has_not_been_transferred_out_yet">LP has not been transferred out yet</string>
    <string name="msg_lp_has_been_transferred_out">LP has been transferred out</string>
    <string name="msg_close_transfer_out_task">Close Transfer Out Task</string>
    <!--Transfer Out end-->

    <!--Transfer In start -->
    <string name="title_transfer_in">Transfer In</string>
    <string name="title_transfer_in_task">Transfer In Task</string>
    <string name="title_transfer_in_task_list">Transfer In Task List</string>
    <string name="msg_invalid_taskid">Invalid task id, please input again</string>
    <string name="hint_input_task_barcode">input or scan task barcode</string>
    <string name="msg_lp_invalid">input lp is not valid</string>
    <string name="btn_stage">STAGE</string>
    <string name="label_next">NEXT</string>
    <string name="msg_load_task_first">please scan or input task id first</string>
    <string name="msg_input_location_id_first">please input or scan locatin id first</string>
    <string name="stage_succ">stage success</string>
    <string name="label_input_or_scan_lp">Input Or Scan Lp</string>
    <string name="msg_input_lp">please input or scan lp</string>
    <string name="msg_not_find_receive_lps">Not found receive lps</string>
    <string name="msg_lp_has_not_been_transferred_in_yet">LP has not been transferred in yet</string>
    <string name="msg_close_transfer_in_task">Close Transfer In Task</string>
    <string name="msg_lp_has_been_transferred_in">LP has been transferred in</string>
    <string name="msg_lp_not_needed_for_transfer_in">LP not needed for transfer in</string>
    <string name="text_new_label">New Label</string>
    <string name="text_need_scan">Need Scan</string>
    <string name="msg_zplcode_missing">zplCode missing</string>
    <!--Transfer In end -->

    <string name="msg_pick_failed">Pick Failed</string>
    <string name="label_tote_is_full">tote is full</string>
    <string name="text_pick_sort_work">Pick Sort Work</string>
    <string name="msg_please_scan_picked_tote_lp">Please scan picked Tote/LP</string>
    <string name="msg_please_scan_sort_to_tote">Please scan sort to Tote</string>
    <string name="title_pick_sort">Pick Sort</string>
    <string name="summary_pick_sort">Sort pick item before pack</string>
    <string name="title_move_every_thing_to_tote_box">Move everything from sorted tote to tote box</string>
    <string name="msg_please_scan_tote_box">Please scan tote box</string>
    <string name="msg_please_scan_sort_location">Please scan sort location</string>
    <string name="msg_scan_location_to_stage_tote">Stage location to stage tote</string>
    <string name="msg_please_scan_sorted_tote">Please scan sorted tote</string>
    <string name="text_invalid_tote">Invalid tote</string>
    <string name="text_put_on_conveyor">Sort completed, put on conveyor</string>
    <string name="text_put_to_sorting_location">Sort no completed, put to sorting location</string>
    <string name="text_lead_time">Lead Time</string>
    <string name="label_ready_for_pack">Ready for pack</string>
    <string name="text_pending_pick_task">Pending Pick Task</string>
    <string name="text_pick_sort_view">Pick Sort View</string>
    <string name="text_scan_tote_lp">Scan Tote/LP</string>
    <string name="text_new_sort_tote">New Tote</string>
    <string name="text_color_format">color : %1$s</string>
    <string name="text_h">h</string>
    <string name="text_min">min</string>
    <string name="text_first">First</string>
    <string name="title_sorting_tote">Sorting Tote/LP :</string>
    <string name="title_sort_to_tote_id">Sort To Tote ID :</string>
    <string name="title_suggest_tote_color">Suggest Tote Color:</string>
    <string name="tile_release_tote_mark">Release Tote:</string>
    <string name="hint_please_scan_the_original_tote_id">Please scan the original Tote ID</string>
    <string name="title_suggest_sorting_Location">Suggest Sorting Location:</string>
    <string name="title_selected_sorting_Location">Selected Sorting Location:</string>
    <string name="text_create_sorting_job">Create Sorting Job</string>
    <string name="text_sort_and_combine">Sort And Combine</string>
    <string name="title_carrier_pick_up">Carrier Pick UP:</string>
    <string name="title_picking_tag">Picking Tag: </string>
    <string name="label_go_to_mark">Go to: </string>
    <string name="btn_not_full">Not Full</string>
    <string name="title_put_back_to_tote_id">Put Back To Tote ID :</string>
    <string name="title_from_tote_id_mark">From Tote ID :</string>
    <string name="title_put_back_sorting">Put Back Sorting</string>
    <string name="carrier_pickup_label">Carrier PickUp Label:</string>
    <string name="carrier_pickup_qty_label">Print Qty:</string>
    <string name="test_remove_all">Remove All</string>
    <string name="msg_are_sure_remove_all_sn">Are you sure to remove all scanned SN/RFID?</string>

    <!-- More Fragment -->
    <string name="folder_inbound">Inbound</string>
    <string name="folder_outbound">Outbound</string>
    <string name="folder_inventory_control">Inventory Control</string>
    <string name="make_pallet_without_SN">Make Pallet w/o SN</string>
    <string name="folder_admin">Admin</string>
    <string name="folder_asset_management">Asset Management</string>
    <string name="folder_material">Material Management</string>

    <!-- More Fragment -->


    <!-- Inventory Issue -->
    <string-array name="pick_task_location_issue_reason_codes">
        <item>Location Unreachable</item>
        <item>Location Not Found</item>
        <item>Location Barcode Not Scannable</item>
        <item>Skip for Now</item>
        <item>Empty Location</item>
        <item>Inventory Not Found</item>
    </string-array>
    <string-array name="pick_task_item_issue_reason_codes">
        <item>Item Not Found</item>
        <item>Item Qty Short</item>
        <item>Item Damage</item>
        <item>Other issue</item>
        <item>Inventory issue</item>
    </string-array>

    <string-array name="pick_task_v1_issue_reason_codes">
        <item>Location issue</item>
        <item>Item Not Found</item>
        <item>Item Qty Short</item>
        <item>Item Damage</item>
        <item>Skip for Now</item>
    </string-array>

    <string-array name="put_away_task_location_issue_reason_codes">
        <item>Location Not Found</item>
        <item>Location Unreachable</item>
        <item>Occupied</item>
        <item>Location Barcode Not Scannable</item>
        <item>Doesn\'t Fit</item>
    </string-array>

    <string-array name="consolidation_movement_task_location_issue_reason_codes">
        <item>Location Full</item>
        <item>Location Issue</item>
        <item>Location Unreachable</item>
    </string-array>

    <string name="title_on_screen_opportunity_count">On-Screen Opportunity Count</string>
    <string name="title_count_detail">Count Detail</string>
    <string name="label_quantity_mark">Quantity:</string>
    <string name="btn_ignore">Ignore</string>
    <string name="msg_inventory_issue_not_configured_yet_please_contact_your_supervisor">Inventory Issue not configured yet,please contact your supervisor</string>

    <!-- Inventory Issue -->

    <!-- Epi Kitting -->
    <string name="text_first_sn">1st Serial Number</string>
    <string name="text_second_sn">2nd Serial Number</string>
    <string name="hint_scan_first_sn">Scan first SN</string>
    <string name="hint_scan_second_sn">Scan second SN</string>
    <string name="text_auto_print">Auto Print</string>
    <string name="error_scan_invalid">SN Invalid, please try again</string>
    <string name="text_please_complete_all_load">Please complete all load</string>
    <string name="please_scan_lot_no">Please scan Lot#</string>
    <string name="text_lot_number_select_location"> Lot# doesn\'t belong to this location, please select location </string>
    <string name="type_not_support">This type is not supported</string>
    <string name="entire_drop">Entire Drop</string>
    <string name="text_select_dn">Select DN</string>
    <string name="message_put_back_qty_invalid">Put back QTY is greater than %1$s picked QTY(%2$s).</string>

    <!-- Epi Kitting -->

    <!--collect item info -S -->
    <string name="title_collect_item_info">Item Info Collect</string>
    <string name="label_collect_item_id_flag">Item：%s</string>
    <string name="collect_item_info_active_item_btn">active item</string>
    <string name="toast_collect_item_info_item_not_found">item not found</string>
    <string name="label_collect_item_info_desc">Description:</string>
    <string name="label_collect_item_info_short_desc">Short Description:</string>
    <string name="label_collect_item_info_upc">UPC:</string>
    <string name="label_collect_item_info_uom_dims_weights"><![CDATA[UOM, Dims, & Weights]]></string>
    <string name="label_collect_item_info_small_parcel_packaging">Small Parcel Packaging:</string>
    <string name="label_small_parcel_packaging">Small Parcel Packaging</string>
    <string name="label_select_small_parcel_packaging">Select Small Parcel Packaging</string>
    <string name="btn_collect_item_info_update">update</string>
    <string name="msg_collect_item_info_update_desc">add item description to complete the item info</string>
    <string name="msg_collect_item_info_update_short_desc">add item short description to complete the item info</string>
    <string name="msg_collect_item_info_update_upc">add item UPC to complete the item info</string>
    <string name="msg_collect_item_info_update_uom"><![CDATA[add item UOM, Dims, & Weights to complete the item info]]></string>
    <string name="msg_collect_item_info_update_small_parcel_packaging">add item small parcel packaging to complete the item info</string>
    <string name="label_collect_item_info_updated_desc_as">Description Updated as:\n %s</string>
    <string name="label_collect_item_info_updated_short_desc_as">Short Description Updated as:\n %s</string>
    <string name="label_collect_item_info_updated_small_parcel_packaging_as">Small Parcel Packaging Updated as:\n %s</string>
    <string name="hint_collect_item_info_scan_or_enter_desc">Scan/Enter Item Description</string>
    <string name="hint_collect_item_info_scan_or_enter_short_desc">Scan/Enter Item short Description</string>
    <string name="hint_collect_item_info_scan_or_enter_upc">Scan/Enter the UPC</string>
    <string name="hint_collect_item_info_upc_error">upc are made up of 12 digits</string>
    <string name="label_collect_item_info_ea_upc_flag">EA UPC</string>
    <string name="label_collect_item_info_case_upc_flag">Case UPC</string>
    <string name="label_collect_item_info_update_ea_upc_succeed">update EA UPC succeed</string>
    <string name="label_collect_item_info_update_case_upc_succeed">update Case UPC succeed</string>
    <string name="msg_collect_item_info_add_more_uom">Item updated! add more UOM?</string>
    <string name="msg_collect_item_update_and_active_succeed"><![CDATA[Item updated & Activated!Continue to Receiving. ]]></string>
    <string name="label_collect_item_info_cubic_scanner">Read from Cubi Scanner</string>
    <string name="msg_collect_item_info_item_is_inactive">Item %s is inactive, proceed to collect item info?</string>
    <string name="msg_collect_item_info_item_is_active_miss_info">Item %s missing some of criteria, proceed to collect item info?</string>
    <string name="hint_collect_item_print_qty">1</string>
    <string name="label_collect_item_info_lp_config">LP Config/TiHi</string>
    <string name="msg_collect_item_info_new_lp_config">New LP Config</string>
    <string name="msg_collect_item_info_new_lp_config_tip">Please select default LP config for each UOM</string>
    <!--collect item info -E -->


    <!--material manager start-->

    <string name="title_material_receiving">Receiving</string>
    <string name="summary_material_receiving">Material Receiving</string>
    <string name="title_material_check_out">Material Check Out</string>
    <string name="summary_material_check_out">Material Check Out</string>
    <string name="title_material_inventory_search">Inventory Search</string>
    <string name="summary_material_inventory_search">Inventory Search</string>
    <string name="title_material_inventory_count">Inventory Count</string>
    <string name="summary_material_inventory_count">Inventory Count</string>
    <string name="title_material_barcode_print">Barcode Print</string>
    <string name="summary_material_barcode_print">Barcode Print</string>
    <string name="text_material_manager_next">NEXT</string>
    <string name="text_material_manager_submit">Submit</string>
    <string name="text_material_manager_continue">Continue</string>
    <string name="text_material_manager_close_receipt">Close Receipt</string>
    <string name="text_material_cancel">Cancel</string>
    <string name="label_material_enter_po_num">Enter Receipt Number</string>
    <string name="msg_enter_po_num">Please Enter Receipt Number</string>
    <string name="material_receiving_please_scan">Please Scan Item</string>
    <string name="material_receiving_label_hint_qty">Enter Qty</string>
    <string name="toast_no_valid_units">There are no valid unit.</string>
    <string name="material_receiving_done">Item %s received!\n Continue Receiving?</string>
    <string name="material_receiving_enter_storage_loc">Enter Pick Location</string>
    <string name="msg_material_receiving_enter_storage_loc">Please Enter Storage Location</string>
    <string name="scan_item_upc_warn">Warning!!\n Dectected Scanned UPC is different from item %1$s! Next to continue with new UPC,Cancel to Scan again</string>
    <string name="scan_item_warn">Dectected scanned item is different from receiving item %s</string>
    <string name="scan_item_not_belong_customer">Item %s does not belong to Customer %s.</string>
    <string name="toast_tast_status">this task status is %s</string>
    <string name="toast_material_receiving_host_account_empty">Material receiving can only be done by the Host Account, but the Host Account is not found,please create it first</string>
    <string name="toast_not_a_receipt_for_hostaccount">this is not a reciept for Host Account</string>
    <string name="toast_multiple_tasks">there are multiple tasks</string>
    <string name="toast_add_item_line">item line is empty</string>
    <string name="toast_no_itemline_selected">unselected item</string>
    <string name="material_receiving_text_quantity">QTY - %1$s %2$s</string>
    <string name="text_material_receiving_ponumb">Receipt %s</string>
    <string name="text_material_receiving_itemline_id">Item %s</string>
    <string name="toast_multiple_locations">there are multiple locations</string>
    <string name="toast_material_receiving_not_a_pick_location">this is not a pick location</string>
    <string name="toast_material_receiving_not_found_a_pick_location">pick location not found</string>
    <string name="label_material_receiving_close_receipt">Close Receipt</string>
    <string name="menu_material_checkout_select_customer">Select Host Account</string>
    <string name="title_material_checkout_selec_item">more than 1 item detected! please select from the following</string>
    <string name="title_material_checkout_selec_loc">more than 1 inv loc detected! please select from the following</string>
    <string name="toast_material_checkout_item_not_for_hostaccount">this is not a item spec for Host Account</string>
    <string name="toast_material_checkout_inventory_empty">inventory not found</string>
    <string name="label_material_receiving_select_location">select location</string>
    <string name="toast_material_receiving_no_found_item_line">item line not found</string>
    <string name="toast_material_checkout_already_on_the_list">this item spec is already on the list</string>
    <string name="toast_material_checkout_greater_or_equal_zero">the qty has to be greater than or equal to zero</string>
    <string name="toast_material_checkout_more_than_total">the qty detected exceeds the total</string>
    <string name="msg_material_checkout_delete_item">remove Item %1$s , Location %2$s</string>
    <string name="toast_material_checkout_adjust_item_empty">the list is empty, please scan checkout material</string>
    <string name="toast_material_checkout_succeed">check out succeed</string>
    <string name="label_material_checkout_desc_item">Item</string>
    <string name="label_material_checkout_desc_loc">Location</string>
    <string name="label_material_checkout_select_itemspec">select item</string>
    <string name="label_material_checkout_select_customer">select Host Account</string>
    <string name="title_material_checkout_input_qty">check Item %1$s out from Location %2$s, QTY - %3$s %4$s,please input qty</string>
    <string name="toast_material_checkout_at_least_greater_zero">at leasat one item checked out greater than zero</string>
    <string name="toast_material_checkout_hostaccount_not_found">Material check out can only be done by the Host Account,the Host Account is not found</string>
    <string name="title_material_checkout_search_host_account">Material check out can only be done by the Host Account,Please search first and then select</string>
    <string name="toast_material_checkout_no_item_for_host_account">item not found for %s</string>
    <string name="lable_material_checkout_inventory_uom">UOM</string>
    <string name="text_material_checkout_host_account_name">Host Account：%s</string>
    <string name="text_material_checkout_host_account_name_flag">Host Account：</string>
    <string name="text_material_checkout_remove_btn">Remove</string>
    <string name="text_material_checkout_item_desc">Item | Description</string>
    <string name="text_material_checkout_item_qty">Qty</string>
    <string name="text_material_checkout_item_uom">UOM</string>
    <string name="title_material_search_select_itemspec">Item selector</string>
    <string name="toast_material_search_inventory_not_found">inventory not found</string>
    <string name="toast_material_search_hostaccount_not_found">Material search can only search for Host Account,the Host Account is not found</string>
    <string name="title_material_search_host_account">Material search can only search for Host Account,Please search first and then select</string>
    <string name="label_item_ms_inv_location">Location</string>
    <string name="label_item_ms_inv_total_qty">Total QTY</string>
    <string name="label_item_ms_itemspec_name">Item Name</string>
    <string name="label_item_ms_itemspec_customer">Customer</string>
    <string name="label_item_ms_itemspec_desc">Description</string>
    <string name="label_ms_item_head_itemname">Item Name</string>
    <string name="label_ms_item_head_total_qty">Total QTY</string>
    <string name="label_mp_head_item">Item</string>
    <string name="label_mp_head_ea_upc">EA UPC</string>
    <string name="label_mp_head_case_upc">CASE UPC</string>
    <string name="hint_material_please_input_print_qty">1</string>
    <string name="hint_material_print_please_input_description">please input item description</string>
    <string name="text_material_print">Print</string>
    <string name="toast_material_print_greater_zero">the qty has to be greater than zero</string>
    <string name="hint_material_manager_please_scan_upc">Please scan or input Item No/UPC</string>
    <string name="toast_mp_please_select_item">item spec to be printed was not selected,Please scan or input Item No/UPC to search and then select</string>
    <string name="menu_mp_printer_setting">Printer Setting</string>
    <string name="title_mp_search_host_account">Material Item print can only be done by the Host Account,Please search first and then select</string>
    <string name="toast_mp_hostaccount_not_found">Material Item print can only be done by the Host Account,the Host Account is not found</string>
    <string name="label_material_inv_count_physical_ount">Physical Count</string>>
    <string name="label_material_inv_count_scan_item">Scan Item</string>>
    <string name="label_material_inv_count_scan_loc">Scan Loc</string>
    <string name="toast_material_inv_count_invalid_location">pick location not found, scan again</string>
    <string name="toast_material_inv_count_multiple_location_found">Multiple location found, please scan again</string>
    <string name="hint_material_inv_count_enter_location">Please scan or input Location</string>>
    <string name="hint_material_inv_count_enter_or_input_item">Please scan or input Item</string>
    <string name="hint_material_inv_count_enter_or_input_sn">Please scan or input SN</string>
    <string name="label_material_inv_scan_item_item_name">Item:</string>
    <string name="label_material_inv_scan_item_desc">Descption:</string>
    <string name="toast_material_inv_count_hostaccount_not_found">Material count can only be done by the Host Account,the Host Account is not found</string>
    <string name="toast_material_inv_count_invalid_item">item for host account not found, scan again</string>
    <string name="label_material_inv_count_select_itemspec">select item</string>
    <string name="toast_material_inv_count_inventory_not_found">Inventory not found on this location</string>
    <string name="toast_material_count_complete_count">complete the count</string>
    <string name="toast_material_count_dif_qty">counted qty is dif from the booked qty,please enter the qty again</string>
    <string name="toast_material_count_qty_greater_than_zero">qty must equal or greater than 0</string>
    <string name="toast_material_count_invalid_unit">invalid unit</string>
    <string name="toast_material_count_qty_is_empty">qty is empty</string>
    <string name="toast_material_receiving_item_line_receipt_detail">Receipt Detail</string>
    <!--material manager end-->
    <!--transload receiving and loading task adapter -s-->
    <string name="label_item_transload_task_start_time">Start Time:</string>
    <string name="label_item_transload_task_create_time">Create time:</string>
    <string name="label_item_transload_task_create_by">Create By:</string>
    <string name="label_item_transload_task_notes">Notes:</string>
    <string name="label_item_transload_task_receipt_number">Receipt Number:</string>
    <string name="label_item_transload_task_priority">Priority:</string>
    <string name="label_item_transload_task_entry_id">Entry ID:</string>
    <!--transload receiving and loading task adapter -e-->

    <!--transload-receiving -S-->
    <string name="err_not_allowed_delete_when_uploading">Some cartons are still being uploaded, please wait</string>
    <!--transload-receiving -E-->

    <!--transload-loading -S-->
    <string name="text_transload_complete_load">Complete Load</string>
    <string name="text_transload_item_reopen_btn">Reopen</string>
    <string name="text_transload_scan_slp_shipping_label_to_load">Scan Carton/Pallet to load</string>
    <string name="text_transload_scan_slp_shipping_label_to_unload">Scan Carton/Pallet to unload</string>
    <string name="toast_transload_slp_is_empty">please scan or input SLP/Shipping</string>
    <string name="toast_transloding_unload_succeed">unload succeed</string>
    <string name="toast_transloading_load_succeed">load succeed</string>
    <string name="toast_transloading_load_reopen_order_succeed">reopen succeed</string>
    <string name="toast_transloading_load_start_step">please start step</string>
    <string name="dialog_dock_checkin_select_entry">Select Entry ID</string>
    <string name="toast_transloading_load_take_over_task_or_step">This task or step does not belong to you,please task over it.</string>
    <string name="label_text_order_dst">DST:</string>
    <string name="label_text_loaded_pallet">Loaded pallet:</string>
    <string name="menu_transload_loading_add_pro_no">Add Load Pro No</string>
    <string name="menu_transload_loading_batch_scan_by_po">Batch Scan by PO#</string>
    <string name="menu_transload_loading_batch_scan_by_location">Batch Scan by Location</string>
    <string name="menu_transload_loading_batch_scan_by_container">Batch Scan by Container</string>
    <string name="title_transload_loading_add_pro_no">Add Load Pro No</string>
    <string name="toast_transload_loading_add_pro_no_succeed">add Pro No succeed</string>
    <string name="label_transload_loading_trailers_no">Trailers：</string>
    <string name="label_transload_loading_containernos_no">ContainerNOs：</string>
    <string name="menu_transload_calculate_total_weight">Calculate Total Weight</string>
    <string name="message_transload_calculate_total_weight">Total %1$s pound has been loaded on this trailer %2$s, Max weight limit is %3$s pound</string>
    <!--transload-loading -E-->

    <string name="sn_already_exists">SN already exists</string>
    <string name="tip_continue_to_drop">Are you continue to drop the rest of item?</string>
    <string name="msg_force_close_step">\nDo you want to force close this step?</string>
    <string name="title_receive_create">Create Parcel Receive Task</string>
    <string name="title_receive_task">Receive Task</string>
    <string name="title_rn_locator">RN Locator</string>
    <string name="text_view">VIEW</string>
    <string name="text_work">WORK</string>
    <string name="text_complete_task">COMPLETE TASK</string>
    <string name="text_search">SEARCH</string>
    <string name="text_submit_uppercase">SUBMIT</string>
    <string name="text_select">SELECT</string>
    <string name="text_item_condition">GoodsType</string>
    <string name="hint_scan_rn_locator">Enter Reference/PO/RN#/BOL</string>
    <string name="hint_scan_enter_item">Enter Item</string>
    <string name="hint_scan_location">Scan Location</string>
    <string name="msg_select_location">Please select location</string>
    <string name="msg_select_item_unit">Please select item unit</string>
    <string name="msg_select_receipt">Please select receipt</string>
    <string name="text_ref">Ref: </string>
    <string name="text_po">PO: </string>
    <string name="text_bol_with_colon">BOL: </string>
    <string name="text_or">OR</string>
    <string name="msg_select_sn">Please select SN</string>
    <string name="msg_add_item_first">Please add item first</string>
    <string name="msg_select_receive_task">Please select receive task</string>
    <string name="msg_complete_task">complete task</string>
    <string name="tv_tracking_label">Tracking:%1$s</string>
    <string name="msg_auto_add_item">An item has been automatically added</string>
    <string name="label_small_parcel_receiving">Small Parcel Receiving</string>
    <string name="label_qr_code_receiving">QR Code Receiving</string>
    <string name="label_small_parcel_receiving_summary">Create Small Parcel Receiving Task</string>
    <string name="msg_submit_first">Please submit first!</string>
    <string name="msg_inconsistent_item_condition">Inconsistent item condition in item list</string>
    <string name="msg_not_the_same_item">Not the same item</string>
    <string name="hint_scan_putaway_lp">Scan Putaway LP</string>
    <string name="label_pallet_stack_height">Pallet Stack Height: </string>

    <!--print carton number -S-->
    <string name="title_print_carton_numer">SSCC Carton Label Print</string>
    <string name="label_print_carton_facility">Facility:</string>
    <string name="label_print_carton_customer">Customer:</string>
    <string name="label_print_carton_copies">Label Copies:</string>
    <string name="label_print_carton_select_printer">Select Printer:</string>
    <string name="btn_print_carton_print">Print</string>
    <string name="toast_print_carton_not_found_customer">not found customer</string>
    <string name="toast_print_carton_not_found_printer">not found printer</string>
    <string name="toast_print_carton_print_succeed">print succeed</string>
    <string name="hint_carton_print_enter_customer">please enter customer</string>
    <string name="msg_start_permission">Please go to setting and start permission</string>
    <!--print carton number -E-->

    <!--InventoryConsolidation -S-->
    <string name="title_inven_consolidation">Inventory Consolidation</string>
    <string name="inven_consolidation_subtitle">from bulk to defined unit</string>
    <string name="inven_consolidation_hint_input_scan_content">Scan or Enter Item, LP, Location</string>
    <string name="inven_consolidation_title_select_item">Select Item</string>
    <string name="inven_consolidation_inventory_not_found">inventory not found</string>
    <string name="inven_consolidation_next_btn">Next</string>
    <string name="inven_consolidation_close_task_btn">Close Task</string>
    <string name="inven_consolidation_back_btn">Back</string>
    <string name="inven_consolidation_new_lp">New Lp</string>
    <string name="label_inven_consolidation_from">From</string>
    <string name="label_inven_consolidation_to">To</string>
    <string name="inven_consolidation_not_found_uom">not found uom</string>
    <string name="inven_consolidation_please_input_qty">please input qty</string>
    <string name="toast_inven_consolidation_qty_into_one_full"> %1$s %2$s can not be combined into 1 full %3$s</string>
    <string name="toast_inven_consolidation_lower_to_highter_uom">Not allowed to consolidate from Higher UOM to Lower UOM</string>
    <string name="toast_inven_consolidation_qty_less_than_inventory_qty">the consolidate qty must less than or equal to inventory qty</string>
    <string name="toast_only_each_consolidate">Only %1$s each Consolidated to %2$s %3$s</string>
    <string name="toast_inventory_selected_not_enough_to_consolidate">inventory selected not enough to consolidate to a %1$s</string>
    <string name="inven_consolidation_not_found_consolidate_uom">only allow to conversion from LOWER to Higher UOM，not found valid uom</string>
    <string name="inven_consolidation_start_task_btn">start task</string>
    <string name="inven_consolidation_start_step_btn">start step</string>
    <string name="inven_consolidation_take_over_btn">take over</string>
    <string name="title_inven_consolidation_task">Inventory Consolidation Task</string>
    <string name="label_inven_consolidation_force_close">Force close</string>
    <string name="msg_inven_consolidation_force_close_step">\nDo you want to force close this step?</string>
    <string name="msg_inven_consolidation_force_close_task">\nDo you want to force close this task?</string>
    <string name="msg_inven_consolidation_close_task_succeed">close task succeed</string>
    <string name="hint_inven_consolidation_print_new_lp">please print new lp</string>
    <string name="toast_inven_consolidation_invalid_lp">invalid lp</string>
    <string name="label_inven_consolidation_item">Item：</string>
    <string name="label_inven_consolidation_description">Description：</string>
    <string name="label_inven_consolidation_LP">LP：</string>
    <string name="label_inven_consolidation_qty">Qty：</string>
    <string name="label_inven_consolidation_location">Location：</string>
    <string name="label_inven_consolidation_consolidation_as">Consolidated as：</string>
    <string name="toast_inven_consolidation_scan_input_print_lp">please input or scan or print lp</string>
    <string name="hint_inven_consolidation_scan_location">please input or scan location</string>
    <string name="hint_inven_consolidation_scan_lp">please input or scan lp</string>
    <string name="label_inven_consolidation_select_validate">select search mode</string>
    <string name="label_inven_consolidation_location_not_found">location not found</string>
    <string name="label_inven_consolidation_consolidate_result">Inventory Consolidated as</string>
    <string name="label_inven_consolidation_lp_not_found">lp not found</string>
    <string name="label_inven_consolidation_lp_status_is_not_new">this lp status is not NEW,please print new lp</string>
    <string name="label_inven_consolidation_consolidate_greater_than_zero">consolidate qty must be greater than 0</string>
    <string name="label_inven_consolidation_error_same_lp">Not allowed to stage in the same LP</string>
    <!--InventoryConsolidation -E-->
    <string name="text_scan_current_location">Scan Current Location</string>
    <string name="text_scan_master_sn">Scan Master SN</string>
    <string name="text_scan_individual_sn">Scan Individual SN</string>
    <string name="label_suggestion_location">suggestion location:</string>
    <string name="label_no_suggestion_location">no suggestion location</string>
    <string name="label_suggestion_item">suggestion item:</string>
    <string name="label_no_suggestion_item">no suggestion item</string>
    <string name="label_selected_drop_lp_item">selected drop LP/item:</string>
    <string name="label_suggested_drop_location">suggested drop location:</string>
    <string name="label_no_suggested_drop_location">no suggestion drop location</string>
    <string name="text_shipping_rule">Shipping Rule</string>
    <string name="msg_no_suggestion_drop_location">no suggestion drop location</string>
    <string name="msg_suggestion_task_unfinished">Suggested task unfinished. Do you want to close the task</string>
    <string name="msg_lp_or_location_not_match_the_suggestion">LP or location does not match the suggestion, continue?</string>
    <string name="hint_scan_master_sn">please scan master SN</string>
    <string name="hint_scan_individual_sn">please scan individual SN</string>
    <string name="msg_sns_no_match">SNs not match</string>
    <string name="msg_sns_length_no_match">Sn length not match in SNS</string>
    <string name="action_help">Help</string>
    <string name="text_load_all">Load All</string>
    <string name="msg_load_all_success">Load all successful</string>
    <string name="msg_force_complete_load">\nDo you want to force complete this load?</string>
    <string name="text_ready_to_load">Ready to load</string>
    <string name="text_take_pallet_to_location">Take Pallet(s) to Location:</string>


    <!--Cycle Count Validate Message-->
    <string name="msg_location_xx_is_empty_location">Location: %1$s is Empty Location</string>
    <string name="msg_task_locations_is_empty">Task Locations is empty</string>
    <string name="msg_location_is_not_within_the_cycle_count_range">Location is not within the Cycle Count Range! Please Enter the Location Again!</string>
    <string name="msg_empty_location_failed_item_has_been_new_at_location_xx">Empty Location failed, item has been new at location: %1$s</string>
    <string name="msg_new_lp_found">New LP Found</string>
    <string name="msg_item_xx_found_in_xxx_please_enter_item">Item: %1$s found in %2$s, Please Enter Item</string>
    <string name="msg_location_xx">Location: %1$s </string>
    <string name="msg_lp_xx">LP: %1$s </string>
    <string name="msg_item_is_counted">Item is Counted</string>
    <string name="msg_item_lot_no_is_counted">This Lot# already counted. Re-count and overwrite?</string>
    <string name="msg_item_lot_no_not_exist_add_new_inventory">The Lot# not exist, add new inventory?</string>
    <string name="msg_please_scan_sn">Please scan SN</string>
    <string name="msg_sn_qty_not_match">SN qty not match</string>
    <string name="msg_qty_not_match_please_enter_again">Qty not match! Please Enter Again!</string>
    <string name="msg_item_unit_not_match">Item unit not match</string>
    <string name="msg_invalid_item_please_enter_again">Invalid Item! Please Enter Again!</string>
    <string name="msg_invalid_location_please_enter_again">Invalid Location! Please Enter Again!</string>
    <string name="msg_please_input_or_print_lp">Please input or print LP</string>
    <string name="msg_please_input_lot_no">Please input Lot NO</string>
    <string name="msg_please_select_exp">Please select EXP</string>
    <string name="msg_please_select_mfg_date">Please select Mfg date</string>
    <string name="msg_confirmed_scanned_sn_qty">Confirmed scanned sn qty</string>
    <string name="msg_please_scan_or_input_location">Please scan or input Location</string>
    <string name="msg_please_scan_item_no">Please Scan Item No.</string>
    <string name="msg_item_not_matched_new_inventory_found">Item Not Matched, new inventory found ?</string>
    <string name="msg_item_not_matched">Item Not Matched</string>
    <string name="msg_lp_not_matched">LP Not Matched</string>
    <string name="msg_this_item_does_not_belong_to_customer_xx">This item does not belong to customer %1$s</string>
    <string name="msg_this_item_is_xx_can_not_be_counting">Item is %1$s can not be counting!</string>
    <string name="msg_lp_counted_at_location">LP(%1$s) has been counted at Location %2$s,do you want to overwrite it?</string>

    <!--Cycle Count Validate Message-->


    <!--Put Away Validate Message-->
    <string name="msg_duplicate">Duplicate</string>
    <string name="msg_nothing_to_put_away">Nothing to put awy</string>
    <string name="msg_lp_xx_not_belong_to_task_xx">LP: %1$s not belong to task: %2$s</string>
    <string name="msg_xx_xx_not_belong_to_task_xx">%1$s: %2$s not belong to task: %3$s</string>
    <string name="msg_lp_not_found_by_xx">LP not found by: %1$s</string>
    <string name="msg_location_not_found_by_xx">Location not found by: %1$s</string>
    <string name="msg_please_scan_location_first">Please scan location first before confirm.</string>
    <string name="msg_please_scan_confirm_location">Please scan confirm location</string>
    <string name="msg_scanned_location_is_different_from_suggested_please_select_reason">Scanned Location is different from suggested, please select reason</string>
    <string name="msg_please_scan_location_xx">Please scan Location: %1$s</string>
    <string name="msg_suggested_location_not_found">Suggested Location not found</string>
    <string name="msg_please_select_lp_to_put_away">Please select LP to putaway</string>
    <string name="message_putaway_task_can_not_load_facility">Can not load facility config, please re-enter Put Away task</string>
    <string name="msg_please_select_lp_to_batch_movement">Please select LP to batch movement</string>
    <string name="msg_not_put_away_yet">not put away yet</string>
    <string name="msg_lp_xx_is_put_away">LP: %1$s is put away</string>
    <string name="msg_tote_lp_xx_is_put_away">Tote/LP: %1$s is put away</string>
    <string name="msg_please_scan_location">Please scan Location</string>
    <string name="msg_please_check_digit">Please Check Digit</string>
    <string name="msg_please_scan_lp">Please Scan LP</string>
    <string name="msg_please_scan_item_to_put_away">Please Scan item to Putaway</string>
    <string name="msg_please_scan_other_location_or_scan_item_to_put_away">Please Scan other Location or item to Putaway</string>
    <string name="msg_please_check_lp_xx_in_location_xx">Please check LP:%1$s in Location:%2$s</string>
    <string name="msg_digit_checked">Digit Checked</string>
    <string name="msg_digit_not_match">Digit Not Match</string>
    <string name="msg_digit_not_required">Digit Not Required</string>
    <string name="msg_item_xx_not_found_in_tote_lp">Item:%1$s not found in Tote / LP</string>
    <string name="msg_tote_lp_not_match">Tote/LP Not Match</string>
    <string name="msg_item_xx_not_fount_in_tote_lp_xx">Item:%1$s not found in Tote/LP:%2$s</string>
    <string name="msg_put_away_qty_xx_greater_tote_lp_qty">Putaway Qty:%1$s greater Tote/LP Qty</string>
    <string name="msg_please_select_item_to_put_away">Please Select item to Putaway</string>
    <string name="msg_switch_to_next_location">Item Not Putaway.\nDo you want to switch to next Location?</string>
    <string name="msg_switch_to_new_lp">Item Not Putaway.\nDo you want to putaway item to new LP?</string>
    <string name="msg_lp_not_found_in_inventory">LP not found in inventory</string>
    <string name="msg_lp_inventory_not_found">LP inventory not found</string>
    <string name="msg_item_scanned">Item Scanned</string>
    <string name="msg_please_scan_tote_lp">Please scan Tote/LP</string>
    <string name="msg_please_scan_suggest_tote_lp">Please scan suggest Tote/LP</string>
    <string name="msg_tote_lp_not_found">Tote/LP not found</string>
    <string name="msg_please_scan_to_lp">Please Scan To LP</string>
    <string name="msg_please_enter_qty_to_put_away">Please enter qty to Putaway</string>
    <string name="msg_lp_xx_not_allow_put_away_multiple_item">LP:%1$s not allow putaway multiple item</string>
    <string name="msg_ilp_not_allow_put_away_multiple_item">ILP not allow putaway multiple item</string>
    <string name="msg_please_scan_item">Please scan item</string>
    <string name="msg_item_not_match_with_lot_no">Item mot match with lotNo.</string>
    <string name="msg_not_allow_put_away_to_pick_location">Not allow put away onHold/damage inventory to pick location.</string>
    <string name="error_failed_get_customer_from_lps">Failed to get customer from selected LPs</string>
    <string name="error_p_d_location_can_only_hold_one_lp">P&#038;D location can only hold one LP, please select others.</string>
    <!--Put Away Validate Message-->


    <!--Print-->
    <string name="msg_print_success">Print Success.</string>
    <string name="msg_print_failed">Print Failed:%1$s,please check and try again.</string>
    <string name="msg_please_contact_you_system_admin_to_setup_printer_server">Please contact you system admin to setup printer server.</string>
    <string name="msg_device_not_support_bluetooth">Device not support bluetooth</string>
    <string name="msg_device_not_found">Device not found</string>
    <string name="msg_device_offline">Device offline</string>
    <string name="msg_bluetooth_is_disable">Bluetooth is disable</string>
    <string name="msg_unknown_error">Unknown error</string>
    <string name="msg_unknown_host">Unknown host:%1$s, please check.</string>
    <string name="msg_connect_failed">Connect failed:%1$s,please check that the wireless connection is the same between mobile phone and printer.</string>

    <string name="msg_xx_two_one_xx">%1$s (2X1):%2$s</string>
    <string name="msg_xx_four_six_xx">%1$s (4X6):%2$s</string>
    <string name="msg_xx_letter_xx">%1$s (LETTER):%2$s</string>
    <string name="msg_xx_a_four_xx">%1$s (A4):%2$s</string>
    <string name="msg_xx_xx">%1$s :%2$s</string>
    <string name="msg_all_item_has_been_printed">All Item Has Been Printed.</string>
    <!--Print-->

    <!--Transfer Out-->
    <string name="msg_lp_submitted">LP Submitted</string>
    <!--Transfer Out-->

    <!--Pick To SLP Validate Message-->
    <string name="msg_lp_not_in_task_slp_list">LP:%1$s not in task SLP list</string>
    <string name="msg_lp_has_been_staged">LP:%1$s has been staged</string>
    <string name="msg_task_have_picking_lp">Task have picking LP:%1$s, please stage it first</string>
    <string name="msg_lp_is_new">Not item picked on LP:%1$s, LP status is NEW</string>
    <string name="msg_no_material">No Material for this Task.</string>
    <!--Pick To SLP Validate Message-->

    <!--Force Close Receive Validate Message-->
    <string name="msg_capture_packing_list_photo">Please capture packing list photo.</string>
    <string name="msg_capture_damage_photo">Please capture damage/contain damage/on hold LP photos.</string>
    <string name="msg_select_force_close_reason">Please select force close reason.</string>
    <string name="msg_fill_force_close_reason">Missing Information.</string>
    <string name="msg_task_not_contain_rn">Task no contain RN: %1$s, please check!</string>
    <string name="msg_processing_rn_not_complete">RN: %1$s not complete, please finish it first!</string>
    <string name="msg_input_or_scan_to_lp">please scan or input to LP</string>
    <string name="msg_select_lp_condition">please select LP condition</string>
    <!--Force Close Receive Validate Message-->

    <!--Pick-->
    <string name="msg_invalid_qty_format">Invalid qty format</string>
    <string name="msg_enter_at_least_one_item">Enter at least one item.</string>
    <string name="msg_create_print_job_failed">Create Print Job Failed.</string>
    <string name="msg_pick_success">Pick success</string>
    <string name="msg_pick_task_not_found">Pick task not found</string>
    <string name="msg_so_id_qty_not_match">SOID qty not match</string>
    <string name="msg_inventory_not_found">Inventory not found on this location/LP, please check inventory!</string>
    <string name="msg_inventory_not_found_about_clo_bounding">Inventory not found about clp bonding!</string>
    <string name="msg_report_partial_pallet_succeed">report partial pallet issue succeed</string>
    <string name="toast_report_partial_pallet_issue_input_function_pwd">please input function password</string>
    <string name="toast_report_partial_pallet_issue_func_pwd_error">function password error</string>
    <string name="toast_report_partial_pallet_issue_succeed">report pallet issue succeed</string>
    <string name="incorrect_password">Incorrect password</string>
    <string name="error_auto_print_item_label">Print item label failed</string>
    <!--Pick-->

    <!--Receive By Carton Validate Message-->
    <string name="msg_not_allow_over_received">Not Allow over received</string>
    <string name="msg_qty_can_not_be_zero">Invalid qty: 0, please check.</string>
    <string name="msg_xx_received">%1$s Received</string>
    <string name="msg_invalid_coo">Invalid COO</string>
    <!--Receive By Carton Validate Message-->

    <!--Collect Item Dimension-->
    <string name="msg_server_not_found">Cube scan server not found,please check.</string>
    <string name="label_item_not_found">Item not found: %1$s.</string>
    <string name="msg_invalid_item_dimension">Item length/width/height/weight can not be null.</string>
    <string name="msg_please_check_item_unit">Please check item unit.</string>
    <string name="msg_no_server">No server,please check.</string>
    <string name="msg_no_server_for_version"> not have a configured version number, please check.</string>
    <string name="label_item_dimension_weight">Weight: %1$s (%2$s)</string>
    <string name="label_item_dimension_dim_weight">Dim Weight: %1$s (%2$s)</string>
    <string name="label_item_dimension_dim_factor">Dim Factor: %1$s (%2$s)</string>
    <!--Collect Item Dimension-->

    <!--Print Location Label-->
    <string name="msg_no_location_selected">No Location Selected.</string>
    <!--Print Location Label-->

    <!--Parcel Receive-->
    <string name="msg_please_scan_or_input_tracking_no">Please scan or input tracking NO.</string>
    <string name="msg_please_scan_or_input_so_id">Please scan or input SOID</string>
    <string name="msg_please_input_to_report_other_issue">Please input item/LP then report other issue</string>
    <string name="msg_receipt_not_found">Receipt not found</string>
    <string name="text_parcel_receiving">Parcel Receiving</string>
    <string name="msg_item_received">Item Received</string>
    <string name="msg_item_not_found_by_xx">Item not found by: %1$s</string>
    <string name="msg_item_line_not_found">ItemLine not found</string>
    <string name="msg_invalid_tracking_no">Invalid trackingNo</string>

    <!--Parcel Receive-->

    <string name="message_batch_order_pick_to_lp_validate">Current order have pick to %1$s, please use it</string>
    <string name="message_invalid_location">Invalid Location</string>
    <string name="message_location_disabled">Location disabled</string>
    <string name="message_scan_soid">Scan SOID</string>
    <string name="message_scan_to_lp">Scan To LP</string>
    <string name="message_confirm_quantity">Confirm Quantity</string>
    <string name="message_lp_order_shipped">LP: %1$s binded to shipped order: %2$s, please use other LP</string>
    <string name="text_sort_success">Sort success</string>
    <string name="msg_no_need_sort_please_put_back">No need sort, please put back to conveyor</string>
    <string name="msg_ready_for_pack">Ready for pack, please put the Tote/LP to conveyor or pack station</string>
    <string name="msg_multiple_equipment_found">Multiple equipment found</string>
    <string name="text_location_scan_suggest">Go to Location %1$s, scan %2$s</string>
    <string name="please_scan_sorted_lp">Please scan sorted LP</string>
    <string name="msg_please_scan_sorted_tote_lp">Please scan sorted Tote/LP</string>
    <string name="msg_pick_sort_complete_location">Pick sort complete, please put on conveyor</string>
    <string name="message_sort_location_occupy_by_other_tote">Location occupy by other tote, please put to other sorting location</string>
    <string name="text_no_suggest_location_confirm">No suggest location, please confirm</string>
    <string name="text_no_suggestion">No suggestion</string>
    <string name="text_pick_sort_complete_conveyor">Sort is complete, please put on Conveyor</string>
    <string name="text_pick_sort_complete_conveyor_pack_station">Sort is complete, please put on Conveyor/Pack Station</string>
    <string name="msg_pick_sort_not_complete_location">Sort is not complete, please put to sorting location</string>
    <string name="message_can_not_sort_to_the_same_lp">Can not sort to the same LP</string>
    <string name="message_can_not_put_back_the_same_lp">Can not put back the same LP</string>
    <string name="message_can_not_find_item">Can not find item, please check scan code</string>
    <string name="message_pick_task_can_not_load_facility">Can not load facility config, please re-enter pick task</string>
    <string name="msg_scan_qty_does_not_match_to_required_qty_proceed">Scan qty does not match to required qty. Proceed?</string>
    <string name="msg_so_id_not_match">SOID not match</string>
    <string name="msg_please_scan_so_id">Please Scan SOID</string>
    <string name="msg_from_tote_lp_is_empty">From Tote/LP is empty</string>
    <string name="msg_please_scan_put_back_to_tote_lp">Please scan put back to Tote/LP</string>
    <string name="msg_please_confirm_tote_lp">Please confirm Tote/LP</string>

    <!--Receive To PutAway-->
    <string name="msg_invalid_sn_format">Invalid SN Format</string>
    <string name="msg_invalid_sn">Invalid SN</string>
    <string name="only_support_lp_and_rn">Only supports LP and RN</string>
    <string name="msg_lp_no_exist">LP Is Not Exist</string>
    <string name="msg_not_allow_scan_rn">This Customer do not allow RN#, Please scan LP instead!</string>
    <string name="msg_lot_no_not_match">LotNo. not match</string>
    <string name="msg_invalid_lot_no">Invalid LotNo.</string>
    <string name="msg_lot_no_items">LotNo. item: </string>
    <string name="msg_invalid_lot_no_try_again">Invalid Lot#, Please try again.</string>
    <string name="msg_not_support_multiple_item">Not Support Multiple Item</string>
    <string name="msg_please_print_lp">Please print LP</string>
    <string name="msg_item_line_not_confirmed_by_sn">ItemLine not confirmed by SN</string>
    <string name="msg_please_scan_or_input_sn">Please scan or input SN</string>
    <string name="msg_please_select_location">Please select Location</string>
    <string name="msg_success">Success</string>
    <string name="msg_put_away_location_must_be_a_pick_location_please_select_again">Putaway location must be a Pick Location, please select again!</string>
    <string name="msg_forklift_occupied_by_task_xx">Forklift occupied by task: %1$s</string>
    <string name="msg_number_of_sn_scanned_overage">Number of SN scanned Overage</string>
    <string name="msg_qty_not_define_check_item">Item total qty not define,try to rescan item.</string>
    <string name="msg_item_sn_not_match">Item SN not match.</string>
    <string name="msg_item_lot_not_match">Item lotNo not match.</string>
    <string name="msg_sn_not_on_the_forklift">SN: %1$s not on this forklift.</string>
    <string name="msg_item_number_of_sn_scanned_overage">Item: %1$s(%2$s) number of SN scanned Overage</string>
    <string name="msg_scanned_entered_is_not_an_equipment_please_scan_enter_again">Scanned/entered is not an equipment, please scan/enter again</string>
    <string name="msg_Equipment_contain_un_receiving_inventory">Equipment contain un-receiving inventory, can not operate</string>
    <string name="text_not_allowed_to_override_the_suggested_put_away_location">Not allowed to override the suggested put away location</string>
    <string name="text_not_allowed_to_put_away_with_multiple_customers">not allowed to put away with multiple customers</string>
    <!--Receive To PutAway-->

    <!-- QA -->
    <string name="message_invalid_tracking_number">Invalid tracking number</string>
    <string name="message_not_shipped">%1$s status are not SHIPPED</string>
    <string name="message_order_null">can not find attach order</string>
    <string name="message_truck_pick_up_label_not_same">carrier pick up label is not same</string>
    <string name="msg_carrier_pickup_null">Carrier pick up label is null, scan another</string>
    <string name="msg_xx_package_qc_pass">%1$s Package QC Pass</string>
    <string name="msg_please_scan_carrier_pick_up_label">Please Scan Carrier Pick Up Label</string>
    <string name="msg_please_scan_carrier_pick_up_xx">Please Scan Carrier Pick Up: %1$s</string>
    <!-- end QA -->

    <!-- Re-Palletize start -->
    <string name="text_repalletize">Re-Palletize</string>
    <string name="msg_only_allow_one_lp_for_split">Only allow splitting LP one by one.</string>
    <string name="msg_only_allow_two_lp_for_combine">Only allow two LP for combine.</string>
    <string name="msg_data_load_not_complete">Data not loaded complete, please refresh page.</string>
    <string name="msg_no_lp_for_staging">No LP for staging, please check.</string>
    <string name="msg_stage_lp_first">Please stage LP: %1$s first.</string>
    <string name="msg_stage_lp_is_empty">Stage LP is empty, please check.</string>
    <string name="msg_add_material">Please add material</string>
    <!-- Re-Palletize end -->

    <string name="location_not_found">Location Not Found</string>
    <string name="location_barcode_not_scannable">Location Barcode Not Scannable</string>
    <string name="pick_issue_other_issue">Other issue, please suggest a different location</string>
    <string name="pick_issue_inventory_issue">Inventory issue, please suggest a different location</string>
    <string name="msg_pick_suggest_item">Pick Suggest item : </string>
    <string name="please_select_inventory_issue">Please select inventory issue</string>

    <!-- LP Setup -->
    <string name="msg_scan_finished">Scan Finished</string>
    <string name="msg_please_remove_item_that_not_require_sn">Please remove item that not require SN</string>
    <string name="msg_sn_qty_not_match_skip_continue_scan_sn">SN qty not match,skip or continue scan SN</string>
    <string name="msg_please_input_supervisor">Please input supervisor</string>
    <string name="msg_please_input_password">Please input password</string>
    <string name="scan_equipment_to_start">scan equipment to start</string>
    <string name="inventory_collect">Inventory Collect</string>
    <string name="scan_from_location">scan from location</string>
    <string name="inventory_drop">Inventory Drop</string>
    <string name="scan_or_select_item_lp_to_put_away">scan or select item/LP to put away</string>
    <string name="collect_from">Collect From :</string>
    <string name="collect_item">Collect Item :</string>
    <string name="entire_collect">Entire Collect</string>
    <string name="enter_the_qty">Enter the QTY:</string>
    <string name="to_unit">To&#8194;&#8194;&#8194;unit:</string>
    <string name="input_lot_number">Input Lot Number:</string>
    <string name="scan_sn">Scan SN: </string>
    <string name="drop_to">Drop to :</string>
    <string name="drop_item">Drop Item :</string>
    <string name="scan_to_lp">Scan To LP</string>
    <string name="print">Print</string>

    <string name="scan_item">scan Item</string>
    <string name="scan_destination_location">Scan Destination Location</string>
    <string name="collect_location_lp">collect location/LP</string>
    <string name="collect_list">collect list:</string>
    <string name="more_than_the_inventory">More than the inventory</string>
    <string name="qty_and_num_miss">Quantity and lot number are missing</string>
    <string name="qty_or_num_miss">Quantity or lot number are missing</string>
    <string name="qty_is_missing">QTY is missing</string>
    <string name="sn_is_empty">Sn is Empty</string>
    <string name="no_content">no content</string>
    <string name="no_correspinding_item">There is no corresponding item</string>
    <string name="not_consistent_with_current_stock">Not consistent with current stock</string>
    <string name="support_pick_location_lp">only support pick location or Lp</string>
    <string name="lot_number_no_belong">lot number not belong to item</string>
    <string name="sn_list">SN List</string>
    <string name="inventory_drop_success">inventory drop success</string>
    <string name="inventory_collect_success">inventory collect success</string>
    <string name="inventory_movement_complete">inventory movement complete</string>
    <string name="please_check_lp">please check again LP</string>
    <string name="more_one_lot_number">Tasks that there is more than one batch number for the product</string>
    <string name="should_equipment_first">You should scan equipment first</string>
    <string name="location_lp_no_found">location or Lp no found</string>
    <string name="sn_no_belong_item">sn no belong to item</string>
    <string name="check_input_location">Verify that the Complete input</string>
    <string name="qty_must_zero">QTY must be greater than 0</string>
    <string name="text_hlp_not_allowed_please_scan_location">HLP not allowed, please scan location</string>
    <string name="scan_hlp_not_allowed">HLP not allowed</string>
    <string name="scan_hlp_allowed">only can scan HLP </string>
    <!-- LP Setup -->
    <string name="toast_not_found_entry_id">not found Entry ID</string>
    <string name="toast_please_input_entry_id">Please input entry id</string>
    <string name="toast_please_input_equipment_to_search">Please input equipment to search entry id</string>
    <string name="msg_no_unit">No unit found for the item</string>
    <string name="msg_lot_number_empty">Lot number is missing</string>
    <string name="msg_no_receipt">No receipt id found</string>
    <string name="msg_no_receipt_item_line">No receipt item line found</string>
    <string name="msg_sn_no_match_item">Sn does not match item</string>
    <string name="msg_item_no_sn">The item has no Sn</string>
    <string name="msg_please_check_location">Please check again location</string>
    <string name="msg_please_check_sn">Please check again SN</string>
    <string name="msg_no_tracking_no_task_found">No trackingNO task found</string>
    <string name="msg_receive_task_submit_success">Receive task submit success!</string>
    <string name="msg_item_exists">Item already exists in the list</string>

    <!--count msg -s-->
    <string name="msg_count_location_error">Location error, please scan again</string>
    <string name="msg_count_item_not_found">item not found</string>
    <string name="msg_count_lp_error">LP error, please scan again</string>
    <string name="msg_count_case_upc_error">Case UPC error, please scan again</string>
    <string name="msg_count_ea_upc_error">EA UPC error, please scan again</string>
    <string name="msg_count_lot_no_error">Please scan or input Lot No.</string>
    <string name="msg_count_exp_date_error">Select Exp Date</string>
    <string name="msg_count_qty_error">QTY error, please input again</string>
    <string name="msg_count_scan_sn">Please scan or input SN</string>
    <string name="msg_count_sn_qty_not_match">SN qty not match</string>
    <string name="msg_count_enable_scan_sn">Please enable Scan SN</string>
    <string name="msg_count_scan_or_input_sn">Please scan or input SN</string>
    <string name="msg_scan_suggestion_location_lp">Please scan suggestion location or lp</string>
    <!--count msg -e-->
    <string name="text_v_cancel">Cancel</string>
    <string name="text_v_ok">Ok</string>
    <string name="text_v_warn_tips">Warm Tips</string>
    <string name="msg_no_location_permission">Please turn on the mobile location</string>

    <string name="msg_no_location_found">No location found</string>

    <!--Transload Receive Validate Message-->
    <string name="msg_carton_not_in_receipt">Carton not in receipt.</string>
    <!--Receive By Carton Validate Message-->
    <string name="msg_regular_movement_unfinished">Task unfinished. Please go to the regular movement to finish it</string>

    <string name="label_item_qty_with_colon">Item Qty:</string>
    <string name="msg_no_help_page_url_configured">no help page url configured</string>
    <string name="msg_load_task_not_found">Load task not found</string>
    <string name="msg_order_more_than_one">Order more than one</string>
    <string name="msg_order_is_empty">Order is empty</string>

    <string name="not_found">Not found!</string>
    <string name="label_doc_series">Doc#/Receipt#/Order#/Load#/ET#</string>
    <string name="label_loc_series">Loc#/Dock#/Spot#/Cone#</string>
    <string name="hint_please_enter_or_scan">please enter or scan</string>
    <string name="label_need_approval">Needs\nApproval</string>
    <string name="label_assignee_text">Assignee</string>
    <string name="hint_please_input_assignee">Please input assignee</string>
    <string name="hint_the_assignee_already_exists">The assignee already exists</string>
    <string name="msg_task_success_batch_create">Task success batch create</string>
    <string name="msg_no_locate_customer_found">no locate customer found</string>
    <string name="msg_exchange_customer">Current Selected customer: %1$s\nSwitch to customer: %2$s?</string>
    <string name="msg_search_locate_customer_successfully">search locate customer successfully</string>
    <string name="msg_continue">Continue</string>
    <string name="label_assigned_by">Assigned by:</string>

    <!--Approve And Rating Start-->
    <string name="title_approval_and_rating">Approval And Rating</string>
    <string name="swipe">swipe</string>
    <string name="scheduled_hour">Scheduled hours</string>
    <string name="work_hour">Work hours</string>
    <string name="general_task_only">General Tasks Only</string>
    <string name="approve_all">Approve All</string>
    <string name="job_code">Job Code</string>
    <string name="from_to">From - To</string>
    <string name="today">Today</string>
    <string name="last_30_days">Last\n30 Days</string>
    <string name="today_avg">Today Avg</string>
    <string name="approve_all_confirm">Confirm to approve all?</string>
    <string name="error_tasks_not_approved">Please approve all tasks that need to be approved first!</string>
    <string name="on_hand_task">On Hand Tasks</string>
    <string name="work_date">Work Date</string>
    <string name="approval_list">Approval List</string>
    <string name="search_here">Search here</string>
    <string name="undo">Undo</string>
    <string name="avg_30_day">30 day\navg.</string>
    <string name="avg_team">Team\navg.</string>
    <string name="good_job">Good job</string>
    <string name="poor_job">Poor job</string>
    <string name="active_hour">Active hours</string>
    <string name="edit_this_task">Edit this task</string>
    <string name="hrs">hrs</string>
    <string name="save_changes">Save changes</string>
    <string name="filter_information">Filter Information</string>
    <string name="sort_a_z">Sort A ——> Z</string>
    <string name="sort_z_a">Sort Z ——> A</string>
    <string name="approval_status">Approval Status</string>
    <string name="incomplete">Incomplete</string>
    <string name="complete_uppercase_first">Complete</string>
    <string name="department">Department</string>
    <string name="apply">Apply</string>
    <string name="message_approval_unrated_task">This employee has unrated tasks in the past. Do you want to continue to check this employee\'s tasks or jump to the next employee?</string>
    <string name="not_show_message_again">Do not show this message again</string>
    <string name="no_work_record_on_date">Current user has no work record on %1$s</string>
    <string name="error_item_have_no_default_receiving_uom">item doesn\'t have a default receiving uom</string>

    <string name="text_reply">reply</string>
    <string name="msg_please_input_reply_content">Please input reply content</string>
    <string name="msg_reply_successful">Reply success</string>
    <string name="label_vendor_item_id">Vendor Item Id</string>
    <string name="content_vendor_item_id">Vendor Item# %1$s</string>
    <!--Approve And Rating End-->
    <string name="text_count_by_lp_qty">Count By LP QTY</string>
    <string name="hint_scan_upc_or_type_in_item_no">Scan UPC or type in Item No.</string>
    <string name="msg_please_scan_upc_or_type_in_item_no">Please Scan UPC or type in Item No.</string>
    <string name="input_lp_pallet_qty">Input LP/Pallet QTY</string>
    <string name="msg_please_input_lp_pallet_qty">Please input LP/Pallet QTY</string>
    <string name="label_location_crumb">>>> Location</string>
    <string name="label_item_crumb">>>> Item</string>
    <string name="msg_qty_is_not_match_scan_pallet_by_pallet">qty is not match, scan pallet by pallet</string>
    <string name="hint_please_scan_ucc">Please Scan UCC</string>
    <string name="title_scan_ucc">Scan UCC</string>
    <string name="msg_ucc_not_exist">Loading Cannot be Performed, UCC Doesn\'t Exist, please check with Supervisor</string>
    <string name="msg_ucc_not_belong_this_load">UCC doesn\'t belong this load, please check with Supervisor</string>
    <string name="text_no_sn_in_inventory">No SN in inventory</string>
    <string name="text_sn_not_found_in_inventory">SN:%s not found in inventory</string>
    <string name="hint_input_or_scan_item_upc">Input or Scan Item/UPC</string>
    <string name="hint_input_or_scan_serial_number">Input or Scan Serial #</string>
    <string name="label_copy">Copy:</string>
    <string name="text_same_item_lock">Same Item Lock</string>
    <string name="text_same_print_copy_lock">Same Print Copy Lock</string>
    <string name="please_scan_or_input_job_code">Please scan or input job code</string>
    <string name="please_scan_or_input_customer_name">Please scan or input customer</string>
    <string name="msg_please_check_item_name_or_rescan_item">Please check item name or rescan item</string>
    <string name="text_pallet_carton">Pallet#/Carton#</string>
    <string name="msg_please_input_copy">Please input copy</string>
    <string name="label_pick_location">Pick Location:</string>
    <string name="hint_scan_pick_location">Scan Pick Location</string>
    <string name="msg_please_scan_pick_location">Please Scan Pick Location</string>
    <string name="msg_location_inconsistency">Invalid Entry, please enter the correct location and make sure the item is pick from location</string>
    <string name="msg_please_input_more_than_three_characters">Please input more than 3 characters</string>
    <string name="label_consolidation_no">Consolidation No:</string>
    <string name="msg_order_consolidation_key_and_lp_consolidation_key_is_inconsistent">The order consolidation key and LP consolidation key is inconsistent</string>

    <!--timesheet start-->
    <string name="take_photo_retake">RETAKE</string>
    <string name="take_photo_use">Use Photo</string>
    <string name="take_photo_prompt">Center your face to the screen and take a picture</string>
    <string name="timesheet_time_entry">TIME ENTRY</string>
    <string name="timesheet_expand_all">EXPAND ALL</string>
    <string name="timesheet_collapse_all">COLLAPSE ALL</string>
    <string name="timesheet_time_count">%s Hrs</string>
    <string name="btn_confirm_capital">CONFIRM</string>
    <string name="timesheet_clock_fail">System Generated Error.</string>
    <string name="timesheet_employee_id">ID:%s</string>
    <string name="timesheet_not_found_facility">invalid facility IP</string>
    <string name="timesheet_login_to_wise">Login to WMS</string>
    <string name="timesheet_hrs">Hrs</string>
    <string name="timesheet_miss_punch_type_title">Missing a previous %s punch</string>
    <string name="timesheet_miss_punch_type_content">Please contact your manager to correct any missing punches. Are you sure you want to punch %s?</string>
    <string name="timesheet_more">MORE</string>
    <string name="timesheet_in_day">IN DAY</string>
    <string name="timesheet_out_lunch">OUT LUNCH</string>
    <string name="timesheet_in_lunch">IN LUNCH</string>
    <string name="timesheet_out_day">OUT DAY</string>
    <string name="timesheet_punch_suc">You successfully %1$s at  %2$s</string>
    <string name="timesheet_taking_picture_please_wait">Taking pictures, please wait</string>
    <!--timesheet end-->

    <string name="month_january">January</string>
    <string name="month_february">February</string>
    <string name="month_march">March</string>
    <string name="month_april">April</string>
    <string name="month_may">May</string>
    <string name="month_june">June</string>
    <string name="month_july">July</string>
    <string name="month_august">August</string>
    <string name="month_september">September</string>
    <string name="month_october">October</string>
    <string name="month_november">November</string>
    <string name="month_december">December</string>
    <string name="week_sunday">Sunday</string>
    <string name="week_monday">Monday</string>
    <string name="week_tuesday">Tuesday</string>
    <string name="week_wednesday">Wednesday</string>
    <string name="week_thursday">Thursday</string>
    <string name="week_friday">Friday</string>
    <string name="week_saturday">Saturday</string>

    <string name="device_id">Device Id</string>

    <string-array name="custom_weekdays">
        <item>M</item>
        <item>T</item>
        <item>W</item>
        <item>T</item>
        <item>F</item>
        <item>S</item>
        <item>S</item>
    </string-array>

    <string-array name="sn_dimension_uom">
        <item>INCH</item>
        <item>CM</item>
        <item>FEET</item>
        <item>M</item>
    </string-array>

    <string-array name="sn_dimension_weight_uom">
        <item>LB</item>
        <item>G</item>
        <item>KG</item>
    </string-array>

    <string name="msg_please_input_lp_associated_with_current_location">Please input LP associated with current location: %1$s</string>
    <string name="title_inventory_transfer">Inventory Transfer</string>
    <string name="text_separate_lp_by_item">Separate Lp by Item</string>
    <string name="text_simplify_receiving_of_inventory_transfer">Simplify Receiving of Inventory Transfer</string>

    <string name="label_receive_when">Receive When</string>
    <string name="text_fast_add">Fast Add</string>
    <string name="text_fast_add_by_pallet">By Pallet</string>
    <string name="text_fast_add_by_location">By Location</string>
    <string-array name="inventory_initial_fast_add_method">
        <item>@string/text_fast_add_by_pallet</item>
        <item>@string/text_fast_add_by_location</item>
    </string-array>
    <string name="msg_please_select_title">Please select title</string>
    <string name="msg_please_scan_or_input_lot_no">Please scan or input lot no</string>
    <string name="msg_please_select_receive_when">Please select receive when</string>
    <string name="text_oder_id">Order: %1$s</string>
    <string name="text_retailer">Retailer</string>
    <string name="text_arn">ARN</string>
    <string name="title_location_creation">Location Creation</string>
    <string name="text_temperature">Temperature</string>
    <string name="text_linear_unit">Linear Unit</string>
    <string name="text_capacity_type">Capacity Type</string>
    <string name="text_capacity_qty">Capacity Qty</string>
    <string name="text_sub_type">Sub Type</string>
    <string name="text_stack">Stack</string>
    <string name="msg_please_submit_to_create_location_first">Please submit to create location first</string>
    <string name="msg_please_submit_to_update_location_first">Please submit to update location first</string>
    <string name="msg_create_location_successful">Create location successful</string>
    <string name="msg_update_location_successful">Update location successful</string>
    <string name="msg_please_input_length">Please input length</string>
    <string name="msg_please_input_width">Please input width</string>
    <string name="msg_please_input_height">Please input height</string>
    <string name="msg_please_select_location_to_submit_first">Please select location to submit first</string>
    <string name="msg_clear_all_location">Clear all location?</string>

    <!--load task v1 start-->
    <string name="text_info">Info</string>
    <string name="text_create_by">Created By</string>
    <string name="btn_start">Start</string>
    <string name="btn_continue">Continue</string>
    <string name="entry_id_add_v1">Entry ID Added</string>
    <string name="entry_id_remove_v1">%s was removed</string>
    <string name="entry_id_check_error">Please Rescan Entry ID or Input</string>
    <string name="dock_name_add_v1">Dock Check In</string>
    <string name="dock_name_remove_v1">Dock was removed</string>
    <string name="dock_check_error">Dock Check Error</string>
    <string name="input_entry_id_or_scan">Input Entry ID/Container or Scan</string>
    <string name="input_dock_number_or_scan">Input Dock Number or Scan</string>
    <string name="label_load_number">Load No.</string>
    <string name="label_load_seq">Load Seq.</string>
    <string name="text_load_summary">%s SUMMARY</string>
    <string name="label_plt">PLT</string>
    <string name="label_loads">Loads</string>
    <string name="input_or_scan_pallet">Enter or scan pallet</string>
    <string name="scan_to_unload">Scan to unload</string>
    <string name="confirm_partial_load">Confirm Partial Load?</string>
    <string name="proceed_to_next_load">Proceed to next load?</string>
    <string name="add_photos_required">ADD PHOTOS(REQUIRED)</string>
    <string name="text_load_all_orders">Load All Orders</string>
    <string name="text_orders">ORDERS</string>
    <string name="msg_not_found_photo">Not found photo</string>
    <string name="upload_photo_of_seal_require"><Data><![CDATA[Upload photo of seal<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_or_video_of_seal_require"><Data><![CDATA[Upload photo or video of seal<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_of_seal">Upload photo of seal</string>
    <string name="upload_photo_or_video_of_seal">Upload photo or video of seal</string>
    <string name="text_seal_number_require"><Data><![CDATA[Seal No.<font color="#B71C1C">*</font>]]></Data></string>
    <string name="text_seal_number">Seal No.</string>
    <string name="hint_seal">Seal #</string>
    <string name="upload_photo_of_count_sheet_require"><Data><![CDATA[Upload photo of count sheet(s)<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_or_video_of_count_sheet_require"><Data><![CDATA[Upload photo or video of count sheet(s)<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_of_count_sheet">Upload photo of count sheet(s)</string>
    <string name="upload_photo_or_video_of_count_sheet">Upload photo or video of count sheet(s)</string>
    <string name="upload_photo_of_pallet_require"><Data><![CDATA[Upload photo of pallet<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_of_pallet">Upload photo of pallet</string>
    <string name="upload_photo_of_truck"><Data><![CDATA[Upload photo of truck<font color="#B71C1C">*</font>]]></Data></string>
    <string name="upload_photo_or_video_of_truck"><Data><![CDATA[Upload photo or video of truck<font color="#B71C1C">*</font>]]></Data></string>
    <string name="add_pro_number_require"><Data><![CDATA[Add Pro No.<font color="#B71C1C">*</font>]]></Data></string>
    <string name="add_pro_number">Add Pro No.</string>
    <string name="add_pro_number_hit">Pro-####</string>
    <string name="text_retry">Retry</string>
    <string name="btn_next_order">Next Order</string>
    <string name="msg_is_last_order">This is the last order</string>
    <string name="proceed_to_next_order">Proceed to next order?</string>
    <string name="text_delete_count_sheet">Delete count sheet?</string>
    <string name="text_delete_truck">Delete truck?</string>
    <string name="text_delete_seal">Delete seal?</string>
    <string name="msg_please_operate_in_sequence">Please operate in sequence</string>
    <string name="text_delete_image">DELETE IMAGE?</string>
    <string name="btn_remove_all">REMOVE ALL </string>
    <string name="msg_take_photo_for_each_pallet">Please take photo for each pallet</string>
    <string name="msg_no_pallet_available">no pallet available</string>
    <string name="upload_photo_of_order">Upload photo of order</string>
    <string name="upload_photo_or_video_of_order">Upload photo or video of order</string>
    <string name="label_expected">Expected</string>
    <string name="text_Location_s">Location(s)</string>
    <string name="text_reopen_load">Reopen Load</string>
    <string name="text_reopen_order">Reopen Order</string>
    <string name="text_complete_order">Complete Order</string>
    <string name="msg_please_load_all_pallet">Please load all pallet</string>
    <string name="text_qty_of_clp"># of CLP</string>
    <string name="label_lp_number">LP #</string>
    <string name="text_clp_count">CLP Count</string>
    <string name="text_load_log_summary">Load Log Summary</string>
    <string name="text_loaded_lp_history">Loaded LP History:</string>
    <string name="text_unloaded_lp_history">UnLoaded LP History:</string>
    <string name="text_collapse_all">Collapse all</string>
    <string name="label_total_orders_to_load">Total Orders to Load</string>
    <string name="label_total_pallet">Total Pallet</string>
    <string name="label_unload_mode">Unload Mode</string>
    <string name="text_load_all_pallet">LOAD\nALL\nPALLET</string>
    <string name="text_unload_all_pallet">UNLOAD\nALL\nPALLET</string>
    <string name="msg_material_correct">Is the following material correct?</string>
    <string name="text_take_photo_of_count_sheet">Take photo of count sheet</string>
    <string name="text_take_video_of_count_sheet">Take video of count sheet</string>
    <string name="text_take_photo_of_order">Take photo of order</string>
    <string name="text_take_video_of_order">Take video of order</string>
    <string name="text_take_photo_of_pallet">Take photo of pallet</string>
    <string name="text_take_video_of_pallet">Take video of pallet</string>
    <!--load task v1 end-->
    <string name="text_check_is_tablet">Check Is Tablet Device</string>
    <string name="text_is_tablet">The Current Device Is A Tablet</string>
    <string name="text_is_not_tablet">The Current Device Is Not A Tablet</string>
    <string name="text_aisle">Aisle</string>
    <string name="text_bay">Bay</string>
    <string name="text_search_aisle">Search Aisle</string>
    <string name="text_search_bay">Search Bay</string>

    <string name="msg_please_scan_or_input_aisle">Please scan or input Aisle</string>
    <string name="msg_please_scan_or_input_bay">Please scan or input Bay</string>
    <string name="msg_please_configure_adjustment_type_permission_first">Please configure adjustment type permission first</string>

    <string name="text_receipt_number">Receipt Number</string>
    <string name="text_purchase_order_no">Purchase Order No.</string>
    <string name="text_vendor">Vendor</string>
    <string name="text_vendor_item">Vendor Item#</string>
    <string name="text_unis_item">UNIS Item#</string>
    <string name="text_enter_any">Enter %s</string>
    <string name="text_please_enter_any">Please Enter %s</string>
    <string name="text_receive_task_not_found">Receive task not found</string>
    <string name="text_po_no">PoNo:</string>

    <string name="time_elapsed">Time Elapsed</string>

    <!--receive lp setup v1 start-->
    <string name="select_or_search_location">Select or Search Location</string>
    <string name="select_methods_of_receiving">Select Methods of Receiving</string>
    <string name="lowercase_or">or</string>
    <string name="save_configuration">Save Configuration</string>
    <string name="item_config">Item Config</string>
    <string name="auto_config">Auto Config</string>
    <string name="exp_qty">Exp. Qty</string>
    <string name="rec_qty">Rec. Qty</string>
    <string name="partial">Partial</string>
    <string name="failed_to_load_data_retry">Failed to load data. Try again?</string>
    <string name="manufacture_date">Manufacture Date</string>
    <string name="item_condition">Item Condition</string>
    <string name="qty_per_pallet">Qty Per PLT</string>
    <string name="input_lot_no">Input Lot#</string>
    <string name="select_date">Select Date</string>
    <string name="select_manufacture_date">Select Manufacture Date</string>
    <string name="qty_item_selected">%1$s Item Selected</string>
    <string name="remove_lp_mode">Remove LP Mode</string>
    <string name="scan_lp_to_remove">Scan LP to Remove</string>
    <string name="photo_widget_title_upload_photo"><Data><![CDATA[Upload photo<font color="#B71C1C">*</font>]]></Data></string>
    <string name="question_remove_photo">Remove Photo?</string>
    <string name="receiving_qty">Receiving QTY:</string>
    <string name="to_receive_qty">To-Receive QTY:</string>
    <string name="add_more_configuration">Add More Configuration</string>
    <string name="back_to_list">Back To List</string>
    <string name="all_received_close_step">All items have been received. Close this step?</string>
    <string name="xx_is_removed">%1$s is removed</string>
    <string name="xx_add_successfully">%1$s added successfully</string>
    <string name="question_remove_double_quote_xx">Remove \&#034;%1$s\&#034;?</string>
    <string name="exit_remove_mode">Exit Remove Mode</string>
    <string name="lp_count_must_greater_than_zero">LP Count must greater than 0</string>
    <string name="item_no_xx">Item No: %1$s</string>
    <string name="add_item_information">Add Item Information</string>
    <string name="input_info">Input Info</string>
    <string name="input_new_uom">Input new UOM</string>
    <string name="new_uom">NEW UOM</string>
    <string name="uom_setup">UOM Setup</string>
    <string name="inside_uom">Inside UOM</string>
    <string name="select_uom">Select UOM</string>
    <string name="please_select_inside_uom">Please select inside UOM</string>
    <string name="short_desc">Short Desc</string>
    <string name="ask_proceed_to_collect_item_info">Item Information not found, proceed to setup info</string>
    <string name="desc_add_new_item">Need to add extra item? Click to add</string>
    <string name="collapse_all">Collapse All</string>
    <string name="add_configuration">Add Configuration</string>
    <string name="confirmation">Confirmation</string>
    <string name="you_are_at">You are at</string>
    <string name="length_inch">Length (inch)</string>
    <string name="width_inch">Width (inch)</string>
    <string name="height_inch">Height (inch)</string>
    <string name="weight_lbs">Weight (lbs)</string>
    <string name="process_to_configuration">Process to configuration</string>
    <string name="please_setup_default_uom">Please set up default UOM for Item %1$s</string>
    <string name="no_uom_found_for_item">No available UOM was found for Item %1$s</string>
    <string name="msg_barcode_length_not_match">barcode length does not match, please check and re-scan.</string>
    <string name="no_base_unit_configured_for_item">No base unit configured for Item(%1$s), please check!</string>
    <string name="msg_template_is_exist">Template %1$s is exist</string>
    <!--receive lp setup v1 end-->

    <!--offload  v1 start-->
    <string name="text_off_load_verify">VERIFY INFO</string>
    <string name="text_off_load_container">Container Number</string>
    <string name="text_off_load_container_photo">Upload photos of Container*</string>
    <string name="text_off_load_container_photo_or_video">Upload photos or videos of Container*</string>
    <string name="text_off_load_trailer_photo">Upload photos of Trailer*</string>
    <string name="text_off_load_trailer_photo_or_video">Upload photos or videos of Trailer*</string>
    <string name="text_off_load_seal">Seal Number</string>
    <string name="text_off_load_seal_photo">Upload photos of Seal*</string>
    <string name="text_off_load_seal_photo_or_video">Upload photos or videos of Seal*</string>
    <string name="text_off_load_dock_photo">Upload photos of dock door*</string>
    <string name="text_off_load_dock_photo_or_video">Upload photos or videos of dock door*</string>
    <string name="text_off_load_notify">NOTIFY SUPERVISOR</string>
    <string name="text_off_load_type">OFFLOAD TYPE</string>
    <string name="text_off_load_type_seletct_type">Select Offload Type</string>
    <string name="text_off_load_type_seletct_ship">Select Ship Method</string>
    <string name="text_offload_type_checkout">CheckOut</string>
    <string name="text_offload_type_hand">By Hand</string>
    <string name="text_offload_type_hand_shotgun">Hand Shotgun</string>
    <string name="text_offload_type_fwp">Forklift with Pallet</string>
    <string name="text_offload_type_fwp_no">Forklift without Pallet</string>
    <string name="text_offload_type_materials">Materials</string>
    <string name="text_offload_type_add_materials_more">add more materials</string>
    <string name="text_offload_type_Container">Container</string>
    <string name="text_offload_type_trailer">Trailer</string>
    <string name="text_offload_type_equipment">Select Equipment Type</string>
    <string name="text_offload_containers_number">Does container number %s match the photo?</string>
    <string name="text_offload_seal_number">Does seal number %s match the photo?</string>
    <string name="text_offload_dock_number">Does dock number %s match the photo?</string>
    <string name="text_offload_remove_photo_msg">%s photo was removed</string>
    <string name="text_offload_trailer_number">Does trailer number %s match the photo?</string>
    <string name="text_offload_photos">Photos</string>
    <string name="text_offload_photos_or_video">Photos or Videos</string>
    <string name="text_offload_type_equipment_tip">Please Select Equipment Type</string>
    <string name="text_offload_type_equipment_title">Equipment Type</string>
    <string name="text_offload_type_ship_title">Ship Method</string>
    <string name="text_offload_type_offloadtype_tip">Please Select Offload Type</string>
    <string name="text_offload_no_match">%s No Match</string>
    <string name="text_offload_no_match_success">Notify Success!</string>
    <string name="text_offload_no_match_tip">Please Select Container Or Seal No Match</string>
    <string name="text_offload_container_no_match_tip">Please check in with Dock:%s, ContainerNo:%s does not match.</string>
    <string name="text_offload_seal_no_match_tip">Please check in with Dock:%s, SeslNo:%s does not match.</string>
    <string name="text_offload_container_seal_no_match_tip">Please check in with Dock:%s,ContainerNo:%s SeslNo:%s does not match.</string>
    <string name="text_offload_trailer_no_match_tip">Please check in with Dock:%s, TrailerNo:%s does not match.</string>
    <string name="text_offload_trailer_seal_no_match_tip">Please check in with Dock:%s,TrailerNo:%s SeslNo:%s does not match.</string>

    <string name="text_offload_equipment_no_match_match_tip">Please Select Equipment No Match/Match</string>
    <string name="text_offload_seal_no_match_match_tip">Please Select Seal No Match/Match</string>
    <string name="notify_dialog_title">Notify Supervisor</string>
    <string name="notify_dialog_content">Do you want to notify your supervisor?</string>
    <string name="notify_dialog_yes">Yes, Notify</string>
    <string name="by_hand_content">User unloads inventory by hand. Select this option when offloading inventory too small for a forklift.</string>
    <string name="forklift_with_pallet_content">User operates a forklift to offload inventory.
        Select this option when offloading inventory stored on pallets.</string>
    <string name="forklift_without_pallet_content">User operates a forklift with a clamp attachment to offload inventory.
        Select this option when offloading inventory too large to move by hand or items not stored on a pallet but otherwise grouped in a Ti x Hi</string>
    <string name="by_hand_shotgun_content">Choose the Hand Shotgun option when unloading a floor-loaded container with numerous SKUs scattered irregularly. This method is labor-intensive and therefore incurs higher rates due to the increased time and effort required to sort and segregate the items. </string>
    <!--offload  v1 end-->
    <!--sn scan  v1 start-->
    <string name="sn_scan_step_tip1">Scan or input SN</string>
    <string name="sn_scan_step_tip2">Scan LP to Start</string>
    <string name="sn_scan_edittext_tip1">Input or Scan SN</string>
    <string name="sn_scan_edittext_tip2">Input or Scan LP</string>
    <string name="sn_scan_setp_content_lp">LP Staged @ %s</string>
    <string name="sn_scan_setp_content_sn">SN for %s</string>
    <string name="sn_scan_step_tip3">Scan UPC to Start</string>
    <string name="sn_scan_edittext_tip3">Input or Scan UPC</string>
    <string name="sn_scan_setp_content_upc">ITEM for %s</string>
    <string name="sn_scan_remove_sn">Remove SN Mode</string>
    <string name="sn_scan_remove_all_sn">Remove all SN </string>
    <string name="sn_scan_delete_all_sn">delete all SN </string>
    <string name="sn_scan_delete_sn_tip">%s was removed</string>
    <string name="sn_scan_item_not_need_scan">the item is not need to scan</string>
    <string name="sn_scan_lp_not_need">Missing %s LPs</string>
    <string name="sn_scan_master_sn">Scan or input master SN</string>
    <string name="sn_scan_master_sn_title">Master SN</string>


    <string name="sn_scan_step_tip_tablet">Undo</string>
    <string name="sn_scan_step_tip1_tablet">Total LP</string>
    <string name="sn_scan_step_tip2_tablet">LP</string>
    <string name="sn_scan_step_tip3_tablet">scanned</string>
    <string name="sn_scan_step_tip4_tablet">You are at </string>
    <string name="sn_scan_step_tip5_tablet">You scanned </string>
    <string name="sn_scan_step_tip6_tablet">Total SN to Scan</string>
    <string name="sn_scan_step_tip7_tablet">SN</string>
    <string name="sn_scan_step_tip8_tablet">SN for </string>
    <string name="sn_scan_step_tip9_tablet">LP Staged at </string>
    <string name="sn_scan_step_tip10_tablet">Item for </string>

    <string name="sn_scan_step_tip11_tablet">Delete Confirmation</string>
    <string name="sn_scan_step_tip12_tablet">Are you sure you want to delete all SN?</string>
    <string name="sn_scan_step_tip13_tablet">Please confirm short received.</string>

    <string name="sn_scan_invalid_sn">SN is not for container</string>
    <!--sn scan  v1 end-->

    <!--dock_tablet start-->
    <string name="dock_tablet_scan_entry">Scan Entry ID or Container Number</string>
    <string name="dock_tablet_scan_dock">Scan Dock ID</string>
    <string name="label_scan_dock">Scan Dock</string>
    <string name="label_driver_name_mark">Driver Name：</string>
    <string name="label_driver_license_mark_v1">Driver License：</string>
    <!--dock_tablet end-->

    <string name="hint_scan_tracking_no_new">Scan tracking NO.</string>
    <string name="hint_scan_item_upc_aka">Scan Item/UPC/AKA</string>
    <string name="label_not_match">Not Match</string>
    <string name="msg_please_scanned_item_of_correct_qty">Please scanned item of correct qty</string>
    <string name="label_carrier_service">Carrier Service:</string>
    <string name="hint_scan_carrier_service">Scan Carrier Service</string>
    <string name="msg_order_close_success">Order close success</string>
    <string name="msg_invalid_carrier_service">Invalid carrier service</string>
    <string name="msg_order_status_incorrect">%1$s status are not %2$s</string>
    <string name="msg_please_configure_qc_order_status_first">Please configure qc order status first</string>
    <string name="msg_please_scan_carrier_service">Please Scan Carrier Service</string>
    <string name="msg_please_scan_carrier_service_xx">Please Scan Carrier Service: %1$s</string>
    <string name="msg_xx_qc_passed">%s QC Passed.</string>
    <string name="msg_xx_has_been_in_exception_line">%s has been in exception line.</string>
    <string name="msg_dock_null_please_check">Dock is empty. Please check first</string>
    <string name="msg_forbid_scanning_cs_upc_while_piece_picking">Forbid Scanning CS UPC While Piece Picking</string>
    <string name="msg_forbid_scanning_cs_barcode_while_piece_picking">Forbid Scanning CS Barcode While Piece Picking</string>
    <string name="hint_lot_no">Please scan or input lotNo</string>
    <string name="check_lot_no">Lot# not match! Please Enter Again!</string>
    <!-- location split/merge start -->
    <string name="label_location_split_or_merge">Location Split/Merge</string>
    <string name="label_merge_location">Merge Location</string>
    <string name="label_split_location">Split Location</string>
    <string name="text_scan_parent_location">Scan Parent Location:</string>
    <string name="text_scan_location_to_merge">Scan Location to Merge</string>
    <string name="text_scan_location_to_split">Scan Location to Split</string>
    <string name="msg_merge_success">Merge Success</string>
    <string name="msg_split_success">Split Success</string>
    <string name="msg_please_scan_parent_location_first">Please scan parent location first</string>
    <string name="msg_please_scan_location_to_merge">Please scan location to merge</string>
    <string name="msg_cannot_be_the_same_as_the_parent_location">Cannot be the same as the parent location</string>
    <string name="msg_please_scan_location_to_split">Please scan location to split</string>
    <string name="msg_this_location_has_no_child_to_split">This location has no child to split</string>
    <string name="msg_please_scan_the_correct_child_location">Please scan the correct child location</string>
    <string name="msg_this_location_has_be_merged">This location has be merged</string>
    <string name="msg_duplicate_child_location">Duplicate child location</string>
    <string name="menu_switch_1_or_2_segmented_location">Switch 1 or 2 Segmented Location</string>
    <!-- location split/merge end -->
    <string name="remote">Remote</string>
    <string name="local">Local</string>
    <string name="label_server">Server:</string>
    <string name="input_or_scan_server_ip">Input or scan server IP</string>
    <string name="local_print_failed_use_remote">Local server printing failed. Attempting to continue printing using remote server</string>
    <string name="local_printer_not_allowed_select_remote_printer">Printer %s connected by the local print server is not supported to print this type. Please select a remote printer.</string>
    <string name="confirm_selection">Confirm Selection</string>
    <string name="printer_of_size_selection_hint">* Please select a %s printer</string>

    <string name="cycle_count_switch">Switch 1 or 2 Segmented Location</string>
    <string name="cycle_count_switch_title">Segmented Location</string>

    <string name="user_info_no_found_tip">%s no found,please relogin</string>
    <string name="label_user">User</string>
    <string name="msg_step_no_found">Step no found</string>


    <!--home v1 start-->
    <string name="home_pending_task">Pending Tasks</string>
    <string name="home_hello">Hello</string>
    <string name="setting_my_account">My Account</string>
    <string name="home_today_task">%s tasks for you today</string>
    <string name="home_my_profile">My Profile</string>
    <string name="home_chat">Chat</string>
    <string name="home_my_profile_rewards">My Rewards</string>
    <string name="home_my_profile_psw">Password Management</string>
    <string name="home_my_profile_select_facility">Select Facility</string>
    <string name="home_my_profile_select_company">Select Company</string>
    <string name="home_my_profile_select_tip">Please Select First</string>
    <string name="home_my_profile_appearance">Appearance</string>

    <string name="home_my_profile_psw_tip">Your password must be at least 6 characters and should include a combination of numbers, letters, and special characters (!$@%).</string>
    <string name="psw_new_psw">Your new password</string>
    <string name="psw_new_confirm_psw">Confirm new password</string>
    <string name="psw_update">Current password (updated on %s)</string>
    <string name="message_empty_tip">No Message</string>
    <string name="claim_forklift">Claim Forklift</string>
    <string name="scan_forklift_barcode">Scan forklift barcode</string>
    <string name="msg_claim_forklift_success">Claim Forklift Success</string>
    <string name="msg_check_out_forklift_success">Forklift Returned</string>
    <!--home v1 end-->
    <!-- put away auditing task start -->
    <string name="label_put_away_auditing">Put Away Auditing</string>
    <string name="title_put_away_auditing_task_list">Put Away Auditing Task List</string>
    <string name="label_put_away_task_no">PUTAWAY TASK#:</string>
    <string name="label_put_away_by">PUTAWAY BY:</string>
    <string name="label_put_away_when">PUTAWAY WHEN:</string>
    <string name="label_received_by">RECEIVED BY:</string>
    <string name="label_received_when">RECEIVED WHEN:</string>
    <string name="label_put_away_full_pallet_qty">PUTAWAY FULL PALLET QTY:</string>
    <string name="label_put_away_pallet_qty">PUTAWAY PALLET QTY:</string>
    <string name="label_actual_pallet_qty">ACTUAL PALLET QTY:</string>
    <string name="label_actual_qty">| ACTUAL QTY:</string>
    <string name="label_partial_pallet_qty">PARTIAL PALLET QTY:</string>
    <string name="label_scanned_lp_no">SCANNED LP#:</string>
    <string name="msg_input_or_scan_lp_no_of_partial_pallet">Input / Scan LP of Partial Pallet</string>
    <string name="msg_input_or_scan_lp_no_of_full_pallet">Input / Scan LP of Full Pallet</string>
    <string name="label_cs_qty">CS QTY:</string>
    <string name="label_case_qty">CASE QTY:</string>
    <string name="label_put_away_by_and_when">PUTAWAY BY: %1$s at %2$s</string>
    <string name="label_auditing_by">AUDITING BY:</string>
    <string name="btn_previous_location">Previous</string>
    <string name="btn_next_location">Next</string>
    <string name="force_close_task">Force Close Task</string>
    <string name="text_choose_case_uom">Choose Case UOM</string>
    <string name="text_billing_uom_cs">Billing UOM: CS</string>
    <string name="msg_missing_actual_full_pallet_qty">Missing actual full pallet QTY</string>
    <string name="msg_missing_actual_case_qty">Missing actual case QTY</string>
    <string name="msg_missing_scan_lp_of_full_pallet">Missing scan LP of Full Pallet</string>
    <string name="msg_put_away_auditing_not_finish">PutAway Auditing is not finished yet in location[%s].</string>
    <!-- put away auditing task end -->

    <string name="take_photo_confirm">Confirm Photo</string>
    <string name="scanner_msg_scan_default">Please center the barcode on the screen.</string>
    <string name="scanner_msg_scandit_scan_hint">Please place barcode inside the frame</string>
    <string name="scanner_title_scan_equipment">Scan Equipment ID</string>
    <string name="scanner_msg_scan_equipment">Please center the Equipment ID barcode on the screen.</string>
    <string name="scanner_title_scan_entry">Scan Entry ID</string>
    <string name="scanner_msg_scan_entry">Please center the Entry ID barcode on the screen.</string>
    <string name="scanner_title_scan_location">Scan %1$s</string>
    <string name="scanner_msg_scan_location">Please center the location barcode on the screen.</string>
    <string name="scanner_title_scan_aisle">Scan Aisle</string>
    <string name="scanner_msg_scan_aisle">Please center the aisle location barcode on the screen to scan.</string>
    <string name="scanner_title_scan_bay">Scan Bay</string>
    <string name="scanner_msg_scan_bay">Please center the bay location barcode on the screen to scan.</string>
    <string name="scanner_msg_scan_lp">Please center the LP barcode on the screen.</string>
    <string name="text_close_take_photo">Close Take Photo?</string>

    <string name="title_stage_to_load_task">Stage To Load Task</string>
    <string name="title_stage_to_load_task_list">Stage To Load Task List</string>
    <string name="label_scan_lp_pick_task_order">Scan LP/Pick Task/Order</string>
    <string name="label_enter_or_scan_the_staging_location">Enter or Scan the Staging Location</string>
    <string name="label_order_number">Order Number: %1$s</string>
    <string name="label_load_no_colon">Load No.: %1$s</string>
    <string name="label_qty_of_lp">Qty of LP: %1$s</string>
    <string name="msg_please_select_lp_to_stage">Please select lp to stage</string>
    <string name="msg_stage_to_load_lp_no_found">Stage to load lp no found</string>
    <string name="msg_stage_to_load_success">Stage to load success</string>

    <string name="msg_lot_no_location_no_found">location not found for lp: %1$s，lotNo : %2$s</string>
    <string name="menu_collect_material">Collect Material</string>
    <string name="label_receipt">Receipt#</string>
    <string name="label_po">PO#:</string>
    <string name="msg_invalid_tracking">Invalid Tracking#</string>
    <string name="msg_invalid_packing_slip_please_contact_csr">Invalid packing slip, please contact CSR</string>
    <string name="msg_receipt_must_be_imported_ope_status_and_under_the_same_customer">Receipt must be Imported/Open status, and under the same customer</string>
    <string name="title_select_print_options">Select print options</string>
    <string name="print_ilp_by_sku">Print ILP by SKU</string>
    <string name="print_ilp_by_receipt">Print ILP by Receipt</string>
    <string name="msg_tiem_condition_must_be_all_good_type">Item Condition must be all good type</string>

    <string name="title_equipment_inquiry">Equipment Inquiry</string>
    <string name="title_check_equipment_status_and_information">Check equipment status and information</string>
    <string name="search_other_equipment">Search other equipment</string>
    <string name="transfer">Transfer</string>
    <string name="edit_equipment">Edit Equipment</string>
    <string name="inventory_detail">Inventory Detail</string>
    <string name="label_inventory_status_with_colon">Inventory Status:</string>
    <string name="related_task">Related Task</string>
    <string name="msg_related_task_no_found">Related Task No Found</string>
    <string name="text_row">Row</string>
    <string name="equipment_message_tote_is_empty">%1$s is empty.</string>
    <string name="equipment_message_transfer_associated_with_task">%1$s is associated with in progress %2$s Task (%3$s), please complete the task.</string>
    <string name="equipment_message_transfer_associated_with_task_transfer_on_not">%1$s is associated with picked inventory from Pick Task (%2$s). Do you want to transfer the picked inventory to other LP or Tote?</string>
    <string name="equipment_message_before_release">Please make sure the equipment is empty physically before release.</string>
    <string name="equipment_message_release_successful">Release Successful</string>
    <string name="equipment_message_force_release_successful">Force Release Successful</string>
    <string name="equipment_message_associated_to_task">%1$s is associated to an %2$s %3$s Task (%4$s). Please complete the task</string>
    <string name="equipment_message_associated_with_received_inventory_receive_task">%1$s is associated with received inventory from Receive Task (%2$s). Please putaway the received inventory</string>
    <string name="equipment_message_associated_with_received_inventory_receive_task_done">%1$s is associated with received inventory from Receive Task (%2$s).</string>
    <string name="equipment_message_associated_with_pick_inventory_pick_task">%1$s is associated with pick inventory from Pick Task (%2$s). Do you want to putback the picked inventory</string>
    <string name="text_put_back_task">Putback Task</string>
    <string name="equipment_message_putback_task_completed">Putback Task(%1$s) is completed and inventory is putback to Location:\n EQ RELEASE HOLD</string>
    <string name="text_transfer_confirmation">Transfer Confirmation</string>
    <string name="text_confirm_transfer">Confirm Transfer</string>
    <string name="text_generate_lp">Generate LP</string>
    <string name="text_tote_lp_with_colon">Tote/LP:</string>
    <string name="msg_transfer_success">Transfer success</string>
    <string name="msg_no_picked_inventory_need_to_be_transferred">%1$s(%2$s) no picked inventory need to be transferred</string>
    <string name="msg_picked_inventory_still_need_to_be_transferred">Picked inventory in(%1$s) still need to be transferred, Do you want to continue?</string>

    <string name="text_two_step_scanning">2 step scanning</string>
    <string name="text_three_step_scanning">3 step scanning</string>
    <string name="text_date_stamp_lot_no">Date Stamp(LOT No.)</string>
    <string name="text_date_stamp_lot_no_with_colon">date Stamp(Lot No.): </string>
    <string name="text_three_party_pallet_id">Three Party Pallet ID</string>
    <string name="text_three_party_pallet_id_with_colon">3rd Party Pallet ID: </string>
    <string name="text_pallet_detail_info">Pallet Detail Info</string>
    <string name="text_item_number_with_colon">Item Number: </string>
    <string name="text_supplier_id_with_colon">Supplier ID: </string>
    <string name="text_qty_with_colon">qty: </string>
    <string name="msg_cannot_recognize">cannot recognize</string>
    <string name="text_current_lp_counts">Current LP Counts: %d</string>

    <string name="tip_please_wait_until_replenishment_is_finished">Please wait until replenishment %1$s is finished.</string>
    <string name="tip_pick_task_existed_pick_or_nor_first">Pick task(s): %1$s existed, pick or not first?</string>
    <string name="tip_replenishment_task_existed_replenish_or_nor_first">Replenishment task(s): %1$s existed, replenish or not first?</string>
    <string name="label_questionnaire">Questionnaire</string>
    <string name="label_required">Required</string>
    <string name="on_screen_count_complete">on screen count complete</string>
    <string name="text_physically_verify">physically verify</string>
    <string name="msg_before_drop_inventory_confirm_inventory_match_system_record">Before you drop inventory, please physically verify and confirm inventory of the location match system record.</string>
    <string name="msg_before_drop_inventory_confirm_inventory_location_empty">Before you drop inventory, the system indicates that the location is empty. Can you please physically verify and confirm the empty status of the location?</string>
    <string name="msg_after_picking_confirm_inventory_location_empty">After picking, the system indicates that the location is now empty. Can you please physically verify and confirm the empty status of the location?</string>
    <string name="msg_after_picking_confirm_inventory_match_system_record">After picking, please physically verify and confirm inventory of the location match system record.</string>
    <string name="timer_paused">Timer Paused</string>
    <string name="resume_timer_to_continue_task">Resume timer to continue task</string>
    <string name="task_time_alarm_message">Attention: No activity detected for over 10 minutes. Are you still there?</string>
    <string name="label_item_and_lot_no">Item#|Lot#:</string>
    <string name="text_mixed_item">Mixed</string>
    <string name="error_found_multiple_pallet">Found multiple pallet</string>
    <string name="error_scan_lp_of_full_pallet_quantity_exceed_max">Scan Lp of full pallet quantity exceed max quantity</string>
    <string name="msg_input_actual_full_pallet_qty">Please input actual pallet qty first！</string>
    <string name="expected_packages">Expected Packages:</string>
    <string name="received_packages">Received Packages:</string>
    <string name="route_name">Route Name</string>
    <string name="scan_parcel_label">Scan parcel label</string>
    <string name="label_tracking_number">Tracking#</string>
    <string name="label_damaged">Damaged</string>
    <string name="filter_by_tracking">Filter by Tracking</string>

    <string name="title_location_info_collection">Location Info Collection</string>
    <string name="hint_scan_or_input_location_v1">Scan or Input Location</string>
    <string name="text_batch_update">Batch Update</string>
    <string name="label_location_name">Location Name</string>
    <string name="label_temperature_control">Temperature Control</string>
    <string name="label_location_type">Location Type</string>
    <string name="label_location_type_with_colon">Location Type:%1$s</string>
    <string name="label_pick_type">Pick Type</string>
    <string name="label_length_inch">Length(INCH)</string>
    <string name="label_width_inch">Width(INCH)</string>
    <string name="label_height_inch">Height(INCH)</string>
    <string name="label_pallet_deep">Pallet Deep</string>
    <string name="label_stack_high">Stack High</string>
    <string name="label_location_tag">Location Tag</string>
    <string name="label_support_equipment">Support Equipment</string>
    <string name="label_location_pattern">Location Pattern</string>
    <string name="text_view_example">View Example</string>
    <string name="text_use_to_specify_a_range">(Use [001-099] to specify a range)</string>
    <string name="text_view_list">View List</string>
    <string name="text_please_input_location_pattern">Please Input Location Pattern</string>
    <string name="text_usable_location_count">Usable Location Count:%1$d</string>
    <string name="title_location_pattern_example">Location Pattern Example</string>
    <string name="text_location_pattern_example_content">The location is divided into different sections separated by periods, within square brackets [ ], it represents a variable range.</string>
    <string name="text_location_pattern_example_1">Example 1: 01.[001-120]</string>
    <string name="text_location_pattern_example_1_description_front">▪ 01: The number \"01\" must be present.</string>
    <string name="text_location_pattern_example_1_description_behind">▪ [001-120]: Any 3-digit number from 001 to 120 is allowed.</string>
    <string name="text_location_pattern_example_2">Example 2: [241-262].031.[1-2].[1-2]</string>
    <string name="text_location_pattern_example_2_description_front">▪ [241-262]: Any 3-digit number from 241 to 262 is allowed.</string>
    <string name="text_location_pattern_example_2_description_middle">▪ 031: The sequence \"031\" must be present.</string>
    <string name="text_location_pattern_example_2_description_behind">▪ [1-2]: Any number from 1 to 2 is allowed.</string>
    <string name="msg_please_input_pallet_deep">Please input pallet deep</string>
    <string name="title_location_list">Location List</string>
    <string name="text_location_count">%1$d of %2$d</string>
    <string name="msg_please_provide_the_usable_location_before_proceeding_batch_update">Please provide the usable location before proceeding BATCH UPDATE.</string>
    <string name="msg_please_select_status">Please select status</string>
    <string name="msg_please_select_temperature_control">Please select temperature control</string>
    <string name="msg_please_select_location_type">Please select location type</string>
    <string name="msg_please_select_pick_type">Please select pick type</string>
    <string name="msg_please_select_capacity_type">Please select capacity type</string>
    <string name="msg_please_select_stack_high">Please select stack high</string>
    <string name="msg_please_input_correct_location_pattern">Please input correct location pattern</string>
    <string name="label_container_number">Container#:</string>
    <string name="label_scanned_expected">Scanned / Expected:</string>
    <string name="next_facility">Next Facility</string>
    <string name="view_local_exception_list">View Local Exception List</string>
    <string name="label_scan_time">Scan Time</string>
    <string name="input_parcel_label_switch">Input Parcel Label Switch</string>
    <string name="input_parcel_label_switch_message">Is it allowed to enter parcel labels manually?</string>
    <string name="label_scan_type">Scan Type:</string>
    <string name="last_mile_qc">Last Mile</string>
    <string name="title_local_delivery_driver_qc">Local Delivery Driver QC</string>
    <string name="hint_scan_parcel_label">Enter or scan parcel label</string>
    <string name="label_route">Route:</string>
    <string name="label_area_code">Route NO:</string>
    <string name="label_assigned_driver">Assigned Driver:</string>
    <string name="label_load_packages">Load Packages:</string>
    <string name="label_driver_scan_packages">Driver Scanned Packages:</string>
    <string name="error_package_qty_not_match">Package quantities do not match. \nQC and driver must both sign to release the load for delivery.</string>
    <string name="title_package_qty_not_match">Package qty is not match</string>
    <string name="text_package_qty_not_match">Have the driver scan all packages again, or you can release the load for delivery.</string>
    <string name="label_qc_signature">QC Signature </string>
    <string name="label_driver_signature">Driver Signature</string>
    <string name="btn_sign">Release for Delivery </string>
    <string name="btn_rescan">Scan Again </string>
    <string name="error_qc_upload_failed">QC Signature upload failed </string>
    <string name="error_driver_upload_failed">Driver Signature upload failed </string>
    <string name="title_overview">Overview </string>
    <string name="text_trip">Trip </string>
    <string name="text_route">Route </string>
    <string name="text_route_no_and_driver">Route# / Driver</string>
    <string name="text_exception">Excep.</string>
    <string name="text_packages">Pkgs</string>
    <string name="text_actual_and_expected">Actual/Expected</string>
    <string name="text_actual">Actual</string>
    <string name="text_expect">Expect</string>
    <string name="text_expect_qty">Expected Qty</string>
    <string name="search_route">Please input route number</string>
    <string name="search_driver">Please input driver name</string>
    <string name="text_drive_name">Driver name</string>
    <string name="label_routes">Route(s)</string>
    <string name="label_plate_num">Plate number</string>
    <string name="label_qc_name">QC name</string>
    <string name="label_total_pkgs">Total pkgs</string>
    <string name="text_overage">Overage</string>
    <string name="text_shortage">Shortage</string>
    <string name="label_result">Result</string>
    <string name="hint_input_short_volume">please input short volume</string>
    <string name="hint_input_over_volume">please input over volume</string>
    <string name="text_scan_aibill_number">Scan Airbill Number</string>
    <string name="text_sequence">Sequence</string>
    <string name="msg_confirm_delete_track_no">Are you sure delete NO# %s?</string>
    <string name="not_assigned">Not Assigned</string>
    <string name="hint_driver_sign">Driver please sign</string>
    <string name="text_switch_to_scanner">Switch to scanner or barcode</string>
    <string name="message_result_overwritten">The result will be overwritten are you sure scan</string>
    <string name="text_how_many_packages_count">How many packages do you count?</string>
    <string name="hint_input_package_count">Please input package count</string>
    <string name="text_over">Over</string>
    <string name="text_short">Short</string>
    <string name="menu_scan">Scan</string>
    <string name="text_invalid">Invalid</string>
    <string name="text_duplicated">Duplicated</string>
    <string name="label_occurred_qty">Occurred QTY: </string>
    <string name="network_unavailable">Network Unavailable</string>
    <string name="label_section">Section</string>
    <string name="upload_lso_tracking_scan_exceptions">Upload LSO Tracking# Exceptions</string>
    <string name="msg_no_exceptions_to_upload">There are currently no exceptions to upload.</string>
    <string name="msg_exceptions_uploading">Exception uploading, please try again later.</string>
    <string name="message_count_again">Do you want to count again</string>
    <string name="msg_lso_tracking_exceptions_upload_success">LSO Tracking# Exceptions upload success</string>
    <string name="msg_item_received_dates_differ">Item received dates differ. Please confirm if you want to put items in the same pallet</string>
    <string name="msg_item_expiration_dates_differ">Item expiration dates differ. Please confirm if you want to put items in the same pallet</string>
    <string name="text_barcode">Barcode</string>
    <string name="exception_package_list">Exception package list</string>
    <string name="label_asset_assigned">Asset Assigned</string>
    <string name="label_view_assigned_assets">View Assigned Assets</string>
    <string name="label_asset_inquiry">Asset Inquiry</string>
    <string name="label_view_asset_status">View Asset Status</string>
    <string name="label_move_asset">Move Asset</string>
    <string name="label_change_the_storage_location">Change the Storage Location</string>
    <string name="label_asset_audit">Asset Audit</string>
    <string name="label_report_issue">Report Issue</string>
    <string name="hint_scan_or_enter_asset_barcode">Scan or enter asset barcode</string>
    <string name="hint_scan_or_enter_move_to_location">scan or enter move to location</string>
    <string name="msg_please_scan_or_enter_move_to_location">Please scan or enter move to location</string>
    <string name="label_move_from_user_name">Move From(User Name)</string>
    <string name="title_asset_details">Asset Details</string>
    <string name="title_checked_out">Checked Out</string>
    <string name="title_move_asset">Move Asset</string>
    <string name="title_assign_asset">Assign Asset</string>
    <string name="title_unassign_asset">Unassign Asset</string>
    <string name="title_review_assignee">Review Assignee</string>
    <string name="title_asset_moved">Asset Moved</string>
    <string name="title_current_log_record">Current Log Record</string>
    <string name="msg_please_scan_or_asset_barcode">Please scan or enter asset barcode</string>
    <string name="text_print_barcode">Print Barcode</string>
    <string name="text_move">Move</string>
    <string name="text_assign_asset">Assign Asset</string>
    <string name="text_unassign_asset">Unassign Asset</string>
    <string name="label_current_location">Current Location:</string>
    <string name="label_asset_name">Asset Name</string>
    <string name="label_asset_assignee">Asset Assignee</string>
    <string name="label_select_asset_assignee">Select Asset Assignee</string>
    <string name="label_change_assignee">Change Assignee</string>
    <string name="text_unassign">Unassign</string>
    <string name="label_reason_code">Reason Code:</string>
    <string name="label_check_out_date">Check Out Date:</string>
    <string name="label_name_of_user">Name of User:</string>
    <string name="msg_unassign_this_asset">You are about to unassign this asset, and the action is irreversible. Are you sure you want to continue?</string>
    <string name="msg_asset_no_found">Asset no found</string>
    <string name="text_assigned_to_you">Assigned to you</string>
    <string name="msg_you_successfully_checked_out">You successfully checked out</string>
    <string name="msg_your_asset_has_been_moved_successfully">Your asset has been moved successfully</string>
    <string name="msg_this_location_is_not_allowed_please_scan_different_location">This location is not allowed. Please scan different location</string>
    <string name="msg_this_user_is_not_allowed_please_select_another_user">This user is not allowed. Please select another user</string>
    <string name="label_asset_barcode">Asset Barcode</string>
    <string name="label_asset_name_with_colon">Asset Name:</string>
    <string name="label_asset_description">Asset Description:</string>
    <string name="label_date_time"><![CDATA[Date & Time:]]></string>
    <string name="label_assigned_by2">Assigned By:</string>
    <string name="msg_you_do_not_have_a_permission_to_check_in">You do not have a permission to check in. Please see you supervisor.</string>
    <string name="msg_you_are_about_to_check_in_the_asset">You are about to check in the asset. Would you like to continue?</string>
    <string name="title_permission_denied">Permission Denied</string>
    <string name="text_yes_continue">Yes, Continue</string>
    <string name="hint_scan_or_enter_asset_to_use">Scan or enter asset(s) to use</string>
    <string name="label_select_assignee_asset">Select Assigned Assets</string>
    <string name="title_select_asset">Select Assets</string>
    <string name="text_add_asset">Add Asset</string>
    <string name="text_added">Added</string>
    <string name="text_not_added">Not Added</string>
    <string name="title_remove_asset">Remove Asset</string>
    <string name="title_check_in_asset">Check In Asset</string>
    <string name="text_checked_in">Checked In</string>
    <string name="msg_this_asset_status_is_please_another_asset">This asset\'s status is %1$s. Please scan another asset</string>
    <string name="msg_you_are_about_to_remove_this_asset">You are about to remove this asset, and this action is irreversible. Are you sure you want to continue?</string>
    <string name="msg_this_asset_cant_be_used_in_please_another_asset">This asset can\'t be used in %1$s task. Please scan another asset</string>
    <string name="msg_this_asset_is_being_used_by_others_please_another_asset">This asset is being used by others. Please scan another asset</string>
    <string name="msg_this_asset_user_role_is_not_allow_please_another_asset">This asset\'s user role is not allowed. Please scan another asset</string>
    <string name="msg_this_asset_cant_be_used_in_task_please_another_asset">This asset can\'t be used in task. Please scan another asset</string>
    <string name="menu_scan_asset">Scan Asset</string>
    <string name="msg_get_assigned_asset_failure">Get assigned asset failure</string>
    <string name="text_child_asset">Child asset</string>
    <string name="title_detailed_version_attached">Detailed Version Attached</string>
    <string name="hint_please_input_xxx">Please input %1$s</string>
    <string name="msg_please_select_date">Please select date</string>
    <string name="msg_please_select_xxx">Please select %1$s</string>
    <string name="lso_exception_hold">LSO Exception Hold</string>
    <string name="lso_exception_hold_summary">Update package status</string>
    <string name="selected_packages">Selected Packages</string>
    <string name="date_from">Date From</string>
    <string name="label_reason">Reason</string>
    <string name="label_other_reason">Other reason</string>
    <string name="msg_date_from_to_error">Date To must be after Date From</string>
    <string name="text_update_status">Update Status</string>
    <string name="title_multiple_ship_to_address">Warning:Multiple Ship-To Address</string>
    <string name="text_multiple_ship_to_address_detected">Multiple Ship-To Address detected,are sure to combine pallets?</string>
    <string name="msg_item_is_already_in_this_lp_Continue">Item %1$s is already in this LP. Continue?</string>
    <string name="msg_this_location_has_already_been_counted_continue">This location %1$s has already been counted. Continue?</string>
    <string name="msg_this_location_already_counted_delete_previous_continue">Location already counted. Recounting will delete the previous count entry. Do you want to continue the recount?</string>
    <string name="title_child_asset">Child Asset</string>
    <string name="label_parent_asset">Parent Asset:%1$s</string>
    <string name="text_location_done">Location Done</string>
    <string name="msg_please_double_confirm_current_location_complete">Please double confirm current location complete?</string>
    <string name="msg_done_location_success">Done location success</string>
    <string name="label_rfid_device">RFID Device</string>
    <string name="text_connected_rfid_device">Connected Device : %s</string>
    <string name="text_rfid_device_disconnect">RFID device disconnected</string>
    <string name="error_rfid_server_not_found">RFID Server Not Found, Please contact IT Admin</string>
    <string name="none_paired">No Devices Found</string>
    <string name="title_available_readers">Available RFID Readers</string>
    <string name="label_rfid">RFID</string>
    <string name="label_rssi">RSSI</string>
    <string name="text_rfid_count">RFID Count</string>
    <string name="msg_exit_connect_rfid_reader">You are not connected to the RFID Device.Are you sure want to exit?</string>
    <string name="msg_disconnect_rfid_reader">Are you sure you want to disconnect device?</string>
    <string name="rfid_scan_step_tip6_tablet">Total RFID to Scan</string>
    <string name="rfid_scan_step_tip12_tablet">Are you sure you want to delete all RFID?</string>
    <string name="rfid_amount">RFID amount</string>
    <string name="hint_please_scan_or_input_lp_to_collect">Please Scan or Input Lp to Collect</string>
    <string name="hint_please_scan_or_input_lp_to_drop">Please Scan or Input Lp to Drop</string>
    <string name="hint_please_scan_or_input_drop_location">Please Scan or Input Drop Location</string>
    <string name="label_collect_from">Collect From: %1$s</string>
    <string name="label_item_with_colon">Item: %1$s</string>
    <string name="label_collect_list">Collect list</string>
    <string name="text_drop_all">Drop All</string>
    <string name="label_suggest_drop_location">Suggest Drop Location: %1$s</string>
    <string name="label_drop_location">Drop Location: %1$s</string>
    <string name="msg_item_not_included_in_the_current_task">Item not included in the current task. Please collect item %1$s.</string>
    <string name="msg_lp_belongs_to_another_location_please_confirm_it">This LP belongs to another location. Please confirm you are currently at location %1$s</string>
    <string name="msg_some_lps_with_item_in_this_location">Some LPs with \'%1$s\' in this location. Unable to find them?</string>
    <string name="text_find">Find</string>
    <string name="text_not_find">Not Find</string>
    <string name="msg_the_source_is_full_please_close_this_task">The source location is full, please close this task</string>
    <string name="msg_suggest_to_location_no_found">Suggest to location no found</string>
    <string name="msg_this_lp_exist_multi_item">This lp exist multi item</string>
    <string name="msg_this_lp_has_been_collected">This LP has been collected</string>

    <string name="btn_oversize_handling">Oversize Handling</string>
    <string name="title_oversize_carton_handling">Oversize Carton Handling</string>
    <string name="text_oversized">Oversized</string>
    <string name="text_item_in_box">Item In Box</string>

    <string name="length_mark">Length:</string>
    <string name="width_mark">Width:</string>
    <string name="height_mark">Height:</string>
    <string name="weight_mark">Weight:</string>
    <string name="storage_permission_content">App upgrades require storage permissions to download and update, please allow storage permissions.</string>
    <string name="label_rfid_door">RFID Door</string>
    <string name="text_scanned_items">Scanned Items</string>
    <string name="msg_remove_scanned_items">Please remove the Item(s) color in Red,Then scan again</string>
    <string name="msg_please_press_enter_to_save_before_done">Please press \"ENTER\" to save before done!</string>
    <string name="msg_item_cant_be_done_for_no_count_record">Item can\'t be done for no count record</string>
    <string name="msg_no_count_record_found_is_this_location_empty">No count record found. Is this location empty?</string>
    <string name="label_search_by">Search By:</string>
    <string name="text_inventory_status">Inventory Status</string>
    <string name="msg_status_on_found_in_the_location">Status: %1$s no found in this location</string>
    <string name="msg_return_goods_should_be_put_away_by_item">Return Goods Should be put away by Item</string>
    <string name="msg_returned_goods_without_doing_put_away_cant_do_movement">Returned goods without doing put away, can\'t do movement</string>
    <string name="msg_returned_goods_without_doing_put_away_cant_do_replenishment">Returned goods without doing put away, can\'t do replenishment</string>
    <string name="title_confirm_total_hours">Confirm Total Hours</string>
    <string name="msg_please_confirm_the_total_hours_you_spent_on_this_receiving_task">Please confirm the total hours you spent on this receiving task</string>
    <string name="label_total_hours">Total Hours:</string>
    <string name="msg_please_input_total_hours">Please input total hours</string>
    <string name="msg_please_enter_a_value_greater_than_zero">Please enter a value greater than zero</string>
    <string name="msg_limit_input_two_decimal">Limit input two decimal</string>
    <string name="label_slip_sheets_quantity">Slip Sheets Quantity</string>
    <string name="hint_please_input_slip_sheets_quantity">Please input slip sheets quantity</string>
    <string name="overlay_permission_content">Wise needs to request overlay permission to display messages on top of other applications.</string>
    <string name="cubit_scan_request_data_error">cubitScanner server is not requested, please check service configuration</string>
    <string name="update_item_info">update item info</string>
    <string name="select_item_group">select group</string>
    <string name="please_select_item_group">please select item group</string>
    <string name="not_found_lp_for_customer">The customer corresponding to LP cannot be found</string>
    <string name="scan_other_pallet">Oops! Wrong pallet. Scan a pallet for %s to continue</string>
    <string name="title_clp_bonding_task">CLP Bonding Task</string>
    <string name="robot_slot_wall_slot_to_clp_clp_box_exchange">Robot Slot &amp; Wall Slot to CLP &amp; CLP Box Exchange, CLP Package Update</string>
    <string name="robot_slot_to_clp">Robot Slot to CLP</string>
    <string name="wall_slot_to_clp">Wall Slot to CLP</string>
    <string name="clp_box_exchange">CLP Box Exchange</string>
    <string name="clp_package_update">CLP Package Update</string>
    <string name="scan_robot_slot_number">Scan Robot Slot #</string>
    <string name="scan_wall_slot_number">Scan Wall Slot #</string>
    <string name="scan_clp_label">Scan CLP Label</string>
    <string name="label_scan_clp_colon">Scan CLP:</string>
    <string name="label_scan_item_option_colon">Scan Item(Option):</string>
    <string name="text_add_item">Add Item</string>
    <string name="text_update_qty">Update Qty</string>
    <string name="text_split_clp">Split CLP</string>
    <string name="text_dn">DN</string>
    <string name="split_to_new_clp">Split to New CLP</string>
    <string name="transfer_to_existing_clp">Transfer to Existing CLP</string>
    <string name="new_print_clp">New Print CLP</string>
    <string name="text_split_clp_colon">Split CLP:</string>
    <string name="scan_existing_clp">Scan Existing CLP</string>
    <string name="confirm_new_qty">Confirm New QTY:</string>
    <string name="scan_item_colon">Scan Item:</string>
    <string name="suggest_from_location_colon">Suggest From Location:</string>
    <string name="verify_location_colon">Verify Location:</string>
    <string name="add_qty_colon">Add QTY:</string>
    <string name="print_new_clp_label">Print New CLP Label</string>
    <string name="review_package_details">Review Package Details</string>
    <string name="text_order_id">Order ID</string>
    <string name="label_destination">Destination</string>
    <string name="label_from_slot">From Slot</string>
    <string name="label_box_number">Box #</string>
    <string name="label_item_upc">Item UPC</string>
    <string name="carton_package_dimension">Carton Package Dimension</string>
    <string name="transfer_package_data">Transfer Package Data</string>
    <string name="from_clp">From CLP</string>
    <string name="to_clp">To CLP</string>
    <string name="msg_package_info_no_found">Package info no found</string>
    <string name="not_find_unbound_package_by_slot">Cannot find unbound package data by Slot: %s</string>
    <string name="msg_package_data_has_been_successfully_transferred">Package Data has been successfully transferred.</string>
    <string name="msg_data_transfer_failed_please_try_again">Data transfer failed, please try again. %s</string>
    <string name="msg_package_dimension_no_found_please_contact_you_system_admin_to_add">Package Dimension no found, Please contact you system admin to add</string>
    <string name="text_stage_to_location">Stage to Location</string>
    <string name="msg_please_print_new_clp">Please print new clp</string>
    <string name="msg_please_scan_existing_clp">Please scan existing clp</string>
    <string name="msg_please_enter_qty">Please enter qty</string>
    <string name="msg_please_select_carton_package_dimension">Please select Carton Package Dimension</string>
    <string name="msg_item_on_found_in_the_list">Item: %1$s no found in the list</string>


    <string name="text_pick_to_table_qty_progress"> %1$s/%2$s SKU picked, %3$s/%4$s SKU staged</string>
    <string name="text_stage_all_empty_slot">Stage all of the items to an empty slot box</string>
    <string name="text_scan_input_slot_label">Scan or Input the Slot label</string>
    <string name="text_suuggest_an_empty_slot">Suggest an empty slot</string>
    <string name="text_opportunity_pick">Opportunity Pick</string>
    <string name="msg_item_not_included">Item not included in the current task</string>
    <string name="msg_item_not_pick">Item has not been picked yet</string>
    <string name="msg_item_not_stage">Item has been staged</string>
    <string name="msg_stage_qty_pick">Stage qty must less than or equal picked qty</string>
    <string name="text_suggest_slot">Suggest Slot: %1$s</string>
    <string name="text_suggest_slot_not_empty">The Slot %1$s is not Empty</string>
    <string name="msg_item_stage_complete">Item is complete stage</string>

    <string name="sku_marker">SKU:</string>
    <string name="text_go_to_next_order">Go to next order</string>
    <string name="slot_marker">Slot:</string>
    <string name="msg_order_pick_qty_pick"> Qty must less than or equal picked qty</string>
    <string name="msg_done_stage_first">Please finish Stage step</string>


    <string name="sort_to_slot_marker">Sort to Slot:</string>
    <string name="hint_scan_solt_barcode">Scan Slot Barcode</string>
    <string name="text_sort_qty_progress"> %1$s/%2$s SKU picked, %3$s/%4$s SKU sorted</string>
    <string name="msg_sort_qty_pick">Sort qty must less than or equal picked qty</string>
    <string name="cycle_count_suggest_item">Suggest Item: </string>
    <string name="please_input_suggestion_item">Please count %s only. Item %s is not included in this task</string>

    <!-- hospital task -->
    <string name="hospital_task">Hospital Task</string>
    <string name="create_hospital_task">Create Hospital Task</string>
    <string name="create_hospital_tasks">Create Hospital Task(s)</string>
    <string name="select_items">Select Item(s)</string>
    <string name="request_qty">Requested QTY: %s</string>
    <string name="locations">Location(s): %s</string>
    <string name="request_qty_label">Requested QTY</string>
    <string name="requested_reason_label">Requested Reason</string>
    <string name="target_completion_data">Target Completion Date</string>
    <string name="request_time">Requested Time</string>
    <string name="error_msg_select_customerId">Please select Customer</string>
    <string name="error_msg_input_item">Please input item name</string>
    <string name="error_msg_input_reason">Please input reason</string>
    <string name="error_msg_input_other_reason">Please input other reason</string>
    <string name="hospital_reason_item_not_found">Item Not Found</string>
    <string name="hospital_reason_location_not_pickable">Location Not Pickable</string>
    <string name="hospital_reason_ilp_on_hold">ILP ON Hold</string>
    <string name="hospital_reason_inventory_discrepancey">Inventory Discrepancey</string>
    <string name="hospital_reason_customer_requested">Customer Requested</string>
    <string name="hospital_reason_item_on_hold">Item On Hold</string>
    <string name="hospital_reason_item_in_LIW_location">Item In LIW location</string>
    <string name="hospital_reason_need_adjust">Need Adjust Title/UOM/Status</string>
    <string name="hospital_reason_other">Other</string>
    <string name="hospital_task_has_been_create">Hospital task has been created</string>
    <string name="hospital_task_list">Hospital Task List</string>
    <string name="hospital_location_active_date">Date</string>
    <string name="label_comments">Comments</string>
    <string name="label_new_cycle_count">+ New Count Task</string>
    <string name="label_click_here">Click Here</string>
    <string name="hint_please_input_dn">Input DN</string>
    <string name="hint_please_input_other_reason">Input other reason</string>
    <string name="hint_please_select_result">Please Select Result</string>
    <string name="title_select_user">Select User</string>
    <string name="title_new_count_task">New Count Task(s)</string>
    <string name="select_item_with_item">Location(s) with this item:</string>
    <string name="count_by_text">Count by</string>
    <string name="error_msg_select_customer">please select customer</string>
    <string name="error_msg_select_item">please select item</string>
    <string name="error_msg_select_reason">please select reason</string>
    <string name="error_msg_select_other_reason">please input other reason</string>
    <string name="hint_please_select_record">please select cycle count record</string>
    <string name="hint_please_select_adjust">please input adjustmentId</string>
    <string name="menu_processing_order_list">Processing Order List</string>
    <string name="label_assigned_at">Assigned at:</string>
    <string name="text_orders_finished">%1$s/%2$s Orders Finished</string>
    <string name="msg_no_other_order_can_take_over">No other order can take over</string>

    <string name="error_please_input_DN">please input DN</string>
    <string name="hint_please_scan_or_input_user">Please scan or input user</string>
    <string name="hint_please_scan_or_input_cycle_record">scan or input cycle record</string>
    <string name="hint_please_input_user">Please input user</string>
    <string name="create_hospital_task_from_input">create task manually</string>
    <string name="user_not_found">User Not Found</string>
    <string name="cr_not_found">CR Not Found</string>
    <string name="please_input_valid_pick_location">Invalid location, please input pick location.</string>
    <string name="text_enter_or_scan_lp_3rd_party_lpn">Enter or Scan LP/3rd party LPN</string>
    <string name="hint_scan_lp_rn_3rd_party_lpn">Scan LP/RN/3rd party LPN</string>
    <string name="hint_scan_putaway_lp_3rd_party_lpn">Scan Putaway LP/3rd party LPN</string>
    <string name="msg_invalid_third_party_lpn_format">Invalid Third Party LPN format</string>
    <string name="msg_duplicate_lpn_detected_lpns_must_be_unique">Duplicate LPN detected. LPNs must be unique. Please enter a different LPN</string>
    <string name="msg_this_lpn_no_set_up_for_receive">This Third Party LPN no set up for receive</string>
    <string name="msg_the_data_has_changed_please_try_again">The data has changed, please try again.</string>
    <string name="msg_duplicate_lp_putaway">LP put away complete (%s). No further action required.</string>
    <string name="batch_movement_valid_receiving_lp">This LP cannot be moved. It is currently in receiving status.</string>
    <string name="receive_full_pallet">Full Pallet: %s</string>
    <string name="receive_partial_pallet">Partial Pallet: %s</string>
    <string name="outbound_collect_sn">Collect SN</string>
    <string name="outbound_collect_rfid">Collect RFID</string>
    <string name="clp_bonding_transfer_empty">clp bonding transfer package data is empty</string>
    <string name="text_enter_or_scan_lp_carton_id">Enter or Scan LP/CartonId</string>
    <string name="hint_please_scan_item_label_carton">Please scan item label/CartonId</string>
    <string name="msg_receive_item_line_no_relation_to_the_carton">Receive item line no relation to the carton</string>
    <string name="msg_receive_item_line_no_relation_to_item_label">Receive item line no relation to %1$s</string>
    <string name="msg_there_are_multiple_packages_containing_item">There are multiple packages containing %1$s. Please scan the package ID to confirm</string>
    <string name="msg_cartonid_no_match">CartonId no match</string>
    <string name="hint_close_stage_step_msg">All LPs have been staged, close this step?</string>
    <string name="label_carton_quality">Carton Quality</string>
    <string name="label_rn">RN</string>
    <string name="label_carton_qty">Carton Qty</string>
    <string name="msg_please_input_carton_qty">Please input carton qty</string>
    <string name="msg_no_carton_info_please_use_single_item_receive">no carton info, please use single item receive</string>
    <string name="msg_scanned_location_differs_from_search_suggested_put_away_here_anyway">Scanned location(%1$s) differs from search/suggested. Put away here anyway?</string>
    <string name="title_consolidate_pallet_task">Consolidate to Pallet Task</string>
    <string name="label_describe_consolidate_pallet">Create an empty Consolidate Pallet Task</string>
    <string name="consolidate_to">Consolidate to</string>
    <string name="create_new_slp">Generate &amp; Print a new SLP</string>
    <string name="continue_to_consolidate">Continue to Consolidate</string>
    <string name="scan_lps_to">Scan LP(s) to</string>
    <string name="text_box">Box</string>
    <string name="text_PO">PO</string>
    <string name="text_go_back_to_update">Go back to update</string>
    <string name="confirm_to_consolidate">Confirm to Consolidate</string>
    <string name="consolidate_success_tips">There are %s CLP(s) consolidated to the parent SLP %s</string>
    <string name="consolidate_another">Consolidate Another</string>
    <string name="hint_please_input_or_scan_slp">please input or scan slp</string>
    <string name="hint_consolidate_input_or_scan_clp">LP will be added to the SLP</string>
    <string name="please_input_consolidate_clp">please input or scan need consolidate clp</string>
    <string name="label_selected_location">Selected Location:</string>
    <string name="label_suggestion_location_with_colon">Suggestion Location:</string>
    <string name="msg_get_customer_fail">get customer fail</string>
    <string name="msg_get_task_fail">get task fail</string>
    <string name="label_scan_or_input_load_id_optional">Scan or input Load ID (Optional)</string>
    <string name="hint_please_input_or_scan_load_id">please input or scan load id</string>
    <string name="menu_unconsolidated_boxes">Unconsolidated Boxes</string>
    <string name="msg_no_unconsolidated_boxes">no unconsolidated boxes</string>
    <string name="text_boxes_consolidated">%1$s/%2$s boxes consolidated</string>
    <string name="text_received_by">Received By</string>
    <string name="material_item_name">Item Name(short desc.)</string>
    <string name="text_expected_qty">Exp.Qty</string>
    <string name="text_received_qty">Rec.Qty</string>
    <string name="wrong_password_or_supervisor">Wrong password or user name</string>
    <string name="hint_please_input_or_scan_item">please input or scan item name</string>
    <string name="msg_delete_this_data">Delete this data?</string>
    <string name="sku_count">SKU Count:</string>
    <string name="scanned_result">Scanned Results:</string>
    <string name="rfid_qty">RFID QTY</string>
    <string name="lp_level_item_qty">LP Level Item QTY</string>
    <string name="rfid_results_error">Please verify the highlighted line item and scan again.</string>
    <string name="label_search_for_asset">Search for Asset</string>
    <string name="hint_enter_or_scan_asset_id_or_vin">Enter or scan asset ID or VIN</string>
    <string name="title_asset_audit_task_id">Asset Audit Task - %1$s</string>
    <string name="label_total_asset_in_task">Total Asset In Task</string>
    <string name="title_asset_detail">Asset Detail</string>
    <string name="msg_this_item_is_not_recorded_in_the_current_facility">This item is not recorded in the current facility.</string>
    <string name="msg_please_pay_attention_and_continue">Please pay attention and continue.</string>
    <string name="msg_please_check_the_values_and_type">Please check the values and type the current value if they are not the same.</string>
    <string name="title_asset_audit_task">Asset Audit Task</string>
    <string name="title_audit_tasks">Audit Tasks</string>
    <string name="text_detail">Detail</string>
    <string name="text_start_date">Start date</string>
    <string name="text_plan_start_date">Plan start date</string>
    <string name="text_done_date">Done date</string>
    <string name="msg_before_you_begin_make_sure_you_have_items_from_these_categories_ready">Before you begin, make sure you have items from these categories ready.</string>
    <string name="text_expected_items">Expected %1$s items</string>
    <string name="text_is_this_item_still_in_use">Is this item still in use?</string>
    <string name="text_please_take_one_or_more_photos">Please take one or more photos.</string>
    <string name="hint_select_an_reason">select an reason</string>
    <string name="msg_please_select_an_reason">Please select an reason</string>
    <string name="text_model_no">Model No.</string>
    <string name="hint_current_facility">Current Facility</string>
    <string name="msg_please_input_note">Please input note</string>
    <string name="hint_select_an_issue_type">Select an issue type</string>
    <string name="hint_please_select_an_issue_type">Please select an issue type</string>
    <string name="text_please_take_photos_as_proof">Please take photos as proof.</string>
    <string name="msg_asset_issue_reported_successfully">%1$s issue reported successfully.</string>
    <string name="msg_please_input_select_current_value">Please input/select current %1$s</string>
    <string name="select_vlg_tips">Select the VLG\n(Virtual Location Group)\nto do the Opportunity Pick</string>
    <string name="start_to_stage">Start to Stage</string>
    <string name="un_staged_sku">Unstaged SKUs</string>
    <string name="label_item_id">Item ID</string>
    <string name="label_item_description">Item Description</string>
    <string name="return_to_stage">Return to Stage</string>
    <string name="text_suggest_location">Suggest location</string>
    <string name="msg_asset_not_received">%1$s has not been received into the inventory, please create an asset receiving task for this asset.</string>
    <string name="text_equipment_inspection">Equipment Inspection</string>
    <string name="text_equipment_no">Equipment #</string>
    <string name="text_gate_check_in_time">Gate Check In Time</string>
    <string name="label_bad">Bad</string>
    <string name="text_comment_optional">Comment (OPTIONAL)</string>
    <string name="text_inside_door">Inside Door</string>
    <string name="text_inside_ceiling">Inside Ceiling</string>
    <string name="text_inside_floor">Inside Floor</string>
    <string name="text_inside_left_wall">Inside Left Wall</string>
    <string name="text_inside_right_wall">Inside Right Wall</string>
    <string name="text_inside_inner_wall">Inside Inner Wall</string>
    <string name="msg_select_condition">please select condition for %s</string>
    <string name="transit_package_label">Transit package label</string>
    <string name="print_transit_package_label">Print Transit Package Label</string>
    <string name="terminal_text">Terminal</string>
    <string name="label_receive_an_asset">Receive an Asset</string>
    <string name="label_asset_information">Asset Information</string>
    <string name="label_photo_collection">Photo Collection</string>
    <string name="label_asset_info">Asset Info</string>
    <string name="hint_select_a_category">Select a Category</string>
    <string name="hint_select_an_item_model_no">Select an Item Model No</string>
    <string name="hint_select_an_ownership_type">Select an Ownership Type</string>
    <string name="msg_please_select_a_catecory_first">Please select a category first</string>
    <string name="hint_vin">VIN</string>
    <string name="hint_serial_number">Serial Number</string>
    <string name="hint_using_company">Using Company</string>
    <string name="label_additional_inventory_detail">Additional Inventory Detail</string>
    <string name="hint_model_year">Model Year</string>
    <string name="hint_select_a_condition">Select a condition</string>
    <string name="hint_select_a_using_company">Select a Using Company</string>
    <string name="label_photo_of_asset">Photo of Asset</string>
    <string name="label_photo_of_vin_sn">Photo of VIN/SN</string>
    <string name="label_print_asset_label">Print Asset Label</string>
    <string name="msg_asset_has_been_created">%1$s has been created</string>
    <string name="msg_asset_has_been_update">%1$s has been updated</string>
    <string name="msg_no_pending_receiving_asset">No pending receiving Asset for %1$s(%2$s), Please contact UNIS Accounting.</string>
    <string name="msg_the_category_no_item_model">The category no item model</string>
    <string name="lso_bonding_task">LSO Bonding Task</string>
    <string name="scan_label_text">Scan Label</string>
    <string name="please_input_slot">Please input or scan slot</string>
    <string name="please_input_available_transit_package_label">Please input or scan available Transit Package Label</string>
    <string name="text_Chute_label">Chute#</string>
    <string name="text_Terminal_label">Terminal</string>
    <string name="text_Route_name_label">Route Name</string>
    <string name="title_pallet_consolidation">Pallet Consolidation</string>
    <string name="title_receive_by_carton_id">Receive by cartonID</string>
    <string name="title_combine_pallet">Combine Pallet</string>
    <string name="please_scan_or_input_target_lp_carton_id">Please scan or input target LP or carton id</string>
    <string name="msg_carton_no_found">Carton no found</string>
    <string name="msg_item_status_must_be_available_or_receiving_for_consolidation">Error: Item status must be Available or Receiving for consolidation</string>
    <string name="msg_item_status_must_be_the_same_for_consolidation">Error: Item statuses must be the same for consolidation</string>
    <string name="msg_items_must_be_the_same_for_consolidation">Error: Items must be the same for consolidation</string>
    <string name="msg_pallet_consolidation_has_been_complete_please_use_lp_to_putAway">Pallet Consolidation has been complete! Please use %1$s to PutAway</string>
    <string name="msg_pallet_consolidation_combine_failure">Pallet Consolidation combine failure</string>
    <string name="msg_your_pallet_consolidation_is_not_completed_please_complete_it">Your pallet consolidation is not complete, please complete it</string>
    <string name="upload_photo_of_demage">Upload photo of damage</string>
    <string name="collection_damage_info">Collection Damage Info</string>
    <string name="tracking_num_detail">Tracking Num Detail(%s pkg)</string>
    <string name="msg_short_ship_error_tips">Detected order:%s has not loaded items:%s</string>
    <string name="msg_not_allow_short_ship_error_tips">Detected order:%s has not loaded，customer %s not allow short ship or partial ship</string>
    <string name="msg_bonding_task_confirm">Bonding package not matched，%s Pkgs scanned but %s Pkgs received.</string>
    <string name="title_search_asset">Search Asset</string>
    <string name="label_view_asset_detail">View Asset Detail</string>
    <string name="msg_the_asset_category_or_item_no_found_please_search_other_asset">The asset\'s category or item no found, please search other asset</string>
    <string name="text_accounting_information">Accounting Information</string>
    <string name="text_asset_photo">Asset Photo</string>
    <string name="text_additional_inventory_detail">Additional Inventory Detail</string>
    <string name="text_ownership_type">Ownership Type</string>
    <string name="text_using_customer">Using Customer</string>
    <string name="text_purchase_date">Purchase Date</string>
    <string name="text_asset_description">Asset Description</string>
    <string name="text_notes">Notes</string>
    <string name="text_ownership">Ownership</string>
    <string name="text_fixed_asset">Fixed Asset</string>
    <string name="text_accounting_status">Accounting Status</string>
    <string name="text_accounting_id">Accounting ID</string>
    <string name="text_cost">Cost</string>
    <string name="text_purchase_order_number">Purchase Order Number</string>
    <string name="msg_please_input_xxx">Please input %1$s</string>
    <!--cyclecount start-->
    <string name="text_location_type">LOCATION TYPE: %1$s</string>
    <string name="text_no_suggest_location">No suggest location</string>
    <string name="text_new_item_upper">NEW ITEM</string>
    <string name="text_report_empty">Report Empty</string>
    <string name="text_no_counted_lp">No counted LP</string>
    <string name="text_counted_lp">Counted LP(S): %1$s</string>
    <string name="text_counted_by_lp_qty">Counted by LP QTY</string>
    <string name="text_enter_or_scan_item">Enter or Scan Item</string>
    <string name="text_no_counted_item">No counted item</string>
    <string name="text_counted_item">Counted Item(S): %1$s</string>
    <string name="hint_quantity">Quantity</string>
    <string name="hint_lot">Lot #</string>
    <string name="hint_mfg_date">Mfg Date</string>
    <string name="text_add_lot_no">+ Lot #</string>
    <string name="label_expiration">Expiration</string>
    <string name="label_mfg">Mfg</string>
    <string name="label_shelf_life_days">Shelf Life Days</string>
    <string name="btn_append">Append</string>
    <string name="btn_overwrite">Overwrite</string>
    <string name="title_items_exist">Item(s) Exist</string>
    <string name="msg_please_input_quantity">Please input quantity</string>
    <string name="msg_please_input_shelf_life_days">Please input shelf life days</string>
    <string name="msg_please_select_expiration_date">Please select Expiration date</string>
    <string name="title_select_expiration_date">Select Expiration Date</string>
    <string name="title_select_mfg_date">Select Mfg Date</string>
    <string name="text_go_back_to_top">Go Back to Top</string>
    <string name="title_count_history_upper">COUNT HISTORY</string>
    <string name="msg_no_counted_history">No counted history</string>
    <string name="hint_lp_quantity">LP Quantity</string>
    <string name="msg_are_you_want_delete_it">Are you want to delete it?</string>
    <string name="msg_lp_has_been_added">LP has been added</string>
    <string name="msg_item_has_been_added">Item has been added</string>
    <string name="msg_item_has_been_counted">Item has been counted</string>
    <string name="msg_task_has_been_completed">%1$s has been completed</string>
    <string name="msg_is_the_location_empty">Is the location empty?</string>
    <string name="msg_please_enter_again">Please Enter Again</string>
    <string name="msg_this_item_does_not_belonged_to_this_customer">This item does not belonged to this customer.</string>
    <string name="tip_close_task">Are you sure you want to close task now?</string>
    <string name="msg_there_is_no_next_location_would_you_like_to_close_task">There is no next location. Would you like to close task?</string>
    <string name="text_maybe_later">Maybe later</string>
    <string name="msg_the_new_item_has_been_counted">The new item has been counted</string>
    <string name="please_item_not_included_in_this_task">Item %s is not included in this task</string>
    <string name="msg_lp_inventory_at_location">LP(%1$s) inventory at Location %2$s,do you want to continue it?</string>
    <string name="msg_new_lp_found_is_this_new_inventory">New LP found. Is this new inventory?</string>
    <string name="msg_there_is_already_items_linked_to_this_lp">There is already item(s) linked to this LP:\n%1$s</string>
    <string name="msg_task_un_counted_confirm">There is exist item(s) uncounted, Confirm counting to 0 for uncounted item(s) or Force close</string>
    <string name="label_require_count_lp">Require Count Lp:</string>
    <string name="label_require_count_item">Require Count Item:</string>
    <string name="label_require_count_qty">Require Count Qty:</string>
    <string name="msg_item_inventory_at_location">Item(%1$s) inventory at Location %2$s,do you want to continue it?</string>
    <string name="msg_item_inventory_at_lp">Item(%1$s) inventory at Lp %2$s,do you want to continue it?</string>
    <string name="msg_item_exists_in_lps">The item: %1$s exists in LP(s):\n %2$s</string>
    <!--cyclecount end-->

    <string name="msg_create_lp_or_lp_set_up_failure">Create lp or lp setup failure</string>

    <!--pick task start-->
    <string name="label_lp_counts">LP Counts</string>
    <string name="label_entire_lp_pick">Entire LP Pick</string>
    <string name="label_item_info">Item Info</string>
    <string name="label_skip_for_now">SKIP FOR NOW</string>
    <string name="hint_scan_to_lp">Scan or Input Pick to LP</string>
    <string name="label_picking_area">Picking Area</string>
    <string name="label_picked_message">Picked Message</string>
    <string name="text_required">Required: %s</string>
    <string name="text_picked_qty_count">Picked: %s</string>
    <string name="text_need_pick">Need Pick: %s</string>
    <string name="text_suggest_qty">Suggest: %s</string>
    <string name="text_qty_count">Qty: %s</string>
    <string name="hint_please_input_or_scan_suggest_lp">please input or scan Suggest LP</string>
    <string name="hint_please_input_or_scan_suggest_item">please input or scan Suggest Item</string>
    <string name="error_input_qty_over_suggest_qty">Input qty not allow more than suggest qty</string>
    <string name="override_not_allowed_depleted_location">Override not allowed: Depleted location</string>
    <!--pick task end-->

    <string name="label_pick_step">Pick</string>
    <string name="label_offload_step">Offload</string>
    <string name="label_lp_setup_step">LP Setup</string>
    <string name="label_scan_sn_step">Scan SN</string>
    <string name="label_stage_step">Stage</string>
    <string name="label_replenish_step">Replenish</string>

    <string name="hint_scan_item_or_lp">Scan Item or LP</string>
    <string name="msg_create_task_failure">create task failure</string>
    <string name="msg_bind_equipment_success_please_start">bind equipment success, please start</string>

    <!--put away task start-->
    <string name="hint_scan_lp_or_rn">Scan or Input LP / RN</string>
    <string name="title_not_progcessed_lps">Not Processed LPs:</string>
    <string name="hint_scan_or_input_lp_select_or_unselect">Scan or Input LP to select / unselect</string>
    <string name="title_selected_lps">Selected LPs</string>
    <string name="title_location_suggestion">Location Suggestion</string>
    <string name="please_scan_or_input_lp_to_suggest_location">Please scan or input LP to suggest location</string>
    <string name="please_scan_or_input_lp_to_put_away">Please scan or input LP to put away</string>
    <string name="msg_not_allow_put_away_to_depleted_location">Not allow putaway to depleted location</string>
    <!--put away task end-->
    <string name="text_check_in_verification">Check-In Verification</string>
    <string name="text_checked_in_driver_info">Checked In Driver Info</string>
    <string name="text_selected_load">Selected Load</string>
    <string name="text_carrier_showed_up">Carrier Showed Up:</string>
    <string name="text_driver_showed_up">Driver Showed Up:</string>
    <string name="text_driver_phone_number">Driver\'s Phone Number:</string>
    <string name="text_verified_carrier">Verified Carrier:</string>
    <string name="text_verified_driver">Verified Driver:</string>
    <string name="text_verified_driver_number">Verified Driver\'s Number:</string>
    <string name="text_pre_checked_driver_info">Pre Checked Driver Info</string>
    <string name="text_ship_to">Ship to: %1$s</string>
    <string name="text_carrier_for_load">Carrier for Load: %1$s</string>
    <string name="text_appointment">Appointment: %1$s</string>
    <string name="label_retailer">Retailer: %1$s</string>
    <string name="text_load_id_load_no">%1$s (%2$s)</string>
    <string name="text_review_and_scroll_to_bottom_to_proceed">Review and scroll to the bottom to proceed</string>
    <string name="msg_put_away_lp_select_change_tips">The suggested location will be cleared if you change the selected LP. Would you like to proceed?</string>
    <string name="text_view_driver_and_load_list">View Driver and Load List</string>

    <string name="label_need_equipment">Need Equipment?</string>
    <string name="text_go_to_drop">Go To Drop</string>
    <string name="text_back_to_collect">Back To Collect</string>
    <string name="scan_lp_or_pick_location">Scan LP/Pick Location</string>
    <string name="label_collect_qty">Collect QTY:</string>
    <string name="label_entire_lp_collect">Entire LP Collect:</string>
    <string name="label_available_item_qty">Available Item QTY:</string>
    <string name="label_item_qty_upper">Item QTY:</string>
    <string name="label_entire_drop">Entire Drop:</string>
    <string name="label_destination_location">Destination Location</string>
    <string name="msg_please_scan_destination_location">Please Scan Destination Location</string>
    <string name="msg_equipment_has_scanned_please_click_start">Equipment has scanned, Please click start</string>
    <string name="text_print_pallet">Print Pallet</string>
    <string name="text_dock_no">Dock No.</string>
    <string name="label_trans_load_type">TransLoad Type</string>
    <string name="label_end_time">End Time</string>
    <string name="text_start_receive_task">Start Receive Task</string>
    <string name="text_dock_check_in">Dock Check In</string>
    <string name="text_back_to_task_page">Back to Task Page</string>
    <string name="label_expected_uom">Expected UOM</string>
    <string name="hint_scan_pallet_to_receive">Scan Pallet to Receive</string>
    <string name="hint_scan_carton_to_receive">Scan Carton to Receive</string>
    <string name="msg_please_scan_pallet">Please Scan Pallet</string>
    <string name="label_take_photo_optional">Take Photo (Optional)</string>
    <string name="error_carton_received">Carton has been received!</string>
    <string name="msg_please_scan_carton_to_pallet">Please Scan Carton to Pallet</string>
    <string name="label_general_step">General</string>
    <string name="text_start_load_task">Start Load Task</string>
    <string name="label_loaded_qty">Loaded Qty</string>
    <string name="scan_carton_to_load">Scan Carton to Load</string>
    <string name="scan_pallet_to_load">Scan Pallet to Load</string>
    <string name="msg_please_scan_pallet_to_load">Please Scan Pallet to Load</string>
    <string name="msg_please_scan_carton_to_load">Please Scan Carton to Load</string>
    <string name="label_pallet_s">Pallet(s)</string>
    <string name="label_carton_s">Carton(s)</string>
    <string name="upc_case">UPC Case</string>
    <string name="msg_job_completed_please_select_another">The Job has been Completed,Please select another </string>
    <string name="msg_add_job_first">Please add a Job first</string>
    <string name="msg_do_you_want_start_other_job">Do you want to start other job?</string>
    <string name="drop_qty">Drop Qty</string>

    <!--put back task start-->
    <string name="label_put_back_step">Put Back</string>
    <string name="put_back_qty_invalid">Put back QTY is greater than picked QTY.</string>
    <!--put back task start-->
    <string name="msg_no_tote_in_tote_cart">No Tote Suggest In This ToteCart</string>
    <string name="process_node_is_empty">Process Node Is Empty</string>
    <string name="text_inner_door">Inner Door</string>
    <string name="text_exterior_door">Exterior Door</string>
    <string name="label_order_pick_from_wall_step">Order Pick From Wall</string>
    <string name="label_stage_to_wall_step">Stage to Wall</string>
    <string name="label_sorting_to_wall_step">Sorting to Wall</string>
    <string name="label_pick_to_wall">Pick To Wall</string>
    <string name="create_lp_fail">Create LP Fail</string>
    <string name="text_dock_check">Dock Check</string>
    <string name="label_seal_Info_collect">Seal Info Collect</string>
    <string name="error_order_plan_is_empty">Order Plan Is Empty!</string>
    <string name="title_ucc_label_check">Check UCC Label</string>
    <string name="error_select_load_equipment_not_match">Entry Ticket Equipment Type Is Empty</string>
    <string name="hint_input_trailer_no">Input trailer no</string>
    <string name="text_trailer_no">Trailer No.</string>
    <string name="label_take_photo_for_trailer">Take photo for trailer</string>
    <string name="label_take_photo_for_seal">Take photo for seal (Option)</string>
    <string name="label_take_photo_for_manifest">Take photo manifest (Option)</string>
    <string name="label_take_photo_for_progress">Take photo for progress</string>
    <string name="label_take_photo_for_sheet">Take photo for sheet</string>
    <string name="label_osd">OSD</string>
    <string name="title_dock_checked_in">Dock Checked In!</string>
    <string name="siganture_all_success">Signature All Over!</string>
    <string name="label_complete_signature">Complete Signature</string>
    <string name="error_duplicate_lp">Duplicate Lp!</string>
    <string name="please_input_signature_first">Please Input Signature First!</string>
    <string name="error_not_signature_load_bol">There are also %s that are not signed yet.</string>
    <string name="error_not_item_in_this_xx">%s has no items to put back!</string>
    <string name="msg_force_close_task">Are you force close this task now?</string>
    <string name="label_exit_task">Exit Task</string>
    <string name="msg_not_lp_remove">Not LP Can Remove!</string>
    <string name="msg_lp_not_item_to_return">LP Not Item To Return!</string>
    <string name="msg_connect_success">Connect Success!</string>
    <string name="print_by_zebra">Bluetooth Print By Zebra</string>
    <string name="label_item_edit">Item Edit</string>
    <string name="by_carton_automated">By Carton(Automated)</string>
    <string name="hint_please_scan_carton">Please scan carton</string>

    <string name="hint_scan_ip">Please Input or scan IP</string>
    <string name="title_set_up_light_and_location">Set and Bind Light ID</string>
    <string name="text_pick_to_light_id">Pick To Light ID</string>
    <string name="msg_group_not_found">light group not found</string>
    <string name="please_refresh_light_id">Please refresh light ID</string>
    <string name="text_pick_to_light_id_mark">Pick To Light ID:</string>
    <string name="title_setup_id">How to Set the Light ID</string>
    <string name="text_setup_step_1">Step 1: Long-press the light you want to set.</string>
    <string name="text_setup_step_2">Step 2: After long-pressing, tap OK to complete the setup.</string>
    <string name="msg_all_the_lights_have_been_set_scan_other_ip">All the lights have been set,Please scan other IP</string>
    <string name="msg_ip_invalid">Invalid IP!</string>
    <string name="tote_cart_not_found">Tote Cart Not Found!</string>
    <string name="msg_please_scan_or_input_port">please scan or input port</string>
    <string name="label_light_setting">Light Setting</string>
    <string name="label_light_setting_description">Pick To Light Setting</string>
    <string name="label_empty_carton_detected_confirm">Empty Carton Detected, Please Confirm!</string>
    <string name="msg_carton_check_content">If empty, place it to the bottom slot.\nIf not empty, place it to the Exception Station.</string>
    <string name="msg_carton_check_content_2">If empty, place it to the Collection Station.\nIf not empty, place it to the Exception Station.</string>
    <string name="label_not_empty">Not Empty</string>
    <string name="error_wcs_local_unavailable">Wcs Local Unavailable!</string>
    <string name="msg_partial_receive_tips">Detected Partial Receive, Do You Force Close This Step?</string>
    <string name="msg_short_receive_tips">Detected Short Receive, Do You Force Close This Step?</string>
    <string name="msg_over_receive_tips">Detected Over Receive, Do You Force Close This Step?</string>
    <string name="msg_invalid_barcode">Invalid Barcode</string>
    <string name="text_pallet_mark">Pallet：</string>
    <string name="msg_multiple_lp_found">Multiple LPs Found: %s</string>
    <string name="text_facility_not_found">Facility not found</string>
    <string name="msg_add_material_success">Add Material Success!</string>
    <string name="msg_delete_all_received_material">Are You Delete All Received Material?</string>
    <string name="label_qty_edit">Qty Edit</string>
    <string name="text_material_receive">Material Receiving</string>
    <string name="error_msg_material_received_location">Material should be received to pick location, please check.</string>
    <string name="label_batch_print">Label Batch Print</string>
    <string name="hint_scan_or_input_order_or_task">Scan or Input the Order Number or Task ID</string>
    <string name="print_label_of_Special_Order">Print Label of Special Order</string>
    <string name="label_upload_of_truck">Upload photo of truck</string>
    <string name="label_load_items">Load LPs</string>
    <string name="title_collect_info">Collect Task Info</string>
    <string name="pallet_weight_warning">Warning! Deviation exceeds %s %s, Please check. Estimated weight: %s %s, Collected weight: %s %s. </string>
    <string name="unable_close_task">Unable Close Task!</string>
    <string name="unable_force_close_task">Unable Force Close Task!</string>
    <string name="msg_lp_not_belong_to_loaction">LP: %s Not Belong To Location: %s</string>
    <string name="msg_all_lp_already_staged">All lp already staged!</string>
    <string name="msg_lp_was_not_collected_before">LP %s was not collected before.</string>
    <string name="text_force_release">Force Release</string>
    <string name="text_force_release_confirm">Force Release Confirm</string>
    <string name="label_scan_to_add">Scan to Add</string>
    <string name="label_scan_to_subtract">Scan to Subtract</string>
    <string name="tote_mark">Tote:</string>
    <string name="please_input_scan_tote">Please scan or input Tote</string>
    <string name="tote_not_found">Tote not found</string>
    <string name="only_set_light_id">Only Set Light ID</string>
    <string name="light_id_mark">Light Id：</string>
    <string name="test_to_light">Test to Light</string>
    <string name="msg_from_lp_is_empty">pick submit from lp is empty!</string>
    <string name="msg_to_lp_is_empty">Pick Submit to Lp is empty</string>
    <string name="please_input_group_name">Please input group name</string>
    <string name="please_input_port">Please input port</string>
    <string name="ip_mark">IP:</string>
    <string name="light_to_test">All Light to Test</string>
    <string name="pick_to_light_event">Pick To Light Event</string>
    <string name="get_start">Get Start</string>
    <string name="text_put_away_scan_item_tip">Scan or select item to start, or click \'Get Start\' for recommendations.</string>
    <string name="msg_lp_put_away_complete_confirm">LP %s has been put away. Continue with other LPs or close task?</string>
    <string name="msg_please_print_new_lp">Please print new lp</string>
    <string name="height_must_greater_than_zero">Height must greater than 0</string>
    <string name="location_does_not_allow_mixing_items">Location %s does not allow mixing items</string>
    <string name="rebuild_pick_strategy">Rebuild Pick Strategy</string>
    <string name="lp_label_print">LP Label Print</string>
    <string name="hint_scan_or_input_lp_tote_sn">Scan or input LP/Tote/SN</string>
    <string name="original_lp">Original LP</string>
    <string name="hint_input_ip">Please Input Ip</string>
    <string name="pick_to_light_server">Light Server Setting</string>
    <string name="msg_resend_pick_to_light">Network issue detected. Unable to connect to Pick to Light server. Click "Retry" to try again.</string>
    <string name="send_close_light_message_error">Send Close Light Message Fail!</string>
    <string name="label_click_to_start_working">Click the button to start working.</string>
    <string name="consolidate_by_item">Consolidate by item</string>
    <string name="consolidate_by_lp">Consolidate by Lp</string>
    <string name="msg_no_picked_inventory_found">Not Found Picked Inventory</string>
    <string name="label_move_qty_colon">Move Qty:</string>
    <string name="msg_scan_or_input_consolidate_to_slp">please scan or input consolidate to slp</string>
    <string name="msg_scan_or_input_from_lp">please scan or input from lp</string>
    <string name="msg_movement_success">Movement Lp Success!</string>
    <string name="text_unload_lp">UnLoad Lps</string>
    <string name="msg_all_pallet_already_loaded">All pallet already loaded!</string>
    <string name="lp_location_not_found">LP Location Not Found!</string>
    <string name="lp_not_found_by_xxx">LP not found by: %s</string>
    <string name="msg_release_dock_tips">Are you releasing the Dock now?</string>
    <string name="load_task_not_succest_unable_release_dock">Load task not success unable release dock!</string>
    <string name="title_inner_lp">Inner LP</string>
    <string name="create_new_lp">Generate &amp; Print a new LP</string>
    <string name="label_count_type">Cycle Count Type</string>
    <string name="text_by_item">By Item</string>
    <string name="text_by_item_location">By Item Location</string>
    <string name="label_expand">Expand</string>
    <string name="label_collapse">collapse</string>
    <string name="label_upload_photos">Upload Photos</string>
    <string name="msg_expiration_date_confirm">The item %s\'s shelf life days is less than ship allow days.Proceed with receipt?</string>
    <string name="msg_no_match_simple_qty_count">Quantity mismatch! You must manually count each ILP!</string>
    <string name="msg_no_match_qty_count">Quantity mismatch!</string>
    <string name="label_count_method">Count Method</string>
    <string name="error_item_counted_no_match_please_count_by_lp">Item counted no match please count by lp</string>
    <string name="msg_please_reopen_step">Please reopen step</string>
    <string name="msg_order_rollback_to_picked">Please roll back the order to the picked state</string>
    <string name="title_assembly_task">Assembly Task</string>
    <string name="title_assembly_task_list">Assembly task list</string>
    <string name="msg_complete_assembly_pick_first">Please complete assembly pick step first</string>
    <string name="msg_complete_assembly_kitting_first">Please complete assembly kitting step first</string>
    <string name="project_code">Project Code</string>
    <string name="assembly_receipt_id">Assembly Receipt ID</string>
    <string name="title_assembly_pick_step">Step 1:Assembly Pick</string>
    <string name="title_component_list">Component List</string>
    <string name="title_component_item">Component Item</string>
    <string name="title_suggest_location">Suggest Location</string>
    <string name="title_picked_total_qty">Picked /Total Qty</string>
    <string name="hint_enter_pick_qty">Enter Pick Qty</string>
    <string name="label_estimated_time">Estimated Time</string>
    <string name="label_job_duration">Job Duration</string>
    <string name="label_job_code_remain_time">Job Code Remain Time</string>
    <string name="title_assembly_kitting_step">Step 2:Kitting</string>
    <string name="error_component_not_match">Component not match</string>
    <string name="msg_please_scan_or_item_and_lp_first">Please scan or input item and lp</string>
    <string name="error_kitting_item_not_in_putaway_list">Input kitting item not in putAway list</string>
    <string name="title_assembly_pick">Assembly Pick</string>
    <string name="title_assembly_put_away">Assembly Putaway</string>
    <string name="label_job_name">Job Name</string>
    <string name="msg_general_step_not_all_complete">There are still unfinished jobs: %s</string>
    <string name="msg_have_no_put_away_items">There are items that have not been put away: %s</string>
    <string name="msg_item_exceeds_expected_quantity_confirm">Item %1$s exceeds the expected quantity (%2$s). Continue receiving?</string>
    <string name="msg_please_confirm_multiple_order_stage_in_location">Please confirm that %1$s are stage in %2$s</string>
    <string name="title_outbound_qc_task">Outbound QC Task</string>
    <string name="text_inspection">Inspection</string>
    <string name="text_fix">FIX</string>
    <string name="text_staging_location">Staging Location</string>
    <string name="text_label_requirement">Label Requirement</string>
    <string name="text_height_requirement">Height Requirement</string>
    <string name="text_pallet_height">Pallet Height</string>
    <string name="text_packaging_condition">Packaging Condition</string>
    <string name="title_item_condition">Item Condition</string>
    <string name="error_exit_xxx_uncheck_over_please_check_again_this_item">There are unchecked items (%s). Please check all unchecked items again.</string>
    <string name="msg_need_update_content_tips">The items have been modified. Do you want to save the changes?</string>
    <string name="error_exit_xxx_unfix_over_please_check_again_this_item">There are unfinished items: (%s), please fix these items.</string>
    <string name="text_packing_list">Packing List</string>
    <string name="msg_complete_inspection_first">Cannot proceed to the next step because the inspection is not yet complete.</string>
    <string name="msg_order_qc_task_not_complete">Order QC is not completed, loading cannot proceed.</string>
    <string name="text_task_action">Task Action</string>
    <string name="title_place_action">Place Action</string>
    <string name="title_pick_action">Pick Action</string>
    <string name="title_action_detail">Action Detail</string>
    <string name="title_capability_and_location">Capability And Location</string>
    <string name="text_user_capability">User Capability:</string>
    <string name="msg_please_input_or_scan_cur_location">Please input or scan the current location</string>
    <string name="text_forklift_operator">Forklift Operator</string>
    <string name="error_task_action_not_found_wms_task_id">wmsTaskId in task action is empty</string>
    <string name="suggest_location_hint">Go to location %s and scan the location tag</string>
    <string name="label_target_location">Target Location:</string>
    <string name="error_user_no_available_capability">There is no capability available for the current user</string>
    <string name="hint_pick_action_enter_qty">LP quantity exceeds remaining quantity.\n Please enter the quantity to pick:</string>
    <string name="error_not_found_task_action">No pending task actions</string>
    <string name="error_no_data_to_submit">No data to submit</string>
    <string name="label_place_lps">Place LP(s):</string>
    <string name="text_go_to_stage">Go To Stage</string>
    <string name="msg_exit_not_submit_do_you_go_to_stage">You have unsaved data. It is recommended to submit the data before proceeding with this action. If you proceed to STAGE, this unsaved data will be discarded. Do you still want to go to STAGE?</string>
    <string name="msg_exit_not_submit_do_you_want_to_process">You have unsaved data. It is recommended to submit the data before proceeding. If you continue, this unsaved data will be discarded. Do you still want to proceed?</string>
    <string name="change_user_capability">Change User Capability</string>
    <string name="no_capabilities_selected">No capabilities selected</string>
    <string name="error_please_select_user_capability_first">Please select at least one user capability</string>
    <string name="error_item_inconsistency">Item Inconsistency. Please check.</string>
    <string name="msg_not_allow_over_picked">Not Allow over picked</string>
    <string name="btn_unload_all">unLoad All</string>
    <string name="text_unload_all_confirm_title">Confirm Unload All?</string>
    <string name="text_unload_all_confirm_message">Are you sure to unload all pallets?</string>
    <string name="msg_no_unloadable_pallets">No unloadable pallets</string>
    <string name="text_send_command">send command</string>
    <string name="only_allow_move_inventory_with_status_of_available_damage_and_on_hold">Only allow move inventory with status of Available, Damge and On Hold.</string>
    <string name="text_submit_and_stage">Submit And Stage</string>
    <string name="text_skip_action">Skip Action</string>
    <string name="text_user_pending_action">User Pending Action</string>
    <string name="title_pick_work_data">Pick Work Data</string>
    <string name="msg_skip_action_check">Whether to confirm skipping the %s operation</string>
    <string name="title_pick_auto_submit_stage">Notice: System will automatically submit picked items and proceed to staging</string>
    <string name="title_pending_action">Pending Action</string>
    <string name="label_action_type">Action Type</string>
    <string name="label_action_id">Action ID</string>
    <string name="error_invalid_lp">Invalid LP format</string>
    <string name="task_type_select_title">Below are the task types related to %s, Please select to search.</string>
    <string name="msg_tote_qty_less_than_order_qty">Tote qty less than order qty. Please bind another tote cart or split the task.</string>
    <string name="text_please_call">Please call</string>
    <string name="text_issue">Issue: </string>
    <string name="text_waiting_for_approval">Waiting for Approval</string>
    <string name="text_override_request_sent">Override request sent. Awaiting supervisor approval.</string>
    <string name="text_next_supervisor">Next Supervisor</string>
    <string name="text_override_approved">Override Approved</string>
    <string name="text_override_rejected">Override Rejected</string>
    <string name="title_approval_center">Approval Center</string>
    <string name="summary_approval_center">Review and approve pending requests</string>
    <string name="title_location_override_request">Location Override Request</string>
    <string name="label_issue">Issue: </string>
    <string name="status_approved">Approved</string>
    <string name="status_rejected">Rejected</string>
    <string name="override_location_description">Override location %1$s to %2$s</string>
    <string name="override_location_request_description">%1$s request to override location.</string>
    <string name="msg_close_location_light">Please Close Location Light First</string>
    <string name="text_visible_or_gone_submit">Visible Or Gone Submit</string>
    <string name="title_location_override_report">Location Override Report</string>
    <string name="error_lp_already_not_item_able_pick">%s There are no %s items that can be picked</string>
    <string name="error_not_allow_entire_lp_pick">Not allow entire lp pick</string>
    <string name="title_affix_label_action">Affix Label Action</string>
    <string name="msg_complete_task_action_confirm">Are you sure to complete this task action?</string>
    <string name="error_pick_to_light_submit_fail">Pick To Light Submit Fail !</string>
    <string name="text_fixed">Fixed</string>
    <string name="text_unfixed">unFixed</string>
    <string name="msg_please_input_comment_and_take_photo">Please input repair description and take photos of repair results</string>
    <string name="msg_replenishment_notification">There are still Replenishment Tasks: %s not completed, please complete them first</string>
    <string name="btn_preload_driver_sign">Preload Driver Sign</string>
    <string name="msg_carrier_sign_confirm">This is a Preload Task. Please confirm carrier signature is signed by driver. DN will mark shipped after signing.</string>
    <string name="error_signature_empty">Please sign first!</string>
    <!-- 增强的Putaway功能 -->
    <string name="current_rn_item">Current RN Item: %1$s</string>
    <string name="msg_rn_item_mismatch">The scanned RN %1$s contains item %3$s, which does not match the existing item %2$s. Please ensure all RNs contain the same item.</string>
    <string name="msg_receipt_date_range_exceeded">Receipts %1$s have a date range exceeding the allowed %2$d days (actual: %3$d days).</string>
    <string name="msg_mixed_pallet_types_not_allowed">Full and Partial Pallets cannot be Put Away together</string>
    <string name="use_scandit_for_scan">Use Scandit to scan barcode</string>
    <string name="enable_scandit_scan">Enable Scandit scanner</string>
    <string name="title_item_stack_selection">Select Item Stack Height</string>
    <string name="msg_item_stack_selection">Please select the stack Height for item: %1$s</string>
    <string name="hint_enter_stack_quantity">Enter stack height (1–999)</string>
    <string name="error_stack_height_required">Stack height is required</string>
    <string name="error_stack_height_range">Stack height must be between 1 and 999</string>
    <string name="error_invalid_number">Invalid number format</string>
    <string name="msg_item_stack_saved_success">Item stack saved successfully</string>
    <string name="error_item_stack_save_failed">Failed to save item stack</string>
    <string name="label_stack_height">Stack Height</string>
    <string name="msg_item_info">Item: %1$s</string>
    <string name="team_name_mark">Team Name:</string>
    <string name="labor_type_mark">Labor Type:</string>
    <string name="operation_type_mark">Operation Type:</string>
    <string name="today_work_hours">Today\'s Work Hours</string>
    <string name="weekly_hours">Weekly Hours</string>
    <string name="extend_shift">Extend Shift</string>
    <string name="end_work">End Work</string>
    <string name="extension_duration">Extension Duration</string>
    <string name="request_type">Request Type</string>
    <string name="reason_for_extension">Reason for Extension</string>
    <string name="hint_please_provide_reason">Please provide the reason for extending your shift…</string>
    <string name="work_hours_alert_tips">You have completed %1$s hours of work assignment today and have been automatically clocked out.</string>
    <string name="work_hours_alert_tips_weekly">You have completed %1$s hours of work assignment this week and have been automatically clocked out.</string>
    <string name="pending_tips">Your extension request is pending approval. please wait for supervisor response.</string>
    <string name="request_extend_shift_tips">!!! You have completed %1$s hours of work assignment today. But you are requested to extend shift</string>
    <string name="request_time_1">Request Time</string>
    <string name="extension_time">Extension Time</string>
    <string name="waiting_for_supervisor_approval">Waiting for supervisor approval…</string>
    <string name="pending_tips_1">You will be notified when your supervisor responds to this request.</string>
    <string name="approved_time">Approved Time</string>
    <string name="approved_by">Approved By</string>
    <string name="approved_tips">Your extension request has been approved. Click the button below to start your extended shift.</string>
    <string name="start_extended_shift">Start Extended Shift</string>
    <string name="response_time">Response Time</string>
    <string name="rejected_by">Rejected by</string>
    <string name="request_additional_extension">Request Additional Extension</string>
    <string name="employee">Employee</string>
    <string name="team_name">Team Name</string>
    <string name="labor_type">Labor Type</string>
    <string name="operation_type">Operation Type</string>
    <string name="worker_assignment">Worker Assignment</string>
    <string name="extend_shift_request">Extend Shift Request</string>
    <string name="approved_tips_1">Your extension request has been approved!</string>
    <string name="rejected_tips">Your extension request has been rejected!</string>
    <string name="completed_tips">You have completed your approved extension hours. The system has automatically clocked you out.</string>
    <string name="work_hours_alert">Work Hours Alert</string>
    <string name="text_xx_hours">%1$s Hours</string>
    <string name="text_extended_hours">Extended Hours</string>
    <string name="text_daily_ot">Daily OT</string>
    <string name="text_daily_dt">Daily DT</string>
    <string name="text_weekly_ot">Weekly OT</string>
    <string name="text_hours">Hours</string>
    <string name="text_minutes">Minutes</string>
    <string name="are_you_sure_you_want_to_end_work">Are you sure you want to end work?</string>
    <string name="text_approve_request">Approve Request</string>
    <string name="text_reject_request">Reject Request</string>
    <string name="msg_please_select_extension_duration">Please select extension duration</string>
    <string name="requested_time">Requested Time</string>
    <string name="requested_by">Requested By</string>
    <string name="extension_request">Extension Request</string>
    <string name="extension_requests">Extension Requests</string>
    <string name="extension_request_status">Extension Request Status</string>
    <string name="extension_request_approved">Extension Request Approved</string>
    <string name="extension_request_rejected">Extension Request Rejected</string>
    <string name="extension_completion">Extension Completion</string>
    <string name="extension_request_details">Extension Request Details</string>
    <string name="submit_shift">Submit Shift</string>
    <string name="xxx_pending_requests">%1$s Pending Requests</string>
    <string name="extension_request_duration">Extension: %1$s</string>
    <string name="please_select_team">Please select team</string>
    <string name="please_select_labor">Please select labor</string>
    <string name="please_add_operation_type">Please add operation type</string>
    <string name="multiple_operation_type">Multiple Operation Type</string>
    <string name="no_team_tips">Unable to find your assigned team? Please contact your supervisor immediately.</string>
    <string name="no_labor_type_tips">Unable to find your assigned labor type? Please contact your supervisor immediately.</string>
    <string name="no_operation_type_tips">Unable to find your assigned operation type? Please contact your supervisor immediately.</string>
    <string name="no_employee_id_tips">Employee ID not configured, Please contact your supervisor immediately.</string>
    <string name="processed_by">Processed By</string>
    <string name="submit_time_from">Submit Date From</string>
    <string name="submit_time_to">Submit Date To</string>
    <string name="processed_time_from">Processed Date From</string>
    <string name="processed_time_to">Processed Date To</string>
    <string name="error_start_date_after_end_date">The start date cannot be later than the end date</string>
    <string name="error_end_date_before_start_date">The end date cannot be earlier than the start date</string>
    <string name="msg_maximum_labor_limit_reached">Maximum labor limit reached, please contact your supervisor immediately.</string>
    <string name="label_turn_off_load">Turn OFF Direct Load</string>
    <string name="label_turn_on_load">Turn On Direct Load</string>
    <string name="title_load_task_describe">Load Task Describe</string>
</resources>