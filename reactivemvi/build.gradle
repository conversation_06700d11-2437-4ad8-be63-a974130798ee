apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply from: file("${rootDir}/config/library.gradle")

android {
    namespace "com.unis.reactivemvi"
    lintOptions {
        abortOnError false
    }

    buildFeatures{
        viewBinding true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(':platform')
    api project(':widget')
    api rootProject.ext.dependencies["title-bar"]
    implementation files('lib/zebra_bluetooth.jar')
}